"use strict";(globalThis.webpackChunknotion_next=globalThis.webpackChunknotion_next||[]).push([[6187],{7040:(r,t,e)=>{var n=e(604495);r.exports=n&&!Symbol.sham&&"symbol"==typeof Symbol.iterator},16280:(r,t,e)=>{var n=e(746518),o=e(444576),i=e(318745),u=e(714601),a="WebAssembly",c=o[a],f=7!==new Error("e",{cause:7}).cause,s=function(r,t){var e={};e[r]=u(r,t,f),n({global:!0,constructor:!0,arity:1,forced:f},e)},p=function(r,t){if(c&&c[r]){var e={};e[r]=u(a+"."+r,t,f),n({target:a,stat:!0,constructor:!0,arity:1,forced:f},e)}};s("Error",(function(r){return function(t){return i(r,this,arguments)}})),s("EvalError",(function(r){return function(t){return i(r,this,arguments)}})),s("RangeError",(function(r){return function(t){return i(r,this,arguments)}})),s("ReferenceError",(function(r){return function(t){return i(r,this,arguments)}})),s("SyntaxError",(function(r){return function(t){return i(r,this,arguments)}})),s("TypeError",(function(r){return function(t){return i(r,this,arguments)}})),s("URIError",(function(r){return function(t){return i(r,this,arguments)}})),p("CompileError",(function(r){return function(t){return i(r,this,arguments)}})),p("LinkError",(function(r){return function(t){return i(r,this,arguments)}})),p("RuntimeError",(function(r){return function(t){return i(r,this,arguments)}}))},28551:(r,t,e)=>{var n=e(820034),o=String,i=TypeError;r.exports=function(r){if(n(r))return r;throw new i(o(r)+" is not an object")}},39297:(r,t,e)=>{var n=e(179504),o=e(748981),i=n({}.hasOwnProperty);r.exports=Object.hasOwn||function(r,t){return i(o(r),t)}},48686:(r,t,e)=>{var n=e(743724),o=e(779039);r.exports=n&&o((function(){return 42!==Object.defineProperty((function(){}),"prototype",{value:42,writable:!1}).prototype}))},80741:r=>{var t=Math.ceil,e=Math.floor;r.exports=Math.trunc||function(r){var n=+r;return(n>0?e:t)(n)}},92140:(r,t,e)=>{var n={};n[e(978227)("toStringTag")]="z",r.exports="[object z]"===String(n)},113925:(r,t,e)=>{var n=e(820034);r.exports=function(r){return n(r)||null===r}},116823:r=>{var t=String;r.exports=function(r){try{return t(r)}catch(e){return"Object"}}},130421:r=>{r.exports={}},135031:(r,t,e)=>{var n=e(497751),o=e(179504),i=e(138480),u=e(933717),a=e(28551),c=o([].concat);r.exports=n("Reflect","ownKeys")||function(r){var t=i.f(a(r)),e=u.f;return e?c(t,e(r)):t}},135917:(r,t,e)=>{var n=e(743724),o=e(779039),i=e(404055);r.exports=!n&&!o((function(){return 7!==Object.defineProperty(i("div"),"a",{get:function(){return 7}}).a}))},136955:(r,t,e)=>{var n=e(92140),o=e(194901),i=e(544576),u=e(978227)("toStringTag"),a=Object,c="Arguments"===i(function(){return arguments}());r.exports=n?i:function(r){var t,e,n;return void 0===r?"Undefined":null===r?"Null":"string"==typeof(e=function(r,t){try{return r[t]}catch(e){}}(t=a(r),u))?e:c?i(t):"Object"===(n=i(t))&&o(t.callee)?"Arguments":n}},138480:(r,t,e)=>{var n=e(961828),o=e(188727).concat("length","prototype");t.f=Object.getOwnPropertyNames||function(r){return n(r,o)}},146706:(r,t,e)=>{var n=e(179504),o=e(479306);r.exports=function(r,t,e){try{return n(o(Object.getOwnPropertyDescriptor(r,t)[e]))}catch(i){}}},152967:(r,t,e)=>{var n=e(146706),o=e(820034),i=e(567750),u=e(473506);r.exports=Object.setPrototypeOf||("__proto__"in{}?function(){var r,t=!1,e={};try{(r=n(Object.prototype,"__proto__","set"))(e,[]),t=e instanceof Array}catch(a){}return function(e,n){return i(e),u(n),o(e)?(t?r(e,n):e.__proto__=n,e):e}}():void 0)},179504:(r,t,e)=>{var n=e(640616),o=Function.prototype,i=o.call,u=n&&o.bind.bind(i,i);r.exports=n?u:function(r){return function(){return i.apply(r,arguments)}}},188727:r=>{r.exports=["constructor","hasOwnProperty","isPrototypeOf","propertyIsEnumerable","toLocaleString","toString","valueOf"]},194901:r=>{var t="object"==typeof document&&document.all;r.exports=void 0===t&&void 0!==t?function(r){return"function"==typeof r||r===t}:function(r){return"function"==typeof r}},210757:(r,t,e)=>{var n=e(497751),o=e(194901),i=e(401625),u=e(7040),a=Object;r.exports=u?function(r){return"symbol"==typeof r}:function(r){var t=n("Symbol");return o(t)&&i(t.prototype,a(r))}},218014:(r,t,e)=>{var n=e(991291),o=Math.min;r.exports=function(r){var t=n(r);return t>0?o(t,9007199254740991):0}},225397:(r,t,e)=>{var n=e(947055),o=e(567750);r.exports=function(r){return n(o(r))}},248773:(r,t)=>{var e={}.propertyIsEnumerable,n=Object.getOwnPropertyDescriptor,o=n&&!e.call({1:2},1);t.f=o?function(r){var t=n(this,r);return!!t&&t.enumerable}:e},258622:(r,t,e)=>{var n=e(444576),o=e(194901),i=n.WeakMap;r.exports=o(i)&&/native code/.test(String(i))},266699:(r,t,e)=>{var n=e(743724),o=e(824913),i=e(406980);r.exports=n?function(r,t,e){return o.f(r,t,i(1,e))}:function(r,t,e){return r[t]=e,r}},318745:(r,t,e)=>{var n=e(640616),o=Function.prototype,i=o.apply,u=o.call;r.exports="object"==typeof Reflect&&Reflect.apply||(n?u.bind(i):function(){return u.apply(i,arguments)})},323167:(r,t,e)=>{var n=e(194901),o=e(820034),i=e(152967);r.exports=function(r,t,e){var u,a;return i&&n(u=t.constructor)&&u!==e&&o(a=u.prototype)&&a!==e.prototype&&i(r,a),r}},324659:(r,t,e)=>{var n=e(779039),o=e(406980);r.exports=!n((function(){var r=new Error("a");return!("stack"in r)||(Object.defineProperty(r,"stack",o(1,7)),7!==r.stack)}))},326198:(r,t,e)=>{var n=e(218014);r.exports=function(r){return n(r.length)}},332603:(r,t,e)=>{var n=e(500655);r.exports=function(r,t){return void 0===r?arguments.length<2?"":t:n(r)}},350283:(r,t,e)=>{var n=e(179504),o=e(779039),i=e(194901),u=e(39297),a=e(743724),c=e(610350).CONFIGURABLE,f=e(933706),s=e(591181),p=s.enforce,l=s.get,v=String,y=Object.defineProperty,h=n("".slice),b=n("".replace),g=n([].join),x=a&&!o((function(){return 8!==y((function(){}),"length",{value:8}).length})),m=String(String).split("String"),d=r.exports=function(r,t,e){"Symbol("===h(v(t),0,7)&&(t="["+b(v(t),/^Symbol\(([^)]*)\).*$/,"$1")+"]"),e&&e.getter&&(t="get "+t),e&&e.setter&&(t="set "+t),(!u(r,"name")||c&&r.name!==t)&&(a?y(r,"name",{value:t,configurable:!0}):r.name=t),x&&e&&u(e,"arity")&&r.length!==e.arity&&y(r,"length",{value:e.arity});try{e&&u(e,"constructor")&&e.constructor?a&&y(r,"prototype",{writable:!1}):r.prototype&&(r.prototype=void 0)}catch(o){}var n=p(r);return u(n,"source")||(n.source=g(m,"string"==typeof t?t:"")),r};Function.prototype.toString=d((function(){return i(this)&&l(this).source||f(this)}),"toString")},377347:(r,t,e)=>{var n=e(743724),o=e(969565),i=e(248773),u=e(406980),a=e(225397),c=e(956969),f=e(39297),s=e(135917),p=Object.getOwnPropertyDescriptor;t.f=n?p:function(r,t){if(r=a(r),t=c(t),s)try{return p(r,t)}catch(e){}if(f(r,t))return u(!o(i.f,r,t),r[t])}},380747:(r,t,e)=>{var n=e(266699),o=e(516193),i=e(324659),u=Error.captureStackTrace;r.exports=function(r,t,e,a){i&&(u?u(r,t):n(r,"stack",o(e,a)))}},401625:(r,t,e)=>{var n=e(179504);r.exports=n({}.isPrototypeOf)},404055:(r,t,e)=>{var n=e(444576),o=e(820034),i=n.document,u=o(i)&&o(i.createElement);r.exports=function(r){return u?i.createElement(r):{}}},406980:r=>{r.exports=function(r,t){return{enumerable:!(1&r),configurable:!(2&r),writable:!(4&r),value:t}}},435610:(r,t,e)=>{var n=e(991291),o=Math.max,i=Math.min;r.exports=function(r,t){var e=n(r);return e<0?o(e+t,0):i(e,t)}},436840:(r,t,e)=>{var n=e(194901),o=e(824913),i=e(350283),u=e(939433);r.exports=function(r,t,e,a){a||(a={});var c=a.enumerable,f=void 0!==a.name?a.name:t;if(n(e)&&i(e,f,a),a.global)c?r[t]=e:u(t,e);else{try{a.unsafe?r[t]&&(c=!0):delete r[t]}catch(s){}c?r[t]=e:o.f(r,t,{value:e,enumerable:!1,configurable:!a.nonConfigurable,writable:!a.nonWritable})}return r}},444576:function(r,t,e){var n=function(r){return r&&r.Math===Math&&r};r.exports=n("object"==typeof globalThis&&globalThis)||n("object"==typeof window&&window)||n("object"==typeof self&&self)||n("object"==typeof e.g&&e.g)||n("object"==typeof this&&this)||function(){return this}()||Function("return this")()},473506:(r,t,e)=>{var n=e(113925),o=String,i=TypeError;r.exports=function(r){if(n(r))return r;throw new i("Can't set "+o(r)+" as a prototype")}},477629:(r,t,e)=>{var n=e(996395),o=e(444576),i=e(939433),u="__core-js_shared__",a=r.exports=o[u]||i(u,{});(a.versions||(a.versions=[])).push({version:"3.38.1",mode:n?"pure":"global",copyright:"© 2014-2024 Denis Pushkarev (zloirock.ru)",license:"https://github.com/zloirock/core-js/blob/v3.38.1/LICENSE",source:"https://github.com/zloirock/core-js"})},479306:(r,t,e)=>{var n=e(194901),o=e(116823),i=TypeError;r.exports=function(r){if(n(r))return r;throw new i(o(r)+" is not a function")}},492796:(r,t,e)=>{var n=e(779039),o=e(194901),i=/#|\.prototype\./,u=function(r,t){var e=c[a(r)];return e===s||e!==f&&(o(t)?n(t):!!t)},a=u.normalize=function(r){return String(r).replace(i,".").toLowerCase()},c=u.data={},f=u.NATIVE="N",s=u.POLYFILL="P";r.exports=u},497751:(r,t,e)=>{var n=e(444576),o=e(194901);r.exports=function(r,t){return arguments.length<2?(e=n[r],o(e)?e:void 0):n[r]&&n[r][t];var e}},500655:(r,t,e)=>{var n=e(136955),o=String;r.exports=function(r){if("Symbol"===n(r))throw new TypeError("Cannot convert a Symbol value to a string");return o(r)}},511056:(r,t,e)=>{var n=e(824913).f;r.exports=function(r,t,e){e in r||n(r,e,{configurable:!0,get:function(){return t[e]},set:function(r){t[e]=r}})}},516193:(r,t,e)=>{var n=e(179504),o=Error,i=n("".replace),u=String(new o("zxcasd").stack),a=/\n\s*at [^:]*:[^\n]*/,c=a.test(u);r.exports=function(r,t){if(c&&"string"==typeof r&&!o.prepareStackTrace)for(;t--;)r=i(r,a,"");return r}},544576:(r,t,e)=>{var n=e(179504),o=n({}.toString),i=n("".slice);r.exports=function(r){return i(o(r),8,-1)}},567750:(r,t,e)=>{var n=e(964117),o=TypeError;r.exports=function(r){if(n(r))throw new o("Can't call method on "+r);return r}},577584:(r,t,e)=>{var n=e(820034),o=e(266699);r.exports=function(r,t){n(t)&&"cause"in t&&o(r,"cause",t.cause)}},591181:(r,t,e)=>{var n,o,i,u=e(258622),a=e(444576),c=e(820034),f=e(266699),s=e(39297),p=e(477629),l=e(766119),v=e(130421),y="Object already initialized",h=a.TypeError,b=a.WeakMap;if(u||p.state){var g=p.state||(p.state=new b);g.get=g.get,g.has=g.has,g.set=g.set,n=function(r,t){if(g.has(r))throw new h(y);return t.facade=r,g.set(r,t),t},o=function(r){return g.get(r)||{}},i=function(r){return g.has(r)}}else{var x=l("state");v[x]=!0,n=function(r,t){if(s(r,x))throw new h(y);return t.facade=r,f(r,x,t),t},o=function(r){return s(r,x)?r[x]:{}},i=function(r){return s(r,x)}}r.exports={set:n,get:o,has:i,enforce:function(r){return i(r)?o(r):n(r,{})},getterFor:function(r){return function(t){var e;if(!c(t)||(e=o(t)).type!==r)throw new h("Incompatible receiver, "+r+" required");return e}}}},596837:r=>{var t=TypeError;r.exports=function(r){if(r>9007199254740991)throw t("Maximum allowed index exceeded");return r}},604495:(r,t,e)=>{var n=e(839519),o=e(779039),i=e(444576).String;r.exports=!!Object.getOwnPropertySymbols&&!o((function(){var r=Symbol("symbol detection");return!i(r)||!(Object(r)instanceof Symbol)||!Symbol.sham&&n&&n<41}))},610350:(r,t,e)=>{var n=e(743724),o=e(39297),i=Function.prototype,u=n&&Object.getOwnPropertyDescriptor,a=o(i,"name"),c=a&&"something"===function(){}.name,f=a&&(!n||n&&u(i,"name").configurable);r.exports={EXISTS:a,PROPER:c,CONFIGURABLE:f}},640616:(r,t,e)=>{var n=e(779039);r.exports=!n((function(){var r=function(){}.bind();return"function"!=typeof r||r.hasOwnProperty("prototype")}))},655966:(r,t,e)=>{var n=e(479306),o=e(964117);r.exports=function(r,t){var e=r[t];return o(e)?void 0:n(e)}},714601:(r,t,e)=>{var n=e(497751),o=e(39297),i=e(266699),u=e(401625),a=e(152967),c=e(877740),f=e(511056),s=e(323167),p=e(332603),l=e(577584),v=e(380747),y=e(743724),h=e(996395);r.exports=function(r,t,e,b){var g="stackTraceLimit",x=b?2:1,m=r.split("."),d=m[m.length-1],w=n.apply(null,m);if(w){var O=w.prototype;if(!h&&o(O,"cause")&&delete O.cause,!e)return w;var S=n("Error"),j=t((function(r,t){var e=p(b?t:r,void 0),n=b?new w(r):new w;return void 0!==e&&i(n,"message",e),v(n,j,n.stack,2),this&&u(O,this)&&s(n,this,j),arguments.length>x&&l(n,arguments[x]),n}));if(j.prototype=O,"Error"!==d?a?a(j,S):c(j,S,{name:!0}):y&&g in w&&(f(j,w,g),f(j,w,"prepareStackTrace")),c(j,w),!h)try{O.name!==d&&i(O,"name",d),O.constructor=j}catch(E){}return j}}},725745:(r,t,e)=>{var n=e(477629);r.exports=function(r,t){return n[r]||(n[r]=t||{})}},733392:(r,t,e)=>{var n=e(179504),o=0,i=Math.random(),u=n(1..toString);r.exports=function(r){return"Symbol("+(void 0===r?"":r)+")_"+u(++o+i,36)}},734376:(r,t,e)=>{var n=e(544576);r.exports=Array.isArray||function(r){return"Array"===n(r)}},743724:(r,t,e)=>{var n=e(779039);r.exports=!n((function(){return 7!==Object.defineProperty({},1,{get:function(){return 7}})[1]}))},746518:(r,t,e)=>{var n=e(444576),o=e(377347).f,i=e(266699),u=e(436840),a=e(939433),c=e(877740),f=e(492796);r.exports=function(r,t){var e,s,p,l,v,y=r.target,h=r.global,b=r.stat;if(e=h?n:b?n[y]||a(y,{}):n[y]&&n[y].prototype)for(s in t){if(l=t[s],p=r.dontCallGetSet?(v=o(e,s))&&v.value:e[s],!f(h?s:y+(b?".":"#")+s,r.forced)&&void 0!==p){if(typeof l==typeof p)continue;c(l,p)}(r.sham||p&&p.sham)&&i(l,"sham",!0),u(e,s,l,r)}}},748981:(r,t,e)=>{var n=e(567750),o=Object;r.exports=function(r){return o(n(r))}},766119:(r,t,e)=>{var n=e(725745),o=e(733392),i=n("keys");r.exports=function(r){return i[r]||(i[r]=o(r))}},779039:r=>{r.exports=function(r){try{return!!r()}catch(t){return!0}}},784270:(r,t,e)=>{var n=e(969565),o=e(194901),i=e(820034),u=TypeError;r.exports=function(r,t){var e,a;if("string"===t&&o(e=r.toString)&&!i(a=n(e,r)))return a;if(o(e=r.valueOf)&&!i(a=n(e,r)))return a;if("string"!==t&&o(e=r.toString)&&!i(a=n(e,r)))return a;throw new u("Can't convert object to primitive value")}},820034:(r,t,e)=>{var n=e(194901);r.exports=function(r){return"object"==typeof r?null!==r:n(r)}},824913:(r,t,e)=>{var n=e(743724),o=e(135917),i=e(48686),u=e(28551),a=e(956969),c=TypeError,f=Object.defineProperty,s=Object.getOwnPropertyDescriptor,p="enumerable",l="configurable",v="writable";t.f=n?i?function(r,t,e){if(u(r),t=a(t),u(e),"function"==typeof r&&"prototype"===t&&"value"in e&&v in e&&!e[v]){var n=s(r,t);n&&n[v]&&(r[t]=e.value,e={configurable:l in e?e[l]:n[l],enumerable:p in e?e[p]:n[p],writable:!1})}return f(r,t,e)}:f:function(r,t,e){if(u(r),t=a(t),u(e),o)try{return f(r,t,e)}catch(n){}if("get"in e||"set"in e)throw new c("Accessors not supported");return"value"in e&&(r[t]=e.value),r}},839519:(r,t,e)=>{var n,o,i=e(444576),u=e(882839),a=i.process,c=i.Deno,f=a&&a.versions||c&&c.version,s=f&&f.v8;s&&(o=(n=s.split("."))[0]>0&&n[0]<4?1:+(n[0]+n[1])),!o&&u&&(!(n=u.match(/Edge\/(\d+)/))||n[1]>=74)&&(n=u.match(/Chrome\/(\d+)/))&&(o=+n[1]),r.exports=o},872777:(r,t,e)=>{var n=e(969565),o=e(820034),i=e(210757),u=e(655966),a=e(784270),c=e(978227),f=TypeError,s=c("toPrimitive");r.exports=function(r,t){if(!o(r)||i(r))return r;var e,c=u(r,s);if(c){if(void 0===t&&(t="default"),e=n(c,r,t),!o(e)||i(e))return e;throw new f("Can't convert object to primitive value")}return void 0===t&&(t="number"),a(r,t)}},877740:(r,t,e)=>{var n=e(39297),o=e(135031),i=e(377347),u=e(824913);r.exports=function(r,t,e){for(var a=o(t),c=u.f,f=i.f,s=0;s<a.length;s++){var p=a[s];n(r,p)||e&&n(e,p)||c(r,p,f(t,p))}}},882839:(r,t,e)=>{var n=e(444576).navigator,o=n&&n.userAgent;r.exports=o?String(o):""},919617:(r,t,e)=>{var n=e(225397),o=e(435610),i=e(326198),u=function(r){return function(t,e,u){var a=n(t),c=i(a);if(0===c)return!r&&-1;var f,s=o(u,c);if(r&&e!=e){for(;c>s;)if((f=a[s++])!=f)return!0}else for(;c>s;s++)if((r||s in a)&&a[s]===e)return r||s||0;return!r&&-1}};r.exports={includes:u(!0),indexOf:u(!1)}},933706:(r,t,e)=>{var n=e(179504),o=e(194901),i=e(477629),u=n(Function.toString);o(i.inspectSource)||(i.inspectSource=function(r){return u(r)}),r.exports=i.inspectSource},933717:(r,t)=>{t.f=Object.getOwnPropertySymbols},934527:(r,t,e)=>{var n=e(743724),o=e(734376),i=TypeError,u=Object.getOwnPropertyDescriptor,a=n&&!function(){if(void 0!==this)return!0;try{Object.defineProperty([],"length",{writable:!1}).length=1}catch(r){return r instanceof TypeError}}();r.exports=a?function(r,t){if(o(r)&&!u(r,"length").writable)throw new i("Cannot set read only .length");return r.length=t}:function(r,t){return r.length=t}},939433:(r,t,e)=>{var n=e(444576),o=Object.defineProperty;r.exports=function(r,t){try{o(n,r,{value:t,configurable:!0,writable:!0})}catch(e){n[r]=t}return t}},944114:(r,t,e)=>{var n=e(746518),o=e(748981),i=e(326198),u=e(934527),a=e(596837);n({target:"Array",proto:!0,arity:1,forced:e(779039)((function(){return 4294967297!==[].push.call({length:4294967296},1)}))||!function(){try{Object.defineProperty([],"length",{writable:!1}).push()}catch(r){return r instanceof TypeError}}()},{push:function(r){var t=o(this),e=i(t),n=arguments.length;a(e+n);for(var c=0;c<n;c++)t[e]=arguments[c],e++;return u(t,e),e}})},947055:(r,t,e)=>{var n=e(179504),o=e(779039),i=e(544576),u=Object,a=n("".split);r.exports=o((function(){return!u("z").propertyIsEnumerable(0)}))?function(r){return"String"===i(r)?a(r,""):u(r)}:u},956969:(r,t,e)=>{var n=e(872777),o=e(210757);r.exports=function(r){var t=n(r,"string");return o(t)?t:t+""}},961828:(r,t,e)=>{var n=e(179504),o=e(39297),i=e(225397),u=e(919617).indexOf,a=e(130421),c=n([].push);r.exports=function(r,t){var e,n=i(r),f=0,s=[];for(e in n)!o(a,e)&&o(n,e)&&c(s,e);for(;t.length>f;)o(n,e=t[f++])&&(~u(s,e)||c(s,e));return s}},964117:r=>{r.exports=function(r){return null==r}},969565:(r,t,e)=>{var n=e(640616),o=Function.prototype.call;r.exports=n?o.bind(o):function(){return o.apply(o,arguments)}},978227:(r,t,e)=>{var n=e(444576),o=e(725745),i=e(39297),u=e(733392),a=e(604495),c=e(7040),f=n.Symbol,s=o("wks"),p=c?f.for||f:f&&f.withoutSetter||u;r.exports=function(r){return i(s,r)||(s[r]=a&&i(f,r)?f[r]:p("Symbol."+r)),s[r]}},991291:(r,t,e)=>{var n=e(80741);r.exports=function(r){var t=+r;return t!=t||0===t?0:n(t)}},996395:r=>{r.exports=!1}}]);