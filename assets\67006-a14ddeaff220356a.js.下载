(globalThis.webpackChunknotion_next=globalThis.webpackChunknotion_next||[]).push([[31238,67006],{3939:e=>{var t,n;t="ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789+/",n={rotl:function(e,t){return e<<t|e>>>32-t},rotr:function(e,t){return e<<32-t|e>>>t},endian:function(e){if(e.constructor==Number)return 16711935&n.rotl(e,8)|4278255360&n.rotl(e,24);for(var t=0;t<e.length;t++)e[t]=n.endian(e[t]);return e},randomBytes:function(e){for(var t=[];e>0;e--)t.push(Math.floor(256*Math.random()));return t},bytesToWords:function(e){for(var t=[],n=0,r=0;n<e.length;n++,r+=8)t[r>>>5]|=e[n]<<24-r%32;return t},wordsToBytes:function(e){for(var t=[],n=0;n<32*e.length;n+=8)t.push(e[n>>>5]>>>24-n%32&255);return t},bytesToHex:function(e){for(var t=[],n=0;n<e.length;n++)t.push((e[n]>>>4).toString(16)),t.push((15&e[n]).toString(16));return t.join("")},hexToBytes:function(e){for(var t=[],n=0;n<e.length;n+=2)t.push(parseInt(e.substr(n,2),16));return t},bytesToBase64:function(e){for(var n=[],r=0;r<e.length;r+=3)for(var i=e[r]<<16|e[r+1]<<8|e[r+2],o=0;o<4;o++)8*r+6*o<=8*e.length?n.push(t.charAt(i>>>6*(3-o)&63)):n.push("=");return n.join("")},base64ToBytes:function(e){e=e.replace(/[^A-Z0-9+\/]/gi,"");for(var n=[],r=0,i=0;r<e.length;i=++r%4)0!=i&&n.push((t.indexOf(e.charAt(r-1))&Math.pow(2,-2*i+8)-1)<<2*i|t.indexOf(e.charAt(r))>>>6-2*i);return n}},e.exports=n},12610:(e,t)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),t.NetworkParam=t.NetworkDefault=t.Endpoint=void 0,t.Endpoint={_initialize:"initialize",_rgstr:"rgstr",_download_config_specs:"download_config_specs"},t.NetworkDefault={[t.Endpoint._rgstr]:"https://prodregistryv2.org/v1",[t.Endpoint._initialize]:"https://featureassets.org/v1",[t.Endpoint._download_config_specs]:"https://api.statsigcdn.com/v1"},t.NetworkParam={EventCount:"ec",SdkKey:"k",SdkType:"st",SdkVersion:"sv",Time:"t",SessionID:"sid",StatsigEncoded:"se",IsGzipped:"gz"}},14358:function(e,t,n){"use strict";var r=this&&this.__awaiter||function(e,t,n,r){return new(n||(n=Promise))((function(i,o){function a(e){try{l(r.next(e))}catch(t){o(t)}}function s(e){try{l(r.throw(e))}catch(t){o(t)}}function l(e){var t;e.done?i(e.value):(t=e.value,t instanceof n?t:new n((function(e){e(t)}))).then(a,s)}l((r=r.apply(e,t||[])).next())}))};Object.defineProperty(t,"__esModule",{value:!0}),t.ErrorBoundary=t.EXCEPTION_ENDPOINT=void 0;t.EXCEPTION_ENDPOINT="https://statsigapi.net/v1/sdk_exception";const i="[Statsig] UnknownError";function o(e){return e instanceof Error?e:"string"==typeof e?new Error(e):new Error("An unknown error occurred.")}function a(e){if(!e)return{};const t={};return Object.entries(e).forEach((([e,n])=>{switch(typeof n){case"number":case"bigint":case"boolean":t[String(e)]=n;break;case"string":n.length<50?t[String(e)]=n:t[String(e)]="set";break;case"object":"environment"===e?t.environment=n:"networkConfig"===e?t.networkConfig=n:t[String(e)]=null!=n?"set":"unset"}})),t}t.ErrorBoundary=class{constructor(e,t,n,r){this._sdkKey=e,this._options=t,this._emitter=n,this._lastSeenError=r,this._seen=new Set}wrap(e){try{const t=e;(function(e){const t=new Set;let n=Object.getPrototypeOf(e);for(;n&&n!==Object.prototype;)Object.getOwnPropertyNames(n).filter((e=>"function"==typeof(null==n?void 0:n[e]))).forEach((e=>t.add(e))),n=Object.getPrototypeOf(n);return Array.from(t)})(t).forEach((n=>{const r=t[n];"$EB"in r||(t[n]=(...t)=>this._capture(n,(()=>r.apply(e,t))),t[n].$EB=!0)}))}catch(t){this._onError("eb:wrap",t)}}logError(e,t){this._onError(e,t)}getLastSeenErrorAndReset(){const e=this._lastSeenError;return this._lastSeenError=void 0,null!=e?e:null}attachErrorIfNoneExists(e){this._lastSeenError||(this._lastSeenError=o(e))}_capture(e,t){try{const n=t();return n&&n instanceof Promise?n.catch((t=>this._onError(e,t))):n}catch(n){return this._onError(e,n),null}}_onError(e,s){try{n(668024).Log.warn(`Caught error in ${e}`,{error:s});(()=>r(this,void 0,void 0,(function*(){var r,l,u,c,f,d,h;const p=s||Error(i),m=p instanceof Error,g=m?p.name:"No Name",v=o(p);if(this._lastSeenError=v,this._seen.has(g))return;if(this._seen.add(g),null===(l=null===(r=this._options)||void 0===r?void 0:r.networkConfig)||void 0===l?void 0:l.preventAllNetworkTraffic)return void(null===(u=this._emitter)||void 0===u||u.call(this,{name:"error",error:s,tag:e}));const y=n(810686).SDKType._get(this._sdkKey),b=n(146512).StatsigMetadataProvider.get(),_=m?p.stack:function(e){try{return JSON.stringify(e)}catch(r){return i}}(p),w=Object.assign({tag:e,exception:g,info:_,statsigOptions:a(this._options)},Object.assign(Object.assign({},b),{sdkType:y})),S=null!==(d=null===(f=null===(c=this._options)||void 0===c?void 0:c.networkConfig)||void 0===f?void 0:f.networkOverrideFunc)&&void 0!==d?d:fetch;yield S(t.EXCEPTION_ENDPOINT,{method:"POST",headers:{"STATSIG-API-KEY":this._sdkKey,"STATSIG-SDK-TYPE":String(y),"STATSIG-SDK-VERSION":String(b.sdkVersion),"Content-Type":"application/json"},body:JSON.stringify(w)}),null===(h=this._emitter)||void 0===h||h.call(this,{name:"error",error:s,tag:e})})))().then((()=>{})).catch((()=>{}))}catch(l){}}}},18107:(e,t,n)=>{"use strict";var r=n(746518),i=n(748981),o=n(326198),a=n(991291),s=n(206469);r({target:"Array",proto:!0},{at:function(e){var t=i(this),n=o(t),r=a(e),s=r>=0?r:n+r;return s<0||s>=n?void 0:t[s]}}),s("at")},32626:(e,t,n)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),t.StableID=void 0;const r=()=>n(894681),i=()=>n(601638),o={},a={},s={};function l(e){return`statsig.stable_id.${(0,r()._getStorageKey)(e)}`}function u(e,t){const r=l(t);try{(0,i()._setObjectInStorage)(r,e)}catch(o){n(668024).Log.warn("Failed to save StableID to storage")}}function c(e,t){if(!a[t]||!document)return;const n=new Date;n.setFullYear(n.getFullYear()+1),document.cookie=`${f(t)}=${encodeURIComponent(e)}; expires=${n.toUTCString()}; path=/`}function f(e){return`statsig.stable_id.${(0,r()._getStorageKey)(e)}`}t.StableID={cookiesEnabled:!1,randomID:Math.random().toString(36),get:e=>{if(s[e])return null;if(null!=o[e])return o[e];let t=null;return t=function(e){if(!a[e]||null==(0,n(600414)._getDocumentSafe)())return null;const t=document.cookie.split(";");for(const n of t){const[t,r]=n.trim().split("=");if(t===f(e))return decodeURIComponent(r)}return null}(e),null!=t?(o[e]=t,u(t,e),t):(t=function(e){const t=l(e);return(0,i()._getObjectFromStorage)(t)}(e),null==t&&(t=(0,n(537047).getUUID)()),u(t,e),c(t,e),o[e]=t,t)},setOverride:(e,t)=>{o[t]=e,u(e,t),c(e,t)},_setCookiesEnabled:(e,t)=>{a[e]=t},_setDisabled:(e,t)=>{s[e]=t}}},36944:(e,t,n)=>{var r=n(873893)("round");e.exports=r},37253:(e,t)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),t._createLayerParameterExposure=t._createConfigExposure=t._mapExposures=t._createGateExposure=t._isExposureEvent=void 0;const n="statsig::config_exposure",r="statsig::gate_exposure",i="statsig::layer_exposure",o=(e,t,n,r,i)=>(n.bootstrapMetadata&&(r.bootstrapMetadata=n.bootstrapMetadata),{eventName:e,user:t,value:null,metadata:s(n,r),secondaryExposures:i,time:Date.now()});t._isExposureEvent=({eventName:e})=>e===r||e===n||e===i;function a(e,t){return e.map((e=>"string"==typeof e?(null!=t?t:{})[e]:e)).filter((e=>null!=e))}t._createGateExposure=(e,t,n)=>{var i,s,l;const u={gate:t.name,gateValue:String(t.value),ruleID:t.ruleID};return null!=(null===(i=t.__evaluation)||void 0===i?void 0:i.version)&&(u.configVersion=t.__evaluation.version),o(r,e,t.details,u,a(null!==(l=null===(s=t.__evaluation)||void 0===s?void 0:s.secondary_exposures)&&void 0!==l?l:[],n))},t._mapExposures=a;t._createConfigExposure=(e,t,r)=>{var i,s,l,u;const c={config:t.name,ruleID:t.ruleID};return null!=(null===(i=t.__evaluation)||void 0===i?void 0:i.version)&&(c.configVersion=t.__evaluation.version),null!=(null===(s=t.__evaluation)||void 0===s?void 0:s.passed)&&(c.rulePassed=String(t.__evaluation.passed)),o(n,e,t.details,c,a(null!==(u=null===(l=t.__evaluation)||void 0===l?void 0:l.secondary_exposures)&&void 0!==u?u:[],r))};t._createLayerParameterExposure=(e,t,n,r)=>{var s,l,u,c,f,d;const h=t.__evaluation,p=!0===(null===(s=null==h?void 0:h.explicit_parameters)||void 0===s?void 0:s.includes(n));let m="",g=null!==(l=null==h?void 0:h.undelegated_secondary_exposures)&&void 0!==l?l:[];p&&(m=null!==(u=h.allocated_experiment_name)&&void 0!==u?u:"",g=h.secondary_exposures);const v=null===(c=t.__evaluation)||void 0===c?void 0:c.parameter_rule_ids,y={config:t.name,parameterName:n,ruleID:null!==(f=null==v?void 0:v[n])&&void 0!==f?f:t.ruleID,allocatedExperiment:m,isExplicitParameter:String(p)};return null!=(null===(d=t.__evaluation)||void 0===d?void 0:d.version)&&(y.configVersion=t.__evaluation.version),o(i,e,t.details,y,a(g,r))};const s=(e,t)=>(t.reason=e.reason,e.lcut&&(t.lcut=String(e.lcut)),e.receivedAt&&(t.receivedAt=String(e.receivedAt)),t)},44363:(e,t,n)=>{"use strict";e.exports=n(322799)},54387:(e,t)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0})},79999:(e,t,n)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),t._makeParamStoreGetter=void 0;const r=()=>n(636978),i={disableExposureLog:!0};function o(e){return null==e||!1===e.disableExposureLog}function a(e,t){return null!=t&&!(0,r()._isTypeMatch)(e,t)}t._makeParamStoreGetter=function(e,t,n){return(s,l)=>{if(null==t)return l;const u=t[s];if(null==u||null!=l&&(0,r()._typeOf)(l)!==u.param_type)return l;switch(u.ref_type){case"static":return function(e,t){return e.value}(u);case"gate":return function(e,t,n){return e.getFeatureGate(t.gate_name,o(n)?void 0:i).value?t.pass_value:t.fail_value}(e,u,n);case"dynamic_config":return function(e,t,n,r){const s=e.getDynamicConfig(t.config_name,o(r)?void 0:i).get(t.param_name);return a(s,n)?n:s}(e,u,l,n);case"experiment":return function(e,t,n,r){const s=e.getExperiment(t.experiment_name,o(r)?void 0:i).get(t.param_name);return a(s,n)?n:s}(e,u,l,n);case"layer":return function(e,t,n,r){const s=e.getLayer(t.layer_name,o(r)?void 0:i).get(t.param_name);return a(s,n)?n:s}(e,u,l,n);default:return l}}}},81570:(e,t,n)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),t._getFullUserHash=t._normalizeUser=void 0;t._normalizeUser=function(e,t,r){try{const n=JSON.parse(JSON.stringify(e));return null!=t&&null!=t.environment?n.statsigEnvironment=t.environment:null!=r&&(n.statsigEnvironment={tier:r}),n}catch(i){return n(668024).Log.error("Failed to JSON.stringify user"),{statsigEnvironment:void 0}}},t._getFullUserHash=function(e){return e?(0,n(483918)._DJB2Object)(e):null}},84351:function(e,t,n){"use strict";var r=this&&this.__awaiter||function(e,t,n,r){return new(n||(n=Promise))((function(i,o){function a(e){try{l(r.next(e))}catch(t){o(t)}}function s(e){try{l(r.throw(e))}catch(t){o(t)}}function l(e){var t;e.done?i(e.value):(t=e.value,t instanceof n?t:new n((function(e){e(t)}))).then(a,s)}l((r=r.apply(e,t||[])).next())}))};Object.defineProperty(t,"__esModule",{value:!0}),t.StatsigClientBase=void 0,n(843509);const i=()=>n(668024),o=()=>n(32626),a=()=>n(601638);t.StatsigClientBase=class{constructor(e,t,r,s){var l,u,c,f;this.loadingStatus="Uninitialized",this._initializePromise=null,this._listeners={};const d=this.$emt.bind(this);null!=(null==s?void 0:s.logLevel)&&(i().Log.level=s.logLevel),(null==s?void 0:s.disableStorage)&&a().Storage._setDisabled(!0),(null==s?void 0:s.initialSessionID)&&n(339839).StatsigSession.overrideInitialSessionID(s.initialSessionID,e),(null==s?void 0:s.storageProvider)&&a().Storage._setProvider(s.storageProvider),(null==s?void 0:s.enableCookies)&&o().StableID._setCookiesEnabled(e,s.enableCookies),(null==s?void 0:s.disableStableID)&&o().StableID._setDisabled(e,!0),this._sdkKey=e,this._options=null!=s?s:{},this._memoCache={},this.overrideAdapter=null!==(l=null==s?void 0:s.overrideAdapter)&&void 0!==l?l:null,this._logger=new(n(85434).EventLogger)(e,d,r,s),this._errorBoundary=new(n(14358).ErrorBoundary)(e,s,d),this._errorBoundary.wrap(this),this._errorBoundary.wrap(t),this._errorBoundary.wrap(this._logger),r.setErrorBoundary(this._errorBoundary),this.dataAdapter=t,this.dataAdapter.attach(e,s,r),this.storageProvider=a().Storage,null===(f=null===(c=null===(u=this.overrideAdapter)||void 0===u?void 0:u.loadFromStorage)||void 0===c?void 0:c.call(u))||void 0===f||f.catch((e=>this._errorBoundary.logError("OA::loadFromStorage",e))),this._primeReadyRipcord(),function(e,t){var r;if((0,n(600414)._isServerEnv)())return;const o=(0,n(843509)._getStatsigGlobal)(),a=null!==(r=o.instances)&&void 0!==r?r:{},s=t;null!=a[e]&&i().Log.warn("Creating multiple Statsig clients with the same SDK key can lead to unexpected behavior. Multi-instance support requires different SDK keys.");a[e]=s,o.firstInstance||(o.firstInstance=s);o.instances=a,__STATSIG__=o}(e,this)}updateRuntimeOptions(e){e.loggingEnabled?(this._options.loggingEnabled=e.loggingEnabled,this._logger.setLoggingEnabled(e.loggingEnabled)):null!=e.disableLogging&&(this._options.disableLogging=e.disableLogging,this._logger.setLoggingEnabled(e.disableLogging?"disabled":"browser-only")),null!=e.disableStorage&&(this._options.disableStorage=e.disableStorage,a().Storage._setDisabled(e.disableStorage)),null!=e.enableCookies&&(this._options.enableCookies=e.enableCookies,o().StableID._setCookiesEnabled(this._sdkKey,e.enableCookies)),e.logEventCompressionMode?this._logger.setLogEventCompressionMode(e.logEventCompressionMode):e.disableCompression&&this._logger.setLogEventCompressionMode(n(647754).LogEventCompressionMode.Disabled)}flush(){return this._logger.flush()}shutdown(){return r(this,void 0,void 0,(function*(){this.$emt({name:"pre_shutdown"}),this._setStatus("Uninitialized",null),this._initializePromise=null,yield this._logger.stop()}))}on(e,t){this._listeners[e]||(this._listeners[e]=[]),this._listeners[e].push(t)}off(e,t){if(this._listeners[e]){const n=this._listeners[e].indexOf(t);-1!==n&&this._listeners[e].splice(n,1)}}$on(e,t){t.__isInternal=!0,this.on(e,t)}$emt(e){var t;const n=t=>{try{t(e)}catch(n){if(!0===t.__isInternal)return void this._errorBoundary.logError(`__emit:${e.name}`,n);i().Log.error("An error occurred in a StatsigClientEvent listener. This is not an issue with Statsig.",e)}};this._listeners[e.name]&&this._listeners[e.name].forEach((e=>n(e))),null===(t=this._listeners["*"])||void 0===t||t.forEach(n)}_setStatus(e,t){this.loadingStatus=e,this._memoCache={},this.$emt({name:"values_updated",status:e,values:t})}_enqueueExposure(e,t,n){!0!==(null==n?void 0:n.disableExposureLog)?this._logger.enqueue(t):this._logger.incrementNonExposureCount(e)}_memoize(e,t){return(r,i)=>{if(this._options.disableEvaluationMemoization)return t(r,i);const o=(0,n(240843).createMemoKey)(e,r,i);return o?(o in this._memoCache||(Object.keys(this._memoCache).length>=3e3&&(this._memoCache={}),this._memoCache[o]=t(r,i)),this._memoCache[o]):t(r,i)}}}},85434:function(e,t,n){"use strict";var r=this&&this.__awaiter||function(e,t,n,r){return new(n||(n=Promise))((function(i,o){function a(e){try{l(r.next(e))}catch(t){o(t)}}function s(e){try{l(r.throw(e))}catch(t){o(t)}}function l(e){var t;e.done?i(e.value):(t=e.value,t instanceof n?t:new n((function(e){e(t)}))).then(a,s)}l((r=r.apply(e,t||[])).next())}))};Object.defineProperty(t,"__esModule",{value:!0}),t.EventLogger=void 0;const i=()=>n(668024),o=()=>n(12610),a=()=>n(600414),s=()=>n(647754),l=()=>n(601638),u=()=>n(592701),c={},f="startup",d="gained_focus";class h{static _safeFlushAndForget(e){var t;null===(t=c[e])||void 0===t||t.flush().catch((()=>{}))}static _safeRetryFailedLogs(e){var t;null===(t=c[e])||void 0===t||t._retryFailedLogs(d)}constructor(e,t,r,a){var l,u;this._sdkKey=e,this._emitter=t,this._network=r,this._options=a,this._queue=[],this._lastExposureTimeMap={},this._nonExposedChecks={},this._hasRunQuickFlush=!1,this._creationTime=Date.now(),this._loggingEnabled=null!==(l=null==a?void 0:a.loggingEnabled)&&void 0!==l?l:!0===(null==a?void 0:a.disableLogging)?s().LoggingEnabledOption.disabled:s().LoggingEnabledOption.browserOnly,(null==a?void 0:a.loggingEnabled)&&void 0!==a.disableLogging&&i().Log.warn("Detected both loggingEnabled and disableLogging options. loggingEnabled takes precedence - please remove disableLogging."),this._maxQueueSize=null!==(u=null==a?void 0:a.loggingBufferMaxSize)&&void 0!==u?u:100;const c=null==a?void 0:a.networkConfig;this._logEventUrlConfig=new(n(188873).UrlConfiguration)(o().Endpoint._rgstr,null==c?void 0:c.logEventUrl,null==c?void 0:c.api,null==c?void 0:c.logEventFallbackUrls)}setLogEventCompressionMode(e){this._network.setLogEventCompressionMode(e)}setLoggingEnabled(e){this._loggingEnabled=e}enqueue(e){this._shouldLogEvent(e)&&(this._normalizeAndAppendEvent(e),this._quickFlushIfNeeded(),this._queue.length>this._maxQueueSize&&h._safeFlushAndForget(this._sdkKey))}incrementNonExposureCount(e){var t;const n=null!==(t=this._nonExposedChecks[e])&&void 0!==t?t:0;this._nonExposedChecks[e]=n+1}reset(){this._lastExposureTimeMap={}}start(){var e;const t=(0,a()._isServerEnv)();t&&"always"!==(null===(e=this._options)||void 0===e?void 0:e.loggingEnabled)||(c[this._sdkKey]=this,t||(0,u()._subscribeToVisiblityChanged)((e=>{"background"===e?h._safeFlushAndForget(this._sdkKey):"foreground"===e&&h._safeRetryFailedLogs(this._sdkKey)})),this._retryFailedLogs(f),this._startBackgroundFlushInterval())}stop(){return r(this,void 0,void 0,(function*(){this._flushIntervalId&&(clearInterval(this._flushIntervalId),this._flushIntervalId=null),delete c[this._sdkKey],yield this.flush()}))}flush(){return r(this,void 0,void 0,(function*(){if(this._appendAndResetNonExposedChecks(),0===this._queue.length)return;const e=this._queue;this._queue=[],yield this._sendEvents(e)}))}_quickFlushIfNeeded(){this._hasRunQuickFlush||(this._hasRunQuickFlush=!0,Date.now()-this._creationTime>200||setTimeout((()=>h._safeFlushAndForget(this._sdkKey)),200))}_shouldLogEvent(e){var t;if("always"!==(null===(t=this._options)||void 0===t?void 0:t.loggingEnabled)&&(0,a()._isServerEnv)())return!1;if(!(0,n(37253)._isExposureEvent)(e))return!0;const r=e.user?e.user:{statsigEnvironment:void 0},i=(0,n(894681)._getUserStorageKey)(this._sdkKey,r),o=e.metadata?e.metadata:{},s=[e.eventName,i,o.gate,o.config,o.ruleID,o.allocatedExperiment,o.parameterName,String(o.isExplicitParameter),o.reason].join("|"),l=this._lastExposureTimeMap[s],u=Date.now();return!(l&&u-l<6e5)&&(Object.keys(this._lastExposureTimeMap).length>1e3&&(this._lastExposureTimeMap={}),this._lastExposureTimeMap[s]=u,!0)}_sendEvents(e){var t,n;return r(this,void 0,void 0,(function*(){if("disabled"===this._loggingEnabled)return this._saveFailedLogsToStorage(e),!1;try{const r=(0,u()._isUnloading)()&&this._network.isBeaconSupported()&&null==(null===(n=null===(t=this._options)||void 0===t?void 0:t.networkConfig)||void 0===n?void 0:n.networkOverrideFunc);this._emitter({name:"pre_logs_flushed",events:e});return(r?yield this._sendEventsViaBeacon(e):yield this._sendEventsViaPost(e)).success?(this._emitter({name:"logs_flushed",events:e}),!0):(i().Log.warn("Failed to flush events."),this._saveFailedLogsToStorage(e),!1)}catch(r){return i().Log.warn("Failed to flush events."),!1}}))}_sendEventsViaPost(e){var t;return r(this,void 0,void 0,(function*(){const n=yield this._network.post(this._getRequestData(e)),r=null!==(t=null==n?void 0:n.code)&&void 0!==t?t:-1;return{success:r>=200&&r<300}}))}_sendEventsViaBeacon(e){return r(this,void 0,void 0,(function*(){return{success:yield this._network.beacon(this._getRequestData(e))}}))}_getRequestData(e){return{sdkKey:this._sdkKey,data:{events:e},urlConfig:this._logEventUrlConfig,retries:3,isCompressable:!0,params:{[o().NetworkParam.EventCount]:String(e.length)},credentials:"same-origin"}}_saveFailedLogsToStorage(e){for(;e.length>500;)e.shift();const t=this._getStorageKey();try{(0,l()._setObjectInStorage)(t,e)}catch(n){i().Log.warn("Unable to save failed logs to storage")}}_retryFailedLogs(e){const t=this._getStorageKey();(()=>r(this,void 0,void 0,(function*(){l().Storage.isReady()||(yield l().Storage.isReadyResolver());const n=(0,l()._getObjectFromStorage)(t);if(!n)return;e===f&&l().Storage.removeItem(t);(yield this._sendEvents(n))&&e===d&&l().Storage.removeItem(t)})))().catch((()=>{i().Log.warn("Failed to flush stored logs")}))}_getStorageKey(){return`statsig.failed_logs.${(0,n(483918)._DJB2)(this._sdkKey)}`}_normalizeAndAppendEvent(e){e.user&&(e.user=Object.assign({},e.user),delete e.user.privateAttributes);const t={},n=this._getCurrentPageUrl();n&&(t.statsigMetadata={currentPage:n});const r=Object.assign(Object.assign({},e),t);i().Log.debug("Enqueued Event:",r),this._queue.push(r)}_appendAndResetNonExposedChecks(){0!==Object.keys(this._nonExposedChecks).length&&(this._normalizeAndAppendEvent({eventName:"statsig::non_exposed_checks",user:null,time:Date.now(),metadata:{checks:Object.assign({},this._nonExposedChecks)}}),this._nonExposedChecks={})}_getCurrentPageUrl(){var e;if(!1!==(null===(e=this._options)||void 0===e?void 0:e.includeCurrentPageUrlWithEvents))return(0,a()._getCurrentPageUrlSafe)()}_startBackgroundFlushInterval(){var e,t;const n=null!==(t=null===(e=this._options)||void 0===e?void 0:e.loggingIntervalMs)&&void 0!==t?t:1e4,r=setInterval((()=>{const e=c[this._sdkKey];e&&e._flushIntervalId===r?h._safeFlushAndForget(this._sdkKey):clearInterval(r)}),n);this._flushIntervalId=r}}t.EventLogger=h},87677:(e,t,n)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0});const r=()=>n(636978);t.default=class{constructor(e){this._sdkKey=e,this._rawValues=null,this._values=null,this._source="Uninitialized",this._lcut=0,this._receivedAt=0,this._bootstrapMetadata=null,this._warnings=new Set}reset(){this._values=null,this._rawValues=null,this._source="Loading",this._lcut=0,this._receivedAt=0,this._bootstrapMetadata=null}finalize(){this._values||(this._source="NoValues")}getValues(){return this._rawValues?(0,r()._typedJsonParse)(this._rawValues,"has_updates","EvaluationStoreValues"):null}setValues(e,t){var n;if(!e)return!1;const i=(0,r()._typedJsonParse)(e.data,"has_updates","EvaluationResponse");return null!=i&&(this._source=e.source,!0!==(null==i?void 0:i.has_updates)||(this._rawValues=e.data,this._lcut=i.time,this._receivedAt=e.receivedAt,this._values=i,this._bootstrapMetadata=this._extractBootstrapMetadata(e.source,i),e.source&&i.user&&this._setWarningState(t,i),r().SDKFlags.setFlags(this._sdkKey,null!==(n=i.sdk_flags)&&void 0!==n?n:{})),!0)}getWarnings(){if(0!==this._warnings.size)return Array.from(this._warnings)}getGate(e){var t;return this._getDetailedStoreResult(null===(t=this._values)||void 0===t?void 0:t.feature_gates,e)}getConfig(e){var t;return this._getDetailedStoreResult(null===(t=this._values)||void 0===t?void 0:t.dynamic_configs,e)}getLayer(e){var t;return this._getDetailedStoreResult(null===(t=this._values)||void 0===t?void 0:t.layer_configs,e)}getParamStore(e){var t;return this._getDetailedStoreResult(null===(t=this._values)||void 0===t?void 0:t.param_stores,e)}getSource(){return this._source}getExposureMapping(){var e;return null===(e=this._values)||void 0===e?void 0:e.exposures}_extractBootstrapMetadata(e,t){if("Bootstrap"!==e)return null;const n={};return t.user&&(n.user=t.user),t.sdkInfo&&(n.generatorSDKInfo=t.sdkInfo),n.lcut=t.time,n}_getDetailedStoreResult(e,t){let n=null;return e&&(n=e[t]?e[t]:e[(0,r()._DJB2)(t)]),{result:n,details:this._getDetails(null==n)}}_setWarningState(e,t){var n,i;const o=r().StableID.get(this._sdkKey);if((null===(n=e.customIDs)||void 0===n?void 0:n.stableID)===o||!(null===(i=e.customIDs)||void 0===i?void 0:i.stableID)&&!o){if("user"in t){const n=t.user;(0,r()._getFullUserHash)(e)!==(0,r()._getFullUserHash)(n)&&this._warnings.add("PartialUserMatch")}}else this._warnings.add("StableIDMismatch")}getCurrentSourceDetails(){if("Uninitialized"===this._source||"NoValues"===this._source)return{reason:this._source};const e={reason:this._source,lcut:this._lcut,receivedAt:this._receivedAt};return this._warnings.size>0&&(e.warnings=Array.from(this._warnings)),e}_getDetails(e){var t,n;const r=this.getCurrentSourceDetails();let i=r.reason;const o=null!==(t=r.warnings)&&void 0!==t?t:[];if("Bootstrap"===this._source&&o.length>0&&(i+=o[0]),"Uninitialized"!==i&&"NoValues"!==i){i=`${i}:${e?"Unrecognized":"Recognized"}`}const a="Bootstrap"===this._source&&null!==(n=this._bootstrapMetadata)&&void 0!==n?n:void 0;return a&&(r.bootstrapMetadata=a),Object.assign(Object.assign({},r),{reason:i})}}},99652:(e,t)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0})},105138:function(e,t,n){"use strict";var r=this&&this.__createBinding||(Object.create?function(e,t,n,r){void 0===r&&(r=n);var i=Object.getOwnPropertyDescriptor(t,n);i&&!("get"in i?!t.__esModule:i.writable||i.configurable)||(i={enumerable:!0,get:function(){return t[n]}}),Object.defineProperty(e,r,i)}:function(e,t,n,r){void 0===r&&(r=n),e[r]=t[n]}),i=this&&this.__exportStar||function(e,t){for(var n in e)"default"===n||Object.prototype.hasOwnProperty.call(t,n)||r(t,e,n)};Object.defineProperty(t,"__esModule",{value:!0}),t.StatsigClient=void 0;const o=()=>n(907686);t.StatsigClient=o().default,i(n(636978),t);const a=Object.assign((0,n(636978)._getStatsigGlobal)(),{StatsigClient:o().default});t.default=a},128845:(e,t,n)=>{"use strict";var r=n(444576),i=n(969565),o=n(794644),a=n(326198),s=n(958229),l=n(748981),u=n(779039),c=r.RangeError,f=r.Int8Array,d=f&&f.prototype,h=d&&d.set,p=o.aTypedArray,m=o.exportTypedArrayMethod,g=!u((function(){var e=new Uint8ClampedArray(2);return i(h,e,{length:1,0:3},1),3!==e[1]})),v=g&&o.NATIVE_ARRAY_BUFFER_VIEWS&&u((function(){var e=new f(2);return e.set(1),e.set("2",1),0!==e[0]||2!==e[1]}));m("set",(function(e){p(this);var t=s(arguments.length>1?arguments[1]:void 0,1),n=l(e);if(g)return i(h,this,n,t);var r=this.length,o=a(n),u=0;if(o+t>r)throw new c("Wrong length");for(;u<o;)this[t+u]=n[u++]}),!g||v)},137007:e=>{"use strict";var t,n="object"==typeof Reflect?Reflect:null,r=n&&"function"==typeof n.apply?n.apply:function(e,t,n){return Function.prototype.apply.call(e,t,n)};t=n&&"function"==typeof n.ownKeys?n.ownKeys:Object.getOwnPropertySymbols?function(e){return Object.getOwnPropertyNames(e).concat(Object.getOwnPropertySymbols(e))}:function(e){return Object.getOwnPropertyNames(e)};var i=Number.isNaN||function(e){return e!=e};function o(){o.init.call(this)}e.exports=o,e.exports.once=function(e,t){return new Promise((function(n,r){function i(n){e.removeListener(t,o),r(n)}function o(){"function"==typeof e.removeListener&&e.removeListener("error",i),n([].slice.call(arguments))}m(e,t,o,{once:!0}),"error"!==t&&function(e,t,n){"function"==typeof e.on&&m(e,"error",t,n)}(e,i,{once:!0})}))},o.EventEmitter=o,o.prototype._events=void 0,o.prototype._eventsCount=0,o.prototype._maxListeners=void 0;var a=10;function s(e){if("function"!=typeof e)throw new TypeError('The "listener" argument must be of type Function. Received type '+typeof e)}function l(e){return void 0===e._maxListeners?o.defaultMaxListeners:e._maxListeners}function u(e,t,n,r){var i,o,a,u;if(s(n),void 0===(o=e._events)?(o=e._events=Object.create(null),e._eventsCount=0):(void 0!==o.newListener&&(e.emit("newListener",t,n.listener?n.listener:n),o=e._events),a=o[t]),void 0===a)a=o[t]=n,++e._eventsCount;else if("function"==typeof a?a=o[t]=r?[n,a]:[a,n]:r?a.unshift(n):a.push(n),(i=l(e))>0&&a.length>i&&!a.warned){a.warned=!0;var c=new Error("Possible EventEmitter memory leak detected. "+a.length+" "+String(t)+" listeners added. Use emitter.setMaxListeners() to increase limit");c.name="MaxListenersExceededWarning",c.emitter=e,c.type=t,c.count=a.length,u=c,console&&console.warn&&console.warn(u)}return e}function c(){if(!this.fired)return this.target.removeListener(this.type,this.wrapFn),this.fired=!0,0===arguments.length?this.listener.call(this.target):this.listener.apply(this.target,arguments)}function f(e,t,n){var r={fired:!1,wrapFn:void 0,target:e,type:t,listener:n},i=c.bind(r);return i.listener=n,r.wrapFn=i,i}function d(e,t,n){var r=e._events;if(void 0===r)return[];var i=r[t];return void 0===i?[]:"function"==typeof i?n?[i.listener||i]:[i]:n?function(e){for(var t=new Array(e.length),n=0;n<t.length;++n)t[n]=e[n].listener||e[n];return t}(i):p(i,i.length)}function h(e){var t=this._events;if(void 0!==t){var n=t[e];if("function"==typeof n)return 1;if(void 0!==n)return n.length}return 0}function p(e,t){for(var n=new Array(t),r=0;r<t;++r)n[r]=e[r];return n}function m(e,t,n,r){if("function"==typeof e.on)r.once?e.once(t,n):e.on(t,n);else{if("function"!=typeof e.addEventListener)throw new TypeError('The "emitter" argument must be of type EventEmitter. Received type '+typeof e);e.addEventListener(t,(function i(o){r.once&&e.removeEventListener(t,i),n(o)}))}}Object.defineProperty(o,"defaultMaxListeners",{enumerable:!0,get:function(){return a},set:function(e){if("number"!=typeof e||e<0||i(e))throw new RangeError('The value of "defaultMaxListeners" is out of range. It must be a non-negative number. Received '+e+".");a=e}}),o.init=function(){void 0!==this._events&&this._events!==Object.getPrototypeOf(this)._events||(this._events=Object.create(null),this._eventsCount=0),this._maxListeners=this._maxListeners||void 0},o.prototype.setMaxListeners=function(e){if("number"!=typeof e||e<0||i(e))throw new RangeError('The value of "n" is out of range. It must be a non-negative number. Received '+e+".");return this._maxListeners=e,this},o.prototype.getMaxListeners=function(){return l(this)},o.prototype.emit=function(e){for(var t=[],n=1;n<arguments.length;n++)t.push(arguments[n]);var i="error"===e,o=this._events;if(void 0!==o)i=i&&void 0===o.error;else if(!i)return!1;if(i){var a;if(t.length>0&&(a=t[0]),a instanceof Error)throw a;var s=new Error("Unhandled error."+(a?" ("+a.message+")":""));throw s.context=a,s}var l=o[e];if(void 0===l)return!1;if("function"==typeof l)r(l,this,t);else{var u=l.length,c=p(l,u);for(n=0;n<u;++n)r(c[n],this,t)}return!0},o.prototype.addListener=function(e,t){return u(this,e,t,!1)},o.prototype.on=o.prototype.addListener,o.prototype.prependListener=function(e,t){return u(this,e,t,!0)},o.prototype.once=function(e,t){return s(t),this.on(e,f(this,e,t)),this},o.prototype.prependOnceListener=function(e,t){return s(t),this.prependListener(e,f(this,e,t)),this},o.prototype.removeListener=function(e,t){var n,r,i,o,a;if(s(t),void 0===(r=this._events))return this;if(void 0===(n=r[e]))return this;if(n===t||n.listener===t)0==--this._eventsCount?this._events=Object.create(null):(delete r[e],r.removeListener&&this.emit("removeListener",e,n.listener||t));else if("function"!=typeof n){for(i=-1,o=n.length-1;o>=0;o--)if(n[o]===t||n[o].listener===t){a=n[o].listener,i=o;break}if(i<0)return this;0===i?n.shift():function(e,t){for(;t+1<e.length;t++)e[t]=e[t+1];e.pop()}(n,i),1===n.length&&(r[e]=n[0]),void 0!==r.removeListener&&this.emit("removeListener",e,a||t)}return this},o.prototype.off=o.prototype.removeListener,o.prototype.removeAllListeners=function(e){var t,n,r;if(void 0===(n=this._events))return this;if(void 0===n.removeListener)return 0===arguments.length?(this._events=Object.create(null),this._eventsCount=0):void 0!==n[e]&&(0==--this._eventsCount?this._events=Object.create(null):delete n[e]),this;if(0===arguments.length){var i,o=Object.keys(n);for(r=0;r<o.length;++r)"removeListener"!==(i=o[r])&&this.removeAllListeners(i);return this.removeAllListeners("removeListener"),this._events=Object.create(null),this._eventsCount=0,this}if("function"==typeof(t=n[e]))this.removeListener(e,t);else if(void 0!==t)for(r=t.length-1;r>=0;r--)this.removeListener(e,t[r]);return this},o.prototype.listeners=function(e){return d(this,e,!0)},o.prototype.rawListeners=function(e){return d(this,e,!1)},o.listenerCount=function(e,t){return"function"==typeof e.listenerCount?e.listenerCount(t):h.call(e,t)},o.prototype.listenerCount=h,o.prototype.eventNames=function(){return this._eventsCount>0?t(this._events):[]}},143839:(e,t,n)=>{"use strict";var r=n(276080),i=n(947055),o=n(748981),a=n(326198),s=function(e){var t=1===e;return function(n,s,l){for(var u,c=o(n),f=i(c),d=a(f),h=r(s,l);d-- >0;)if(h(u=f[d],d,c))switch(e){case 0:return u;case 1:return d}return t?-1:void 0}};e.exports={findLast:s(0),findLastIndex:s(1)}},146512:(e,t)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),t.StatsigMetadataProvider=t.SDK_VERSION=void 0,t.SDK_VERSION="3.17.1";let n={sdkVersion:t.SDK_VERSION,sdkType:"js-mono"};t.StatsigMetadataProvider={get:()=>n,add:e=>{n=Object.assign(Object.assign({},n),e)}}},147566:(e,t,n)=>{"use strict";var r=n(436840),i=n(179504),o=n(500655),a=n(422812),s=URLSearchParams,l=s.prototype,u=i(l.getAll),c=i(l.has),f=new s("a=1");!f.has("a",2)&&f.has("a",void 0)||r(l,"has",(function(e){var t=arguments.length,n=t<2?void 0:arguments[1];if(t&&void 0===n)return c(this,e);var r=u(this,e);a(t,1);for(var i=o(n),s=0;s<r.length;)if(r[s++]===i)return!0;return!1}),{enumerable:!0,unsafe:!0})},150104:(e,t,n)=>{var r=()=>n(353661);function i(e,t){if("function"!=typeof e||null!=t&&"function"!=typeof t)throw new TypeError("Expected a function");var n=function(){var r=arguments,i=t?t.apply(this,r):r[0],o=n.cache;if(o.has(i))return o.get(i);var a=e.apply(this,r);return n.cache=o.set(i,a)||o,a};return n.cache=new(i.Cache||r()),n}i.Cache=r(),e.exports=i},175854:(e,t,n)=>{"use strict";var r=n(872777),i=TypeError;e.exports=function(e){var t=r(e,"number");if("number"==typeof t)throw new i("Can't convert number to bigint");return BigInt(t)}},177936:(e,t,n)=>{"use strict";var r=n(746518),i=n(595636);i&&r({target:"ArrayBuffer",proto:!0},{transferToFixedLength:function(){return i(this,arguments.length?arguments[0]:void 0,!1)}})},183673:e=>{e.exports=function(e){for(var t=-1,n=null==e?0:e.length,r=0,i=[];++t<n;){var o=e[t];o&&(i[r++]=o)}return i}},184343:(e,t,n)=>{"use strict";t.parse=n(755776),t.stringify=n(969086)},185015:(e,t,n)=>{e.exports=function(e){return"string"==typeof e||!n(956449)(e)&&n(540346)(e)&&"[object String]"==n(472552)(e)}},188835:(e,t,n)=>{"use strict";var r=()=>n(732268);function i(){this.protocol=null,this.slashes=null,this.auth=null,this.host=null,this.port=null,this.hostname=null,this.hash=null,this.search=null,this.query=null,this.pathname=null,this.path=null,this.href=null}t.parse=y,t.resolve=function(e,t){return y(e,!1,!0).resolve(t)},t.resolveObject=function(e,t){return e?y(e,!1,!0).resolveObject(t):t},t.format=function(e){r().isString(e)&&(e=y(e));return e instanceof i?e.format():i.prototype.format.call(e)},t.Url=i;var o=/^([a-z0-9.+-]+:)/i,a=/:[0-9]*$/,s=/^(\/\/?(?!\/)[^\?\s]*)(\?[^\s]*)?$/,l=["{","}","|","\\","^","`"].concat(["<",">",'"',"`"," ","\r","\n","\t"]),u=["'"].concat(l),c=["%","/","?",";","#"].concat(u),f=["/","?","#"],d=/^[+a-z0-9A-Z_-]{0,63}$/,h=/^([+a-z0-9A-Z_-]{0,63})(.*)$/,p={javascript:!0,"javascript:":!0},m={javascript:!0,"javascript:":!0},g={http:!0,https:!0,ftp:!0,gopher:!0,file:!0,"http:":!0,"https:":!0,"ftp:":!0,"gopher:":!0,"file:":!0},v=()=>n(747186);function y(e,t,n){if(e&&r().isObject(e)&&e instanceof i)return e;var o=new i;return o.parse(e,t,n),o}i.prototype.parse=function(e,t,i){if(!r().isString(e))throw new TypeError("Parameter 'url' must be a string, not "+typeof e);var a=e.indexOf("?"),l=-1!==a&&a<e.indexOf("#")?"?":"#",y=e.split(l);y[0]=y[0].replace(/\\/g,"/");var b=e=y.join(l);if(b=b.trim(),!i&&1===e.split("#").length){var _=s.exec(b);if(_)return this.path=b,this.href=b,this.pathname=_[1],_[2]?(this.search=_[2],this.query=t?v().parse(this.search.substr(1)):this.search.substr(1)):t&&(this.search="",this.query={}),this}var w=o.exec(b);if(w){var S=(w=w[0]).toLowerCase();this.protocol=S,b=b.substr(w.length)}if(i||w||b.match(/^\/\/[^@\/]+@[^@\/]+/)){var E="//"===b.substr(0,2);!E||w&&m[w]||(b=b.substr(2),this.slashes=!0)}if(!m[w]&&(E||w&&!g[w])){for(var k,T,x=-1,O=0;O<f.length;O++){-1!==(C=b.indexOf(f[O]))&&(-1===x||C<x)&&(x=C)}-1!==(T=-1===x?b.lastIndexOf("@"):b.lastIndexOf("@",x))&&(k=b.slice(0,T),b=b.slice(T+1),this.auth=decodeURIComponent(k)),x=-1;for(O=0;O<c.length;O++){var C;-1!==(C=b.indexOf(c[O]))&&(-1===x||C<x)&&(x=C)}-1===x&&(x=b.length),this.host=b.slice(0,x),b=b.slice(x),this.parseHost(),this.hostname=this.hostname||"";var N="["===this.hostname[0]&&"]"===this.hostname[this.hostname.length-1];if(!N)for(var I=this.hostname.split(/\./),D=(O=0,I.length);O<D;O++){var A=I[O];if(A&&!A.match(d)){for(var P="",L=0,M=A.length;L<M;L++)A.charCodeAt(L)>127?P+="x":P+=A[L];if(!P.match(d)){var R=I.slice(0,O),F=I.slice(O+1),U=A.match(h);U&&(R.push(U[1]),F.unshift(U[2])),F.length&&(b="/"+F.join(".")+b),this.hostname=R.join(".");break}}}this.hostname.length>255?this.hostname="":this.hostname=this.hostname.toLowerCase(),N||(this.hostname=n(361270).toASCII(this.hostname));var j=this.port?":"+this.port:"",B=this.hostname||"";this.host=B+j,this.href+=this.host,N&&(this.hostname=this.hostname.substr(1,this.hostname.length-2),"/"!==b[0]&&(b="/"+b))}if(!p[S])for(O=0,D=u.length;O<D;O++){var z=u[O];if(-1!==b.indexOf(z)){var H=encodeURIComponent(z);H===z&&(H=escape(z)),b=b.split(z).join(H)}}var V=b.indexOf("#");-1!==V&&(this.hash=b.substr(V),b=b.slice(0,V));var $=b.indexOf("?");if(-1!==$?(this.search=b.substr($),this.query=b.substr($+1),t&&(this.query=v().parse(this.query)),b=b.slice(0,$)):t&&(this.search="",this.query={}),b&&(this.pathname=b),g[S]&&this.hostname&&!this.pathname&&(this.pathname="/"),this.pathname||this.search){j=this.pathname||"";var G=this.search||"";this.path=j+G}return this.href=this.format(),this},i.prototype.format=function(){var e=this.auth||"";e&&(e=(e=encodeURIComponent(e)).replace(/%3A/i,":"),e+="@");var t=this.protocol||"",n=this.pathname||"",i=this.hash||"",o=!1,a="";this.host?o=e+this.host:this.hostname&&(o=e+(-1===this.hostname.indexOf(":")?this.hostname:"["+this.hostname+"]"),this.port&&(o+=":"+this.port)),this.query&&r().isObject(this.query)&&Object.keys(this.query).length&&(a=v().stringify(this.query));var s=this.search||a&&"?"+a||"";return t&&":"!==t.substr(-1)&&(t+=":"),this.slashes||(!t||g[t])&&!1!==o?(o="//"+(o||""),n&&"/"!==n.charAt(0)&&(n="/"+n)):o||(o=""),i&&"#"!==i.charAt(0)&&(i="#"+i),s&&"?"!==s.charAt(0)&&(s="?"+s),t+o+(n=n.replace(/[?#]/g,(function(e){return encodeURIComponent(e)})))+(s=s.replace("#","%23"))+i},i.prototype.resolve=function(e){return this.resolveObject(y(e,!1,!0)).format()},i.prototype.resolveObject=function(e){if(r().isString(e)){var t=new i;t.parse(e,!1,!0),e=t}for(var n=new i,o=Object.keys(this),a=0;a<o.length;a++){var s=o[a];n[s]=this[s]}if(n.hash=e.hash,""===e.href)return n.href=n.format(),n;if(e.slashes&&!e.protocol){for(var l=Object.keys(e),u=0;u<l.length;u++){var c=l[u];"protocol"!==c&&(n[c]=e[c])}return g[n.protocol]&&n.hostname&&!n.pathname&&(n.path=n.pathname="/"),n.href=n.format(),n}if(e.protocol&&e.protocol!==n.protocol){if(!g[e.protocol]){for(var f=Object.keys(e),d=0;d<f.length;d++){var h=f[d];n[h]=e[h]}return n.href=n.format(),n}if(n.protocol=e.protocol,e.host||m[e.protocol])n.pathname=e.pathname;else{for(var p=(e.pathname||"").split("/");p.length&&!(e.host=p.shift()););e.host||(e.host=""),e.hostname||(e.hostname=""),""!==p[0]&&p.unshift(""),p.length<2&&p.unshift(""),n.pathname=p.join("/")}if(n.search=e.search,n.query=e.query,n.host=e.host||"",n.auth=e.auth,n.hostname=e.hostname||e.host,n.port=e.port,n.pathname||n.search){var v=n.pathname||"",y=n.search||"";n.path=v+y}return n.slashes=n.slashes||e.slashes,n.href=n.format(),n}var b=n.pathname&&"/"===n.pathname.charAt(0),_=e.host||e.pathname&&"/"===e.pathname.charAt(0),w=_||b||n.host&&e.pathname,S=w,E=n.pathname&&n.pathname.split("/")||[],k=(p=e.pathname&&e.pathname.split("/")||[],n.protocol&&!g[n.protocol]);if(k&&(n.hostname="",n.port=null,n.host&&(""===E[0]?E[0]=n.host:E.unshift(n.host)),n.host="",e.protocol&&(e.hostname=null,e.port=null,e.host&&(""===p[0]?p[0]=e.host:p.unshift(e.host)),e.host=null),w=w&&(""===p[0]||""===E[0])),_)n.host=e.host||""===e.host?e.host:n.host,n.hostname=e.hostname||""===e.hostname?e.hostname:n.hostname,n.search=e.search,n.query=e.query,E=p;else if(p.length)E||(E=[]),E.pop(),E=E.concat(p),n.search=e.search,n.query=e.query;else if(!r().isNullOrUndefined(e.search)){if(k)n.hostname=n.host=E.shift(),(N=!!(n.host&&n.host.indexOf("@")>0)&&n.host.split("@"))&&(n.auth=N.shift(),n.host=n.hostname=N.shift());return n.search=e.search,n.query=e.query,r().isNull(n.pathname)&&r().isNull(n.search)||(n.path=(n.pathname?n.pathname:"")+(n.search?n.search:"")),n.href=n.format(),n}if(!E.length)return n.pathname=null,n.search?n.path="/"+n.search:n.path=null,n.href=n.format(),n;for(var T=E.slice(-1)[0],x=(n.host||e.host||E.length>1)&&("."===T||".."===T)||""===T,O=0,C=E.length;C>=0;C--)"."===(T=E[C])?E.splice(C,1):".."===T?(E.splice(C,1),O++):O&&(E.splice(C,1),O--);if(!w&&!S)for(;O--;O)E.unshift("..");!w||""===E[0]||E[0]&&"/"===E[0].charAt(0)||E.unshift(""),x&&"/"!==E.join("/").substr(-1)&&E.push("");var N,I=""===E[0]||E[0]&&"/"===E[0].charAt(0);k&&(n.hostname=n.host=I?"":E.length?E.shift():"",(N=!!(n.host&&n.host.indexOf("@")>0)&&n.host.split("@"))&&(n.auth=N.shift(),n.host=n.hostname=N.shift()));return(w=w||n.host&&E.length)&&!I&&E.unshift(""),E.length?n.pathname=E.join("/"):(n.pathname=null,n.path=null),r().isNull(n.pathname)&&r().isNull(n.search)||(n.path=(n.pathname?n.pathname:"")+(n.search?n.search:"")),n.auth=e.auth||n.auth,n.slashes=n.slashes||e.slashes,n.href=n.format(),n},i.prototype.parseHost=function(){var e=this.host,t=a.exec(e);t&&(":"!==(t=t[0])&&(this.port=t.substr(1)),e=e.substr(0,e.length-t.length)),e&&(this.hostname=e)}},188873:(e,t,n)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),t.UrlConfiguration=void 0;const r=()=>n(12610),i={[r().Endpoint._initialize]:"i",[r().Endpoint._rgstr]:"e",[r().Endpoint._download_config_specs]:"d"};t.UrlConfiguration=class{constructor(e,t,n,o){this.customUrl=null,this.fallbackUrls=null,this.endpoint=e,this.endpointDnsKey=i[e],t&&(this.customUrl=t),!t&&n&&(this.customUrl=n.endsWith("/")?`${n}${e}`:`${n}/${e}`),o&&(this.fallbackUrls=o);const a=r().NetworkDefault[e];this.defaultUrl=`${a}/${e}`}getUrl(){var e;return null!==(e=this.customUrl)&&void 0!==e?e:this.defaultUrl}getChecksum(){var e;const t=(null!==(e=this.fallbackUrls)&&void 0!==e?e:[]).sort().join(",");return(0,n(483918)._DJB2)(this.customUrl+t)}}},190031:(e,t)=>{!function(e){var t,n,r,i=String.fromCharCode;function o(e){for(var t,n,r=[],i=0,o=e.length;i<o;)(t=e.charCodeAt(i++))>=55296&&t<=56319&&i<o?56320==(64512&(n=e.charCodeAt(i++)))?r.push(((1023&t)<<10)+(1023&n)+65536):(r.push(t),i--):r.push(t);return r}function a(e){if(e>=55296&&e<=57343)throw Error("Lone surrogate U+"+e.toString(16).toUpperCase()+" is not a scalar value")}function s(e,t){return i(e>>t&63|128)}function l(e){if(0==(4294967168&e))return i(e);var t="";return 0==(4294965248&e)?t=i(e>>6&31|192):0==(4294901760&e)?(a(e),t=i(e>>12&15|224),t+=s(e,6)):0==(4292870144&e)&&(t=i(e>>18&7|240),t+=s(e,12),t+=s(e,6)),t+=i(63&e|128)}function u(){if(r>=n)throw Error("Invalid byte index");var e=255&t[r];if(r++,128==(192&e))return 63&e;throw Error("Invalid continuation byte")}function c(){var e,i;if(r>n)throw Error("Invalid byte index");if(r==n)return!1;if(e=255&t[r],r++,0==(128&e))return e;if(192==(224&e)){if((i=(31&e)<<6|u())>=128)return i;throw Error("Invalid continuation byte")}if(224==(240&e)){if((i=(15&e)<<12|u()<<6|u())>=2048)return a(i),i;throw Error("Invalid continuation byte")}if(240==(248&e)&&(i=(7&e)<<18|u()<<12|u()<<6|u())>=65536&&i<=1114111)return i;throw Error("Invalid UTF-8 detected")}e.version="3.0.0",e.encode=function(e){for(var t=o(e),n=t.length,r=-1,i="";++r<n;)i+=l(t[r]);return i},e.decode=function(e){t=o(e),n=t.length,r=0;for(var a,s=[];!1!==(a=c());)s.push(a);return function(e){for(var t,n=e.length,r=-1,o="";++r<n;)(t=e[r])>65535&&(o+=i((t-=65536)>>>10&1023|55296),t=56320|1023&t),o+=i(t);return o}(s)}}(t)},198023:(e,t,n)=>{e.exports=function(e){return"number"==typeof e||n(540346)(e)&&"[object Number]"==n(472552)(e)}},198721:(e,t,n)=>{"use strict";var r=n(743724),i=n(179504),o=n(562106),a=URLSearchParams.prototype,s=i(a.forEach);r&&!("size"in a)&&o(a,"size",{get:function(){var e=0;return s(this,(function(){e++})),e},configurable:!0,enumerable:!0})},206469:(e,t,n)=>{"use strict";var r=n(978227),i=n(202360),o=n(824913).f,a=r("unscopables"),s=Array.prototype;void 0===s[a]&&o(s,a,{configurable:!0,value:i(null)}),e.exports=function(e){s[a][e]=!0}},214263:function(e,t,n){var r,i,o;!function(a,s){"use strict";i=[n(540343)],void 0===(o="function"==typeof(r=function(e){var t=/(^|@)\S+:\d+/,n=/^\s*at .*(\S+:\d+|\(native\))/m,r=/^(eval@)?(\[native code])?$/;return{parse:function(e){if(void 0!==e.stacktrace||void 0!==e["opera#sourceloc"])return this.parseOpera(e);if(e.stack&&e.stack.match(n))return this.parseV8OrIE(e);if(e.stack)return this.parseFFOrSafari(e);throw new Error("Cannot parse given Error object")},extractLocation:function(e){if(-1===e.indexOf(":"))return[e];var t=/(.+?)(?::(\d+))?(?::(\d+))?$/.exec(e.replace(/[()]/g,""));return[t[1],t[2]||void 0,t[3]||void 0]},parseV8OrIE:function(t){return t.stack.split("\n").filter((function(e){return!!e.match(n)}),this).map((function(t){t.indexOf("(eval ")>-1&&(t=t.replace(/eval code/g,"eval").replace(/(\(eval at [^()]*)|(,.*$)/g,""));var n=t.replace(/^\s+/,"").replace(/\(eval code/g,"(").replace(/^.*?\s+/,""),r=n.match(/ (\(.+\)$)/);n=r?n.replace(r[0],""):n;var i=this.extractLocation(r?r[1]:n),o=r&&n||void 0,a=["eval","<anonymous>"].indexOf(i[0])>-1?void 0:i[0];return new e({functionName:o,fileName:a,lineNumber:i[1],columnNumber:i[2],source:t})}),this)},parseFFOrSafari:function(t){return t.stack.split("\n").filter((function(e){return!e.match(r)}),this).map((function(t){if(t.indexOf(" > eval")>-1&&(t=t.replace(/ line (\d+)(?: > eval line \d+)* > eval:\d+:\d+/g,":$1")),-1===t.indexOf("@")&&-1===t.indexOf(":"))return new e({functionName:t});var n=/((.*".+"[^@]*)?[^@]*)(?:@)/,r=t.match(n),i=r&&r[1]?r[1]:void 0,o=this.extractLocation(t.replace(n,""));return new e({functionName:i,fileName:o[0],lineNumber:o[1],columnNumber:o[2],source:t})}),this)},parseOpera:function(e){return!e.stacktrace||e.message.indexOf("\n")>-1&&e.message.split("\n").length>e.stacktrace.split("\n").length?this.parseOpera9(e):e.stack?this.parseOpera11(e):this.parseOpera10(e)},parseOpera9:function(t){for(var n=/Line (\d+).*script (?:in )?(\S+)/i,r=t.message.split("\n"),i=[],o=2,a=r.length;o<a;o+=2){var s=n.exec(r[o]);s&&i.push(new e({fileName:s[2],lineNumber:s[1],source:r[o]}))}return i},parseOpera10:function(t){for(var n=/Line (\d+).*script (?:in )?(\S+)(?:: In function (\S+))?$/i,r=t.stacktrace.split("\n"),i=[],o=0,a=r.length;o<a;o+=2){var s=n.exec(r[o]);s&&i.push(new e({functionName:s[3]||void 0,fileName:s[2],lineNumber:s[1],source:r[o]}))}return i},parseOpera11:function(n){return n.stack.split("\n").filter((function(e){return!!e.match(t)&&!e.match(/^Error created at/)}),this).map((function(t){var n,r=t.split("@"),i=this.extractLocation(r.pop()),o=r.shift()||"",a=o.replace(/<anonymous function(: (\w+))?>/,"$2").replace(/\([^)]*\)/g,"")||void 0;o.match(/\(([^)]*)\)/)&&(n=o.replace(/^[^(]+\(([^)]*)\)$/,"$1"));var s=void 0===n||"[arguments not available]"===n?void 0:n.split(",");return new e({functionName:a,args:s,fileName:i[0],lineNumber:i[1],columnNumber:i[2],source:t})}),this)}}})?r.apply(t,i):r)||(e.exports=o)}()},215067:(e,t,n)=>{"use strict";n.d(t,{$x:()=>a,Ei:()=>l,IF:()=>o,O4:()=>r,Zo:()=>s});var r,i=()=>n(331635);!function(e){e.MISSING_VALUE="MISSING_VALUE",e.INVALID_VALUE="INVALID_VALUE",e.MISSING_INTL_API="MISSING_INTL_API"}(r||(r={}));var o=function(e){function t(t,n,r){var i=e.call(this,t)||this;return i.code=n,i.originalMessage=r,i}return(0,i().__extends)(t,e),t.prototype.toString=function(){return"[formatjs Error: ".concat(this.code,"] ").concat(this.message)},t}(Error),a=function(e){function t(t,n,i,o){return e.call(this,'Invalid values for "'.concat(t,'": "').concat(n,'". Options are "').concat(Object.keys(i).join('", "'),'"'),r.INVALID_VALUE,o)||this}return(0,i().__extends)(t,e),t}(o),s=function(e){function t(t,n,i){return e.call(this,'Value for "'.concat(t,'" must be of type ').concat(n),r.INVALID_VALUE,i)||this}return(0,i().__extends)(t,e),t}(o),l=function(e){function t(t,n){return e.call(this,'The intl string context variable "'.concat(t,'" was not provided to the string "').concat(n,'"'),r.MISSING_VALUE,n)||this}return(0,i().__extends)(t,e),t}(o)},221020:(e,t,n)=>{"use strict";var r=n(296540),i=Symbol.for("react.element"),o=Symbol.for("react.fragment"),a=Object.prototype.hasOwnProperty,s=r.__SECRET_INTERNALS_DO_NOT_USE_OR_YOU_WILL_BE_FIRED.ReactCurrentOwner,l={key:!0,ref:!0,__self:!0,__source:!0};function u(e,t,n){var r,o={},u=null,c=null;for(r in void 0!==n&&(u=""+n),void 0!==t.key&&(u=""+t.key),void 0!==t.ref&&(c=t.ref),t)a.call(t,r)&&!l.hasOwnProperty(r)&&(o[r]=t[r]);if(e&&e.defaultProps)for(r in t=e.defaultProps)void 0===o[r]&&(o[r]=t[r]);return{$$typeof:i,type:e,key:u,ref:c,props:o,_owner:s.current}}t.Fragment=o,t.jsx=u,t.jsxs=u},230882:(e,t)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),t.Diagnostics=void 0;const n=new Map,r="start",i="end",o="statsig::diagnostics";function a(e,t,n,r){return Object.assign({key:n,action:t,step:r,timestamp:Date.now()},e)}function s(e,t){var r;const i=null!==(r=n.get(e))&&void 0!==r?r:[];i.push(t),n.set(e,i)}function l(e,t){if(t in e)return e[t]}t.Diagnostics={_getMarkers:e=>n.get(e),_markInitOverallStart:e=>{s(e,a({},r,"overall"))},_markInitOverallEnd:(e,t,n)=>{s(e,a({success:t,error:t?void 0:{name:"InitializeError",message:"Failed to initialize"},evaluationDetails:n},i,"overall"))},_markInitNetworkReqStart:(e,t)=>{s(e,a(t,r,"initialize","network_request"))},_markInitNetworkReqEnd:(e,t)=>{s(e,a(t,i,"initialize","network_request"))},_markInitProcessStart:e=>{s(e,a({},r,"initialize","process"))},_markInitProcessEnd:(e,t)=>{s(e,a(t,i,"initialize","process"))},_clearMarkers:e=>{n.delete(e)},_formatError(e){if(e&&"object"==typeof e)return{code:l(e,"code"),name:l(e,"name"),message:l(e,"message")}},_getDiagnosticsData(e,n,r,i){var o;return{success:!0===(null==e?void 0:e.ok),statusCode:null==e?void 0:e.status,sdkRegion:null===(o=null==e?void 0:e.headers)||void 0===o?void 0:o.get("x-statsig-region"),isDelta:!0===r.includes('"is_delta":true')||void 0,attempt:n,error:t.Diagnostics._formatError(i)}},_enqueueDiagnosticsEvent(e,n,r,i){const a=t.Diagnostics._getMarkers(r);if(null==a||a.length<=0)return-1;const s=a[a.length-1].timestamp-a[0].timestamp;t.Diagnostics._clearMarkers(r);const l=function(e,t){const n={eventName:o,user:e,value:null,metadata:t,time:Date.now()};return n}(e,{context:"initialize",markers:a.slice(),statsigOptions:i});return n.enqueue(l),s}}},235953:(e,t,n)=>{"use strict";n.d(t,{HM:()=>s,JF:()=>l,bN:()=>c,yU:()=>u});var r=()=>n(331635),i=n(296540),o=n(331221),a=n(531234);function s(e){(0,o.V1)(e,"[React Intl] Could not find required `intl` object. <IntlProvider> needs to exist in the component ancestry.")}var l=(0,r().__assign)((0,r().__assign)({},a.JF),{textComponent:i.Fragment});function u(e){return function(t){return e(i.Children.toArray(t))}}function c(e,t){if(e===t)return!0;if(!e||!t)return!1;var n=Object.keys(e),r=Object.keys(t),i=n.length;if(r.length!==i)return!1;for(var o=0;o<i;o++){var a=n[o];if(e[a]!==t[a]||!Object.prototype.hasOwnProperty.call(t,a))return!1}return!0}},237467:(e,t,n)=>{"use strict";var r=n(437628),i=n(794644),o=i.aTypedArray,a=i.exportTypedArrayMethod,s=i.getTypedArrayConstructor;a("toReversed",(function(){return r(o(this),s(this))}))},240843:(e,t)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),t.createMemoKey=t.MemoPrefix=void 0,t.MemoPrefix={_gate:"g",_dynamicConfig:"c",_experiment:"e",_layer:"l",_paramStore:"p"};const n=new Set([]),r=new Set(["userPersistedValues"]);t.createMemoKey=function(e,t,i){let o=`${e}|${t}`;if(!i)return o;for(const a of Object.keys(i)){if(r.has(a))return;n.has(a)?o+=`|${a}=true`:o+=`|${a}=${i[a]}`}return o}},244641:(e,t,n)=>{"use strict";n.d(t,{c9:()=>sr,dw:()=>en,oh:()=>Le,R2:()=>rn,IX:()=>nn,wB:()=>Ge});class r extends Error{}class i extends r{constructor(e){super(`Invalid DateTime: ${e.toMessage()}`)}}class o extends r{constructor(e){super(`Invalid Interval: ${e.toMessage()}`)}}class a extends r{constructor(e){super(`Invalid Duration: ${e.toMessage()}`)}}class s extends r{}class l extends r{constructor(e){super(`Invalid unit ${e}`)}}class u extends r{}class c extends r{constructor(){super("Zone is an abstract class")}}const f="numeric",d="short",h="long",p={year:f,month:f,day:f},m={year:f,month:d,day:f},g={year:f,month:d,day:f,weekday:d},v={year:f,month:h,day:f},y={year:f,month:h,day:f,weekday:h},b={hour:f,minute:f},_={hour:f,minute:f,second:f},w={hour:f,minute:f,second:f,timeZoneName:d},S={hour:f,minute:f,second:f,timeZoneName:h},E={hour:f,minute:f,hourCycle:"h23"},k={hour:f,minute:f,second:f,hourCycle:"h23"},T={hour:f,minute:f,second:f,hourCycle:"h23",timeZoneName:d},x={hour:f,minute:f,second:f,hourCycle:"h23",timeZoneName:h},O={year:f,month:f,day:f,hour:f,minute:f},C={year:f,month:f,day:f,hour:f,minute:f,second:f},N={year:f,month:d,day:f,hour:f,minute:f},I={year:f,month:d,day:f,hour:f,minute:f,second:f},D={year:f,month:d,day:f,weekday:d,hour:f,minute:f},A={year:f,month:h,day:f,hour:f,minute:f,timeZoneName:d},P={year:f,month:h,day:f,hour:f,minute:f,second:f,timeZoneName:d},L={year:f,month:h,day:f,weekday:h,hour:f,minute:f,timeZoneName:h},M={year:f,month:h,day:f,weekday:h,hour:f,minute:f,second:f,timeZoneName:h};function R(e){return void 0===e}function F(e){return"number"==typeof e}function U(e){return"number"==typeof e&&e%1==0}function j(){try{return"undefined"!=typeof Intl&&!!Intl.RelativeTimeFormat}catch(e){return!1}}function B(e,t,n){if(0!==e.length)return e.reduce(((e,r)=>{const i=[t(r),r];return e&&n(e[0],i[0])===e[0]?e:i}),null)[1]}function z(e,t){return Object.prototype.hasOwnProperty.call(e,t)}function H(e,t,n){return U(e)&&e>=t&&e<=n}function V(e,t=2){let n;return n=e<0?"-"+(""+-e).padStart(t,"0"):(""+e).padStart(t,"0"),n}function $(e){return R(e)||null===e||""===e?void 0:parseInt(e,10)}function G(e){return R(e)||null===e||""===e?void 0:parseFloat(e)}function q(e){if(!R(e)&&null!==e&&""!==e){const t=1e3*parseFloat("0."+e);return Math.floor(t)}}function W(e,t,n=!1){const r=10**t;return(n?Math.trunc:Math.round)(e*r)/r}function K(e){return e%4==0&&(e%100!=0||e%400==0)}function Z(e){return K(e)?366:365}function J(e,t){const n=function(e,t){return e-t*Math.floor(e/t)}(t-1,12)+1;return 2===n?K(e+(t-n)/12)?29:28:[31,null,31,30,31,30,31,31,30,31,30,31][n-1]}function Y(e){let t=Date.UTC(e.year,e.month-1,e.day,e.hour,e.minute,e.second,e.millisecond);return e.year<100&&e.year>=0&&(t=new Date(t),t.setUTCFullYear(t.getUTCFullYear()-1900)),+t}function Q(e){const t=(e+Math.floor(e/4)-Math.floor(e/100)+Math.floor(e/400))%7,n=e-1,r=(n+Math.floor(n/4)-Math.floor(n/100)+Math.floor(n/400))%7;return 4===t||3===r?53:52}function X(e){return e>99?e:e>60?1900+e:2e3+e}function ee(e,t,n,r=null){const i=new Date(e),o={hourCycle:"h23",year:"numeric",month:"2-digit",day:"2-digit",hour:"2-digit",minute:"2-digit"};r&&(o.timeZone=r);const a={timeZoneName:t,...o},s=new Intl.DateTimeFormat(n,a).formatToParts(i).find((e=>"timezonename"===e.type.toLowerCase()));return s?s.value:null}function te(e,t){let n=parseInt(e,10);Number.isNaN(n)&&(n=0);const r=parseInt(t,10)||0;return 60*n+(n<0||Object.is(n,-0)?-r:r)}function ne(e){const t=Number(e);if("boolean"==typeof e||""===e||Number.isNaN(t))throw new u(`Invalid unit value ${e}`);return t}function re(e,t){const n={};for(const r in e)if(z(e,r)){const i=e[r];if(null==i)continue;n[t(r)]=ne(i)}return n}function ie(e,t){const n=Math.trunc(Math.abs(e/60)),r=Math.trunc(Math.abs(e%60)),i=e>=0?"+":"-";switch(t){case"short":return`${i}${V(n,2)}:${V(r,2)}`;case"narrow":return`${i}${n}${r>0?`:${r}`:""}`;case"techie":return`${i}${V(n,2)}${V(r,2)}`;default:throw new RangeError(`Value format ${t} is out of range for property format`)}}function oe(e){return function(e,t){return t.reduce(((t,n)=>(t[n]=e[n],t)),{})}(e,["hour","minute","second","millisecond"])}const ae=/[A-Za-z_+-]{1,256}(?::?\/[A-Za-z0-9_+-]{1,256}(?:\/[A-Za-z0-9_+-]{1,256})?)?/;const se=["January","February","March","April","May","June","July","August","September","October","November","December"],le=["Jan","Feb","Mar","Apr","May","Jun","Jul","Aug","Sep","Oct","Nov","Dec"],ue=["J","F","M","A","M","J","J","A","S","O","N","D"];function ce(e){switch(e){case"narrow":return[...ue];case"short":return[...le];case"long":return[...se];case"numeric":return["1","2","3","4","5","6","7","8","9","10","11","12"];case"2-digit":return["01","02","03","04","05","06","07","08","09","10","11","12"];default:return null}}const fe=["Monday","Tuesday","Wednesday","Thursday","Friday","Saturday","Sunday"],de=["Mon","Tue","Wed","Thu","Fri","Sat","Sun"],he=["M","T","W","T","F","S","S"];function pe(e){switch(e){case"narrow":return[...he];case"short":return[...de];case"long":return[...fe];case"numeric":return["1","2","3","4","5","6","7"];default:return null}}const me=["AM","PM"],ge=["Before Christ","Anno Domini"],ve=["BC","AD"],ye=["B","A"];function be(e){switch(e){case"narrow":return[...ye];case"short":return[...ve];case"long":return[...ge];default:return null}}function _e(e,t){let n="";for(const r of e)r.literal?n+=r.val:n+=t(r.val);return n}const we={D:p,DD:m,DDD:v,DDDD:y,t:b,tt:_,ttt:w,tttt:S,T:E,TT:k,TTT:T,TTTT:x,f:O,ff:N,fff:A,ffff:L,F:C,FF:I,FFF:P,FFFF:M};class Se{static create(e,t={}){return new Se(e,t)}static parseFormat(e){let t=null,n="",r=!1;const i=[];for(let o=0;o<e.length;o++){const a=e.charAt(o);"'"===a?(n.length>0&&i.push({literal:r,val:n}),t=null,n="",r=!r):r||a===t?n+=a:(n.length>0&&i.push({literal:!1,val:n}),n=a,t=a)}return n.length>0&&i.push({literal:r,val:n}),i}static macroTokenToFormatOpts(e){return we[e]}constructor(e,t){this.opts=t,this.loc=e,this.systemLoc=null}formatWithSystemDefault(e,t){null===this.systemLoc&&(this.systemLoc=this.loc.redefaultToSystem());return this.systemLoc.dtFormatter(e,{...this.opts,...t}).format()}formatDateTime(e,t={}){return this.loc.dtFormatter(e,{...this.opts,...t}).format()}formatDateTimeParts(e,t={}){return this.loc.dtFormatter(e,{...this.opts,...t}).formatToParts()}resolvedOptions(e,t={}){return this.loc.dtFormatter(e,{...this.opts,...t}).resolvedOptions()}num(e,t=0){if(this.opts.forceSimple)return V(e,t);const n={...this.opts};return t>0&&(n.padTo=t),this.loc.numberFormatter(n).format(e)}formatDateTimeFromString(e,t){const n="en"===this.loc.listingMode(),r=this.loc.outputCalendar&&"gregory"!==this.loc.outputCalendar,i=(t,n)=>this.loc.extract(e,t,n),o=t=>e.isOffsetFixed&&0===e.offset&&t.allowZ?"Z":e.isValid?e.zone.formatOffset(e.ts,t.format):"",a=()=>n?function(e){return me[e.hour<12?0:1]}(e):i({hour:"numeric",hourCycle:"h12"},"dayperiod"),s=(t,r)=>n?function(e,t){return ce(t)[e.month-1]}(e,t):i(r?{month:t}:{month:t,day:"numeric"},"month"),l=(t,r)=>n?function(e,t){return pe(t)[e.weekday-1]}(e,t):i(r?{weekday:t}:{weekday:t,month:"long",day:"numeric"},"weekday"),u=t=>{const n=Se.macroTokenToFormatOpts(t);return n?this.formatWithSystemDefault(e,n):t},c=t=>n?function(e,t){return be(t)[e.year<0?0:1]}(e,t):i({era:t},"era");return _e(Se.parseFormat(t),(t=>{switch(t){case"S":return this.num(e.millisecond);case"u":case"SSS":return this.num(e.millisecond,3);case"s":return this.num(e.second);case"ss":return this.num(e.second,2);case"uu":return this.num(Math.floor(e.millisecond/10),2);case"uuu":return this.num(Math.floor(e.millisecond/100));case"m":return this.num(e.minute);case"mm":return this.num(e.minute,2);case"h":return this.num(e.hour%12==0?12:e.hour%12);case"hh":return this.num(e.hour%12==0?12:e.hour%12,2);case"H":return this.num(e.hour);case"HH":return this.num(e.hour,2);case"Z":return o({format:"narrow",allowZ:this.opts.allowZ});case"ZZ":return o({format:"short",allowZ:this.opts.allowZ});case"ZZZ":return o({format:"techie",allowZ:this.opts.allowZ});case"ZZZZ":return e.zone.offsetName(e.ts,{format:"short",locale:this.loc.locale});case"ZZZZZ":return e.zone.offsetName(e.ts,{format:"long",locale:this.loc.locale});case"z":return e.zoneName;case"a":return a();case"d":return r?i({day:"numeric"},"day"):this.num(e.day);case"dd":return r?i({day:"2-digit"},"day"):this.num(e.day,2);case"c":case"E":return this.num(e.weekday);case"ccc":return l("short",!0);case"cccc":return l("long",!0);case"ccccc":return l("narrow",!0);case"EEE":return l("short",!1);case"EEEE":return l("long",!1);case"EEEEE":return l("narrow",!1);case"L":return r?i({month:"numeric",day:"numeric"},"month"):this.num(e.month);case"LL":return r?i({month:"2-digit",day:"numeric"},"month"):this.num(e.month,2);case"LLL":return s("short",!0);case"LLLL":return s("long",!0);case"LLLLL":return s("narrow",!0);case"M":return r?i({month:"numeric"},"month"):this.num(e.month);case"MM":return r?i({month:"2-digit"},"month"):this.num(e.month,2);case"MMM":return s("short",!1);case"MMMM":return s("long",!1);case"MMMMM":return s("narrow",!1);case"y":return r?i({year:"numeric"},"year"):this.num(e.year);case"yy":return r?i({year:"2-digit"},"year"):this.num(e.year.toString().slice(-2),2);case"yyyy":return r?i({year:"numeric"},"year"):this.num(e.year,4);case"yyyyyy":return r?i({year:"numeric"},"year"):this.num(e.year,6);case"G":return c("short");case"GG":return c("long");case"GGGGG":return c("narrow");case"kk":return this.num(e.weekYear.toString().slice(-2),2);case"kkkk":return this.num(e.weekYear,4);case"W":return this.num(e.weekNumber);case"WW":return this.num(e.weekNumber,2);case"o":return this.num(e.ordinal);case"ooo":return this.num(e.ordinal,3);case"q":return this.num(e.quarter);case"qq":return this.num(e.quarter,2);case"X":return this.num(Math.floor(e.ts/1e3));case"x":return this.num(e.ts);default:return u(t)}}))}formatDurationFromString(e,t){const n=e=>{switch(e[0]){case"S":return"millisecond";case"s":return"second";case"m":return"minute";case"h":return"hour";case"d":return"day";case"w":return"week";case"M":return"month";case"y":return"year";default:return null}},r=Se.parseFormat(t),i=r.reduce(((e,{literal:t,val:n})=>t?e:e.concat(n)),[]);return _e(r,(e=>t=>{const r=n(t);return r?this.num(e.get(r),t.length):t})(e.shiftTo(...i.map(n).filter((e=>e)))))}}class Ee{constructor(e,t){this.reason=e,this.explanation=t}toMessage(){return this.explanation?`${this.reason}: ${this.explanation}`:this.reason}}class ke{get type(){throw new c}get name(){throw new c}get ianaName(){return this.name}get isUniversal(){throw new c}offsetName(e,t){throw new c}formatOffset(e,t){throw new c}offset(e){throw new c}equals(e){throw new c}get isValid(){throw new c}}let Te=null;class xe extends ke{static get instance(){return null===Te&&(Te=new xe),Te}get type(){return"system"}get name(){return(new Intl.DateTimeFormat).resolvedOptions().timeZone}get isUniversal(){return!1}offsetName(e,{format:t,locale:n}){return ee(e,t,n)}formatOffset(e,t){return ie(this.offset(e),t)}offset(e){return-new Date(e).getTimezoneOffset()}equals(e){return"system"===e.type}get isValid(){return!0}}let Oe={};function Ce(e){return Oe[e]||(Oe[e]=new Intl.DateTimeFormat("en-US",{hour12:!1,timeZone:e,year:"numeric",month:"2-digit",day:"2-digit",hour:"2-digit",minute:"2-digit",second:"2-digit",era:"short"})),Oe[e]}const Ne={year:0,month:1,day:2,era:3,hour:4,minute:5,second:6};function Ie(e,t){if(Le.hackyOffsetParsesCorrectly())return De(e,t);if(e.formatToParts)return function(e,t){const n=e.formatToParts(t),r=[];for(let i=0;i<n.length;i++){const{type:e,value:t}=n[i],o=Ne[e];"era"===e?r[o]=t:R(o)||(r[o]=parseInt(t,10))}return r}(e,t);throw new Error("Unable to compute time zone offset using Intl.DateTimeFormat")}function De(e,t){const n=e.format(t).replace(/\u200E/g,""),r=/(\d+)\/(\d+)\/(\d+) (AD|BC),? (\d+):(\d+):(\d+)/.exec(n),[,i,o,a,s,l,u,c]=r;return[+a,+i,+o,s,+l,+u,+c]}let Ae,Pe={};class Le extends ke{static create(e){return Pe[e]||(Pe[e]=new Le(e)),Pe[e]}static resetCache(){Pe={},Oe={},Ae=void 0}static getDtf(e){return Ce(e)}static hackyOffsetParsesCorrectly(){if(void 0===Ae){const e=Ce("UTC");try{const[t,n,r,i,o,a,s]=De(e,new Date(Date.UTC(1969,11,31,15,45,55)));Ae=1969===t&&12===n&&31===r&&"AD"===i&&15===o&&45===a&&55===s}catch{Ae=!1}}return Ae}static isValidSpecifier(e){return this.isValidZone(e)}static isValidZone(e){if(!e)return!1;try{return new Intl.DateTimeFormat("en-US",{timeZone:e}).format(),!0}catch(t){return!1}}constructor(e){super(),this.zoneName=e,this.valid=Le.isValidZone(e)}get type(){return"iana"}get name(){return this.zoneName}get isUniversal(){return!1}offsetName(e,{format:t,locale:n}){return ee(e,t,n,this.name)}formatOffset(e,t){return ie(this.offset(e),t)}offset(e){const t=new Date(e);if(isNaN(t))return NaN;const n=Ce(this.name);let[r,i,o,a,s,l,u]=Ie(n,t);"BC"===a&&(r=1-Math.abs(r));let c=+t;const f=c%1e3;return c-=f>=0?f:1e3+f,(Y({year:r,month:i,day:o,hour:24===s?0:s,minute:l,second:u,millisecond:0})-c)/6e4}equals(e){return"iana"===e.type&&e.name===this.name}get isValid(){return this.valid}}let Me=null;class Re extends ke{static get utcInstance(){return null===Me&&(Me=new Re(0)),Me}static instance(e){return 0===e?Re.utcInstance:new Re(e)}static parseSpecifier(e){if(e){const t=e.match(/^utc(?:([+-]\d{1,2})(?::(\d{2}))?)?$/i);if(t)return new Re(te(t[1],t[2]))}return null}constructor(e){super(),this.fixed=e}get type(){return"fixed"}get name(){return 0===this.fixed?"UTC":`UTC${ie(this.fixed,"narrow")}`}get ianaName(){return 0===this.fixed?"Etc/UTC":`Etc/GMT${ie(-this.fixed,"narrow")}`}offsetName(){return this.name}formatOffset(e,t){return ie(this.fixed,t)}get isUniversal(){return!0}offset(){return this.fixed}equals(e){return"fixed"===e.type&&e.fixed===this.fixed}get isValid(){return!0}}class Fe extends ke{constructor(e){super(),this.zoneName=e}get type(){return"invalid"}get name(){return this.zoneName}get isUniversal(){return!1}offsetName(){return null}formatOffset(){return""}offset(){return NaN}equals(){return!1}get isValid(){return!1}}function Ue(e,t){if(R(e)||null===e)return t;if(e instanceof ke)return e;if("string"==typeof e){const n=e.toLowerCase();return"default"===n?t:"local"===n||"system"===n?xe.instance:"utc"===n||"gmt"===n?Re.utcInstance:Re.parseSpecifier(n)||Le.create(e)}return F(e)?Re.instance(e):"object"==typeof e&&e.offset&&"number"==typeof e.offset?e:new Fe(e)}let je,Be=()=>Date.now(),ze="system",He=null,Ve=null,$e=null;class Ge{static get now(){return Be}static set now(e){Be=e}static set defaultZone(e){ze=e}static get defaultZone(){return Ue(ze,xe.instance)}static get defaultLocale(){return He}static set defaultLocale(e){He=e}static get defaultNumberingSystem(){return Ve}static set defaultNumberingSystem(e){Ve=e}static get defaultOutputCalendar(){return $e}static set defaultOutputCalendar(e){$e=e}static get throwOnInvalid(){return je}static set throwOnInvalid(e){je=e}static resetCaches(){it.resetCache(),Le.resetCache()}}let qe={};let We={};function Ke(e,t={}){const n=JSON.stringify([e,t]);let r=We[n];return r||(r=new Intl.DateTimeFormat(e,t),We[n]=r),r}let Ze={};let Je={};let Ye=null;let Qe={};function Xe(e){return Qe[e]||(Qe[e]=new Intl.DateTimeFormat(e).resolvedOptions()),Qe[e]}function et(e,t,n,r,i){const o=e.listingMode(n);return"error"===o?null:"en"===o?r(t):i(t)}class tt{constructor(e,t,n){this.padTo=n.padTo||0,this.floor=n.floor||!1;const{padTo:r,floor:i,...o}=n;if(!t||Object.keys(o).length>0){const t={useGrouping:!1,...n};n.padTo>0&&(t.minimumIntegerDigits=n.padTo),this.inf=function(e,t={}){const n=JSON.stringify([e,t]);let r=Ze[n];return r||(r=new Intl.NumberFormat(e,t),Ze[n]=r),r}(e,t)}}format(e){if(this.inf){const t=this.floor?Math.floor(e):e;return this.inf.format(t)}return V(this.floor?Math.floor(e):W(e,3),this.padTo)}}class nt{constructor(e,t,n){let r;if(this.opts=n,e.zone.isUniversal){const t=e.offset/60*-1,i=t>=0?`Etc/GMT+${t}`:`Etc/GMT${t}`;0!==e.offset&&Le.create(i).valid?(r=i,this.dt=e):(r="UTC",n.timeZoneName?this.dt=e:this.dt=0===e.offset?e:sr.fromMillis(e.ts+60*e.offset*1e3))}else"system"===e.zone.type?this.dt=e:(this.dt=e,r=e.zone.name);const i={...this.opts};r&&(i.timeZone=r),this.dtf=Ke(t,i)}format(){return this.dtf.format(this.dt.toJSDate())}formatToParts(){return this.dtf.formatToParts(this.dt.toJSDate())}resolvedOptions(){return this.dtf.resolvedOptions()}}class rt{constructor(e,t,n){this.opts={style:"long",...n},!t&&j()&&(this.rtf=function(e,t={}){const{base:n,...r}=t,i=JSON.stringify([e,r]);let o=Je[i];return o||(o=new Intl.RelativeTimeFormat(e,t),Je[i]=o),o}(e,n))}format(e,t){return this.rtf?this.rtf.format(e,t):function(e,t,n="always",r=!1){const i={years:["year","yr."],quarters:["quarter","qtr."],months:["month","mo."],weeks:["week","wk."],days:["day","day","days"],hours:["hour","hr."],minutes:["minute","min."],seconds:["second","sec."]},o=-1===["hours","minutes","seconds"].indexOf(e);if("auto"===n&&o){const n="days"===e;switch(t){case 1:return n?"tomorrow":`next ${i[e][0]}`;case-1:return n?"yesterday":`last ${i[e][0]}`;case 0:return n?"today":`this ${i[e][0]}`}}const a=Object.is(t,-0)||t<0,s=Math.abs(t),l=1===s,u=i[e],c=r?l?u[1]:u[2]||u[1]:l?i[e][0]:e;return a?`${s} ${c} ago`:`in ${s} ${c}`}(t,e,this.opts.numeric,"long"!==this.opts.style)}formatToParts(e,t){return this.rtf?this.rtf.formatToParts(e,t):[]}}class it{static fromOpts(e){return it.create(e.locale,e.numberingSystem,e.outputCalendar,e.defaultToEN)}static create(e,t,n,r=!1){const i=e||Ge.defaultLocale,o=i||(r?"en-US":Ye||(Ye=(new Intl.DateTimeFormat).resolvedOptions().locale,Ye)),a=t||Ge.defaultNumberingSystem,s=n||Ge.defaultOutputCalendar;return new it(o,a,s,i)}static resetCache(){Ye=null,We={},Ze={},Je={},Qe={}}static fromObject({locale:e,numberingSystem:t,outputCalendar:n}={}){return it.create(e,t,n)}constructor(e,t,n,r){const[i,o,a]=function(e){const t=e.indexOf("-u-");if(-1===t)return[e];{let r;const i=e.substring(0,t);try{r=Ke(e).resolvedOptions()}catch(n){r=Ke(i).resolvedOptions()}const{numberingSystem:o,calendar:a}=r;return[i,o,a]}}(e);this.locale=i,this.numberingSystem=t||o||null,this.outputCalendar=n||a||null,this.intl=function(e,t,n){return n||t?(e+="-u",n&&(e+=`-ca-${n}`),t&&(e+=`-nu-${t}`),e):e}(this.locale,this.numberingSystem,this.outputCalendar),this.weekdaysCache={format:{},standalone:{}},this.monthsCache={format:{},standalone:{}},this.meridiemCache=null,this.eraCache={},this.specifiedLocale=r,this.fastNumbersCached=null}get fastNumbers(){var e;return null==this.fastNumbersCached&&(this.fastNumbersCached=(!(e=this).numberingSystem||"latn"===e.numberingSystem)&&("latn"===e.numberingSystem||!e.locale||e.locale.startsWith("en")||"latn"===Xe(e.locale).numberingSystem)),this.fastNumbersCached}listingMode(){const e=this.isEnglish(),t=!(null!==this.numberingSystem&&"latn"!==this.numberingSystem||null!==this.outputCalendar&&"gregory"!==this.outputCalendar);return e&&t?"en":"intl"}clone(e){return e&&0!==Object.getOwnPropertyNames(e).length?it.create(e.locale||this.specifiedLocale,e.numberingSystem||this.numberingSystem,e.outputCalendar||this.outputCalendar,e.defaultToEN||!1):this}redefaultToEN(e={}){return this.clone({...e,defaultToEN:!0})}redefaultToSystem(e={}){return this.clone({...e,defaultToEN:!1})}months(e,t=!1,n=!0){return et(this,e,n,ce,(()=>{const n=t?{month:e,day:"numeric"}:{month:e},r=t?"format":"standalone";return this.monthsCache[r][e]||(this.monthsCache[r][e]=function(e){const t=[];for(let n=1;n<=12;n++){const r=sr.utc(2016,n,1);t.push(e(r))}return t}((e=>this.extract(e,n,"month")))),this.monthsCache[r][e]}))}weekdays(e,t=!1,n=!0){return et(this,e,n,pe,(()=>{const n=t?{weekday:e,year:"numeric",month:"long",day:"numeric"}:{weekday:e},r=t?"format":"standalone";return this.weekdaysCache[r][e]||(this.weekdaysCache[r][e]=function(e){const t=[];for(let n=1;n<=7;n++){const r=sr.utc(2016,11,13+n);t.push(e(r))}return t}((e=>this.extract(e,n,"weekday")))),this.weekdaysCache[r][e]}))}meridiems(e=!0){return et(this,void 0,e,(()=>me),(()=>{if(!this.meridiemCache){const e={hour:"numeric",hourCycle:"h12"};this.meridiemCache=[sr.utc(2016,11,13,9),sr.utc(2016,11,13,19)].map((t=>this.extract(t,e,"dayperiod")))}return this.meridiemCache}))}eras(e,t=!0){return et(this,e,t,be,(()=>{const t={era:e};return this.eraCache[e]||(this.eraCache[e]=[sr.utc(-40,1,1),sr.utc(2017,1,1)].map((e=>this.extract(e,t,"era")))),this.eraCache[e]}))}extract(e,t,n){const r=this.dtFormatter(e,t).formatToParts().find((e=>e.type.toLowerCase()===n));return r?r.value:null}numberFormatter(e={}){return new tt(this.intl,e.forceSimple||this.fastNumbers,e)}dtFormatter(e,t={}){return new nt(e,this.intl,t)}relFormatter(e={}){return new rt(this.intl,this.isEnglish(),e)}listFormatter(e={}){return function(e,t={}){const n=JSON.stringify([e,t]);let r=qe[n];return r||(r=new Intl.ListFormat(e,t),qe[n]=r),r}(this.intl,e)}isEnglish(){return"en"===this.locale||"en-us"===this.locale.toLowerCase()||Xe(this.intl).locale.startsWith("en-us")}equals(e){return this.locale===e.locale&&this.numberingSystem===e.numberingSystem&&this.outputCalendar===e.outputCalendar}toString(){return`Locale(${this.locale}, ${this.numberingSystem}, ${this.outputCalendar})`}}function ot(...e){const t=e.reduce(((e,t)=>e+t.source),"");return RegExp(`^${t}$`)}function at(...e){return t=>e.reduce((([e,n,r],i)=>{const[o,a,s]=i(t,r);return[{...e,...o},a||n,s]}),[{},null,1]).slice(0,2)}function st(e,...t){if(null==e)return[null,null];for(const[n,r]of t){const t=n.exec(e);if(t)return r(t)}return[null,null]}function lt(...e){return(t,n)=>{const r={};let i;for(i=0;i<e.length;i++)r[e[i]]=$(t[n+i]);return[r,null,n+i]}}const ut=/(?:(Z)|([+-]\d\d)(?::?(\d\d))?)/,ct=/(\d\d)(?::?(\d\d)(?::?(\d\d)(?:[.,](\d{1,30}))?)?)?/,ft=RegExp(`${ct.source}${`(?:${ut.source}?(?:\\[(${ae.source})\\])?)?`}`),dt=RegExp(`(?:T${ft.source})?`),ht=lt("weekYear","weekNumber","weekDay"),pt=lt("year","ordinal"),mt=RegExp(`${ct.source} ?(?:${ut.source}|(${ae.source}))?`),gt=RegExp(`(?: ${mt.source})?`);function vt(e,t,n){const r=e[t];return R(r)?n:$(r)}function yt(e,t){return[{hours:vt(e,t,0),minutes:vt(e,t+1,0),seconds:vt(e,t+2,0),milliseconds:q(e[t+3])},null,t+4]}function bt(e,t){const n=!e[t]&&!e[t+1],r=te(e[t+1],e[t+2]);return[{},n?null:Re.instance(r),t+3]}function _t(e,t){return[{},e[t]?Le.create(e[t]):null,t+1]}const wt=RegExp(`^T?${ct.source}$`),St=/^-?P(?:(?:(-?\d{1,20}(?:\.\d{1,20})?)Y)?(?:(-?\d{1,20}(?:\.\d{1,20})?)M)?(?:(-?\d{1,20}(?:\.\d{1,20})?)W)?(?:(-?\d{1,20}(?:\.\d{1,20})?)D)?(?:T(?:(-?\d{1,20}(?:\.\d{1,20})?)H)?(?:(-?\d{1,20}(?:\.\d{1,20})?)M)?(?:(-?\d{1,20})(?:[.,](-?\d{1,20}))?S)?)?)$/;function Et(e){const[t,n,r,i,o,a,s,l,u]=e,c="-"===t[0],f=l&&"-"===l[0],d=(e,t=!1)=>void 0!==e&&(t||e&&c)?-e:e;return[{years:d(G(n)),months:d(G(r)),weeks:d(G(i)),days:d(G(o)),hours:d(G(a)),minutes:d(G(s)),seconds:d(G(l),"-0"===l),milliseconds:d(q(u),f)}]}const kt={GMT:0,EDT:-240,EST:-300,CDT:-300,CST:-360,MDT:-360,MST:-420,PDT:-420,PST:-480};function Tt(e,t,n,r,i,o,a){const s={year:2===t.length?X($(t)):$(t),month:le.indexOf(n)+1,day:$(r),hour:$(i),minute:$(o)};return a&&(s.second=$(a)),e&&(s.weekday=e.length>3?fe.indexOf(e)+1:de.indexOf(e)+1),s}const xt=/^(?:(Mon|Tue|Wed|Thu|Fri|Sat|Sun),\s)?(\d{1,2})\s(Jan|Feb|Mar|Apr|May|Jun|Jul|Aug|Sep|Oct|Nov|Dec)\s(\d{2,4})\s(\d\d):(\d\d)(?::(\d\d))?\s(?:(UT|GMT|[ECMP][SD]T)|([Zz])|(?:([+-]\d\d)(\d\d)))$/;function Ot(e){const[,t,n,r,i,o,a,s,l,u,c,f]=e,d=Tt(t,i,r,n,o,a,s);let h;return h=l?kt[l]:u?0:te(c,f),[d,new Re(h)]}const Ct=/^(Mon|Tue|Wed|Thu|Fri|Sat|Sun), (\d\d) (Jan|Feb|Mar|Apr|May|Jun|Jul|Aug|Sep|Oct|Nov|Dec) (\d{4}) (\d\d):(\d\d):(\d\d) GMT$/,Nt=/^(Monday|Tuesday|Wednesday|Thursday|Friday|Saturday|Sunday), (\d\d)-(Jan|Feb|Mar|Apr|May|Jun|Jul|Aug|Sep|Oct|Nov|Dec)-(\d\d) (\d\d):(\d\d):(\d\d) GMT$/,It=/^(Mon|Tue|Wed|Thu|Fri|Sat|Sun) (Jan|Feb|Mar|Apr|May|Jun|Jul|Aug|Sep|Oct|Nov|Dec) ( \d|\d\d) (\d\d):(\d\d):(\d\d) (\d{4})$/;function Dt(e){const[,t,n,r,i,o,a,s]=e;return[Tt(t,i,r,n,o,a,s),Re.utcInstance]}function At(e){const[,t,n,r,i,o,a,s]=e;return[Tt(t,s,n,r,i,o,a),Re.utcInstance]}const Pt=ot(/([+-]\d{6}|\d{4})(?:-?(\d\d)(?:-?(\d\d))?)?/,dt),Lt=ot(/(\d{4})-?W(\d\d)(?:-?(\d))?/,dt),Mt=ot(/(\d{4})-?(\d{3})/,dt),Rt=ot(ft),Ft=at((function(e,t){return[{year:vt(e,t),month:vt(e,t+1,1),day:vt(e,t+2,1)},null,t+3]}),yt,bt,_t),Ut=at(ht,yt,bt,_t),jt=at(pt,yt,bt,_t),Bt=at(yt,bt,_t);const zt=at(yt);const Ht=ot(/(\d{4})-(\d\d)-(\d\d)/,gt),Vt=ot(mt),$t=at(yt,bt,_t);const Gt={weeks:{days:7,hours:168,minutes:10080,seconds:604800,milliseconds:6048e5},days:{hours:24,minutes:1440,seconds:86400,milliseconds:864e5},hours:{minutes:60,seconds:3600,milliseconds:36e5},minutes:{seconds:60,milliseconds:6e4},seconds:{milliseconds:1e3}},qt={years:{quarters:4,months:12,weeks:52,days:365,hours:8760,minutes:525600,seconds:31536e3,milliseconds:31536e6},quarters:{months:3,weeks:13,days:91,hours:2184,minutes:131040,seconds:7862400,milliseconds:78624e5},months:{weeks:4,days:30,hours:720,minutes:43200,seconds:2592e3,milliseconds:2592e6},...Gt},Wt=365.2425,Kt=30.436875,Zt={years:{quarters:4,months:12,weeks:52.1775,days:Wt,hours:8765.82,minutes:525949.2,seconds:525949.2*60,milliseconds:525949.2*60*1e3},quarters:{months:3,weeks:13.044375,days:91.310625,hours:2191.455,minutes:131487.3,seconds:525949.2*60/4,milliseconds:7889237999.999999},months:{weeks:4.3481250000000005,days:Kt,hours:730.485,minutes:43829.1,seconds:2629746,milliseconds:2629746e3},...Gt},Jt=["years","quarters","months","weeks","days","hours","minutes","seconds","milliseconds"],Yt=Jt.slice(0).reverse();function Qt(e,t,n=!1){const r={values:n?t.values:{...e.values,...t.values||{}},loc:e.loc.clone(t.loc),conversionAccuracy:t.conversionAccuracy||e.conversionAccuracy};return new en(r)}function Xt(e,t,n,r,i){const o=e[i][n],a=t[n]/o,s=!(Math.sign(a)===Math.sign(r[i]))&&0!==r[i]&&Math.abs(a)<=1?function(e){return e<0?Math.floor(e):Math.ceil(e)}(a):Math.trunc(a);r[i]+=s,t[n]-=s*o}class en{constructor(e){const t="longterm"===e.conversionAccuracy||!1;this.values=e.values,this.loc=e.loc||it.create(),this.conversionAccuracy=t?"longterm":"casual",this.invalid=e.invalid||null,this.matrix=t?Zt:qt,this.isLuxonDuration=!0}static fromMillis(e,t){return en.fromObject({milliseconds:e},t)}static fromObject(e,t={}){if(null==e||"object"!=typeof e)throw new u("Duration.fromObject: argument expected to be an object, got "+(null===e?"null":typeof e));return new en({values:re(e,en.normalizeUnit),loc:it.fromObject(t),conversionAccuracy:t.conversionAccuracy})}static fromDurationLike(e){if(F(e))return en.fromMillis(e);if(en.isDuration(e))return e;if("object"==typeof e)return en.fromObject(e);throw new u(`Unknown duration argument ${e} of type ${typeof e}`)}static fromISO(e,t){const[n]=function(e){return st(e,[St,Et])}(e);return n?en.fromObject(n,t):en.invalid("unparsable",`the input "${e}" can't be parsed as ISO 8601`)}static fromISOTime(e,t){const[n]=function(e){return st(e,[wt,zt])}(e);return n?en.fromObject(n,t):en.invalid("unparsable",`the input "${e}" can't be parsed as ISO 8601`)}static invalid(e,t=null){if(!e)throw new u("need to specify a reason the Duration is invalid");const n=e instanceof Ee?e:new Ee(e,t);if(Ge.throwOnInvalid)throw new a(n);return new en({invalid:n})}static normalizeUnit(e){const t={year:"years",years:"years",quarter:"quarters",quarters:"quarters",month:"months",months:"months",week:"weeks",weeks:"weeks",day:"days",days:"days",hour:"hours",hours:"hours",minute:"minutes",minutes:"minutes",second:"seconds",seconds:"seconds",millisecond:"milliseconds",milliseconds:"milliseconds"}[e?e.toLowerCase():e];if(!t)throw new l(e);return t}static isDuration(e){return e&&e.isLuxonDuration||!1}get locale(){return this.isValid?this.loc.locale:null}get numberingSystem(){return this.isValid?this.loc.numberingSystem:null}toFormat(e,t={}){const n={...t,floor:!1!==t.round&&!1!==t.floor};return this.isValid?Se.create(this.loc,n).formatDurationFromString(this,e):"Invalid Duration"}toHuman(e={}){const t=Jt.map((t=>{const n=this.values[t];return R(n)?null:this.loc.numberFormatter({style:"unit",unitDisplay:"long",...e,unit:t.slice(0,-1)}).format(n)})).filter((e=>e));return this.loc.listFormatter({type:"conjunction",style:e.listStyle||"narrow",...e}).format(t)}toObject(){return this.isValid?{...this.values}:{}}toISO(){if(!this.isValid)return null;let e="P";return 0!==this.years&&(e+=this.years+"Y"),0===this.months&&0===this.quarters||(e+=this.months+3*this.quarters+"M"),0!==this.weeks&&(e+=this.weeks+"W"),0!==this.days&&(e+=this.days+"D"),0===this.hours&&0===this.minutes&&0===this.seconds&&0===this.milliseconds||(e+="T"),0!==this.hours&&(e+=this.hours+"H"),0!==this.minutes&&(e+=this.minutes+"M"),0===this.seconds&&0===this.milliseconds||(e+=W(this.seconds+this.milliseconds/1e3,3)+"S"),"P"===e&&(e+="T0S"),e}toISOTime(e={}){if(!this.isValid)return null;const t=this.toMillis();if(t<0||t>=864e5)return null;e={suppressMilliseconds:!1,suppressSeconds:!1,includePrefix:!1,format:"extended",...e};const n=this.shiftTo("hours","minutes","seconds","milliseconds");let r="basic"===e.format?"hhmm":"hh:mm";e.suppressSeconds&&0===n.seconds&&0===n.milliseconds||(r+="basic"===e.format?"ss":":ss",e.suppressMilliseconds&&0===n.milliseconds||(r+=".SSS"));let i=n.toFormat(r);return e.includePrefix&&(i="T"+i),i}toJSON(){return this.toISO()}toString(){return this.toISO()}toMillis(){return this.as("milliseconds")}valueOf(){return this.toMillis()}plus(e){if(!this.isValid)return this;const t=en.fromDurationLike(e),n={};for(const r of Jt)(z(t.values,r)||z(this.values,r))&&(n[r]=t.get(r)+this.get(r));return Qt(this,{values:n},!0)}minus(e){if(!this.isValid)return this;const t=en.fromDurationLike(e);return this.plus(t.negate())}mapUnits(e){if(!this.isValid)return this;const t={};for(const n of Object.keys(this.values))t[n]=ne(e(this.values[n],n));return Qt(this,{values:t},!0)}get(e){return this[en.normalizeUnit(e)]}set(e){if(!this.isValid)return this;return Qt(this,{values:{...this.values,...re(e,en.normalizeUnit)}})}reconfigure({locale:e,numberingSystem:t,conversionAccuracy:n}={}){const r={loc:this.loc.clone({locale:e,numberingSystem:t})};return n&&(r.conversionAccuracy=n),Qt(this,r)}as(e){return this.isValid?this.shiftTo(e).get(e):NaN}normalize(){if(!this.isValid)return this;const e=this.toObject();return function(e,t){Yt.reduce(((n,r)=>R(t[r])?n:(n&&Xt(e,t,n,t,r),r)),null)}(this.matrix,e),Qt(this,{values:e},!0)}shiftTo(...e){if(!this.isValid)return this;if(0===e.length)return this;e=e.map((e=>en.normalizeUnit(e)));const t={},n={},r=this.toObject();let i;for(const o of Jt)if(e.indexOf(o)>=0){i=o;let e=0;for(const t in n)e+=this.matrix[t][o]*n[t],n[t]=0;F(r[o])&&(e+=r[o]);const a=Math.trunc(e);t[o]=a,n[o]=(1e3*e-1e3*a)/1e3;for(const n in r)Jt.indexOf(n)>Jt.indexOf(o)&&Xt(this.matrix,r,n,t,o)}else F(r[o])&&(n[o]=r[o]);for(const o in n)0!==n[o]&&(t[i]+=o===i?n[o]:n[o]/this.matrix[i][o]);return Qt(this,{values:t},!0).normalize()}negate(){if(!this.isValid)return this;const e={};for(const t of Object.keys(this.values))e[t]=0===this.values[t]?0:-this.values[t];return Qt(this,{values:e},!0)}get years(){return this.isValid?this.values.years||0:NaN}get quarters(){return this.isValid?this.values.quarters||0:NaN}get months(){return this.isValid?this.values.months||0:NaN}get weeks(){return this.isValid?this.values.weeks||0:NaN}get days(){return this.isValid?this.values.days||0:NaN}get hours(){return this.isValid?this.values.hours||0:NaN}get minutes(){return this.isValid?this.values.minutes||0:NaN}get seconds(){return this.isValid?this.values.seconds||0:NaN}get milliseconds(){return this.isValid?this.values.milliseconds||0:NaN}get isValid(){return null===this.invalid}get invalidReason(){return this.invalid?this.invalid.reason:null}get invalidExplanation(){return this.invalid?this.invalid.explanation:null}equals(e){if(!this.isValid||!e.isValid)return!1;if(!this.loc.equals(e.loc))return!1;for(const r of Jt)if(t=this.values[r],n=e.values[r],!(void 0===t||0===t?void 0===n||0===n:t===n))return!1;var t,n;return!0}}const tn="Invalid Interval";class nn{constructor(e){this.s=e.start,this.e=e.end,this.invalid=e.invalid||null,this.isLuxonInterval=!0}static invalid(e,t=null){if(!e)throw new u("need to specify a reason the Interval is invalid");const n=e instanceof Ee?e:new Ee(e,t);if(Ge.throwOnInvalid)throw new o(n);return new nn({invalid:n})}static fromDateTimes(e,t){const n=lr(e),r=lr(t),i=function(e,t){return e&&e.isValid?t&&t.isValid?t<e?nn.invalid("end before start",`The end of an interval must be after its start, but you had start=${e.toISO()} and end=${t.toISO()}`):null:nn.invalid("missing or invalid end"):nn.invalid("missing or invalid start")}(n,r);return null==i?new nn({start:n,end:r}):i}static after(e,t){const n=en.fromDurationLike(t),r=lr(e);return nn.fromDateTimes(r,r.plus(n))}static before(e,t){const n=en.fromDurationLike(t),r=lr(e);return nn.fromDateTimes(r.minus(n),r)}static fromISO(e,t){const[n,r]=(e||"").split("/",2);if(n&&r){let e,i,o,a;try{e=sr.fromISO(n,t),i=e.isValid}catch(r){i=!1}try{o=sr.fromISO(r,t),a=o.isValid}catch(r){a=!1}if(i&&a)return nn.fromDateTimes(e,o);if(i){const n=en.fromISO(r,t);if(n.isValid)return nn.after(e,n)}else if(a){const e=en.fromISO(n,t);if(e.isValid)return nn.before(o,e)}}return nn.invalid("unparsable",`the input "${e}" can't be parsed as ISO 8601`)}static isInterval(e){return e&&e.isLuxonInterval||!1}get start(){return this.isValid?this.s:null}get end(){return this.isValid?this.e:null}get isValid(){return null===this.invalidReason}get invalidReason(){return this.invalid?this.invalid.reason:null}get invalidExplanation(){return this.invalid?this.invalid.explanation:null}length(e="milliseconds"){return this.isValid?this.toDuration(e).get(e):NaN}count(e="milliseconds"){if(!this.isValid)return NaN;const t=this.start.startOf(e),n=this.end.startOf(e);return Math.floor(n.diff(t,e).get(e))+1}hasSame(e){return!!this.isValid&&(this.isEmpty()||this.e.minus(1).hasSame(this.s,e))}isEmpty(){return this.s.valueOf()===this.e.valueOf()}isAfter(e){return!!this.isValid&&this.s>e}isBefore(e){return!!this.isValid&&this.e<=e}contains(e){return!!this.isValid&&(this.s<=e&&this.e>e)}set({start:e,end:t}={}){return this.isValid?nn.fromDateTimes(e||this.s,t||this.e):this}splitAt(...e){if(!this.isValid)return[];const t=e.map(lr).filter((e=>this.contains(e))).sort(),n=[];let{s:r}=this,i=0;for(;r<this.e;){const e=t[i]||this.e,o=+e>+this.e?this.e:e;n.push(nn.fromDateTimes(r,o)),r=o,i+=1}return n}splitBy(e){const t=en.fromDurationLike(e);if(!this.isValid||!t.isValid||0===t.as("milliseconds"))return[];let n,{s:r}=this,i=1;const o=[];for(;r<this.e;){const e=this.start.plus(t.mapUnits((e=>e*i)));n=+e>+this.e?this.e:e,o.push(nn.fromDateTimes(r,n)),r=n,i+=1}return o}divideEqually(e){return this.isValid?this.splitBy(this.length()/e).slice(0,e):[]}overlaps(e){return this.e>e.s&&this.s<e.e}abutsStart(e){return!!this.isValid&&+this.e==+e.s}abutsEnd(e){return!!this.isValid&&+e.e==+this.s}engulfs(e){return!!this.isValid&&(this.s<=e.s&&this.e>=e.e)}equals(e){return!(!this.isValid||!e.isValid)&&(this.s.equals(e.s)&&this.e.equals(e.e))}intersection(e){if(!this.isValid)return this;const t=this.s>e.s?this.s:e.s,n=this.e<e.e?this.e:e.e;return t>=n?null:nn.fromDateTimes(t,n)}union(e){if(!this.isValid)return this;const t=this.s<e.s?this.s:e.s,n=this.e>e.e?this.e:e.e;return nn.fromDateTimes(t,n)}static merge(e){const[t,n]=e.sort(((e,t)=>e.s-t.s)).reduce((([e,t],n)=>t?t.overlaps(n)||t.abutsStart(n)?[e,t.union(n)]:[e.concat([t]),n]:[e,n]),[[],null]);return n&&t.push(n),t}static xor(e){let t=null,n=0;const r=[],i=e.map((e=>[{time:e.s,type:"s"},{time:e.e,type:"e"}])),o=Array.prototype.concat(...i).sort(((e,t)=>e.time-t.time));for(const a of o)n+="s"===a.type?1:-1,1===n?t=a.time:(t&&+t!=+a.time&&r.push(nn.fromDateTimes(t,a.time)),t=null);return nn.merge(r)}difference(...e){return nn.xor([this].concat(e)).map((e=>this.intersection(e))).filter((e=>e&&!e.isEmpty()))}toString(){return this.isValid?`[${this.s.toISO()} – ${this.e.toISO()})`:tn}toISO(e){return this.isValid?`${this.s.toISO(e)}/${this.e.toISO(e)}`:tn}toISODate(){return this.isValid?`${this.s.toISODate()}/${this.e.toISODate()}`:tn}toISOTime(e){return this.isValid?`${this.s.toISOTime(e)}/${this.e.toISOTime(e)}`:tn}toFormat(e,{separator:t=" – "}={}){return this.isValid?`${this.s.toFormat(e)}${t}${this.e.toFormat(e)}`:tn}toDuration(e,t){return this.isValid?this.e.diff(this.s,e,t):en.invalid(this.invalidReason)}mapEndpoints(e){return nn.fromDateTimes(e(this.s),e(this.e))}}class rn{static hasDST(e=Ge.defaultZone){const t=sr.now().setZone(e).set({month:12});return!e.isUniversal&&t.offset!==t.set({month:6}).offset}static isValidIANAZone(e){return Le.isValidZone(e)}static normalizeZone(e){return Ue(e,Ge.defaultZone)}static months(e="long",{locale:t=null,numberingSystem:n=null,locObj:r=null,outputCalendar:i="gregory"}={}){return(r||it.create(t,n,i)).months(e)}static monthsFormat(e="long",{locale:t=null,numberingSystem:n=null,locObj:r=null,outputCalendar:i="gregory"}={}){return(r||it.create(t,n,i)).months(e,!0)}static weekdays(e="long",{locale:t=null,numberingSystem:n=null,locObj:r=null}={}){return(r||it.create(t,n,null)).weekdays(e)}static weekdaysFormat(e="long",{locale:t=null,numberingSystem:n=null,locObj:r=null}={}){return(r||it.create(t,n,null)).weekdays(e,!0)}static meridiems({locale:e=null}={}){return it.create(e).meridiems()}static eras(e="short",{locale:t=null}={}){return it.create(t,null,"gregory").eras(e)}static features(){return{relative:j()}}}function on(e,t){const n=e=>e.toUTC(0,{keepLocalTime:!0}).startOf("day").valueOf(),r=n(t)-n(e);return Math.floor(en.fromMillis(r).as("days"))}function an(e,t,n,r){let[i,o,a,s]=function(e,t,n){const r=[["years",(e,t)=>t.year-e.year],["quarters",(e,t)=>t.quarter-e.quarter],["months",(e,t)=>t.month-e.month+12*(t.year-e.year)],["weeks",(e,t)=>{const n=on(e,t);return(n-n%7)/7}],["days",on]],i={};let o,a;for(const[s,l]of r)if(n.indexOf(s)>=0){o=s;let n=l(e,t);a=e.plus({[s]:n}),a>t?(e=e.plus({[s]:n-1}),n-=1):e=a,i[s]=n}return[e,i,a,o]}(e,t,n);const l=t-i,u=n.filter((e=>["hours","minutes","seconds","milliseconds"].indexOf(e)>=0));0===u.length&&(a<t&&(a=i.plus({[s]:1})),a!==i&&(o[s]=(o[s]||0)+l/(a-i)));const c=en.fromObject(o,r);return u.length>0?en.fromMillis(l,r).shiftTo(...u).plus(c):c}const sn={arab:"[٠-٩]",arabext:"[۰-۹]",bali:"[᭐-᭙]",beng:"[০-৯]",deva:"[०-९]",fullwide:"[０-９]",gujr:"[૦-૯]",hanidec:"[〇|一|二|三|四|五|六|七|八|九]",khmr:"[០-៩]",knda:"[೦-೯]",laoo:"[໐-໙]",limb:"[᥆-᥏]",mlym:"[൦-൯]",mong:"[᠐-᠙]",mymr:"[၀-၉]",orya:"[୦-୯]",tamldec:"[௦-௯]",telu:"[౦-౯]",thai:"[๐-๙]",tibt:"[༠-༩]",latn:"\\d"},ln={arab:[1632,1641],arabext:[1776,1785],bali:[6992,7001],beng:[2534,2543],deva:[2406,2415],fullwide:[65296,65303],gujr:[2790,2799],khmr:[6112,6121],knda:[3302,3311],laoo:[3792,3801],limb:[6470,6479],mlym:[3430,3439],mong:[6160,6169],mymr:[4160,4169],orya:[2918,2927],tamldec:[3046,3055],telu:[3174,3183],thai:[3664,3673],tibt:[3872,3881]},un=sn.hanidec.replace(/[\[|\]]/g,"").split("");const cn={};function fn({numberingSystem:e},t=""){const n=e||"latn";return cn[n]||(cn[n]={}),cn[n][t]||(cn[n][t]=new RegExp(`${sn[n]}${t}`)),cn[n][t]}function dn(e,t=(e=>e)){return{regex:e,deser:([e])=>t(function(e){let t=parseInt(e,10);if(isNaN(t)){t="";for(let n=0;n<e.length;n++){const r=e.charCodeAt(n);if(-1!==e[n].search(sn.hanidec))t+=un.indexOf(e[n]);else for(const e in ln){const[n,i]=ln[e];r>=n&&r<=i&&(t+=r-n)}}return parseInt(t,10)}return t}(e))}}const hn=`[ ${String.fromCharCode(160)}]`,pn=new RegExp(hn,"g");function mn(e){return e.replace(/\./g,"\\.?").replace(pn,hn)}function gn(e){return e.replace(/\./g,"").replace(pn," ").toLowerCase()}function vn(e,t){return null===e?null:{regex:RegExp(e.map(mn).join("|")),deser:([n])=>e.findIndex((e=>gn(n)===gn(e)))+t}}function yn(e,t){return{regex:e,deser:([,e,t])=>te(e,t),groups:t}}function bn(e){return{regex:e,deser:([e])=>e}}const _n={year:{"2-digit":"yy",numeric:"yyyyy"},month:{numeric:"M","2-digit":"MM",short:"MMM",long:"MMMM"},day:{numeric:"d","2-digit":"dd"},weekday:{short:"EEE",long:"EEEE"},dayperiod:"a",dayPeriod:"a",hour:{numeric:"h","2-digit":"hh"},minute:{numeric:"m","2-digit":"mm"},second:{numeric:"s","2-digit":"ss"},timeZoneName:{long:"ZZZZZ",short:"ZZZ"}};let wn=null;function Sn(e,t){return Array.prototype.concat(...e.map((e=>function(e,t){if(e.literal)return e;const n=Tn(Se.macroTokenToFormatOpts(e.val),t);return null==n||n.includes(void 0)?e:n}(e,t))))}class En{constructor(e,t){if(this.locale=e,this.format=t,this.tokens=Sn(Se.parseFormat(t),e),this.units=this.tokens.map((t=>function(e,t){const n=fn(t),r=fn(t,"{2}"),i=fn(t,"{3}"),o=fn(t,"{4}"),a=fn(t,"{6}"),s=fn(t,"{1,2}"),l=fn(t,"{1,3}"),u=fn(t,"{1,6}"),c=fn(t,"{1,9}"),f=fn(t,"{2,4}"),d=fn(t,"{4,6}"),h=e=>{return{regex:RegExp((t=e.val,t.replace(/[\-\[\]{}()*+?.,\\\^$|#\s]/g,"\\$&"))),deser:([e])=>e,literal:!0};var t},p=(p=>{if(e.literal)return h(p);switch(p.val){case"G":return vn(t.eras("short",!1),0);case"GG":return vn(t.eras("long",!1),0);case"y":return dn(u);case"yy":case"kk":return dn(f,X);case"yyyy":case"kkkk":return dn(o);case"yyyyy":return dn(d);case"yyyyyy":return dn(a);case"M":case"L":case"d":case"H":case"h":case"m":case"q":case"s":case"W":return dn(s);case"MM":case"LL":case"dd":case"HH":case"hh":case"mm":case"qq":case"ss":case"WW":return dn(r);case"MMM":return vn(t.months("short",!0,!1),1);case"MMMM":return vn(t.months("long",!0,!1),1);case"LLL":return vn(t.months("short",!1,!1),1);case"LLLL":return vn(t.months("long",!1,!1),1);case"o":case"S":return dn(l);case"ooo":case"SSS":return dn(i);case"u":return bn(c);case"uu":return bn(s);case"uuu":case"E":case"c":return dn(n);case"a":return vn(t.meridiems(),0);case"EEE":return vn(t.weekdays("short",!1,!1),1);case"EEEE":return vn(t.weekdays("long",!1,!1),1);case"ccc":return vn(t.weekdays("short",!0,!1),1);case"cccc":return vn(t.weekdays("long",!0,!1),1);case"Z":case"ZZ":return yn(new RegExp(`([+-]${s.source})(?::(${r.source}))?`),2);case"ZZZ":return yn(new RegExp(`([+-]${s.source})(${r.source})?`),2);case"z":return bn(/[a-z_+-/]{1,256}?/i);default:return h(p)}})(e)||{invalidReason:"missing Intl.DateTimeFormat.formatToParts support"};return p.token=e,p}(t,e))),this.disqualifyingUnit=this.units.find((e=>e.invalidReason)),!this.disqualifyingUnit){const[e,t]=[`^${(n=this.units).map((e=>e.regex)).reduce(((e,t)=>`${e}(${t.source})`),"")}$`,n];this.regex=RegExp(e,"i"),this.handlers=t}var n}explainFromTokens(e){if(this.isValid){const[t,n]=function(e,t,n){const r=e.match(t);if(r){const e={};let t=1;for(const i in n)if(z(n,i)){const o=n[i],a=o.groups?o.groups+1:1;!o.literal&&o.token&&(e[o.token.val[0]]=o.deser(r.slice(t,t+a))),t+=a}return[r,e]}return[r,{}]}(e,this.regex,this.handlers),[r,i,o]=n?function(e){let t,n=null;return R(e.z)||(n=Le.create(e.z)),R(e.Z)||(n||(n=new Re(e.Z)),t=e.Z),R(e.q)||(e.M=3*(e.q-1)+1),R(e.h)||(e.h<12&&1===e.a?e.h+=12:12===e.h&&0===e.a&&(e.h=0)),0===e.G&&e.y&&(e.y=-e.y),R(e.u)||(e.S=q(e.u)),[Object.keys(e).reduce(((t,n)=>{const r=(e=>{switch(e){case"S":return"millisecond";case"s":return"second";case"m":return"minute";case"h":case"H":return"hour";case"d":return"day";case"o":return"ordinal";case"L":case"M":return"month";case"y":return"year";case"E":case"c":return"weekday";case"W":return"weekNumber";case"k":return"weekYear";case"q":return"quarter";default:return null}})(n);return r&&(t[r]=e[n]),t}),{}),n,t]}(n):[null,null,void 0];if(z(n,"a")&&z(n,"H"))throw new s("Can't include meridiem when specifying 24-hour format");return{input:e,tokens:this.tokens,regex:this.regex,rawMatches:t,matches:n,result:r,zone:i,specificOffset:o}}return{input:e,tokens:this.tokens,invalidReason:this.invalidReason}}get isValid(){return!this.disqualifyingUnit}get invalidReason(){return this.disqualifyingUnit?this.disqualifyingUnit.invalidReason:null}}function kn(e,t,n){return new En(e,n).explainFromTokens(t)}function Tn(e,t){if(!e)return null;return Se.create(t,e).formatDateTimeParts((wn||(wn=sr.fromMillis(1555555555555)),wn)).map((t=>function(e,t,n){const{type:r,value:i}=e;if("literal"===r)return{literal:!0,val:i};const o=n[r];let a=_n[r];return"object"==typeof a&&(a=a[o]),a?{literal:!1,val:a}:void 0}(t,0,e)))}const xn=[0,31,59,90,120,151,181,212,243,273,304,334],On=[0,31,60,91,121,152,182,213,244,274,305,335];function Cn(e,t){return new Ee("unit out of range",`you specified ${t} (of type ${typeof t}) as a ${e}, which is invalid`)}function Nn(e,t,n){const r=new Date(Date.UTC(e,t-1,n));e<100&&e>=0&&r.setUTCFullYear(r.getUTCFullYear()-1900);const i=r.getUTCDay();return 0===i?7:i}function In(e,t,n){return n+(K(e)?On:xn)[t-1]}function Dn(e,t){const n=K(e)?On:xn,r=n.findIndex((e=>e<t));return{month:r+1,day:t-n[r]}}function An(e){const{year:t,month:n,day:r}=e,i=In(t,n,r),o=Nn(t,n,r);let a,s=Math.floor((i-o+10)/7);return s<1?(a=t-1,s=Q(a)):s>Q(t)?(a=t+1,s=1):a=t,{weekYear:a,weekNumber:s,weekday:o,...oe(e)}}function Pn(e){const{weekYear:t,weekNumber:n,weekday:r}=e,i=Nn(t,1,4),o=Z(t);let a,s=7*n+r-i-3;s<1?(a=t-1,s+=Z(a)):s>o?(a=t+1,s-=Z(t)):a=t;const{month:l,day:u}=Dn(a,s);return{year:a,month:l,day:u,...oe(e)}}function Ln(e){const{year:t,month:n,day:r}=e;return{year:t,ordinal:In(t,n,r),...oe(e)}}function Mn(e){const{year:t,ordinal:n}=e,{month:r,day:i}=Dn(t,n);return{year:t,month:r,day:i,...oe(e)}}function Rn(e){const t=U(e.year),n=H(e.month,1,12),r=H(e.day,1,J(e.year,e.month));return t?n?!r&&Cn("day",e.day):Cn("month",e.month):Cn("year",e.year)}function Fn(e){const{hour:t,minute:n,second:r,millisecond:i}=e,o=H(t,0,23)||24===t&&0===n&&0===r&&0===i,a=H(n,0,59),s=H(r,0,59),l=H(i,0,999);return o?a?s?!l&&Cn("millisecond",i):Cn("second",r):Cn("minute",n):Cn("hour",t)}const Un="Invalid DateTime",jn=864e13;function Bn(e){return new Ee("unsupported zone",`the zone "${e.name}" is not supported`)}function zn(e){return null===e.weekData&&(e.weekData=An(e.c)),e.weekData}function Hn(e,t){const n={ts:e.ts,zone:e.zone,c:e.c,o:e.o,loc:e.loc,invalid:e.invalid};return new sr({...n,...t,old:n})}function Vn(e,t,n){let r=e-60*t*1e3;const i=n.offset(r);if(t===i)return[r,t];r-=60*(i-t)*1e3;const o=n.offset(r);return i===o?[r,i]:[e-60*Math.min(i,o)*1e3,Math.max(i,o)]}function $n(e,t){const n=new Date(e+=60*t*1e3);return{year:n.getUTCFullYear(),month:n.getUTCMonth()+1,day:n.getUTCDate(),hour:n.getUTCHours(),minute:n.getUTCMinutes(),second:n.getUTCSeconds(),millisecond:n.getUTCMilliseconds()}}function Gn(e,t,n){return Vn(Y(e),t,n)}function qn(e,t){const n=e.o,r=e.c.year+Math.trunc(t.years),i=e.c.month+Math.trunc(t.months)+3*Math.trunc(t.quarters),o={...e.c,year:r,month:i,day:Math.min(e.c.day,J(r,i))+Math.trunc(t.days)+7*Math.trunc(t.weeks)},a=en.fromObject({years:t.years-Math.trunc(t.years),quarters:t.quarters-Math.trunc(t.quarters),months:t.months-Math.trunc(t.months),weeks:t.weeks-Math.trunc(t.weeks),days:t.days-Math.trunc(t.days),hours:t.hours,minutes:t.minutes,seconds:t.seconds,milliseconds:t.milliseconds}).as("milliseconds"),s=Y(o);let[l,u]=Vn(s,n,e.zone);return 0!==a&&(l+=a,u=e.zone.offset(l)),{ts:l,o:u}}function Wn(e,t,n,r,i,o){const{setZone:a,zone:s}=n;if(e&&0!==Object.keys(e).length){const r=t||s,i=sr.fromObject(e,{...n,zone:r,specificOffset:o});return a?i:i.setZone(s)}return sr.invalid(new Ee("unparsable",`the input "${i}" can't be parsed as ${r}`))}function Kn(e,t,n=!0){return e.isValid?Se.create(it.create("en-US"),{allowZ:n,forceSimple:!0}).formatDateTimeFromString(e,t):null}function Zn(e,t){const n=e.c.year>9999||e.c.year<0;let r="";return n&&e.c.year>=0&&(r+="+"),r+=V(e.c.year,n?6:4),t?(r+="-",r+=V(e.c.month),r+="-",r+=V(e.c.day)):(r+=V(e.c.month),r+=V(e.c.day)),r}function Jn(e,t,n,r,i,o){let a=V(e.c.hour);return t?(a+=":",a+=V(e.c.minute),0===e.c.second&&n||(a+=":")):a+=V(e.c.minute),0===e.c.second&&n||(a+=V(e.c.second),0===e.c.millisecond&&r||(a+=".",a+=V(e.c.millisecond,3))),i&&(e.isOffsetFixed&&0===e.offset&&!o?a+="Z":e.o<0?(a+="-",a+=V(Math.trunc(-e.o/60)),a+=":",a+=V(Math.trunc(-e.o%60))):(a+="+",a+=V(Math.trunc(e.o/60)),a+=":",a+=V(Math.trunc(e.o%60)))),o&&(a+="["+e.zone.ianaName+"]"),a}const Yn={month:1,day:1,hour:0,minute:0,second:0,millisecond:0},Qn={weekNumber:1,weekday:1,hour:0,minute:0,second:0,millisecond:0},Xn={ordinal:1,hour:0,minute:0,second:0,millisecond:0},er=["year","month","day","hour","minute","second","millisecond"],tr=["weekYear","weekNumber","weekday","hour","minute","second","millisecond"],nr=["year","ordinal","hour","minute","second","millisecond"];function rr(e){const t={year:"year",years:"year",month:"month",months:"month",day:"day",days:"day",hour:"hour",hours:"hour",minute:"minute",minutes:"minute",quarter:"quarter",quarters:"quarter",second:"second",seconds:"second",millisecond:"millisecond",milliseconds:"millisecond",weekday:"weekday",weekdays:"weekday",weeknumber:"weekNumber",weeksnumber:"weekNumber",weeknumbers:"weekNumber",weekyear:"weekYear",weekyears:"weekYear",ordinal:"ordinal"}[e.toLowerCase()];if(!t)throw new l(e);return t}function ir(e,t){const n=Ue(t.zone,Ge.defaultZone);if(!n.isValid)return sr.invalid(Bn(n));const r=it.fromObject(t);let i,o;if(R(e.year))i=Ge.now();else{for(const n of er)R(e[n])&&(e[n]=Yn[n]);const t=Rn(e)||Fn(e);if(t)return sr.invalid(t);const r=function(e){return sr._zoneOffsetGuessCache[e]||(void 0===sr._zoneOffsetTs&&(sr._zoneOffsetTs=Ge.now()),sr._zoneOffsetGuessCache[e]=e.offset(sr._zoneOffsetTs)),sr._zoneOffsetGuessCache[e]}(n);[i,o]=Gn(e,r,n)}return new sr({ts:i,zone:n,loc:r,o})}function or(e,t,n){const r=!!R(n.round)||n.round,i=(e,i)=>{e=W(e,r||n.calendary?0:2,!0);return t.loc.clone(n).relFormatter(n).format(e,i)},o=r=>n.calendary?t.hasSame(e,r)?0:t.startOf(r).diff(e.startOf(r),r).get(r):t.diff(e,r).get(r);if(n.unit)return i(o(n.unit),n.unit);for(const a of n.units){const e=o(a);if(Math.abs(e)>=1)return i(e,a)}return i(e>t?-0:0,n.units[n.units.length-1])}function ar(e){let t,n={};return e.length>0&&"object"==typeof e[e.length-1]?(n=e[e.length-1],t=Array.from(e).slice(0,e.length-1)):t=Array.from(e),[n,t]}class sr{constructor(e){const t=e.zone||Ge.defaultZone;let n=e.invalid||(Number.isNaN(e.ts)?new Ee("invalid input"):null)||(t.isValid?null:Bn(t));this.ts=R(e.ts)?Ge.now():e.ts;let r=null,i=null;if(!n){if(e.old&&e.old.ts===this.ts&&e.old.zone.equals(t))[r,i]=[e.old.c,e.old.o];else{const o=F(e.o)&&!e.old?e.o:t.offset(this.ts);r=$n(this.ts,o),n=Number.isNaN(r.year)?new Ee("invalid input"):null,r=n?null:r,i=n?null:o}}this._zone=t,this.loc=e.loc||it.create(),this.invalid=n,this.weekData=null,this.c=r,this.o=i,this.isLuxonDateTime=!0}static _zoneOffsetTs;static _zoneOffsetGuessCache={};static now(){return new sr({})}static local(){const[e,t]=ar(arguments),[n,r,i,o,a,s,l]=t;return ir({year:n,month:r,day:i,hour:o,minute:a,second:s,millisecond:l},e)}static utc(){const[e,t]=ar(arguments),[n,r,i,o,a,s,l]=t;return e.zone=Re.utcInstance,ir({year:n,month:r,day:i,hour:o,minute:a,second:s,millisecond:l},e)}static fromJSDate(e,t={}){const n=(r=e,"[object Date]"===Object.prototype.toString.call(r)?e.valueOf():NaN);var r;if(Number.isNaN(n))return sr.invalid("invalid input");const i=Ue(t.zone,Ge.defaultZone);return i.isValid?new sr({ts:n,zone:i,loc:it.fromObject(t)}):sr.invalid(Bn(i))}static fromMillis(e,t={}){if(F(e))return e<-jn||e>jn?sr.invalid("Timestamp out of range"):new sr({ts:e,zone:Ue(t.zone,Ge.defaultZone),loc:it.fromObject(t)});throw new u(`fromMillis requires a numerical input, but received a ${typeof e} with value ${e}`)}static fromSeconds(e,t={}){if(F(e))return new sr({ts:1e3*e,zone:Ue(t.zone,Ge.defaultZone),loc:it.fromObject(t)});throw new u("fromSeconds requires a numerical input")}static fromObject(e,t={}){e=e||{};const n=Ue(t.zone,Ge.defaultZone);if(!n.isValid)return sr.invalid(Bn(n));const r=Ge.now(),i=R(t.specificOffset)?n.offset(r):t.specificOffset,o=re(e,rr),a=!R(o.ordinal),l=!R(o.year),u=!R(o.month)||!R(o.day),c=l||u,f=o.weekYear||o.weekNumber,d=it.fromObject(t);if((c||a)&&f)throw new s("Can't mix weekYear/weekNumber units with year/month/day or ordinals");if(u&&a)throw new s("Can't mix ordinal dates with month/day");const h=f||o.weekday&&!c;let p,m,g=$n(r,i);h?(p=tr,m=Qn,g=An(g)):a?(p=nr,m=Xn,g=Ln(g)):(p=er,m=Yn);let v=!1;for(const s of p){R(o[s])?o[s]=v?m[s]:g[s]:v=!0}const y=h?function(e){const t=U(e.weekYear),n=H(e.weekNumber,1,Q(e.weekYear)),r=H(e.weekday,1,7);return t?n?!r&&Cn("weekday",e.weekday):Cn("week",e.week):Cn("weekYear",e.weekYear)}(o):a?function(e){const t=U(e.year),n=H(e.ordinal,1,Z(e.year));return t?!n&&Cn("ordinal",e.ordinal):Cn("year",e.year)}(o):Rn(o),b=y||Fn(o);if(b)return sr.invalid(b);const _=h?Pn(o):a?Mn(o):o,[w,S]=Gn(_,i,n),E=new sr({ts:w,zone:n,o:S,loc:d});return o.weekday&&c&&e.weekday!==E.weekday?sr.invalid("mismatched weekday",`you can't specify both a weekday of ${o.weekday} and a date of ${E.toISO()}`):E}static fromISO(e,t={}){const[n,r]=function(e){return st(e,[Pt,Ft],[Lt,Ut],[Mt,jt],[Rt,Bt])}(e);return Wn(n,r,t,"ISO 8601",e)}static fromRFC2822(e,t={}){const[n,r]=function(e){return st(function(e){return e.replace(/\([^)]*\)|[\n\t]/g," ").replace(/(\s\s+)/g," ").trim()}(e),[xt,Ot])}(e);return Wn(n,r,t,"RFC 2822",e)}static fromHTTP(e,t={}){const[n,r]=function(e){return st(e,[Ct,Dt],[Nt,Dt],[It,At])}(e);return Wn(n,r,t,"HTTP",t)}static fromFormat(e,t,n={}){if(R(e)||R(t))throw new u("fromFormat requires an input string and a format");const{locale:r=null,numberingSystem:i=null}=n,o=it.fromOpts({locale:r,numberingSystem:i,defaultToEN:!0}),[a,s,l,c]=function(e,t,n){const{result:r,zone:i,specificOffset:o,invalidReason:a}=kn(e,t,n);return[r,i,o,a]}(o,e,t);return c?sr.invalid(c):Wn(a,s,n,`format ${t}`,e,l)}static fromString(e,t,n={}){return sr.fromFormat(e,t,n)}static fromSQL(e,t={}){const[n,r]=function(e){return st(e,[Ht,Ft],[Vt,$t])}(e);return Wn(n,r,t,"SQL",e)}static invalid(e,t=null){if(!e)throw new u("need to specify a reason the DateTime is invalid");const n=e instanceof Ee?e:new Ee(e,t);if(Ge.throwOnInvalid)throw new i(n);return new sr({invalid:n})}static isDateTime(e){return e&&e.isLuxonDateTime||!1}static parseFormatForOpts(e,t={}){const n=Tn(e,it.fromObject(t));return n?n.map((e=>e?e.val:null)).join(""):null}get(e){return this[e]}get isValid(){return null===this.invalid}get invalidReason(){return this.invalid?this.invalid.reason:null}get invalidExplanation(){return this.invalid?this.invalid.explanation:null}get locale(){return this.isValid?this.loc.locale:null}get numberingSystem(){return this.isValid?this.loc.numberingSystem:null}get outputCalendar(){return this.isValid?this.loc.outputCalendar:null}get zone(){return this._zone}get zoneName(){return this.isValid?this.zone.name:null}get year(){return this.isValid?this.c.year:NaN}get quarter(){return this.isValid?Math.ceil(this.c.month/3):NaN}get month(){return this.isValid?this.c.month:NaN}get day(){return this.isValid?this.c.day:NaN}get hour(){return this.isValid?this.c.hour:NaN}get minute(){return this.isValid?this.c.minute:NaN}get second(){return this.isValid?this.c.second:NaN}get millisecond(){return this.isValid?this.c.millisecond:NaN}get weekYear(){return this.isValid?zn(this).weekYear:NaN}get weekNumber(){return this.isValid?zn(this).weekNumber:NaN}get weekday(){return this.isValid?zn(this).weekday:NaN}get ordinal(){return this.isValid?Ln(this.c).ordinal:NaN}get monthShort(){return this.isValid?rn.months("short",{locObj:this.loc})[this.month-1]:null}get monthLong(){return this.isValid?rn.months("long",{locObj:this.loc})[this.month-1]:null}get weekdayShort(){return this.isValid?rn.weekdays("short",{locObj:this.loc})[this.weekday-1]:null}get weekdayLong(){return this.isValid?rn.weekdays("long",{locObj:this.loc})[this.weekday-1]:null}get offset(){return this.isValid?+this.o:NaN}get offsetNameShort(){return this.isValid?this.zone.offsetName(this.ts,{format:"short",locale:this.locale}):null}get offsetNameLong(){return this.isValid?this.zone.offsetName(this.ts,{format:"long",locale:this.locale}):null}get isOffsetFixed(){return this.isValid?this.zone.isUniversal:null}get isInDST(){return!this.isOffsetFixed&&(this.offset>this.set({month:1,day:1}).offset||this.offset>this.set({month:5}).offset)}get isInLeapYear(){return K(this.year)}get daysInMonth(){return J(this.year,this.month)}get daysInYear(){return this.isValid?Z(this.year):NaN}get weeksInWeekYear(){return this.isValid?Q(this.weekYear):NaN}resolvedLocaleOptions(e={}){const{locale:t,numberingSystem:n,calendar:r}=Se.create(this.loc.clone(e),e).resolvedOptions(this);return{locale:t,numberingSystem:n,outputCalendar:r}}toUTC(e=0,t={}){return this.setZone(Re.instance(e),t)}toLocal(){return this.setZone(Ge.defaultZone)}setZone(e,{keepLocalTime:t=!1,keepCalendarTime:n=!1}={}){if((e=Ue(e,Ge.defaultZone)).equals(this.zone))return this;if(e.isValid){let r=this.ts;if(t||n){const t=e.offset(this.ts),n=this.toObject();[r]=Gn(n,t,e)}return Hn(this,{ts:r,zone:e})}return sr.invalid(Bn(e))}reconfigure({locale:e,numberingSystem:t,outputCalendar:n}={}){return Hn(this,{loc:this.loc.clone({locale:e,numberingSystem:t,outputCalendar:n})})}setLocale(e){return this.reconfigure({locale:e})}set(e){if(!this.isValid)return this;const t=re(e,rr),n=!R(t.weekYear)||!R(t.weekNumber)||!R(t.weekday),r=!R(t.ordinal),i=!R(t.year),o=!R(t.month)||!R(t.day),a=i||o,l=t.weekYear||t.weekNumber;if((a||r)&&l)throw new s("Can't mix weekYear/weekNumber units with year/month/day or ordinals");if(o&&r)throw new s("Can't mix ordinal dates with month/day");let u;n?u=Pn({...An(this.c),...t}):R(t.ordinal)?(u={...this.toObject(),...t},R(t.day)&&(u.day=Math.min(J(u.year,u.month),u.day))):u=Mn({...Ln(this.c),...t});const[c,f]=Gn(u,this.o,this.zone);return Hn(this,{ts:c,o:f})}plus(e){if(!this.isValid)return this;return Hn(this,qn(this,en.fromDurationLike(e)))}minus(e){if(!this.isValid)return this;return Hn(this,qn(this,en.fromDurationLike(e).negate()))}startOf(e){if(!this.isValid)return this;const t={},n=en.normalizeUnit(e);switch(n){case"years":t.month=1;case"quarters":case"months":t.day=1;case"weeks":case"days":t.hour=0;case"hours":t.minute=0;case"minutes":t.second=0;case"seconds":t.millisecond=0}if("weeks"===n&&(t.weekday=1),"quarters"===n){const e=Math.ceil(this.month/3);t.month=3*(e-1)+1}return this.set(t)}endOf(e){return this.isValid?this.plus({[e]:1}).startOf(e).minus(1):this}toFormat(e,t={}){return this.isValid?Se.create(this.loc.redefaultToEN(t)).formatDateTimeFromString(this,e):Un}toLocaleString(e=p,t={}){return this.isValid?Se.create(this.loc.clone(t),e).formatDateTime(this):Un}toLocaleParts(e={}){return this.isValid?Se.create(this.loc.clone(e),e).formatDateTimeParts(this):[]}toISO({format:e="extended",suppressSeconds:t=!1,suppressMilliseconds:n=!1,includeOffset:r=!0,extendedZone:i=!1}={}){if(!this.isValid)return null;const o="extended"===e;let a=Zn(this,o);return a+="T",a+=Jn(this,o,t,n,r,i),a}toISODate({format:e="extended"}={}){return this.isValid?Zn(this,"extended"===e):null}toISOWeekDate(){return Kn(this,"kkkk-'W'WW-c")}toISOTime({suppressMilliseconds:e=!1,suppressSeconds:t=!1,includeOffset:n=!0,includePrefix:r=!1,extendedZone:i=!1,format:o="extended"}={}){if(!this.isValid)return null;return(r?"T":"")+Jn(this,"extended"===o,t,e,n,i)}toRFC2822(){return Kn(this,"EEE, dd LLL yyyy HH:mm:ss ZZZ",!1)}toHTTP(){return Kn(this.toUTC(),"EEE, dd LLL yyyy HH:mm:ss 'GMT'")}toSQLDate(){return this.isValid?Zn(this,!0):null}toSQLTime({includeOffset:e=!0,includeZone:t=!1,includeOffsetSpace:n=!0}={}){let r="HH:mm:ss.SSS";return(t||e)&&(n&&(r+=" "),t?r+="z":e&&(r+="ZZ")),Kn(this,r,!0)}toSQL(e={}){return this.isValid?`${this.toSQLDate()} ${this.toSQLTime(e)}`:null}toString(){return this.isValid?this.toISO():Un}valueOf(){return this.toMillis()}toMillis(){return this.isValid?this.ts:NaN}toSeconds(){return this.isValid?this.ts/1e3:NaN}toUnixInteger(){return this.isValid?Math.floor(this.ts/1e3):NaN}toJSON(){return this.toISO()}toBSON(){return this.toJSDate()}toObject(e={}){if(!this.isValid)return{};const t={...this.c};return e.includeConfig&&(t.outputCalendar=this.outputCalendar,t.numberingSystem=this.loc.numberingSystem,t.locale=this.loc.locale),t}toJSDate(){return new Date(this.isValid?this.ts:NaN)}diff(e,t="milliseconds",n={}){if(!this.isValid||!e.isValid)return en.invalid("created by diffing an invalid DateTime");const r={locale:this.locale,numberingSystem:this.numberingSystem,...n},i=(s=t,Array.isArray(s)?s:[s]).map(en.normalizeUnit),o=e.valueOf()>this.valueOf(),a=an(o?this:e,o?e:this,i,r);var s;return o?a.negate():a}diffNow(e="milliseconds",t={}){return this.diff(sr.now(),e,t)}until(e){return this.isValid?nn.fromDateTimes(this,e):this}hasSame(e,t){if(!this.isValid)return!1;const n=e.valueOf(),r=this.setZone(e.zone,{keepLocalTime:!0});return r.startOf(t)<=n&&n<=r.endOf(t)}equals(e){return this.isValid&&e.isValid&&this.valueOf()===e.valueOf()&&this.zone.equals(e.zone)&&this.loc.equals(e.loc)}toRelative(e={}){if(!this.isValid)return null;const t=e.base||sr.fromObject({},{zone:this.zone}),n=e.padding?this<t?-e.padding:e.padding:0;let r=["years","months","days","hours","minutes","seconds"],i=e.unit;return Array.isArray(e.unit)&&(r=e.unit,i=void 0),or(t,this.plus(n),{...e,numeric:"always",units:r,unit:i})}toRelativeCalendar(e={}){return this.isValid?or(e.base||sr.fromObject({},{zone:this.zone}),this,{...e,numeric:"auto",units:["years","months","days"],calendary:!0}):null}static min(...e){if(!e.every(sr.isDateTime))throw new u("min requires all arguments be DateTimes");return B(e,(e=>e.valueOf()),Math.min)}static max(...e){if(!e.every(sr.isDateTime))throw new u("max requires all arguments be DateTimes");return B(e,(e=>e.valueOf()),Math.max)}static fromFormatExplain(e,t,n={}){const{locale:r=null,numberingSystem:i=null}=n;return kn(it.fromOpts({locale:r,numberingSystem:i,defaultToEN:!0}),e,t)}static fromStringExplain(e,t,n={}){return sr.fromFormatExplain(e,t,n)}static buildFormatParser(e,t={}){const{locale:n=null,numberingSystem:r=null}=t,i=it.fromOpts({locale:n,numberingSystem:r,defaultToEN:!0});return new En(i,e)}static fromFormatParser(e,t,n={}){if(R(e)||R(t))throw new u("fromFormatParser requires an input string and a format parser");const{locale:r=null,numberingSystem:i=null}=n,o=it.fromOpts({locale:r,numberingSystem:i,defaultToEN:!0});if(!o.equals(t.locale))throw new u(`fromFormatParser called with a locale of ${o}, but the format parser was created for ${t.locale}`);const{result:a,zone:s,specificOffset:l,invalidReason:c}=t.explainFromTokens(e);return c?sr.invalid(c):Wn(a,s,n,`format ${t.format}`,e,l)}static get DATE_SHORT(){return p}static get DATE_MED(){return m}static get DATE_MED_WITH_WEEKDAY(){return g}static get DATE_FULL(){return v}static get DATE_HUGE(){return y}static get TIME_SIMPLE(){return b}static get TIME_WITH_SECONDS(){return _}static get TIME_WITH_SHORT_OFFSET(){return w}static get TIME_WITH_LONG_OFFSET(){return S}static get TIME_24_SIMPLE(){return E}static get TIME_24_WITH_SECONDS(){return k}static get TIME_24_WITH_SHORT_OFFSET(){return T}static get TIME_24_WITH_LONG_OFFSET(){return x}static get DATETIME_SHORT(){return O}static get DATETIME_SHORT_WITH_SECONDS(){return C}static get DATETIME_MED(){return N}static get DATETIME_MED_WITH_SECONDS(){return I}static get DATETIME_MED_WITH_WEEKDAY(){return D}static get DATETIME_FULL(){return A}static get DATETIME_FULL_WITH_SECONDS(){return P}static get DATETIME_HUGE(){return L}static get DATETIME_HUGE_WITH_SECONDS(){return M}}function lr(e){if(sr.isDateTime(e))return e;if(e&&e.valueOf&&F(e.valueOf()))return sr.fromJSDate(e);if(e&&"object"==typeof e)return sr.fromObject(e);throw new u(`Unknown datetime argument: ${e}, of type ${typeof e}`)}},244703:function(e,t,n){"use strict";var r=this&&this.__awaiter||function(e,t,n,r){return new(n||(n=Promise))((function(i,o){function a(e){try{l(r.next(e))}catch(t){o(t)}}function s(e){try{l(r.throw(e))}catch(t){o(t)}}function l(e){var t;e.done?i(e.value):(t=e.value,t instanceof n?t:new n((function(e){e(t)}))).then(a,s)}l((r=r.apply(e,t||[])).next())}))};Object.defineProperty(t,"__esModule",{value:!0}),t.NetworkCore=void 0,n(843509);const i=()=>n(843509),o=()=>n(230882),a=()=>n(668024),s=()=>n(12610),l=()=>n(810686),u=()=>n(339839),c=()=>n(146512),f=()=>n(647754),d=new Set([408,500,502,503,504,522,524,599]);t.NetworkCore=class{constructor(e,t){this._emitter=t,this._errorBoundary=null,this._timeout=1e4,this._netConfig={},this._options={},this._leakyBucket={},this._lastUsedInitUrl=null,e&&(this._options=e),this._options.networkConfig&&(this._netConfig=this._options.networkConfig),this._netConfig.networkTimeoutMs&&(this._timeout=this._netConfig.networkTimeoutMs),this._fallbackResolver=new(n(250052).NetworkFallbackResolver)(this._options),this.setLogEventCompressionMode(this._getLogEventCompressionMode(e))}setLogEventCompressionMode(e){this._options.logEventCompressionMode=e}setErrorBoundary(e){this._errorBoundary=e,this._errorBoundary.wrap(this),this._errorBoundary.wrap(this._fallbackResolver),this._fallbackResolver.setErrorBoundary(e)}isBeaconSupported(){return"undefined"!=typeof navigator&&"function"==typeof navigator.sendBeacon}getLastUsedInitUrlAndReset(){const e=this._lastUsedInitUrl;return this._lastUsedInitUrl=null,e}beacon(e){return r(this,void 0,void 0,(function*(){if(!h(e))return!1;const t=this._getInternalRequestArgs("POST",e);yield this._tryToCompressBody(t);const n=yield this._getPopulatedURL(t),r=navigator;return r.sendBeacon.bind(r)(n,t.body)}))}post(e){return r(this,void 0,void 0,(function*(){const t=this._getInternalRequestArgs("POST",e);return this._tryEncodeBody(t),yield this._tryToCompressBody(t),this._sendRequest(t)}))}get(e){const t=this._getInternalRequestArgs("GET",e);return this._sendRequest(t)}_sendRequest(e){var t,i,l,u;return r(this,void 0,void 0,(function*(){if(!h(e))return null;if(this._netConfig.preventAllNetworkTraffic)return null;const{method:c,body:f,retries:p,attempt:g}=e,v=e.urlConfig.endpoint;if(this._isRateLimited(v))return a().Log.warn(`Request to ${v} was blocked because you are making requests too frequently.`),null;const y=null!=g?g:1,b="undefined"!=typeof AbortController?new AbortController:null,_=setTimeout((()=>{null==b||b.abort(`Timeout of ${this._timeout}ms expired.`)}),this._timeout),w=yield this._getPopulatedURL(e);let S=null;const E=(0,n(592701)._isUnloading)();try{const n={method:c,body:f,headers:Object.assign({},e.headers),signal:null==b?void 0:b.signal,priority:e.priority,keepalive:E};!function(e,t){if(e.urlConfig.endpoint!==s().Endpoint._initialize)return;o().Diagnostics._markInitNetworkReqStart(e.sdkKey,{attempt:t})}(e,y);const r=this._leakyBucket[v];r&&(r.lastRequestTime=Date.now(),this._leakyBucket[v]=r);const i=null!==(t=this._netConfig.networkOverrideFunc)&&void 0!==t?t:fetch;if(S=yield i(w,n),clearTimeout(_),!S.ok){const e=yield S.text().catch((()=>"No Text")),t=new Error(`NetworkError: ${w} ${e}`);throw t.name="NetworkError",t}const a=yield S.text();return m(e,S,y,a),this._fallbackResolver.tryBumpExpiryTime(e.sdkKey,e.urlConfig),{body:a,code:S.status}}catch(T){const t=function(e,t){if((null==e?void 0:e.signal.aborted)&&"string"==typeof e.signal.reason)return e.signal.reason;if("string"==typeof t)return t;if(t instanceof Error)return`${t.name}: ${t.message}`;return"Unknown Error"}(b,T),o=(null==(k=b)?void 0:k.signal.aborted)&&"string"==typeof k.signal.reason&&k.signal.reason.includes("Timeout")||!1;m(e,S,y,"",T);if((yield this._fallbackResolver.tryFetchUpdatedFallbackInfo(e.sdkKey,e.urlConfig,t,o))&&(e.fallbackUrl=this._fallbackResolver.getActiveFallbackUrl(e.sdkKey,e.urlConfig)),!p||y>p||!d.has(null!==(i=null==S?void 0:S.status)&&void 0!==i?i:500)){null===(l=this._emitter)||void 0===l||l.call(this,{name:"error",error:T,tag:n(971180).ErrorTag.NetworkError,requestArgs:e});const r=`A networking error occurred during ${c} request to ${w}.`;return a().Log.error(r,t,T),null===(u=this._errorBoundary)||void 0===u||u.attachErrorIfNoneExists(r),null}return yield function(e){return r(this,void 0,void 0,(function*(){yield new Promise((t=>setTimeout(t,Math.min(e*e*500,3e4))))}))}(y),this._sendRequest(Object.assign(Object.assign({},e),{retries:p,attempt:y+1}))}var k}))}_getLogEventCompressionMode(e){let t=null==e?void 0:e.logEventCompressionMode;return t||!0!==(null==e?void 0:e.disableCompression)||(t=f().LogEventCompressionMode.Disabled),t||(t=f().LogEventCompressionMode.Enabled),t}_isRateLimited(e){var t;const n=Date.now(),r=null!==(t=this._leakyBucket[e])&&void 0!==t?t:{count:0,lastRequestTime:n},i=n-r.lastRequestTime,o=Math.floor(.05*i);return r.count=Math.max(0,r.count-o),r.count>=50||(r.count+=1,r.lastRequestTime=n,this._leakyBucket[e]=r,!1)}_getPopulatedURL(e){var t;return r(this,void 0,void 0,(function*(){const n=null!==(t=e.fallbackUrl)&&void 0!==t?t:e.urlConfig.getUrl();e.urlConfig.endpoint!==s().Endpoint._initialize&&e.urlConfig.endpoint!==s().Endpoint._download_config_specs||(this._lastUsedInitUrl=n);const r=Object.assign({[s().NetworkParam.SdkKey]:e.sdkKey,[s().NetworkParam.SdkType]:l().SDKType._get(e.sdkKey),[s().NetworkParam.SdkVersion]:c().SDK_VERSION,[s().NetworkParam.Time]:String(Date.now()),[s().NetworkParam.SessionID]:u().SessionID.get(e.sdkKey)},e.params),i=Object.keys(r).map((e=>`${encodeURIComponent(e)}=${encodeURIComponent(r[e])}`)).join("&");return`${n}${i?`?${i}`:""}`}))}_tryEncodeBody(e){var t;const r=(0,n(600414)._getWindowSafe)(),o=e.body;if(e.isStatsigEncodable&&!this._options.disableStatsigEncoding&&"string"==typeof o&&null==(0,i()._getStatsigGlobalFlag)("no-encode")&&(null==r?void 0:r.btoa))try{e.body=r.btoa(o).split("").reverse().join(""),e.params=Object.assign(Object.assign({},null!==(t=e.params)&&void 0!==t?t:{}),{[s().NetworkParam.StatsigEncoded]:"1"})}catch(l){a().Log.warn(`Request encoding failed for ${e.urlConfig.getUrl()}`,l)}}_tryToCompressBody(e){var t;return r(this,void 0,void 0,(function*(){const r=e.body;if("string"==typeof r&&function(e,t){if(!e.isCompressable)return!1;if(null!=(0,i()._getStatsigGlobalFlag)("no-compress")||"undefined"==typeof CompressionStream||"undefined"==typeof TextEncoder)return!1;const r=null!=e.urlConfig.customUrl||null!=e.urlConfig.fallbackUrls,o=!0===n(895747).SDKFlags.get(e.sdkKey,"enable_log_event_compression");switch(t.logEventCompressionMode){case f().LogEventCompressionMode.Disabled:return!1;case f().LogEventCompressionMode.Enabled:return!(r&&!o);case f().LogEventCompressionMode.Forced:return!0;default:return!1}}(e,this._options))try{const n=(new TextEncoder).encode(r),i=new CompressionStream("gzip"),o=i.writable.getWriter();o.write(n).catch(a().Log.error),o.close().catch(a().Log.error);const l=i.readable.getReader(),u=[];let c;for(;!(c=yield l.read()).done;)u.push(c.value);const f=u.reduce(((e,t)=>e+t.length),0),d=new Uint8Array(f);let h=0;for(const e of u)d.set(e,h),h+=e.length;e.body=d,e.params=Object.assign(Object.assign({},null!==(t=e.params)&&void 0!==t?t:{}),{[s().NetworkParam.IsGzipped]:"1"})}catch(o){a().Log.warn(`Request compression failed for ${e.urlConfig.getUrl()}`,o)}}))}_getInternalRequestArgs(e,t){const n=this._fallbackResolver.getActiveFallbackUrl(t.sdkKey,t.urlConfig),r=Object.assign(Object.assign({},t),{method:e,fallbackUrl:n});return"data"in t&&p(r,t.data),r}};const h=e=>!!e.sdkKey||(a().Log.warn("Unable to make request without an SDK key"),!1),p=(e,t)=>{const{sdkKey:r,fallbackUrl:i}=e,o=n(32626).StableID.get(r),a=u().SessionID.get(r),s=l().SDKType._get(r);e.body=JSON.stringify(Object.assign(Object.assign({},t),{statsigMetadata:Object.assign(Object.assign({},c().StatsigMetadataProvider.get()),{stableID:o,sessionID:a,sdkType:s,fallbackUrl:i})}))};function m(e,t,n,r,i){e.urlConfig.endpoint===s().Endpoint._initialize&&o().Diagnostics._markInitNetworkReqEnd(e.sdkKey,o().Diagnostics._getDiagnosticsData(t,n,r,i))}},250052:function(e,t,n){"use strict";var r=this&&this.__awaiter||function(e,t,n,r){return new(n||(n=Promise))((function(i,o){function a(e){try{l(r.next(e))}catch(t){o(t)}}function s(e){try{l(r.throw(e))}catch(t){o(t)}}function l(e){var t;e.done?i(e.value):(t=e.value,t instanceof n?t:new n((function(e){e(t)}))).then(a,s)}l((r=r.apply(e,t||[])).next())}))};Object.defineProperty(t,"__esModule",{value:!0}),t._isDomainFailure=t.NetworkFallbackResolver=void 0;const i=()=>n(483918),o=()=>n(601638),a=6048e5;function s(e,t){var n;const r=null!==(n=null==e?void 0:e.toLowerCase())&&void 0!==n?n:"";return t||r.includes("uncaught exception")||r.includes("failed to fetch")||r.includes("networkerror when attempting to fetch resource")}function l(e){return`statsig.network_fallback.${(0,i()._DJB2)(e)}`}function u(e,t){const n=l(e);t&&0!==Object.keys(t).length?o().Storage.setItem(n,JSON.stringify(t)):o().Storage.removeItem(n)}t.NetworkFallbackResolver=class{constructor(e){var t;this._fallbackInfo=null,this._errorBoundary=null,this._dnsQueryCooldowns={},this._networkOverrideFunc=null===(t=e.networkConfig)||void 0===t?void 0:t.networkOverrideFunc}setErrorBoundary(e){this._errorBoundary=e}tryBumpExpiryTime(e,t){var n;const r=null===(n=this._fallbackInfo)||void 0===n?void 0:n[t.endpoint];r&&(r.expiryTime=Date.now()+a,u(e,Object.assign(Object.assign({},this._fallbackInfo),{[t.endpoint]:r})))}getActiveFallbackUrl(e,t){var r,i;if(null!=t.customUrl&&null!=t.fallbackUrls)return null;let a=this._fallbackInfo;null==a&&(a=null!==(r=function(e){const t=l(e),i=o().Storage.getItem(t);if(!i)return null;try{return JSON.parse(i)}catch(r){return n(668024).Log.error("Failed to parse FallbackInfo"),null}}(e))&&void 0!==r?r:{},this._fallbackInfo=a);const s=a[t.endpoint];return!s||Date.now()>(null!==(i=s.expiryTime)&&void 0!==i?i:0)||t.getChecksum()!==s.urlConfigChecksum?(delete a[t.endpoint],this._fallbackInfo=a,u(e,this._fallbackInfo),null):s.url?s.url:null}tryFetchUpdatedFallbackInfo(e,t,n,i){var o,a;return r(this,void 0,void 0,(function*(){try{if(!s(n,i))return!1;const r=null==t.customUrl&&null==t.fallbackUrls?yield this._tryFetchFallbackUrlsFromNetwork(t):t.fallbackUrls,a=this._pickNewFallbackUrl(null===(o=this._fallbackInfo)||void 0===o?void 0:o[t.endpoint],r);return!!a&&(this._updateFallbackInfoWithNewUrl(e,t,a),!0)}catch(r){return null===(a=this._errorBoundary)||void 0===a||a.logError("tryFetchUpdatedFallbackInfo",r),!1}}))}_updateFallbackInfoWithNewUrl(e,t,n){var r,i,o;const s={urlConfigChecksum:t.getChecksum(),url:n,expiryTime:Date.now()+a,previous:[]},l=t.endpoint,c=null===(r=this._fallbackInfo)||void 0===r?void 0:r[l];c&&s.previous.push(...c.previous),s.previous.length>10&&(s.previous=[]);const f=null===(o=null===(i=this._fallbackInfo)||void 0===i?void 0:i[l])||void 0===o?void 0:o.url;null!=f&&s.previous.push(f),this._fallbackInfo=Object.assign(Object.assign({},this._fallbackInfo),{[l]:s}),u(e,this._fallbackInfo)}_tryFetchFallbackUrlsFromNetwork(e){var t;return r(this,void 0,void 0,(function*(){const r=this._dnsQueryCooldowns[e.endpoint];if(r&&Date.now()<r)return null;this._dnsQueryCooldowns[e.endpoint]=Date.now()+144e5;const i=[],o=yield(0,n(660457)._fetchTxtRecords)(null!==(t=this._networkOverrideFunc)&&void 0!==t?t:fetch),a=function(e){try{return new URL(e).pathname}catch(t){return null}}(e.defaultUrl);for(const t of o){if(!t.startsWith(e.endpointDnsKey+"="))continue;const n=t.split("=");if(n.length>1){let e=n[1];e.endsWith("/")&&(e=e.slice(0,-1)),i.push(`https://${e}${a}`)}}return i}))}_pickNewFallbackUrl(e,t){var n;if(null==t)return null;const r=new Set(null!==(n=null==e?void 0:e.previous)&&void 0!==n?n:[]),i=null==e?void 0:e.url;let o=null;for(const a of t){const e=a.endsWith("/")?a.slice(0,-1):a;if(!r.has(a)&&e!==i){o=e;break}}return o}},t._isDomainFailure=s},251148:(e,t)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),t.UPDATE_DETAIL_ERROR_MESSAGES=t.createUpdateDetails=void 0;t.createUpdateDetails=(e,t,n,r,i,o)=>({duration:n,source:t,success:e,error:r,sourceUrl:i,warnings:o}),t.UPDATE_DETAIL_ERROR_MESSAGES={NO_NETWORK_DATA:"No data was returned from the network. This may be due to a network timeout if a timeout value was specified in the options or ad blocker error."}},263950:e=>{e.exports=function(){}},272669:(e,t)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),t.SHA256=void 0,t.SHA256=function(e){return(new o).update(e)};const n=[-2147483648,8388608,32768,128],r=[24,16,8,0],i=[1116352408,1899447441,3049323471,3921009573,961987163,1508970993,2453635748,2870763221,3624381080,310598401,607225278,1426881987,1925078388,2162078206,2614888103,3248222580,3835390401,4022224774,264347078,604807628,770255983,1249150122,1555081692,1996064986,2554220882,2821834349,2952996808,3210313671,3336571891,3584528711,113926993,338241895,666307205,773529912,1294757372,1396182291,1695183700,1986661051,2177026350,2456956037,2730485921,2820302411,3259730800,3345764771,3516065817,3600352804,4094571909,275423344,430227734,506948616,659060556,883997877,958139571,1322822218,1537002063,1747873779,1955562222,2024104815,2227730452,2361852424,2428436474,2756734187,3204031479,3329325298];class o{constructor(){this.blocks=[0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0],this.h0=1779033703,this.h1=3144134277,this.h2=1013904242,this.h3=2773480762,this.h4=1359893119,this.h5=2600822924,this.h6=528734635,this.h7=1541459225,this.block=this.start=this.bytes=this.hBytes=0,this.finalized=this.hashed=!1,this.first=!0,this.lastByteIndex=-1}update(e){if(this.finalized)return this;if("string"!=typeof e)throw new Error('Must be of type "string"');let t,n,i=0;const o=e.length,a=this.blocks;for(;i<o;){for(this.hashed&&(this.hashed=!1,a[0]=this.block,a[16]=a[1]=a[2]=a[3]=a[4]=a[5]=a[6]=a[7]=a[8]=a[9]=a[10]=a[11]=a[12]=a[13]=a[14]=a[15]=0),n=this.start;i<o&&n<64;++i)t=e.charCodeAt(i),t<128?a[n>>2]|=t<<r[3&n++]:t<2048?(a[n>>2]|=(192|t>>6)<<r[3&n++],a[n>>2]|=(128|63&t)<<r[3&n++]):t<55296||t>=57344?(a[n>>2]|=(224|t>>12)<<r[3&n++],a[n>>2]|=(128|t>>6&63)<<r[3&n++],a[n>>2]|=(128|63&t)<<r[3&n++]):(t=65536+((1023&t)<<10|1023&e.charCodeAt(++i)),a[n>>2]|=(240|t>>18)<<r[3&n++],a[n>>2]|=(128|t>>12&63)<<r[3&n++],a[n>>2]|=(128|t>>6&63)<<r[3&n++],a[n>>2]|=(128|63&t)<<r[3&n++]);this.lastByteIndex=n,this.bytes+=n-this.start,n>=64?(this.block=a[16],this.start=n-64,this.hash(),this.hashed=!0):this.start=n}return this.bytes>4294967295&&(this.hBytes+=this.bytes/4294967296<<0,this.bytes=this.bytes%4294967296),this}finalize(){if(this.finalized)return;this.finalized=!0;const e=this.blocks,t=this.lastByteIndex;e[16]=this.block,e[t>>2]|=n[3&t],this.block=e[16],t>=56&&(this.hashed||this.hash(),e[0]=this.block,e[16]=e[1]=e[2]=e[3]=e[4]=e[5]=e[6]=e[7]=e[8]=e[9]=e[10]=e[11]=e[12]=e[13]=e[14]=e[15]=0),e[14]=this.hBytes<<3|this.bytes>>>29,e[15]=this.bytes<<3,this.hash()}hash(){const e=this.blocks;let t,n,r,o,a,s,l,u,c,f,d,h=this.h0,p=this.h1,m=this.h2,g=this.h3,v=this.h4,y=this.h5,b=this.h6,_=this.h7;for(t=16;t<64;++t)a=e[t-15],n=(a>>>7|a<<25)^(a>>>18|a<<14)^a>>>3,a=e[t-2],r=(a>>>17|a<<15)^(a>>>19|a<<13)^a>>>10,e[t]=e[t-16]+n+e[t-7]+r<<0;for(d=p&m,t=0;t<64;t+=4)this.first?(u=704751109,a=e[0]-210244248,_=a-1521486534<<0,g=a+143694565<<0,this.first=!1):(n=(h>>>2|h<<30)^(h>>>13|h<<19)^(h>>>22|h<<10),r=(v>>>6|v<<26)^(v>>>11|v<<21)^(v>>>25|v<<7),u=h&p,o=u^h&m^d,l=v&y^~v&b,a=_+r+l+i[t]+e[t],s=n+o,_=g+a<<0,g=a+s<<0),n=(g>>>2|g<<30)^(g>>>13|g<<19)^(g>>>22|g<<10),r=(_>>>6|_<<26)^(_>>>11|_<<21)^(_>>>25|_<<7),c=g&h,o=c^g&p^u,l=_&v^~_&y,a=b+r+l+i[t+1]+e[t+1],s=n+o,b=m+a<<0,m=a+s<<0,n=(m>>>2|m<<30)^(m>>>13|m<<19)^(m>>>22|m<<10),r=(b>>>6|b<<26)^(b>>>11|b<<21)^(b>>>25|b<<7),f=m&g,o=f^m&h^c,l=b&_^~b&v,a=y+r+l+i[t+2]+e[t+2],s=n+o,y=p+a<<0,p=a+s<<0,n=(p>>>2|p<<30)^(p>>>13|p<<19)^(p>>>22|p<<10),r=(y>>>6|y<<26)^(y>>>11|y<<21)^(y>>>25|y<<7),d=p&m,o=d^p&g^f,l=y&b^~y&_,a=v+r+l+i[t+3]+e[t+3],s=n+o,v=h+a<<0,h=a+s<<0;this.h0=this.h0+h<<0,this.h1=this.h1+p<<0,this.h2=this.h2+m<<0,this.h3=this.h3+g<<0,this.h4=this.h4+v<<0,this.h5=this.h5+y<<0,this.h6=this.h6+b<<0,this.h7=this.h7+_<<0}arrayBuffer(){return this._getOutputs().buffer}dataView(){return this._getOutputs().dataView}_getOutputs(){this.finalize();const e=new ArrayBuffer(32),t=new DataView(e);return t.setUint32(0,this.h0),t.setUint32(4,this.h1),t.setUint32(8,this.h2),t.setUint32(12,this.h3),t.setUint32(16,this.h4),t.setUint32(20,this.h5),t.setUint32(24,this.h6),t.setUint32(28,this.h7),{dataView:t,buffer:e}}}},285914:function(e,t,n){var r;e=n.nmd(e),function(i){var o=t,a=(e&&e.exports,"object"==typeof n.g&&n.g);a.global!==a&&a.window;var s=function(e){this.message=e};(s.prototype=new Error).name="InvalidCharacterError";var l=function(e){throw new s(e)},u="ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789+/",c=/[\t\n\f\r ]/g,f={encode:function(e){e=String(e),/[^\0-\xFF]/.test(e)&&l("The string to be encoded contains characters outside of the Latin1 range.");for(var t,n,r,i,o=e.length%3,a="",s=-1,c=e.length-o;++s<c;)t=e.charCodeAt(s)<<16,n=e.charCodeAt(++s)<<8,r=e.charCodeAt(++s),a+=u.charAt((i=t+n+r)>>18&63)+u.charAt(i>>12&63)+u.charAt(i>>6&63)+u.charAt(63&i);return 2==o?(t=e.charCodeAt(s)<<8,n=e.charCodeAt(++s),a+=u.charAt((i=t+n)>>10)+u.charAt(i>>4&63)+u.charAt(i<<2&63)+"="):1==o&&(i=e.charCodeAt(s),a+=u.charAt(i>>2)+u.charAt(i<<4&63)+"=="),a},decode:function(e){var t=(e=String(e).replace(c,"")).length;t%4==0&&(t=(e=e.replace(/==?$/,"")).length),(t%4==1||/[^+a-zA-Z0-9/]/.test(e))&&l("Invalid character: the string to be decoded is not correctly encoded.");for(var n,r,i=0,o="",a=-1;++a<t;)r=u.indexOf(e.charAt(a)),n=i%4?64*n+r:r,i++%4&&(o+=String.fromCharCode(255&n>>(-2*i&6)));return o},version:"0.1.0"};void 0===(r=function(){return f}.call(t,n,t,e))||(e.exports=r)}()},286913:(e,t)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0})},296540:(e,t,n)=>{"use strict";e.exports=n(815287)},301548:(e,t,n)=>{"use strict";var r=n(444576),i=n(779039),o=n(839519),a=n(184215),s=r.structuredClone;e.exports=!!s&&!i((function(){if("DENO"===a&&o>92||"NODE"===a&&o>94||"BROWSER"===a&&o>97)return!1;var e=new ArrayBuffer(8),t=s(e,{transfer:[e]});return 0!==e.byteLength||8!==t.byteLength}))},302404:(e,t,n)=>{e.exports=function(e,t){return n(860270)(e,t)}},304146:(e,t,n)=>{"use strict";var r=()=>n(44363),i={childContextTypes:!0,contextType:!0,contextTypes:!0,defaultProps:!0,displayName:!0,getDefaultProps:!0,getDerivedStateFromError:!0,getDerivedStateFromProps:!0,mixins:!0,propTypes:!0,type:!0},o={name:!0,length:!0,prototype:!0,caller:!0,callee:!0,arguments:!0,arity:!0},a={$$typeof:!0,compare:!0,defaultProps:!0,displayName:!0,propTypes:!0,type:!0},s={};function l(e){return r().isMemo(e)?a:s[e.$$typeof]||i}s[r().ForwardRef]={$$typeof:!0,render:!0,defaultProps:!0,displayName:!0,propTypes:!0},s[r().Memo]=a;var u=Object.defineProperty,c=Object.getOwnPropertyNames,f=Object.getOwnPropertySymbols,d=Object.getOwnPropertyDescriptor,h=Object.getPrototypeOf,p=Object.prototype;e.exports=function e(t,n,r){if("string"!=typeof n){if(p){var i=h(n);i&&i!==p&&e(t,i,r)}var a=c(n);f&&(a=a.concat(f(n)));for(var s=l(t),m=l(n),g=0;g<a.length;++g){var v=a[g];if(!(o[v]||r&&r[v]||m&&m[v]||s&&s[v])){var y=d(n,v);try{u(t,v,y)}catch(b){}}}}return t}},320295:(e,t)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),t.resultToLayerEval=t.resultToConfigEval=t.resultToGateEval=t.makeEvalResult=void 0,t.makeEvalResult=function(e){const t={unsupported:!1,bool_value:!1,rule_id:"",secondary_exposures:[],json_value:{},explicit_parameters:null,allocated_experiment_name:null,is_experiment_group:!1,group_name:null,undelegated_secondary_exposures:void 0};return Object.assign(Object.assign({},t),e)},t.resultToGateEval=function(e,t){var n;return{name:e.name,id_type:e.idType,rule_id:t.rule_id,value:t.bool_value,secondary_exposures:t.secondary_exposures,version:null===(n=e.version)||void 0===n?void 0:n.toString()}},t.resultToConfigEval=function(e,t){var n,r,i;return{name:e.name,id_type:e.idType,rule_id:t.rule_id,value:t.json_value,secondary_exposures:t.secondary_exposures,group:null!==(n=t.group_name)&&void 0!==n?n:"",group_name:null!==(r=t.group_name)&&void 0!==r?r:void 0,is_device_based:!1,is_experiment_active:e.isActive,is_user_in_experiment:t.is_experiment_group,version:null===(i=e.version)||void 0===i?void 0:i.toString(),passed:t.bool_value}},t.resultToLayerEval=function(e,t,n){var r,i,o,a,s;return{name:e.name,rule_id:n.rule_id,value:n.json_value,secondary_exposures:n.secondary_exposures,undelegated_secondary_exposures:n.undelegated_secondary_exposures,allocated_experiment_name:null!==(r=n.allocated_experiment_name)&&void 0!==r?r:"",explicit_parameters:null!==(i=n.explicit_parameters)&&void 0!==i?i:[],group:null!==(o=n.group_name)&&void 0!==o?o:"",group_name:null!==(a=n.group_name)&&void 0!==a?a:void 0,is_device_based:!1,is_experiment_active:null==t?void 0:t.isActive,is_user_in_experiment:n.is_experiment_group,version:null===(s=e.version)||void 0===s?void 0:s.toString()}}},322799:(e,t)=>{"use strict";var n="function"==typeof Symbol&&Symbol.for,r=n?Symbol.for("react.element"):60103,i=n?Symbol.for("react.portal"):60106,o=n?Symbol.for("react.fragment"):60107,a=n?Symbol.for("react.strict_mode"):60108,s=n?Symbol.for("react.profiler"):60114,l=n?Symbol.for("react.provider"):60109,u=n?Symbol.for("react.context"):60110,c=n?Symbol.for("react.async_mode"):60111,f=n?Symbol.for("react.concurrent_mode"):60111,d=n?Symbol.for("react.forward_ref"):60112,h=n?Symbol.for("react.suspense"):60113,p=n?Symbol.for("react.suspense_list"):60120,m=n?Symbol.for("react.memo"):60115,g=n?Symbol.for("react.lazy"):60116,v=n?Symbol.for("react.block"):60121,y=n?Symbol.for("react.fundamental"):60117,b=n?Symbol.for("react.responder"):60118,_=n?Symbol.for("react.scope"):60119;function w(e){if("object"==typeof e&&null!==e){var t=e.$$typeof;switch(t){case r:switch(e=e.type){case c:case f:case o:case s:case a:case h:return e;default:switch(e=e&&e.$$typeof){case u:case d:case g:case m:case l:return e;default:return t}}case i:return t}}}function S(e){return w(e)===f}t.AsyncMode=c,t.ConcurrentMode=f,t.ContextConsumer=u,t.ContextProvider=l,t.Element=r,t.ForwardRef=d,t.Fragment=o,t.Lazy=g,t.Memo=m,t.Portal=i,t.Profiler=s,t.StrictMode=a,t.Suspense=h,t.isAsyncMode=function(e){return S(e)||w(e)===c},t.isConcurrentMode=S,t.isContextConsumer=function(e){return w(e)===u},t.isContextProvider=function(e){return w(e)===l},t.isElement=function(e){return"object"==typeof e&&null!==e&&e.$$typeof===r},t.isForwardRef=function(e){return w(e)===d},t.isFragment=function(e){return w(e)===o},t.isLazy=function(e){return w(e)===g},t.isMemo=function(e){return w(e)===m},t.isPortal=function(e){return w(e)===i},t.isProfiler=function(e){return w(e)===s},t.isStrictMode=function(e){return w(e)===a},t.isSuspense=function(e){return w(e)===h},t.isValidElementType=function(e){return"string"==typeof e||"function"==typeof e||e===o||e===f||e===s||e===a||e===h||e===p||"object"==typeof e&&null!==e&&(e.$$typeof===g||e.$$typeof===m||e.$$typeof===l||e.$$typeof===u||e.$$typeof===d||e.$$typeof===y||e.$$typeof===b||e.$$typeof===_||e.$$typeof===v)},t.typeOf=w},327025:(e,t,n)=>{"use strict";n.d(t,{A:()=>a});var r=n(296540),i=n(587155),o=n(235953);function a(){var e=r.useContext(i.ob);return(0,o.HM)(e),e}},331221:(e,t,n)=>{"use strict";n.d(t,{V1:()=>r});function r(e,t,n){if(void 0===n&&(n=Error),!e)throw new n(t)}},331635:(e,t,n)=>{"use strict";n.r(t),n.d(t,{__addDisposableResource:()=>M,__assign:()=>o,__asyncDelegator:()=>T,__asyncGenerator:()=>k,__asyncValues:()=>x,__await:()=>E,__awaiter:()=>p,__classPrivateFieldGet:()=>A,__classPrivateFieldIn:()=>L,__classPrivateFieldSet:()=>P,__createBinding:()=>g,__decorate:()=>s,__disposeResources:()=>F,__esDecorate:()=>u,__exportStar:()=>v,__extends:()=>i,__generator:()=>m,__importDefault:()=>D,__importStar:()=>I,__makeTemplateObject:()=>O,__metadata:()=>h,__param:()=>l,__propKey:()=>f,__read:()=>b,__rest:()=>a,__rewriteRelativeImportExtension:()=>U,__runInitializers:()=>c,__setFunctionName:()=>d,__spread:()=>_,__spreadArray:()=>S,__spreadArrays:()=>w,__values:()=>y,default:()=>j});var r=function(e,t){return r=Object.setPrototypeOf||{__proto__:[]}instanceof Array&&function(e,t){e.__proto__=t}||function(e,t){for(var n in t)Object.prototype.hasOwnProperty.call(t,n)&&(e[n]=t[n])},r(e,t)};function i(e,t){if("function"!=typeof t&&null!==t)throw new TypeError("Class extends value "+String(t)+" is not a constructor or null");function n(){this.constructor=e}r(e,t),e.prototype=null===t?Object.create(t):(n.prototype=t.prototype,new n)}var o=function(){return o=Object.assign||function(e){for(var t,n=1,r=arguments.length;n<r;n++)for(var i in t=arguments[n])Object.prototype.hasOwnProperty.call(t,i)&&(e[i]=t[i]);return e},o.apply(this,arguments)};function a(e,t){var n={};for(var r in e)Object.prototype.hasOwnProperty.call(e,r)&&t.indexOf(r)<0&&(n[r]=e[r]);if(null!=e&&"function"==typeof Object.getOwnPropertySymbols){var i=0;for(r=Object.getOwnPropertySymbols(e);i<r.length;i++)t.indexOf(r[i])<0&&Object.prototype.propertyIsEnumerable.call(e,r[i])&&(n[r[i]]=e[r[i]])}return n}function s(e,t,n,r){var i,o=arguments.length,a=o<3?t:null===r?r=Object.getOwnPropertyDescriptor(t,n):r;if("object"==typeof Reflect&&"function"==typeof Reflect.decorate)a=Reflect.decorate(e,t,n,r);else for(var s=e.length-1;s>=0;s--)(i=e[s])&&(a=(o<3?i(a):o>3?i(t,n,a):i(t,n))||a);return o>3&&a&&Object.defineProperty(t,n,a),a}function l(e,t){return function(n,r){t(n,r,e)}}function u(e,t,n,r,i,o){function a(e){if(void 0!==e&&"function"!=typeof e)throw new TypeError("Function expected");return e}for(var s,l=r.kind,u="getter"===l?"get":"setter"===l?"set":"value",c=!t&&e?r.static?e:e.prototype:null,f=t||(c?Object.getOwnPropertyDescriptor(c,r.name):{}),d=!1,h=n.length-1;h>=0;h--){var p={};for(var m in r)p[m]="access"===m?{}:r[m];for(var m in r.access)p.access[m]=r.access[m];p.addInitializer=function(e){if(d)throw new TypeError("Cannot add initializers after decoration has completed");o.push(a(e||null))};var g=(0,n[h])("accessor"===l?{get:f.get,set:f.set}:f[u],p);if("accessor"===l){if(void 0===g)continue;if(null===g||"object"!=typeof g)throw new TypeError("Object expected");(s=a(g.get))&&(f.get=s),(s=a(g.set))&&(f.set=s),(s=a(g.init))&&i.unshift(s)}else(s=a(g))&&("field"===l?i.unshift(s):f[u]=s)}c&&Object.defineProperty(c,r.name,f),d=!0}function c(e,t,n){for(var r=arguments.length>2,i=0;i<t.length;i++)n=r?t[i].call(e,n):t[i].call(e);return r?n:void 0}function f(e){return"symbol"==typeof e?e:"".concat(e)}function d(e,t,n){return"symbol"==typeof t&&(t=t.description?"[".concat(t.description,"]"):""),Object.defineProperty(e,"name",{configurable:!0,value:n?"".concat(n," ",t):t})}function h(e,t){if("object"==typeof Reflect&&"function"==typeof Reflect.metadata)return Reflect.metadata(e,t)}function p(e,t,n,r){return new(n||(n=Promise))((function(i,o){function a(e){try{l(r.next(e))}catch(t){o(t)}}function s(e){try{l(r.throw(e))}catch(t){o(t)}}function l(e){var t;e.done?i(e.value):(t=e.value,t instanceof n?t:new n((function(e){e(t)}))).then(a,s)}l((r=r.apply(e,t||[])).next())}))}function m(e,t){var n,r,i,o={label:0,sent:function(){if(1&i[0])throw i[1];return i[1]},trys:[],ops:[]},a=Object.create(("function"==typeof Iterator?Iterator:Object).prototype);return a.next=s(0),a.throw=s(1),a.return=s(2),"function"==typeof Symbol&&(a[Symbol.iterator]=function(){return this}),a;function s(s){return function(l){return function(s){if(n)throw new TypeError("Generator is already executing.");for(;a&&(a=0,s[0]&&(o=0)),o;)try{if(n=1,r&&(i=2&s[0]?r.return:s[0]?r.throw||((i=r.return)&&i.call(r),0):r.next)&&!(i=i.call(r,s[1])).done)return i;switch(r=0,i&&(s=[2&s[0],i.value]),s[0]){case 0:case 1:i=s;break;case 4:return o.label++,{value:s[1],done:!1};case 5:o.label++,r=s[1],s=[0];continue;case 7:s=o.ops.pop(),o.trys.pop();continue;default:if(!(i=o.trys,(i=i.length>0&&i[i.length-1])||6!==s[0]&&2!==s[0])){o=0;continue}if(3===s[0]&&(!i||s[1]>i[0]&&s[1]<i[3])){o.label=s[1];break}if(6===s[0]&&o.label<i[1]){o.label=i[1],i=s;break}if(i&&o.label<i[2]){o.label=i[2],o.ops.push(s);break}i[2]&&o.ops.pop(),o.trys.pop();continue}s=t.call(e,o)}catch(l){s=[6,l],r=0}finally{n=i=0}if(5&s[0])throw s[1];return{value:s[0]?s[1]:void 0,done:!0}}([s,l])}}}var g=Object.create?function(e,t,n,r){void 0===r&&(r=n);var i=Object.getOwnPropertyDescriptor(t,n);i&&!("get"in i?!t.__esModule:i.writable||i.configurable)||(i={enumerable:!0,get:function(){return t[n]}}),Object.defineProperty(e,r,i)}:function(e,t,n,r){void 0===r&&(r=n),e[r]=t[n]};function v(e,t){for(var n in e)"default"===n||Object.prototype.hasOwnProperty.call(t,n)||g(t,e,n)}function y(e){var t="function"==typeof Symbol&&Symbol.iterator,n=t&&e[t],r=0;if(n)return n.call(e);if(e&&"number"==typeof e.length)return{next:function(){return e&&r>=e.length&&(e=void 0),{value:e&&e[r++],done:!e}}};throw new TypeError(t?"Object is not iterable.":"Symbol.iterator is not defined.")}function b(e,t){var n="function"==typeof Symbol&&e[Symbol.iterator];if(!n)return e;var r,i,o=n.call(e),a=[];try{for(;(void 0===t||t-- >0)&&!(r=o.next()).done;)a.push(r.value)}catch(s){i={error:s}}finally{try{r&&!r.done&&(n=o.return)&&n.call(o)}finally{if(i)throw i.error}}return a}function _(){for(var e=[],t=0;t<arguments.length;t++)e=e.concat(b(arguments[t]));return e}function w(){for(var e=0,t=0,n=arguments.length;t<n;t++)e+=arguments[t].length;var r=Array(e),i=0;for(t=0;t<n;t++)for(var o=arguments[t],a=0,s=o.length;a<s;a++,i++)r[i]=o[a];return r}function S(e,t,n){if(n||2===arguments.length)for(var r,i=0,o=t.length;i<o;i++)!r&&i in t||(r||(r=Array.prototype.slice.call(t,0,i)),r[i]=t[i]);return e.concat(r||Array.prototype.slice.call(t))}function E(e){return this instanceof E?(this.v=e,this):new E(e)}function k(e,t,n){if(!Symbol.asyncIterator)throw new TypeError("Symbol.asyncIterator is not defined.");var r,i=n.apply(e,t||[]),o=[];return r=Object.create(("function"==typeof AsyncIterator?AsyncIterator:Object).prototype),a("next"),a("throw"),a("return",(function(e){return function(t){return Promise.resolve(t).then(e,u)}})),r[Symbol.asyncIterator]=function(){return this},r;function a(e,t){i[e]&&(r[e]=function(t){return new Promise((function(n,r){o.push([e,t,n,r])>1||s(e,t)}))},t&&(r[e]=t(r[e])))}function s(e,t){try{(n=i[e](t)).value instanceof E?Promise.resolve(n.value.v).then(l,u):c(o[0][2],n)}catch(r){c(o[0][3],r)}var n}function l(e){s("next",e)}function u(e){s("throw",e)}function c(e,t){e(t),o.shift(),o.length&&s(o[0][0],o[0][1])}}function T(e){var t,n;return t={},r("next"),r("throw",(function(e){throw e})),r("return"),t[Symbol.iterator]=function(){return this},t;function r(r,i){t[r]=e[r]?function(t){return(n=!n)?{value:E(e[r](t)),done:!1}:i?i(t):t}:i}}function x(e){if(!Symbol.asyncIterator)throw new TypeError("Symbol.asyncIterator is not defined.");var t,n=e[Symbol.asyncIterator];return n?n.call(e):(e=y(e),t={},r("next"),r("throw"),r("return"),t[Symbol.asyncIterator]=function(){return this},t);function r(n){t[n]=e[n]&&function(t){return new Promise((function(r,i){(function(e,t,n,r){Promise.resolve(r).then((function(t){e({value:t,done:n})}),t)})(r,i,(t=e[n](t)).done,t.value)}))}}}function O(e,t){return Object.defineProperty?Object.defineProperty(e,"raw",{value:t}):e.raw=t,e}var C=Object.create?function(e,t){Object.defineProperty(e,"default",{enumerable:!0,value:t})}:function(e,t){e.default=t},N=function(e){return N=Object.getOwnPropertyNames||function(e){var t=[];for(var n in e)Object.prototype.hasOwnProperty.call(e,n)&&(t[t.length]=n);return t},N(e)};function I(e){if(e&&e.__esModule)return e;var t={};if(null!=e)for(var n=N(e),r=0;r<n.length;r++)"default"!==n[r]&&g(t,e,n[r]);return C(t,e),t}function D(e){return e&&e.__esModule?e:{default:e}}function A(e,t,n,r){if("a"===n&&!r)throw new TypeError("Private accessor was defined without a getter");if("function"==typeof t?e!==t||!r:!t.has(e))throw new TypeError("Cannot read private member from an object whose class did not declare it");return"m"===n?r:"a"===n?r.call(e):r?r.value:t.get(e)}function P(e,t,n,r,i){if("m"===r)throw new TypeError("Private method is not writable");if("a"===r&&!i)throw new TypeError("Private accessor was defined without a setter");if("function"==typeof t?e!==t||!i:!t.has(e))throw new TypeError("Cannot write private member to an object whose class did not declare it");return"a"===r?i.call(e,n):i?i.value=n:t.set(e,n),n}function L(e,t){if(null===t||"object"!=typeof t&&"function"!=typeof t)throw new TypeError("Cannot use 'in' operator on non-object");return"function"==typeof e?t===e:e.has(t)}function M(e,t,n){if(null!=t){if("object"!=typeof t&&"function"!=typeof t)throw new TypeError("Object expected.");var r,i;if(n){if(!Symbol.asyncDispose)throw new TypeError("Symbol.asyncDispose is not defined.");r=t[Symbol.asyncDispose]}if(void 0===r){if(!Symbol.dispose)throw new TypeError("Symbol.dispose is not defined.");r=t[Symbol.dispose],n&&(i=r)}if("function"!=typeof r)throw new TypeError("Object not disposable.");i&&(r=function(){try{i.call(this)}catch(e){return Promise.reject(e)}}),e.stack.push({value:t,dispose:r,async:n})}else n&&e.stack.push({async:!0});return t}var R="function"==typeof SuppressedError?SuppressedError:function(e,t,n){var r=new Error(n);return r.name="SuppressedError",r.error=e,r.suppressed=t,r};function F(e){function t(t){e.error=e.hasError?new R(t,e.error,"An error was suppressed during disposal."):t,e.hasError=!0}var n,r=0;return function i(){for(;n=e.stack.pop();)try{if(!n.async&&1===r)return r=0,e.stack.push(n),Promise.resolve().then(i);if(n.dispose){var o=n.dispose.call(n.value);if(n.async)return r|=2,Promise.resolve(o).then(i,(function(e){return t(e),i()}))}else r|=1}catch(a){t(a)}if(1===r)return e.hasError?Promise.reject(e.error):Promise.resolve();if(e.hasError)throw e.error}()}function U(e,t){return"string"==typeof e&&/^\.\.?\//.test(e)?e.replace(/\.(tsx)$|((?:\.d)?)((?:\.[^./]+?)?)\.([cm]?)ts$/i,(function(e,n,r,i,o){return n?t?".jsx":".js":!r||i&&o?r+i+"."+o.toLowerCase()+"js":e})):e}const j={__extends:i,__assign:o,__rest:a,__decorate:s,__param:l,__esDecorate:u,__runInitializers:c,__propKey:f,__setFunctionName:d,__metadata:h,__awaiter:p,__generator:m,__createBinding:g,__exportStar:v,__values:y,__read:b,__spread:_,__spreadArrays:w,__spreadArray:S,__await:E,__asyncGenerator:k,__asyncDelegator:T,__asyncValues:x,__makeTemplateObject:O,__importStar:I,__importDefault:D,__classPrivateFieldGet:A,__classPrivateFieldSet:P,__classPrivateFieldIn:L,__addDisposableResource:M,__disposeResources:F,__rewriteRelativeImportExtension:U}},333031:(e,t,n)=>{var r=()=>n(936800),i=n(269302)((function(e,t){if(null==e)return[];var i=t.length;return i>1&&r()(e,t[0],t[1])?t=[]:i>2&&r()(t[0],t[1],t[2])&&(t=[t[0]]),n(146155)(e,n(983120)(t,1),[])}));e.exports=i},335596:(e,t,n)=>{"use strict";n.d(t,{A:()=>u});var r=n(531234),i=n(296540),o=n(235953),a=n(587155),s=n(360665);function l(e){return{locale:e.locale,timeZone:e.timeZone,fallbackOnEmptyString:e.fallbackOnEmptyString,formats:e.formats,textComponent:e.textComponent,messages:e.messages,defaultLocale:e.defaultLocale,defaultFormats:e.defaultFormats,onError:e.onError,onWarn:e.onWarn,wrapRichTextChunksInFragment:e.wrapRichTextChunksInFragment,defaultRichTextElements:e.defaultRichTextElements}}const u=function(e){function t(){var t=null!==e&&e.apply(this,arguments)||this;return t.cache=(0,r.MT)(),t.state={cache:t.cache,intl:(0,s.E)(l(t.props),t.cache),prevConfig:l(t.props)},t}return(0,n(331635).__extends)(t,e),t.getDerivedStateFromProps=function(e,t){var n=t.prevConfig,r=t.cache,i=l(e);return(0,o.bN)(n,i)?null:{intl:(0,s.E)(i,r),prevConfig:i}},t.prototype.render=function(){return(0,o.HM)(this.state.intl),i.createElement(a.Kq,{value:this.state.intl},this.props.children)},t.displayName="IntlProvider",t.defaultProps=o.JF,t}(i.PureComponent)},336119:(e,t,n)=>{e.exports=function(e){return e&&e.length?n(17721)(e,n(383488)):0}},338823:(e,t,n)=>{"use strict";n.d(t,{k:()=>i});const r=[];for(let o=0;o<256;++o)r.push((o+256).toString(16).slice(1));function i(e,t=0){return r[e[t+0]]+r[e[t+1]]+r[e[t+2]]+r[e[t+3]]+"-"+r[e[t+4]]+r[e[t+5]]+"-"+r[e[t+6]]+r[e[t+7]]+"-"+r[e[t+8]]+r[e[t+9]]+"-"+r[e[t+10]]+r[e[t+11]]+r[e[t+12]]+r[e[t+13]]+r[e[t+14]]+r[e[t+15]]}},339839:(e,t,n)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),t.StatsigSession=t.SessionID=void 0;const r=()=>n(843509),i=()=>n(894681),o=()=>n(668024),a=()=>n(601638),s=()=>n(537047),l=18e5,u=144e5,c={};function f(e,t){return setTimeout((()=>{var t;const n=null===(t=(0,r()._getStatsigGlobal)())||void 0===t?void 0:t.instance(e);n&&n.$emt({name:"session_expired"})}),t)}function d(e){return`statsig.session_id.${(0,i()._getStorageKey)(e)}`}t.SessionID={get:e=>t.StatsigSession.get(e).data.sessionID},t.StatsigSession={get:e=>{null==c[e]&&(c[e]=function(e){let t=function(e){const t=d(e);return(0,a()._getObjectFromStorage)(t)}(e);const n=Date.now();t||(t={sessionID:(0,s().getUUID)(),startTime:n,lastUpdate:n});return{data:t,sdkKey:e}}(e));return function(e){const t=Date.now(),n=e.data,r=e.sdkKey;if(function({lastUpdate:e}){return Date.now()-e>l}(n)||function({startTime:e}){return Date.now()-e>u}(n)){n.sessionID=(0,s().getUUID)(),n.startTime=t;const e=null===__STATSIG__||void 0===__STATSIG__?void 0:__STATSIG__.instance(r);e&&e.$emt({name:"session_expired"})}n.lastUpdate=t,function(e,t){const n=d(t);try{(0,a()._setObjectInStorage)(n,e)}catch(r){o().Log.warn("Failed to save SessionID")}}(n,e.sdkKey),clearTimeout(e.idleTimeoutID),clearTimeout(e.ageTimeoutID);const i=t-n.startTime;return e.idleTimeoutID=f(r,l),e.ageTimeoutID=f(r,u-i),e}(c[e])},overrideInitialSessionID:(e,t)=>{c[t]=function(e,t){const n=Date.now();return{data:{sessionID:e,startTime:n,lastUpdate:n},sdkKey:t}}(e,t)}}},355083:(e,t,n)=>{e.exports=function(e){return n(182609)(e,n(383488))}},355364:(e,t,n)=>{var r=n(920999)((function(e,t,r){n(785250)(e,t,r)}));e.exports=r},360665:(e,t,n)=>{"use strict";n.d(t,{E:()=>H});var r=()=>n(331635),i=n(331221),o=n(822015),a=n(960709),s=n(892677);function l(e,t){return Object.keys(e).reduce((function(n,i){return n[i]=(0,r().__assign)({timeZone:t},e[i]),n}),{})}function u(e,t){return Object.keys((0,r().__assign)((0,r().__assign)({},e),t)).reduce((function(n,i){return n[i]=(0,r().__assign)((0,r().__assign)({},e[i]||{}),t[i]||{}),n}),{})}function c(e,t){if(!t)return e;var n=o.S.formats;return(0,r().__assign)((0,r().__assign)((0,r().__assign)({},n),e),{date:u(l(n.date,t),l(e.date||{},t)),time:u(l(n.time,t),l(e.time||{},t))})}var f=function(e,t,n,o,l){var u=e.locale,f=e.formats,d=e.messages,h=e.defaultLocale,p=e.defaultFormats,m=e.fallbackOnEmptyString,g=e.onError,v=e.timeZone,y=e.defaultRichTextElements;void 0===n&&(n={id:""});var b=n.id,_=n.defaultMessage;(0,i.V1)(!!b,"[@formatjs/intl] An `id` must be provided to format a message. You can either:\n1. Configure your build toolchain with [babel-plugin-formatjs](https://formatjs.io/docs/tooling/babel-plugin)\nor [@formatjs/ts-transformer](https://formatjs.io/docs/tooling/ts-transformer) OR\n2. Configure your `eslint` config to include [eslint-plugin-formatjs](https://formatjs.io/docs/tooling/linter#enforce-id)\nto autofix this issue");var w=String(b),S=d&&Object.prototype.hasOwnProperty.call(d,w)&&d[w];if(Array.isArray(S)&&1===S.length&&S[0].type===s.ZE.literal)return S[0].value;if(!o&&S&&"string"==typeof S&&!y)return S.replace(/'\{(.*?)\}'/gi,"{$1}");if(o=(0,r().__assign)((0,r().__assign)({},y),o||{}),f=c(f,v),p=c(p,v),!S){if(!1===m&&""===S)return S;if((!_||u&&u.toLowerCase()!==h.toLowerCase())&&g(new a.sb(n,u)),_)try{return t.getMessageFormat(_,h,p,l).format(o)}catch(E){return g(new a.Ho('Error formatting default message for: "'.concat(w,'", rendering default message verbatim'),u,n,E)),"string"==typeof _?_:w}return w}try{return t.getMessageFormat(S,u,f,(0,r().__assign)({formatters:t},l||{})).format(o)}catch(E){g(new a.Ho('Error formatting message: "'.concat(w,'", using ').concat(_?"default message":"id"," as fallback."),u,n,E))}if(_)try{return t.getMessageFormat(_,h,p,l).format(o)}catch(E){g(new a.Ho('Error formatting the default message for: "'.concat(w,'", rendering message verbatim'),u,n,E))}return"string"==typeof S?S:"string"==typeof _?_:w},d=n(531234),h=["style","currency","unit","unitDisplay","useGrouping","minimumIntegerDigits","minimumFractionDigits","maximumFractionDigits","minimumSignificantDigits","maximumSignificantDigits","compactDisplay","currencyDisplay","currencySign","notation","signDisplay","unit","unitDisplay","numberingSystem","trailingZeroDisplay","roundingPriority","roundingIncrement","roundingMode"];function p(e,t,n){var r=e.locale,i=e.formats,o=e.onError;void 0===n&&(n={});var a=n.format,s=a&&(0,d.F3)(i,"number",a,o)||{};return t(r,(0,d.J9)(n,h,s))}function m(e,t,n,r){void 0===r&&(r={});try{return p(e,t,r).format(n)}catch(i){e.onError(new a.pg("Error formatting number.",e.locale,i))}return String(n)}function g(e,t,n,r){void 0===r&&(r={});try{return p(e,t,r).formatToParts(n)}catch(i){e.onError(new a.pg("Error formatting number.",e.locale,i))}return[]}var v=n(215067),y=["numeric","style"];function b(e,t,n,r,i){void 0===i&&(i={}),r||(r="second"),Intl.RelativeTimeFormat||e.onError(new v.IF('Intl.RelativeTimeFormat is not available in this environment.\nTry polyfilling it using "@formatjs/intl-relativetimeformat"\n',v.O4.MISSING_INTL_API));try{return function(e,t,n){var r=e.locale,i=e.formats,o=e.onError;void 0===n&&(n={});var a=n.format,s=!!a&&(0,d.F3)(i,"relative",a,o)||{};return t(r,(0,d.J9)(n,y,s))}(e,t,i).format(n,r)}catch(o){e.onError(new a.pg("Error formatting relative time.",e.locale,o))}return String(n)}var _=["formatMatcher","timeZone","hour12","weekday","era","year","month","day","hour","minute","second","timeZoneName","hourCycle","dateStyle","timeStyle","calendar","numberingSystem","fractionalSecondDigits"];function w(e,t,n,i){var o=e.locale,a=e.formats,s=e.onError,l=e.timeZone;void 0===i&&(i={});var u=i.format,c=(0,r().__assign)((0,r().__assign)({},l&&{timeZone:l}),u&&(0,d.F3)(a,t,u,s)),f=(0,d.J9)(i,_,c);return"time"!==t||f.hour||f.minute||f.second||f.timeStyle||f.dateStyle||(f=(0,r().__assign)((0,r().__assign)({},f),{hour:"numeric",minute:"numeric"})),n(o,f)}function S(e,t){for(var n=[],r=2;r<arguments.length;r++)n[r-2]=arguments[r];var i=n[0],o=n[1],s=void 0===o?{}:o,l="string"==typeof i?new Date(i||0):i;try{return w(e,"date",t,s).format(l)}catch(u){e.onError(new a.pg("Error formatting date.",e.locale,u))}return String(l)}function E(e,t){for(var n=[],r=2;r<arguments.length;r++)n[r-2]=arguments[r];var i=n[0],o=n[1],s=void 0===o?{}:o,l="string"==typeof i?new Date(i||0):i;try{return w(e,"time",t,s).format(l)}catch(u){e.onError(new a.pg("Error formatting time.",e.locale,u))}return String(l)}function k(e,t){for(var n=[],r=2;r<arguments.length;r++)n[r-2]=arguments[r];var i=n[0],o=n[1],s=n[2],l=void 0===s?{}:s,u=e.timeZone,c=e.locale,f=e.onError,h=(0,d.J9)(l,_,u?{timeZone:u}:{});try{return t(c,h).formatRange(i,o)}catch(p){f(new a.pg("Error formatting date time range.",e.locale,p))}return String(i)}function T(e,t){for(var n=[],r=2;r<arguments.length;r++)n[r-2]=arguments[r];var i=n[0],o=n[1],s=void 0===o?{}:o,l="string"==typeof i?new Date(i||0):i;try{return w(e,"date",t,s).formatToParts(l)}catch(u){e.onError(new a.pg("Error formatting date.",e.locale,u))}return[]}function x(e,t){for(var n=[],r=2;r<arguments.length;r++)n[r-2]=arguments[r];var i=n[0],o=n[1],s=void 0===o?{}:o,l="string"==typeof i?new Date(i||0):i;try{return w(e,"time",t,s).formatToParts(l)}catch(u){e.onError(new a.pg("Error formatting time.",e.locale,u))}return[]}var O=["type"];function C(e,t,n,r){var i=e.locale,o=e.onError;void 0===r&&(r={}),Intl.PluralRules||o(new v.IF('Intl.PluralRules is not available in this environment.\nTry polyfilling it using "@formatjs/intl-pluralrules"\n',v.O4.MISSING_INTL_API));var s=(0,d.J9)(r,O);try{return t(i,s).select(n)}catch(l){o(new a.pg("Error formatting plural.",i,l))}return"other"}var N=["type","style"],I=Date.now();function D(e,t,n,r){void 0===r&&(r={});var i=A(e,t,n,r).reduce((function(e,t){var n=t.value;return"string"!=typeof n?e.push(n):"string"==typeof e[e.length-1]?e[e.length-1]+=n:e.push(n),e}),[]);return 1===i.length?i[0]:0===i.length?"":i}function A(e,t,n,i){var o=e.locale,s=e.onError;void 0===i&&(i={}),Intl.ListFormat||s(new v.IF('Intl.ListFormat is not available in this environment.\nTry polyfilling it using "@formatjs/intl-listformat"\n',v.O4.MISSING_INTL_API));var l=(0,d.J9)(i,N);try{var u={},c=n.map((function(e,t){if("object"==typeof e){var n=function(e){return"".concat(I,"_").concat(e,"_").concat(I)}(t);return u[n]=e,n}return String(e)}));return t(o,l).formatToParts(c).map((function(e){return"literal"===e.type?e:(0,r().__assign)((0,r().__assign)({},e),{value:u[e.value]||e.value})}))}catch(f){s(new a.pg("Error formatting list.",o,f))}return n}var P=["style","type","fallback","languageDisplay"];function L(e,t,n,r){var i=e.locale,o=e.onError;Intl.DisplayNames||o(new v.IF('Intl.DisplayNames is not available in this environment.\nTry polyfilling it using "@formatjs/intl-displaynames"\n',v.O4.MISSING_INTL_API));var s=(0,d.J9)(r,P);try{return t(i,s).of(n)}catch(l){o(new a.pg("Error formatting display name.",i,l))}}function M(e){var t;e.onWarn&&e.defaultRichTextElements&&"string"==typeof((t=e.messages||{})?t[Object.keys(t)[0]]:void 0)&&e.onWarn('[@formatjs/intl] "defaultRichTextElements" was specified but "message" was not pre-compiled. \nPlease consider using "@formatjs/cli" to pre-compile your messages for performance.\nFor more details see https://formatjs.io/docs/getting-started/message-distribution')}var R,F=n(296540),U=n(235953);function j(e){return"function"==typeof e}function B(e){return e?Object.keys(e).reduce((function(t,n){var r=e[n];return t[n]=j(r)?(0,U.yU)(r):r,t}),{}):e}!function(e){e[e.literal=0]="literal",e[e.object=1]="object"}(R||(R={}));var z=function(e,t,n,i){for(var o=[],a=4;a<arguments.length;a++)o[a-4]=arguments[a];var s=B(i),l=f.apply(void 0,(0,r().__spreadArray)([e,t,n,s],o,!1));return Array.isArray(l)?F.Children.toArray(l):l},H=function(e,t){var n=e.defaultRichTextElements,i=(0,r().__rest)(e,["defaultRichTextElements"]),o=B(n),s=function(e,t){var n=(0,d.GT)(t),i=(0,r().__assign)((0,r().__assign)({},d.JF),e),o=i.locale,s=i.defaultLocale,l=i.onError;return o?!Intl.NumberFormat.supportedLocalesOf(o).length&&l?l(new a.hr('Missing locale data for locale: "'.concat(o,'" in Intl.NumberFormat. Using default locale: "').concat(s,'" as fallback. See https://formatjs.io/docs/react-intl#runtime-requirements for more details'))):!Intl.DateTimeFormat.supportedLocalesOf(o).length&&l&&l(new a.hr('Missing locale data for locale: "'.concat(o,'" in Intl.DateTimeFormat. Using default locale: "').concat(s,'" as fallback. See https://formatjs.io/docs/react-intl#runtime-requirements for more details'))):(l&&l(new a.uo('"locale" was not configured, using "'.concat(s,'" as fallback. See https://formatjs.io/docs/react-intl/api#intlshape for more details'))),i.locale=i.defaultLocale||"en"),M(i),(0,r().__assign)((0,r().__assign)({},i),{formatters:n,formatNumber:m.bind(null,i,n.getNumberFormat),formatNumberToParts:g.bind(null,i,n.getNumberFormat),formatRelativeTime:b.bind(null,i,n.getRelativeTimeFormat),formatDate:S.bind(null,i,n.getDateTimeFormat),formatDateToParts:T.bind(null,i,n.getDateTimeFormat),formatTime:E.bind(null,i,n.getDateTimeFormat),formatDateTimeRange:k.bind(null,i,n.getDateTimeFormat),formatTimeToParts:x.bind(null,i,n.getDateTimeFormat),formatPlural:C.bind(null,i,n.getPluralRules),formatMessage:f.bind(null,i,n),$t:f.bind(null,i,n),formatList:D.bind(null,i,n.getListFormat),formatListToParts:A.bind(null,i,n.getListFormat),formatDisplayName:L.bind(null,i,n.getDisplayNames)})}((0,r().__assign)((0,r().__assign)((0,r().__assign)({},U.JF),i),{defaultRichTextElements:o}),t),l={locale:s.locale,timeZone:s.timeZone,fallbackOnEmptyString:s.fallbackOnEmptyString,formats:s.formats,defaultLocale:s.defaultLocale,defaultFormats:s.defaultFormats,messages:s.messages,onError:s.onError,defaultRichTextElements:o};return(0,r().__assign)((0,r().__assign)({},s),{formatMessage:z.bind(null,l,s.formatters),$t:z.bind(null,l,s.formatters)})}},360895:(e,t,n)=>{var r=n(269302)((function(e,t,r){return n(420085)(e,n(399374)(t)||0,r)}));e.exports=r},361270:function(e,t,n){var r;e=n.nmd(e),function(i){t&&t.nodeType,e&&e.nodeType;var o="object"==typeof n.g&&n.g;o.global!==o&&o.window!==o&&o.self;var a,s=2147483647,l=36,u=1,c=26,f=38,d=700,h=72,p=128,m="-",g=/^xn--/,v=/[^\x20-\x7E]/,y=/[\x2E\u3002\uFF0E\uFF61]/g,b={overflow:"Overflow: input needs wider integers to process","not-basic":"Illegal input >= 0x80 (not a basic code point)","invalid-input":"Invalid input"},_=l-u,w=Math.floor,S=String.fromCharCode;function E(e){throw RangeError(b[e])}function k(e,t){for(var n=e.length,r=[];n--;)r[n]=t(e[n]);return r}function T(e,t){var n=e.split("@"),r="";return n.length>1&&(r=n[0]+"@",e=n[1]),r+k((e=e.replace(y,".")).split("."),t).join(".")}function x(e){for(var t,n,r=[],i=0,o=e.length;i<o;)(t=e.charCodeAt(i++))>=55296&&t<=56319&&i<o?56320==(64512&(n=e.charCodeAt(i++)))?r.push(((1023&t)<<10)+(1023&n)+65536):(r.push(t),i--):r.push(t);return r}function O(e){return k(e,(function(e){var t="";return e>65535&&(t+=S((e-=65536)>>>10&1023|55296),e=56320|1023&e),t+=S(e)})).join("")}function C(e,t){return e+22+75*(e<26)-((0!=t)<<5)}function N(e,t,n){var r=0;for(e=n?w(e/d):e>>1,e+=w(e/t);e>_*c>>1;r+=l)e=w(e/_);return w(r+(_+1)*e/(e+f))}function I(e){var t,n,r,i,o,a,f,d,g,v,y,b=[],_=e.length,S=0,k=p,T=h;for((n=e.lastIndexOf(m))<0&&(n=0),r=0;r<n;++r)e.charCodeAt(r)>=128&&E("not-basic"),b.push(e.charCodeAt(r));for(i=n>0?n+1:0;i<_;){for(o=S,a=1,f=l;i>=_&&E("invalid-input"),((d=(y=e.charCodeAt(i++))-48<10?y-22:y-65<26?y-65:y-97<26?y-97:l)>=l||d>w((s-S)/a))&&E("overflow"),S+=d*a,!(d<(g=f<=T?u:f>=T+c?c:f-T));f+=l)a>w(s/(v=l-g))&&E("overflow"),a*=v;T=N(S-o,t=b.length+1,0==o),w(S/t)>s-k&&E("overflow"),k+=w(S/t),S%=t,b.splice(S++,0,k)}return O(b)}function D(e){var t,n,r,i,o,a,f,d,g,v,y,b,_,k,T,O=[];for(b=(e=x(e)).length,t=p,n=0,o=h,a=0;a<b;++a)(y=e[a])<128&&O.push(S(y));for(r=i=O.length,i&&O.push(m);r<b;){for(f=s,a=0;a<b;++a)(y=e[a])>=t&&y<f&&(f=y);for(f-t>w((s-n)/(_=r+1))&&E("overflow"),n+=(f-t)*_,t=f,a=0;a<b;++a)if((y=e[a])<t&&++n>s&&E("overflow"),y==t){for(d=n,g=l;!(d<(v=g<=o?u:g>=o+c?c:g-o));g+=l)T=d-v,k=l-v,O.push(S(C(v+T%k,0))),d=w(T/k);O.push(S(C(d,0))),o=N(n,_,r==i),n=0,++r}++n,++t}return O.join("")}a={version:"1.3.2",ucs2:{decode:x,encode:O},decode:I,encode:D,toASCII:function(e){return T(e,(function(e){return v.test(e)?"xn--"+D(e):e}))},toUnicode:function(e){return T(e,(function(e){return g.test(e)?I(e.slice(4).toLowerCase()):e}))}},void 0===(r=function(){return a}.call(t,n,t,e))||(e.exports=r)}()},365694:(e,t,n)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),t._resolveDeltasResponse=void 0;const r=()=>n(636978);function i(e,t){null==e||e.forEach((e=>{delete t[e]}))}t._resolveDeltasResponse=function(e,t){const n=(0,r()._typedJsonParse)(t,"checksum","DeltasEvaluationResponse");if(!n)return{hadBadDeltaChecksum:!0};const o=function(e){const t=e;return i(e.deleted_gates,t.feature_gates),delete t.deleted_gates,i(e.deleted_configs,t.dynamic_configs),delete t.deleted_configs,i(e.deleted_layers,t.layer_configs),delete t.deleted_layers,t}(function(e,t){return Object.assign(Object.assign(Object.assign({},e),t),{feature_gates:Object.assign(Object.assign({},e.feature_gates),t.feature_gates),layer_configs:Object.assign(Object.assign({},e.layer_configs),t.layer_configs),dynamic_configs:Object.assign(Object.assign({},e.dynamic_configs),t.dynamic_configs)})}(e,n)),a=(0,r()._DJB2Object)({feature_gates:o.feature_gates,dynamic_configs:o.dynamic_configs,layer_configs:o.layer_configs},2);return a===n.checksumV2?JSON.stringify(o):{hadBadDeltaChecksum:!0,badChecksum:a,badMergedConfigs:o,badFullResponse:n.deltas_full_response}}},385638:function(e,t,n){"use strict";var r=this&&this.__awaiter||function(e,t,n,r){return new(n||(n=Promise))((function(i,o){function a(e){try{l(r.next(e))}catch(t){o(t)}}function s(e){try{l(r.throw(e))}catch(t){o(t)}}function l(e){var t;e.done?i(e.value):(t=e.value,t instanceof n?t:new n((function(e){e(t)}))).then(a,s)}l((r=r.apply(e,t||[])).next())}))};Object.defineProperty(t,"__esModule",{value:!0}),t._makeDataAdapterResult=t.DataAdapterCore=void 0;const i=()=>n(668024),o=()=>n(81570),a=()=>n(601638),s=()=>n(953705);function l(e,t,n,r){return{source:e,data:t,receivedAt:Date.now(),stableID:n,fullUserHash:(0,o()._getFullUserHash)(r)}}t.DataAdapterCore=class{constructor(e,t){this._adapterName=e,this._cacheSuffix=t,this._options=null,this._sdkKey=null,this._lastModifiedStoreKey=`statsig.last_modified_time.${t}`,this._inMemoryCache=new u}attach(e,t,n){this._sdkKey=e,this._options=t}getDataSync(e){const t=e&&(0,o()._normalizeUser)(e,this._options),n=this._getCacheKey(t),r=this._inMemoryCache.get(n,t);if(r)return r;const i=this._loadFromCache(n);return i?(this._inMemoryCache.add(n,i),this._inMemoryCache.get(n,t)):null}setData(e,t){const n=t&&(0,o()._normalizeUser)(t,this._options),r=this._getCacheKey(n);this._inMemoryCache.add(r,l("Bootstrap",e,null,n))}_getDataAsyncImpl(e,t,n){return r(this,void 0,void 0,(function*(){a().Storage.isReady()||(yield a().Storage.isReadyResolver());const r=null!=e?e:this.getDataSync(t),o=[this._fetchAndPrepFromNetwork(r,t,n)];return(null==n?void 0:n.timeoutMs)&&o.push(new Promise((e=>setTimeout(e,n.timeoutMs))).then((()=>(i().Log.debug("Fetching latest value timed out"),null)))),yield Promise.race(o)}))}_prefetchDataImpl(e,t){return r(this,void 0,void 0,(function*(){const n=e&&(0,o()._normalizeUser)(e,this._options),r=this._getCacheKey(n),i=yield this._getDataAsyncImpl(null,n,t);i&&this._inMemoryCache.add(r,Object.assign(Object.assign({},i),{source:"Prefetch"}))}))}_fetchAndPrepFromNetwork(e,t,o){var a;return r(this,void 0,void 0,(function*(){const r=null!==(a=null==e?void 0:e.data)&&void 0!==a?a:null,u=null!=e&&this._isCachedResultValidFor204(e,t),c=yield this._fetchFromNetwork(r,t,o,u);if(!c)return i().Log.debug("No response returned for latest value"),null;const f=(0,s()._typedJsonParse)(c,"has_updates","Response"),d=this._getSdkKey(),h=n(32626).StableID.get(d);let p=null;if(!0===(null==f?void 0:f.has_updates))p=l("Network",c,h,t);else{if(!r||!1!==(null==f?void 0:f.has_updates))return null;p=l("NetworkNotModified",r,h,t)}const m=this._getCacheKey(t);return this._inMemoryCache.add(m,p),this._writeToCache(m,p),p}))}_getSdkKey(){return null!=this._sdkKey?this._sdkKey:(i().Log.error(`${this._adapterName} is not attached to a Client`),"")}_loadFromCache(e){var t;const n=null===(t=a().Storage.getItem)||void 0===t?void 0:t.call(a().Storage,e);if(null==n)return null;const r=(0,s()._typedJsonParse)(n,"source","Cached Result");return r?Object.assign(Object.assign({},r),{source:"Cache"}):null}_writeToCache(e,t){a().Storage.setItem(e,JSON.stringify(t)),this._runLocalStorageCacheEviction(e)}_runLocalStorageCacheEviction(e){var t;const n=null!==(t=(0,a()._getObjectFromStorage)(this._lastModifiedStoreKey))&&void 0!==t?t:{};n[e]=Date.now();const r=c(n,10);r&&(delete n[r],a().Storage.removeItem(r)),(0,a()._setObjectInStorage)(this._lastModifiedStoreKey,n)}},t._makeDataAdapterResult=l;class u{constructor(){this._data={}}get(e,t){var n;const r=this._data[e],o=null==r?void 0:r.stableID,a=null===(n=null==t?void 0:t.customIDs)||void 0===n?void 0:n.stableID;return a&&o&&a!==o?(i().Log.warn("'StatsigUser.customIDs.stableID' mismatch"),null):r}add(e,t){const n=c(this._data,9);n&&delete this._data[n],this._data[e]=t}merge(e){this._data=Object.assign(Object.assign({},this._data),e)}}function c(e,t){const n=Object.keys(e);return n.length<=t?null:n.reduce(((t,n)=>{const r=e[t],i=e[n];return"object"==typeof r&&"object"==typeof i?i.receivedAt<r.receivedAt?n:t:i<r?n:t}))}},392151:e=>{var t={utf8:{stringToBytes:function(e){return t.bin.stringToBytes(unescape(encodeURIComponent(e)))},bytesToString:function(e){return decodeURIComponent(escape(t.bin.bytesToString(e)))}},bin:{stringToBytes:function(e){for(var t=[],n=0;n<e.length;n++)t.push(255&e.charCodeAt(n));return t},bytesToString:function(e){for(var t=[],n=0;n<e.length;n++)t.push(String.fromCharCode(e[n]));return t.join("")}}};e.exports=t},407350:(e,t,n)=>{e.exports=function(e,t,r){var i=!0,o=!0;if("function"!=typeof e)throw new TypeError("Expected a function");return n(223805)(r)&&(i="leading"in r?!!r.leading:i,o="trailing"in r?!!r.trailing:o),n(738221)(e,t,{leading:i,maxWait:t,trailing:o})}},408666:(e,t,n)=>{"use strict";n.d(t,{A:()=>u});var r=()=>n(331635),i=n(296540),o=n(327025),a=n(235953);function s(e){var t=(0,o.A)(),n=t.formatMessage,r=t.textComponent,a=void 0===r?i.Fragment:r,s=e.id,l=e.description,u=e.defaultMessage,c=e.values,f=e.children,d=e.tagName,h=void 0===d?a:d,p=n({id:s,description:l,defaultMessage:u},c,{ignoreTag:e.ignoreTag});return"function"==typeof f?f(Array.isArray(p)?p:[p]):h?i.createElement(h,null,i.Children.toArray(p)):i.createElement(i.Fragment,null,p)}s.displayName="FormattedMessage";var l=i.memo(s,(function(e,t){var n=e.values,i=(0,r().__rest)(e,["values"]),o=t.values,s=(0,r().__rest)(t,["values"]);return(0,a.bN)(o,n)&&(0,a.bN)(i,s)}));l.displayName="MemoizedFormattedMessage";const u=l},412215:(e,t,n)=>{var r,i;!function(o){if(void 0===(i="function"==typeof(r=o)?r.call(t,n,t,e):r)||(e.exports=i),!0,e.exports=o(),!!0){var a=window.Cookies,s=window.Cookies=o();s.noConflict=function(){return window.Cookies=a,s}}}((function(){function e(){for(var e=0,t={};e<arguments.length;e++){var n=arguments[e];for(var r in n)t[r]=n[r]}return t}function t(e){return e.replace(/(%[0-9A-Z]{2})+/g,decodeURIComponent)}return function n(r){function i(){}function o(t,n,o){if("undefined"!=typeof document){"number"==typeof(o=e({path:"/"},i.defaults,o)).expires&&(o.expires=new Date(1*new Date+864e5*o.expires)),o.expires=o.expires?o.expires.toUTCString():"";try{var a=JSON.stringify(n);/^[\{\[]/.test(a)&&(n=a)}catch(u){}n=r.write?r.write(n,t):encodeURIComponent(String(n)).replace(/%(23|24|26|2B|3A|3C|3E|3D|2F|3F|40|5B|5D|5E|60|7B|7D|7C)/g,decodeURIComponent),t=encodeURIComponent(String(t)).replace(/%(23|24|26|2B|5E|60|7C)/g,decodeURIComponent).replace(/[\(\)]/g,escape);var s="";for(var l in o)o[l]&&(s+="; "+l,!0!==o[l]&&(s+="="+o[l].split(";")[0]));return document.cookie=t+"="+n+s}}function a(e,n){if("undefined"!=typeof document){for(var i={},o=document.cookie?document.cookie.split("; "):[],a=0;a<o.length;a++){var s=o[a].split("="),l=s.slice(1).join("=");n||'"'!==l.charAt(0)||(l=l.slice(1,-1));try{var u=t(s[0]);if(l=(r.read||r)(l,u)||t(l),n)try{l=JSON.parse(l)}catch(c){}if(i[u]=l,e===u)break}catch(c){}}return e?i[e]:i}}return i.set=o,i.get=function(e){return a(e,!1)},i.getJSON=function(e){return a(e,!0)},i.remove=function(t,n){o(t,"",e(n,{expires:-1}))},i.defaults={},i.withConverter=n,i}((function(){}))}))},435370:(e,t,n)=>{"use strict";var r=n(326198);e.exports=function(e,t,n){for(var i=0,o=arguments.length>2?n:r(t),a=new e(o);o>i;)a[i]=t[i++];return a}},437628:(e,t,n)=>{"use strict";var r=n(326198);e.exports=function(e,t){for(var n=r(e),i=new t(n),o=0;o<n;o++)i[o]=e[n-o-1];return i}},439928:(e,t,n)=>{"use strict";var r=n(326198),i=n(991291),o=RangeError;e.exports=function(e,t,n,a){var s=r(e),l=i(n),u=l<0?s+l:l;if(u>=s||u<0)throw new o("Incorrect index");for(var c=new t(s),f=0;f<s;f++)c[f]=f===u?a:e[f];return c}},440961:(e,t,n)=>{"use strict";!function e(){if("undefined"!=typeof __REACT_DEVTOOLS_GLOBAL_HOOK__&&"function"==typeof __REACT_DEVTOOLS_GLOBAL_HOOK__.checkDCE)try{__REACT_DEVTOOLS_GLOBAL_HOOK__.checkDCE(e)}catch(t){console.error(t)}}(),e.exports=n(822551)},444732:(e,t,n)=>{"use strict";var r=n(794644),i=n(179504),o=n(479306),a=n(435370),s=r.aTypedArray,l=r.getTypedArrayConstructor,u=r.exportTypedArrayMethod,c=i(r.TypedArrayPrototype.sort);u("toSorted",(function(e){void 0!==e&&o(e);var t=s(this),n=a(l(t),t);return c(n,e)}))},454759:(e,t)=>{"use strict";function n(e){return e.setUTCHours(0,0,0,0),e.getTime()}Object.defineProperty(t,"__esModule",{value:!0}),t.default={compareNumbers(e,t,n){if(null==e||null==t)return!1;const r=Number(e),i=Number(t);if(isNaN(r)||isNaN(i))return!1;switch(n){case"gt":return e>t;case"gte":return e>=t;case"lt":return e<t;case"lte":return e<=t;default:return!1}},compareVersions(e,t,n){if(null==e||null==t)return!1;let r=String(e),i=String(t);const o=e=>{const t=e.indexOf("-");return-1!==t?e.substring(0,t):e};r=o(r),i=o(i);const a=((e,t)=>{const n=e.split(".").map((e=>parseInt(e))),r=t.split(".").map((e=>parseInt(e)));let i=0;for(;i<Math.max(n.length,r.length);){const e=i<n.length?n[i]:0,t=i<r.length?r[i]:0;if(e<t)return-1;if(e>t)return 1;i++}return 0})(r,i);switch(n){case"version_gt":return a>0;case"version_gte":return a>=0;case"version_lt":return a<0;case"version_lte":return a<=0;case"version_eq":return 0===a;case"version_neq":return 0!==a;default:return!1}},compareStringInArray(e,t,n){if(!Array.isArray(t))return!1;const r="any_case_sensitive"!==n&&"none_case_sensitive"!==n,i=-1!==t.findIndex((t=>{const i=String(e),o=String(t),a=r?i.toLowerCase():i,s=r?o.toLowerCase():o;switch(n){case"any":case"none":case"any_case_sensitive":case"none_case_sensitive":return a===s;case"str_starts_with_any":return a.startsWith(s);case"str_ends_with_any":return a.endsWith(s);case"str_contains_any":case"str_contains_none":return a.includes(s);default:return!1}}));switch(n){case"none":case"none_case_sensitive":case"str_contains_none":return!i;default:return i}},compareStringWithRegEx(e,t){try{const n=String(e);if(n.length<1e3)return new RegExp(String(t)).test(n)}catch(n){}return!1},compareTime(e,t,r){if(null==e||null==t)return!1;try{let i=new Date(String(e));isNaN(i.getTime())&&(i=new Date(Number(e)));let o=new Date(String(t));isNaN(o.getTime())&&(o=new Date(Number(t)));const a=i.getTime(),s=o.getTime();if(isNaN(a)||isNaN(s))return!1;switch(r){case"before":return a<s;case"after":return a>s;case"on":return n(i)===n(o);default:return!1}}catch(i){return!1}},arrayHasValue(e,t){const n=new Set(e);for(let r=0;r<t.length;r++)if(n.has(t[r])||n.has(parseFloat(t[r])))return!0;return!1},arrayHasAllValues(e,t){const n=new Set(e);for(let r=0;r<t.length;r++)if(!n.has(t[r])&&!n.has(parseFloat(t[r])))return!1;return!0}}},474848:(e,t,n)=>{"use strict";e.exports=n(221020)},483918:(e,t,n)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),t._getSortedObject=t._DJB2Object=t._DJB2=void 0;t._DJB2=e=>{let t=0;for(let n=0;n<e.length;n++){t=(t<<5)-t+e.charCodeAt(n),t&=t}return String(t>>>0)};t._DJB2Object=(e,n)=>(0,t._DJB2)(JSON.stringify((0,t._getSortedObject)(e,n)));t._getSortedObject=(e,r)=>{if(null==e)return null;const i=Object.keys(e).sort(),o={};return i.forEach((i=>{const a=e[i];0!==r&&"object"===(0,n(690010)._typeOf)(a)?o[i]=(0,t._getSortedObject)(a,null!=r?r-1:r):o[i]=a})),o}},491134:(e,t,n)=>{"use strict";var r=n(794644),i=n(143839).findLastIndex,o=r.aTypedArray;(0,r.exportTypedArrayMethod)("findLastIndex",(function(e){return i(o(this),e,arguments.length>1?arguments[1]:void 0)}))},498731:(e,t)=>{"use strict";function n(e,t){var n=e.length;e.push(t);e:for(;0<n;){var r=n-1>>>1,i=e[r];if(!(0<o(i,t)))break e;e[r]=t,e[n]=i,n=r}}function r(e){return 0===e.length?null:e[0]}function i(e){if(0===e.length)return null;var t=e[0],n=e.pop();if(n!==t){e[0]=n;e:for(var r=0,i=e.length,a=i>>>1;r<a;){var s=2*(r+1)-1,l=e[s],u=s+1,c=e[u];if(0>o(l,n))u<i&&0>o(c,l)?(e[r]=c,e[u]=n,r=u):(e[r]=l,e[s]=n,r=s);else{if(!(u<i&&0>o(c,n)))break e;e[r]=c,e[u]=n,r=u}}}return t}function o(e,t){var n=e.sortIndex-t.sortIndex;return 0!==n?n:e.id-t.id}if("object"==typeof performance&&"function"==typeof performance.now){var a=performance;t.unstable_now=function(){return a.now()}}else{var s=Date,l=s.now();t.unstable_now=function(){return s.now()-l}}var u=[],c=[],f=1,d=null,h=3,p=!1,m=!1,g=!1,v="function"==typeof setTimeout?setTimeout:null,y="function"==typeof clearTimeout?clearTimeout:null,b="undefined"!=typeof setImmediate?setImmediate:null;function _(e){for(var t=r(c);null!==t;){if(null===t.callback)i(c);else{if(!(t.startTime<=e))break;i(c),t.sortIndex=t.expirationTime,n(u,t)}t=r(c)}}function w(e){if(g=!1,_(e),!m)if(null!==r(u))m=!0,P(S);else{var t=r(c);null!==t&&L(w,t.startTime-e)}}function S(e,n){m=!1,g&&(g=!1,y(x),x=-1),p=!0;var o=h;try{for(_(n),d=r(u);null!==d&&(!(d.expirationTime>n)||e&&!N());){var a=d.callback;if("function"==typeof a){d.callback=null,h=d.priorityLevel;var s=a(d.expirationTime<=n);n=t.unstable_now(),"function"==typeof s?d.callback=s:d===r(u)&&i(u),_(n)}else i(u);d=r(u)}if(null!==d)var l=!0;else{var f=r(c);null!==f&&L(w,f.startTime-n),l=!1}return l}finally{d=null,h=o,p=!1}}"undefined"!=typeof navigator&&void 0!==navigator.scheduling&&void 0!==navigator.scheduling.isInputPending&&navigator.scheduling.isInputPending.bind(navigator.scheduling);var E,k=!1,T=null,x=-1,O=5,C=-1;function N(){return!(t.unstable_now()-C<O)}function I(){if(null!==T){var e=t.unstable_now();C=e;var n=!0;try{n=T(!0,e)}finally{n?E():(k=!1,T=null)}}else k=!1}if("function"==typeof b)E=function(){b(I)};else if("undefined"!=typeof MessageChannel){var D=new MessageChannel,A=D.port2;D.port1.onmessage=I,E=function(){A.postMessage(null)}}else E=function(){v(I,0)};function P(e){T=e,k||(k=!0,E())}function L(e,n){x=v((function(){e(t.unstable_now())}),n)}t.unstable_IdlePriority=5,t.unstable_ImmediatePriority=1,t.unstable_LowPriority=4,t.unstable_NormalPriority=3,t.unstable_Profiling=null,t.unstable_UserBlockingPriority=2,t.unstable_cancelCallback=function(e){e.callback=null},t.unstable_continueExecution=function(){m||p||(m=!0,P(S))},t.unstable_forceFrameRate=function(e){0>e||125<e?console.error("forceFrameRate takes a positive int between 0 and 125, forcing frame rates higher than 125 fps is not supported"):O=0<e?Math.floor(1e3/e):5},t.unstable_getCurrentPriorityLevel=function(){return h},t.unstable_getFirstCallbackNode=function(){return r(u)},t.unstable_next=function(e){switch(h){case 1:case 2:case 3:var t=3;break;default:t=h}var n=h;h=t;try{return e()}finally{h=n}},t.unstable_pauseExecution=function(){},t.unstable_requestPaint=function(){},t.unstable_runWithPriority=function(e,t){switch(e){case 1:case 2:case 3:case 4:case 5:break;default:e=3}var n=h;h=e;try{return t()}finally{h=n}},t.unstable_scheduleCallback=function(e,i,o){var a=t.unstable_now();switch("object"==typeof o&&null!==o?o="number"==typeof(o=o.delay)&&0<o?a+o:a:o=a,e){case 1:var s=-1;break;case 2:s=250;break;case 5:s=1073741823;break;case 4:s=1e4;break;default:s=5e3}return e={id:f++,callback:i,priorityLevel:e,startTime:o,expirationTime:s=o+s,sortIndex:-1},o>a?(e.sortIndex=o,n(c,e),null===r(u)&&e===r(c)&&(g?(y(x),x=-1):g=!0,L(w,o-a))):(e.sortIndex=s,n(u,e),m||p||(m=!0,P(S))),e},t.unstable_shouldYield=N,t.unstable_wrapCallback=function(e){var t=h;return function(){var n=h;h=t;try{return e.apply(this,arguments)}finally{h=n}}}},512166:(e,t)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),t.DataAdapterCachePrefix=void 0,t.DataAdapterCachePrefix="statsig.cached"},512335:(e,t,n)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),t.Evaluator=void 0;const r=()=>n(766944),i=()=>n(454759),o=()=>n(320295);function a(e,t){var n,r,i;return"string"==typeof t&&"userid"!==t.toLowerCase()?null!==(r=null===(n=e.customIDs)||void 0===n?void 0:n[t])&&void 0!==r?r:null===(i=null==e?void 0:e.customIDs)||void 0===i?void 0:i[t.toLowerCase()]:e.userID}function s(e,t,n){var r,i;if(100===e.passPercentage)return!0;if(0===e.passPercentage)return!1;const o=l(n.salt+"."+(null!==(r=e.salt)&&void 0!==r?r:e.id)+"."+(null!==(i=a(t,e.idType))&&void 0!==i?i:""));return Number(o%BigInt(1e4))<100*e.passPercentage}function l(e){return(0,r().SHA256)(e).dataView().getBigUint64(0,!1)}function u(e){return null!=e&&"object"==typeof e}t.Evaluator=class{constructor(e){this._store=e}evaluateGate(e,t){const{spec:n,details:r}=this._getSpecAndDetails("gate",e);if(!n)return{evaluation:null,details:r};return{evaluation:(0,o().resultToGateEval)(n,this._evaluateSpec(n,t)),details:r}}evaluateConfig(e,t){const{spec:n,details:r}=this._getSpecAndDetails("config",e);if(!n)return{evaluation:null,details:r};return{evaluation:(0,o().resultToConfigEval)(n,this._evaluateSpec(n,t)),details:r}}evaluateLayer(e,t){var n;const{spec:r,details:i}=this._getSpecAndDetails("layer",e);if(!r)return{evaluation:null,details:i};const a=this._evaluateSpec(r,t),s=null!==(n=null==a?void 0:a.allocated_experiment_name)&&void 0!==n?n:"",l=this._store.getSpecAndSourceInfo("config",s).spec;return{evaluation:(0,o().resultToLayerEval)(r,l,a),details:i}}getParamStoreConfig(e){var t;const n=this._store.getParamStoreAndSourceInfo(e),r=this._getEvaluationDetails(n);return{config:null!==(t=n.paramStoreConfig)&&void 0!==t?t:null,details:r}}_getSpecAndDetails(e,t){const n=this._store.getSpecAndSourceInfo(e,t);return{details:this._getEvaluationDetails(n),spec:n.spec}}_getEvaluationDetails(e){const{source:t,lcut:n,receivedAt:r}=e;if("Uninitialized"===t||"NoValues"===t)return{reason:t};return{reason:`${t}:${null==("spec"in e?e.spec:e.paramStoreConfig)?"Unrecognized":"Recognized"}`,lcut:n,receivedAt:r}}_evaluateSpec(e,t){const n=u(e.defaultValue)?e.defaultValue:void 0;if(!e.enabled)return(0,o().makeEvalResult)({json_value:n,rule_id:"disabled"});const r=[];for(const i of e.rules){const a=this._evaluateRule(i,t);if(a.unsupported)return a;if(r.push(...a.secondary_exposures),!a.bool_value)continue;const l=this._evaluateDelegate(i.configDelegate,t,r);if(l)return l;const u=s(i,t,e);return(0,o().makeEvalResult)({rule_id:a.rule_id,bool_value:u,json_value:u?a.json_value:n,secondary_exposures:r,undelegated_secondary_exposures:r,is_experiment_group:a.is_experiment_group,group_name:a.group_name})}return(0,o().makeEvalResult)({json_value:n,secondary_exposures:r,undelegated_secondary_exposures:r,rule_id:"default"})}_evaluateRule(e,t){const n=[];let r=!0;for(const i of e.conditions){const e=this._evaluateCondition(i,t);if(e.unsupported)return e;n.push(...e.secondary_exposures),e.bool_value||(r=!1)}return(0,o().makeEvalResult)({rule_id:e.id,bool_value:r,json_value:u(e.returnValue)?e.returnValue:void 0,secondary_exposures:n,is_experiment_group:!0===e.isExperimentGroup,group_name:e.groupName})}_evaluateCondition(e,t){var n,r,s;let u=null,c=!1;const f=e.field,d=e.targetValue,h=e.idType,p=e.type;switch(p){case"public":return(0,o().makeEvalResult)({bool_value:!0});case"pass_gate":case"fail_gate":{const e=String(d),n=this._evaluateNestedGate(e,t);return(0,o().makeEvalResult)({bool_value:"fail_gate"===p?!n.bool_value:n.bool_value,secondary_exposures:n.secondary_exposures})}case"multi_pass_gate":case"multi_fail_gate":return this._evaluateMultiNestedGates(d,p,t);case"user_field":case"ip_based":case"ua_based":u=function(e,t){var n,r,i,o,a,s,l,u,c;if(null==t||"object"!=typeof e||null==e)return null;const f=e;return null!==(u=null!==(s=null!==(o=null!==(r=null!==(n=f[t])&&void 0!==n?n:f[t.toLowerCase()])&&void 0!==r?r:null===(i=null==e?void 0:e.custom)||void 0===i?void 0:i[t])&&void 0!==o?o:null===(a=null==e?void 0:e.custom)||void 0===a?void 0:a[t.toLowerCase()])&&void 0!==s?s:null===(l=null==e?void 0:e.privateAttributes)||void 0===l?void 0:l[t])&&void 0!==u?u:null===(c=null==e?void 0:e.privateAttributes)||void 0===c?void 0:c[t.toLowerCase()]}(t,f);break;case"environment_field":u=function(e,t){if(null==t)return null;return function(e,t){if(null==e)return;const n=t.toLowerCase(),r=Object.keys(e).find((e=>e.toLowerCase()===n));if(void 0===r)return;return e[r]}(e.statsigEnvironment,t)}(t,f);break;case"current_time":u=Date.now();break;case"user_bucket":{const i=l(String(null!==(r=null===(n=e.additionalValues)||void 0===n?void 0:n.salt)&&void 0!==r?r:"")+"."+(null!==(s=a(t,h))&&void 0!==s?s:""));u=Number(i%BigInt(1e3));break}case"unit_id":u=a(t,h);break;default:return(0,o().makeEvalResult)({unsupported:!0})}const m=e.operator;switch(m){case"gt":case"gte":case"lt":case"lte":c=i().default.compareNumbers(u,d,m);break;case"version_gt":case"version_gte":case"version_lt":case"version_lte":case"version_eq":case"version_neq":c=i().default.compareVersions(u,d,m);break;case"any":case"none":case"str_starts_with_any":case"str_ends_with_any":case"str_contains_any":case"str_contains_none":case"any_case_sensitive":case"none_case_sensitive":c=i().default.compareStringInArray(u,d,m);break;case"str_matches":c=i().default.compareStringWithRegEx(u,d);break;case"before":case"after":case"on":c=i().default.compareTime(u,d,m);break;case"eq":c=u==d;break;case"neq":c=u!=d;break;case"in_segment_list":case"not_in_segment_list":return(0,o().makeEvalResult)({unsupported:!0});case"array_contains_any":case"array_contains_none":{if(!Array.isArray(d)){c=!1;break}if(!Array.isArray(u)){c=!1;break}const e=i().default.arrayHasValue(u,d);c="array_contains_any"===m?e:!e;break}case"array_contains_all":case"not_array_contains_all":{if(!Array.isArray(d)){c=!1;break}if(!Array.isArray(u)){c=!1;break}const e=i().default.arrayHasAllValues(u,d);c="array_contains_all"===m?e:!e;break}}return(0,o().makeEvalResult)({bool_value:c})}_evaluateDelegate(e,t,n){if(!e)return null;const{spec:r}=this._store.getSpecAndSourceInfo("config",e);if(!r)return null;const i=this._evaluateSpec(r,t);return(0,o().makeEvalResult)(Object.assign(Object.assign({},i),{allocated_experiment_name:e,explicit_parameters:r.explicitParameters,secondary_exposures:n.concat(i.secondary_exposures),undelegated_secondary_exposures:n}))}_evaluateNestedGate(e,t){const n=[];let r=!1;const{spec:i}=this._store.getSpecAndSourceInfo("gate",e);if(i){const o=this._evaluateSpec(i,t);if(o.unsupported)return o;r=o.bool_value,n.push(...o.secondary_exposures),n.push({gate:e,gateValue:String(r),ruleID:o.rule_id})}return(0,o().makeEvalResult)({bool_value:r,secondary_exposures:n})}_evaluateMultiNestedGates(e,t,n){if(!Array.isArray(e))return(0,o().makeEvalResult)({unsupported:!0});const r="multi_pass_gate"===t,i=[];let a=!1;for(const s of e){if("string"!=typeof s)return(0,o().makeEvalResult)({unsupported:!0});const e=this._evaluateNestedGate(s,n);if(e.unsupported)return e;if(i.push(...e.secondary_exposures),r?!0===e.bool_value:!1===e.bool_value){a=!0;break}}return(0,o().makeEvalResult)({bool_value:a,secondary_exposures:i})}}},531234:(e,t,n)=>{"use strict";n.d(t,{F3:()=>d,GT:()=>f,J9:()=>s,JF:()=>l,MT:()=>u});var r=()=>n(331635),i=n(822015),o=n(754819),a=n(960709);function s(e,t,n){return void 0===n&&(n={}),t.reduce((function(t,r){return r in e?t[r]=e[r]:r in n&&(t[r]=n[r]),t}),{})}var l={formats:{},messages:{},timeZone:void 0,defaultLocale:"en",defaultFormats:{},fallbackOnEmptyString:!0,onError:function(e){0},onWarn:function(e){0}};function u(){return{dateTime:{},number:{},message:{},relativeTime:{},pluralRules:{},list:{},displayNames:{}}}function c(e){return{create:function(){return{get:function(t){return e[t]},set:function(t,n){e[t]=n}}}}}function f(e){void 0===e&&(e={dateTime:{},number:{},message:{},relativeTime:{},pluralRules:{},list:{},displayNames:{}});var t=Intl.RelativeTimeFormat,n=Intl.ListFormat,a=Intl.DisplayNames,s=(0,o.B)((function(){for(var e,t=[],n=0;n<arguments.length;n++)t[n]=arguments[n];return new((e=Intl.DateTimeFormat).bind.apply(e,(0,r().__spreadArray)([void 0],t,!1)))}),{cache:c(e.dateTime),strategy:o.W.variadic}),l=(0,o.B)((function(){for(var e,t=[],n=0;n<arguments.length;n++)t[n]=arguments[n];return new((e=Intl.NumberFormat).bind.apply(e,(0,r().__spreadArray)([void 0],t,!1)))}),{cache:c(e.number),strategy:o.W.variadic}),u=(0,o.B)((function(){for(var e,t=[],n=0;n<arguments.length;n++)t[n]=arguments[n];return new((e=Intl.PluralRules).bind.apply(e,(0,r().__spreadArray)([void 0],t,!1)))}),{cache:c(e.pluralRules),strategy:o.W.variadic});return{getDateTimeFormat:s,getNumberFormat:l,getMessageFormat:(0,o.B)((function(e,t,n,o){return new i.S(e,t,n,(0,r().__assign)({formatters:{getNumberFormat:l,getDateTimeFormat:s,getPluralRules:u}},o||{}))}),{cache:c(e.message),strategy:o.W.variadic}),getRelativeTimeFormat:(0,o.B)((function(){for(var e=[],n=0;n<arguments.length;n++)e[n]=arguments[n];return new(t.bind.apply(t,(0,r().__spreadArray)([void 0],e,!1)))}),{cache:c(e.relativeTime),strategy:o.W.variadic}),getPluralRules:u,getListFormat:(0,o.B)((function(){for(var e=[],t=0;t<arguments.length;t++)e[t]=arguments[t];return new(n.bind.apply(n,(0,r().__spreadArray)([void 0],e,!1)))}),{cache:c(e.list),strategy:o.W.variadic}),getDisplayNames:(0,o.B)((function(){for(var e=[],t=0;t<arguments.length;t++)e[t]=arguments[t];return new(a.bind.apply(a,(0,r().__spreadArray)([void 0],e,!1)))}),{cache:c(e.displayNames),strategy:o.W.variadic})}}function d(e,t,n,r){var i,o=e&&e[t];if(o&&(i=o[n]),i)return i;r(new a.OC("No ".concat(t," format named: ").concat(n)))}},537047:(e,t)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),t.getUUID=void 0,t.getUUID=function(){if("undefined"!=typeof crypto&&"function"==typeof crypto.randomUUID)return crypto.randomUUID();let e=(new Date).getTime(),t="undefined"!=typeof performance&&performance.now&&1e3*performance.now()||0;return`xxxxxxxx-xxxx-4xxx-${"89ab"[Math.floor(4*Math.random())]}xxx-xxxxxxxxxxxx`.replace(/[xy]/g,(n=>{let r=16*Math.random();return e>0?(r=(e+r)%16|0,e=Math.floor(e/16)):(r=(t+r)%16|0,t=Math.floor(t/16)),("x"===n?r:7&r|8).toString(16)}))}},539950:function(e,t,n){"use strict";var r=this&&this.__awaiter||function(e,t,n,r){return new(n||(n=Promise))((function(i,o){function a(e){try{l(r.next(e))}catch(t){o(t)}}function s(e){try{l(r.throw(e))}catch(t){o(t)}}function l(e){var t;e.done?i(e.value):(t=e.value,t instanceof n?t:new n((function(e){e(t)}))).then(a,s)}l((r=r.apply(e,t||[])).next())}))};Object.defineProperty(t,"__esModule",{value:!0}),t.LocalOverrideAdapter=void 0;const i=()=>n(636978),o="LocalOverride:Recognized";t.LocalOverrideAdapter=class{constructor(e){this._overrides={gate:{},dynamicConfig:{},experiment:{},layer:{}},this._sdkKey=null!=e?e:null}_getLocalOverridesStorageKey(e){return`statsig.local-overrides.${(0,i()._getStorageKey)(e)}`}loadFromStorage(){return r(this,void 0,void 0,(function*(){if(null==this._sdkKey)return;i().Storage.isReady()||(yield i().Storage.isReadyResolver());const e=this._getLocalOverridesStorageKey(this._sdkKey),t=i().Storage.getItem(e),n=t?(0,i()._typedJsonParse)(t,"gate","LocalOverrideAdapter overrides"):null,r=this._hasInMemoryOverrides();var o,a;n&&(this._overrides=r?(o=n,a=this._overrides,{gate:Object.assign({},o.gate,a.gate),dynamicConfig:Object.assign({},o.dynamicConfig,a.dynamicConfig),experiment:Object.assign({},o.experiment,a.experiment),layer:Object.assign({},o.layer,a.layer)}):n),r&&this._saveOverridesToStorage()}))}_saveOverridesToStorage(){if(null==this._sdkKey||!i().Storage.isReady())return;const e=this._getLocalOverridesStorageKey(this._sdkKey);i().Storage.setItem(e,JSON.stringify(this._overrides))}overrideGate(e,t){this._overrides.gate[e]=t,this._overrides.gate[(0,i()._DJB2)(e)]=t,this._saveOverridesToStorage()}_warnIfStorageNotReady(){i().Storage.isReady()||i().Log.warn("Storage is not ready. Override removal may not persist.")}removeGateOverride(e){this._warnIfStorageNotReady(),delete this._overrides.gate[e],delete this._overrides.gate[(0,i()._DJB2)(e)],this._saveOverridesToStorage()}getGateOverride(e,t){var n;const r=null!==(n=this._overrides.gate[e.name])&&void 0!==n?n:this._overrides.gate[(0,i()._DJB2)(e.name)];return null==r?null:Object.assign(Object.assign({},e),{value:r,details:Object.assign(Object.assign({},e.details),{reason:o})})}overrideDynamicConfig(e,t){this._overrides.dynamicConfig[e]=t,this._overrides.dynamicConfig[(0,i()._DJB2)(e)]=t,this._saveOverridesToStorage()}removeDynamicConfigOverride(e){this._warnIfStorageNotReady(),delete this._overrides.dynamicConfig[e],delete this._overrides.dynamicConfig[(0,i()._DJB2)(e)],this._saveOverridesToStorage()}getDynamicConfigOverride(e,t){return this._getConfigOverride(e,this._overrides.dynamicConfig)}overrideExperiment(e,t){this._overrides.experiment[e]=t,this._overrides.experiment[(0,i()._DJB2)(e)]=t,this._saveOverridesToStorage()}removeExperimentOverride(e){this._warnIfStorageNotReady(),delete this._overrides.experiment[e],delete this._overrides.experiment[(0,i()._DJB2)(e)],this._saveOverridesToStorage()}getExperimentOverride(e,t){return this._getConfigOverride(e,this._overrides.experiment)}overrideLayer(e,t){this._overrides.layer[e]=t,this._overrides.layer[(0,i()._DJB2)(e)]=t,this._saveOverridesToStorage()}removeLayerOverride(e){this._warnIfStorageNotReady(),delete this._overrides.layer[e],delete this._overrides.layer[(0,i()._DJB2)(e)],this._saveOverridesToStorage()}getAllOverrides(){return JSON.parse(JSON.stringify(this._overrides))}removeAllOverrides(){this._warnIfStorageNotReady(),this._overrides={gate:{},dynamicConfig:{},experiment:{},layer:{}},this._saveOverridesToStorage()}getLayerOverride(e,t){var n;const r=null!==(n=this._overrides.layer[e.name])&&void 0!==n?n:this._overrides.layer[(0,i()._DJB2)(e.name)];return null==r?null:Object.assign(Object.assign({},e),{__value:r,get:(0,i()._makeTypedGet)(e.name,r),details:Object.assign(Object.assign({},e.details),{reason:o})})}_getConfigOverride(e,t){var n;const r=null!==(n=t[e.name])&&void 0!==n?n:t[(0,i()._DJB2)(e.name)];return null==r?null:Object.assign(Object.assign({},e),{value:r,get:(0,i()._makeTypedGet)(e.name,r),details:Object.assign(Object.assign({},e.details),{reason:o})})}_hasInMemoryOverrides(){return Object.keys(this._overrides.gate).length>0||Object.keys(this._overrides.dynamicConfig).length>0||Object.keys(this._overrides.experiment).length>0||Object.keys(this._overrides.layer).length>0}}},540343:function(e,t){var n,r,i;!function(o,a){"use strict";r=[],void 0===(i="function"==typeof(n=function(){function e(e){return!isNaN(parseFloat(e))&&isFinite(e)}function t(e){return e.charAt(0).toUpperCase()+e.substring(1)}function n(e){return function(){return this[e]}}var r=["isConstructor","isEval","isNative","isToplevel"],i=["columnNumber","lineNumber"],o=["fileName","functionName","source"],a=["args"],s=["evalOrigin"],l=r.concat(i,o,a,s);function u(e){if(e)for(var n=0;n<l.length;n++)void 0!==e[l[n]]&&this["set"+t(l[n])](e[l[n]])}u.prototype={getArgs:function(){return this.args},setArgs:function(e){if("[object Array]"!==Object.prototype.toString.call(e))throw new TypeError("Args must be an Array");this.args=e},getEvalOrigin:function(){return this.evalOrigin},setEvalOrigin:function(e){if(e instanceof u)this.evalOrigin=e;else{if(!(e instanceof Object))throw new TypeError("Eval Origin must be an Object or StackFrame");this.evalOrigin=new u(e)}},toString:function(){var e=this.getFileName()||"",t=this.getLineNumber()||"",n=this.getColumnNumber()||"",r=this.getFunctionName()||"";return this.getIsEval()?e?"[eval] ("+e+":"+t+":"+n+")":"[eval]:"+t+":"+n:r?r+" ("+e+":"+t+":"+n+")":e+":"+t+":"+n}},u.fromString=function(e){var t=e.indexOf("("),n=e.lastIndexOf(")"),r=e.substring(0,t),i=e.substring(t+1,n).split(","),o=e.substring(n+1);if(0===o.indexOf("@"))var a=/@(.+?)(?::(\d+))?(?::(\d+))?$/.exec(o,""),s=a[1],l=a[2],c=a[3];return new u({functionName:r,args:i||void 0,fileName:s,lineNumber:l||void 0,columnNumber:c||void 0})};for(var c=0;c<r.length;c++)u.prototype["get"+t(r[c])]=n(r[c]),u.prototype["set"+t(r[c])]=function(e){return function(t){this[e]=Boolean(t)}}(r[c]);for(var f=0;f<i.length;f++)u.prototype["get"+t(i[f])]=n(i[f]),u.prototype["set"+t(i[f])]=function(t){return function(n){if(!e(n))throw new TypeError(t+" must be a Number");this[t]=Number(n)}}(i[f]);for(var d=0;d<o.length;d++)u.prototype["get"+t(o[d])]=n(o[d]),u.prototype["set"+t(o[d])]=function(e){return function(t){this[e]=String(t)}}(o[d]);return u})?n.apply(t,r):n)||(e.exports=i)}()},540346:e=>{e.exports=function(e){return null!=e&&"object"==typeof e}},543194:(e,t)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0})},553812:(e,t,n)=>{e.exports=function(e){return!0===e||!1===e||n(540346)(e)&&"[object Boolean]"==n(472552)(e)}},555169:(e,t,n)=>{"use strict";var r=n(903238),i=TypeError;e.exports=function(e){if(r(e))throw new i("ArrayBuffer is detached");return e}},563528:function(e,t,n){"use strict";var r=this&&this.__createBinding||(Object.create?function(e,t,n,r){void 0===r&&(r=n);var i=Object.getOwnPropertyDescriptor(t,n);i&&!("get"in i?!t.__esModule:i.writable||i.configurable)||(i={enumerable:!0,get:function(){return t[n]}}),Object.defineProperty(e,r,i)}:function(e,t,n,r){void 0===r&&(r=n),e[r]=t[n]}),i=this&&this.__exportStar||function(e,t){for(var n in e)"default"===n||Object.prototype.hasOwnProperty.call(t,n)||r(t,e,n)};Object.defineProperty(t,"__esModule",{value:!0}),i(n(908128),t)},587155:(e,t,n)=>{"use strict";n.d(t,{Kq:()=>o,ob:()=>a});var r=n(296540);n(304146);var i="undefined"==typeof window||window.__REACT_INTL_BYPASS_GLOBAL_CONTEXT__?r.createContext(null):window.__REACT_INTL_CONTEXT__||(window.__REACT_INTL_CONTEXT__=r.createContext(null)),o=(i.Consumer,i.Provider),a=i},587206:e=>{function t(e){return!!e.constructor&&"function"==typeof e.constructor.isBuffer&&e.constructor.isBuffer(e)}e.exports=function(e){return null!=e&&(t(e)||function(e){return"function"==typeof e.readFloatLE&&"function"==typeof e.slice&&t(e.slice(0,0))}(e)||!!e._isBuffer)}},590179:(e,t,n)=>{var r=n(538816)((function(e,t){var r={};if(null==e)return r;var i=!1;t=n(634932)(t,(function(t){return t=n(831769)(t,e),i||(i=t.length>1),t})),n(921791)(e,n(483349)(e),r),i&&(r=n(509999)(r,7,n(653138)));for(var o=t.length;o--;)n(419931)(r,t[o]);return r}));e.exports=r},592701:(e,t,n)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),t._notifyVisibilityChanged=t._subscribeToVisiblityChanged=t._isUnloading=t._isCurrentlyVisible=void 0;const r=()=>n(600414),i="foreground",o="background",a=[];let s=i,l=!1;t._isCurrentlyVisible=()=>s===i;t._isUnloading=()=>l;t._subscribeToVisiblityChanged=e=>{a.unshift(e)};t._notifyVisibilityChanged=e=>{e!==s&&(s=e,a.forEach((t=>t(e))))},(0,r()._addWindowEventListenerSafe)("focus",(()=>{l=!1,(0,t._notifyVisibilityChanged)(i)})),(0,r()._addWindowEventListenerSafe)("blur",(()=>(0,t._notifyVisibilityChanged)(o))),(0,r()._addDocumentEventListenerSafe)("visibilitychange",(()=>{(0,t._notifyVisibilityChanged)("visible"===document.visibilityState?i:o)})),(0,r()._addWindowEventListenerSafe)((0,r()._getUnloadEvent)(),(()=>{l=!0,(0,t._notifyVisibilityChanged)(o)}))},595636:(e,t,n)=>{"use strict";var r=n(444576),i=n(179504),o=n(146706),a=n(957696),s=n(555169),l=n(767394),u=n(894483),c=n(301548),f=r.structuredClone,d=r.ArrayBuffer,h=r.DataView,p=Math.min,m=d.prototype,g=h.prototype,v=i(m.slice),y=o(m,"resizable","get"),b=o(m,"maxByteLength","get"),_=i(g.getInt8),w=i(g.setInt8);e.exports=(c||u)&&function(e,t,n){var r,i=l(e),o=void 0===t?i:a(t),m=!y||!y(e);if(s(e),c&&(e=f(e,{transfer:[e]}),i===o&&(n||m)))return e;if(i>=o&&(!n||m))r=v(e,0,o);else{var g=n&&!m&&b?{maxByteLength:b(e)}:void 0;r=new d(o,g);for(var S=new h(e),E=new h(r),k=p(o,i),T=0;T<k;T++)w(E,T,_(S,T))}return c||u(e),r}},600414:(e,t)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),t._getUnloadEvent=t._getCurrentPageUrlSafe=t._addDocumentEventListenerSafe=t._addWindowEventListenerSafe=t._isServerEnv=t._getDocumentSafe=t._getWindowSafe=void 0;t._getWindowSafe=()=>"undefined"!=typeof window?window:null;t._getDocumentSafe=()=>{var e;const n=(0,t._getWindowSafe)();return null!==(e=null==n?void 0:n.document)&&void 0!==e?e:null};t._isServerEnv=()=>{if(null!==(0,t._getDocumentSafe)())return!1;const e="undefined"!=typeof process&&null!=process.versions&&null!=process.versions.node;return"string"==typeof EdgeRuntime||e};t._addWindowEventListenerSafe=(e,n)=>{const r=(0,t._getWindowSafe)();"function"==typeof(null==r?void 0:r.addEventListener)&&r.addEventListener(e,n)};t._addDocumentEventListenerSafe=(e,n)=>{const r=(0,t._getDocumentSafe)();"function"==typeof(null==r?void 0:r.addEventListener)&&r.addEventListener(e,n)};t._getCurrentPageUrlSafe=()=>{var e;try{return null===(e=(0,t._getWindowSafe)())||void 0===e?void 0:e.location.href.split(/[?#]/)[0]}catch(n){return}};t._getUnloadEvent=()=>{const e=(0,t._getWindowSafe)();if(!e)return"beforeunload";return"onpagehide"in e?"pagehide":"beforeunload"}},601638:(e,t,n)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),t._setObjectInStorage=t._getObjectFromStorage=t.Storage=void 0;const r={},i={isReady:()=>!0,isReadyResolver:()=>null,getProviderName:()=>"InMemory",getItem:e=>r[e]?r[e]:null,setItem:(e,t)=>{r[e]=t},removeItem:e=>{delete r[e]},getAllKeys:()=>Object.keys(r)};let o=null;try{const e=(0,n(600414)._getWindowSafe)();e&&e.localStorage&&"function"==typeof e.localStorage.getItem&&(o={isReady:()=>!0,isReadyResolver:()=>null,getProviderName:()=>"LocalStorage",getItem:t=>e.localStorage.getItem(t),setItem:(t,n)=>e.localStorage.setItem(t,n),removeItem:t=>e.localStorage.removeItem(t),getAllKeys:()=>Object.keys(e.localStorage)})}catch(u){n(668024).Log.warn("Failed to setup localStorageProvider.")}let a=null!=o?o:i,s=a;function l(e){try{return e()}catch(u){if(u instanceof Error&&"SecurityError"===u.name)return t.Storage._setProvider(i),null;if(u instanceof Error&&"QuotaExceededError"===u.name){const n=t.Storage.getAllKeys().filter((e=>e.startsWith("statsig.")));u.message=`${u.message}. Statsig Keys: ${n.length}`}throw u}}t.Storage={isReady:()=>s.isReady(),isReadyResolver:()=>s.isReadyResolver(),getProviderName:()=>s.getProviderName(),getItem:e=>l((()=>s.getItem(e))),setItem:(e,t)=>l((()=>s.setItem(e,t))),removeItem:e=>s.removeItem(e),getAllKeys:()=>s.getAllKeys(),_setProvider:e=>{a=e,s=e},_setDisabled:e=>{s=e?i:a}},t._getObjectFromStorage=function(e){const n=t.Storage.getItem(e);return JSON.parse(null!=n?n:"null")},t._setObjectInStorage=function(e,n){t.Storage.setItem(e,JSON.stringify(n))}},611630:e=>{"use strict";function t(e,t){return Object.prototype.hasOwnProperty.call(e,t)}e.exports=function(e,n,r,i){n=n||"&",r=r||"=";var o={};if("string"!=typeof e||0===e.length)return o;var a=/\+/g;e=e.split(n);var s=1e3;i&&"number"==typeof i.maxKeys&&(s=i.maxKeys);var l=e.length;s>0&&l>s&&(l=s);for(var u=0;u<l;++u){var c,f,d,h,p=e[u].replace(a,"%20"),m=p.indexOf(r);m>=0?(c=p.substr(0,m),f=p.substr(m+1)):(c=p,f=""),d=decodeURIComponent(c),h=decodeURIComponent(f),t(o,d)?Array.isArray(o[d])?o[d].push(h):o[d]=[o[d],h]:o[d]=h}return o}},611741:(e,t,n)=>{e.exports=function(e){return n(198023)(e)&&e!=+e}},618727:(e,t,n)=>{"use strict";var r=n(136955);e.exports=function(e){var t=r(e);return"BigInt64Array"===t||"BigUint64Array"===t}},623546:(e,t,n)=>{e.exports=function(e){if(!n(540346)(e))return!1;var t=n(472552)(e);return"[object Error]"==t||"[object DOMException]"==t||"string"==typeof e.message&&"string"==typeof e.name&&!n(411331)(e)}},625473:(e,t,n)=>{var r="undefined"!=typeof JSON?JSON:n(184343);e.exports=function(e,t){t||(t={}),"function"==typeof t&&(t={cmp:t});var n=t.space||"";"number"==typeof n&&(n=Array(n+1).join(" "));var a,s="boolean"==typeof t.cycles&&t.cycles,l=t.replacer||function(e,t){return t},u=t.cmp&&(a=t.cmp,function(e){return function(t,n){var r={key:t,value:e[t]},i={key:n,value:e[n]};return a(r,i)}}),c=[];return function e(t,a,f,d){var h=n?"\n"+new Array(d+1).join(n):"",p=n?": ":":";if(f&&f.toJSON&&"function"==typeof f.toJSON&&(f=f.toJSON()),void 0!==(f=l.call(t,a,f))){if("object"!=typeof f||null===f)return r.stringify(f);if(i(f)){for(var m=[],g=0;g<f.length;g++){var v=e(f,g,f[g],d+1)||r.stringify(null);m.push(h+n+v)}return"["+m.join(",")+h+"]"}if(-1!==c.indexOf(f)){if(s)return r.stringify("__cycle__");throw new TypeError("Converting circular structure to JSON")}c.push(f);var y=o(f).sort(u&&u(f));for(m=[],g=0;g<y.length;g++){var b=e(f,a=y[g],f[a],d+1);if(b){var _=r.stringify(a)+p+b;m.push(h+n+_)}}return c.splice(c.indexOf(f),1),"{"+m.join(",")+h+"}"}}({"":e},"",e,0)};var i=Array.isArray||function(e){return"[object Array]"==={}.toString.call(e)},o=Object.keys||function(e){var t=Object.prototype.hasOwnProperty||function(){return!0},n=[];for(var r in e)t.call(e,r)&&n.push(r);return n}},636978:function(e,t,n){"use strict";var r=this&&this.__createBinding||(Object.create?function(e,t,n,r){void 0===r&&(r=n);var i=Object.getOwnPropertyDescriptor(t,n);i&&!("get"in i?!t.__esModule:i.writable||i.configurable)||(i={enumerable:!0,get:function(){return t[n]}}),Object.defineProperty(e,r,i)}:function(e,t,n,r){void 0===r&&(r=n),e[r]=t[n]}),i=this&&this.__exportStar||function(e,t){for(var n in e)"default"===n||Object.prototype.hasOwnProperty.call(t,n)||r(t,e,n)};Object.defineProperty(t,"__esModule",{value:!0}),t.Storage=t.Log=t.EventLogger=t.Diagnostics=void 0,n(843509);Object.defineProperty(t,"Diagnostics",{enumerable:!0,get:function(){return n(230882).Diagnostics}});Object.defineProperty(t,"EventLogger",{enumerable:!0,get:function(){return n(85434).EventLogger}});const o=()=>n(668024);Object.defineProperty(t,"Log",{enumerable:!0,get:function(){return o().Log}});Object.defineProperty(t,"Storage",{enumerable:!0,get:function(){return n(601638).Storage}}),i(n(843509),t),i(n(894681),t),i(n(658683),t),i(n(385638),t),i(n(230882),t),i(n(54387),t),i(n(14358),t),i(n(543194),t),i(n(286913),t),i(n(483918),t),i(n(757061),t),i(n(668024),t),i(n(240843),t),i(n(12610),t),i(n(244703),t),i(n(882509),t),i(n(905025),t),i(n(600414),t),i(n(810686),t),i(n(339839),t),i(n(32626),t),i(n(84351),t),i(n(971180),t),i(n(512166),t),i(n(37253),t),i(n(146512),t),i(n(647754),t),i(n(968474),t),i(n(709851),t),i(n(99652),t),i(n(81570),t),i(n(601638),t),i(n(953705),t),i(n(690010),t),i(n(188873),t),i(n(537047),t),i(n(592701),t),i(n(251148),t),i(n(895747),t),Object.assign((0,n(843509)._getStatsigGlobal)(),{Log:o().Log,SDK_VERSION:n(146512).SDK_VERSION})},647754:(e,t)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),t.LoggingEnabledOption=t.LogEventCompressionMode=void 0,t.LogEventCompressionMode={Disabled:"d",Enabled:"e",Forced:"f"},t.LoggingEnabledOption={disabled:"disabled",browserOnly:"browser-only",always:"always"}},658683:(e,t)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0})},660457:function(e,t){"use strict";var n=this&&this.__awaiter||function(e,t,n,r){return new(n||(n=Promise))((function(i,o){function a(e){try{l(r.next(e))}catch(t){o(t)}}function s(e){try{l(r.throw(e))}catch(t){o(t)}}function l(e){var t;e.done?i(e.value):(t=e.value,t instanceof n?t:new n((function(e){e(t)}))).then(a,s)}l((r=r.apply(e,t||[])).next())}))};Object.defineProperty(t,"__esModule",{value:!0}),t._fetchTxtRecords=void 0;const r=new Uint8Array([0,0,1,0,0,1,0,0,0,0,0,0,13,102,101,97,116,117,114,101,97,115,115,101,116,115,3,111,114,103,0,0,16,0,1]),i=["i","e","d"],o=200;t._fetchTxtRecords=function(e){return n(this,void 0,void 0,(function*(){const t=yield e("https://cloudflare-dns.com/dns-query",{method:"POST",headers:{"Content-Type":"application/dns-message",Accept:"application/dns-message"},body:r});if(!t.ok){const e=new Error("Failed to fetch TXT records from DNS");throw e.name="DnsTxtFetchError",e}const n=yield t.arrayBuffer();return function(e){const t=e.findIndex(((t,n)=>n<o&&"="===String.fromCharCode(t)&&i.includes(String.fromCharCode(e[n-1]))));if(-1===t){const e=new Error("Failed to parse TXT records from DNS");throw e.name="DnsTxtParseError",e}let n="";for(let r=t-1;r<e.length;r++)n+=String.fromCharCode(e[r]);return n.split(",")}(new Uint8Array(n))}))}},660680:(e,t,n)=>{var r=/[\\^$.*+?()[\]{}|]/g,i=RegExp(r.source);e.exports=function(e){return(e=n(213222)(e))&&i.test(e)?e.replace(r,"\\$&"):e}},668024:(e,t)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),t.Log=t.LogLevel=void 0;function n(e){return e.unshift("[Statsig]"),e}t.LogLevel={None:0,Error:1,Warn:2,Info:3,Debug:4};class r{static info(...e){r.level>=t.LogLevel.Info&&console.info("  INFO ",...n(e))}static debug(...e){r.level>=t.LogLevel.Debug&&console.debug(" DEBUG ",...n(e))}static warn(...e){r.level>=t.LogLevel.Warn&&console.warn("  WARN ",...n(e))}static error(...e){r.level>=t.LogLevel.Error&&console.error(" ERROR ",...n(e))}}t.Log=r,r.level=t.LogLevel.Warn},672577:(e,t,n)=>{"use strict";var r=n(746518),i=n(72652),o=n(479306),a=n(28551),s=n(301767);r({target:"Iterator",proto:!0,real:!0},{find:function(e){a(this),o(e);var t=s(this),n=0;return i(t,(function(t,r){if(e(t,n++))return r(t)}),{IS_RECORD:!0,INTERRUPTED:!0}).result}})},681335:(e,t,n)=>{"use strict";n.d(t,{LV:()=>d,p:()=>c});const r=Symbol("Comlink.proxy"),i=Symbol("Comlink.endpoint"),o=Symbol("Comlink.releaseProxy"),a=Symbol("Comlink.finalizer"),s=Symbol("Comlink.thrown"),l=e=>"object"==typeof e&&null!==e||"function"==typeof e,u=new Map([["proxy",{canHandle:e=>l(e)&&e[r],serialize(e){const{port1:t,port2:n}=new MessageChannel;return c(e,t),[n,[n]]},deserialize:e=>(e.start(),d(e))}],["throw",{canHandle:e=>l(e)&&s in e,serialize({value:e}){let t;return t=e instanceof Error?{isError:!0,value:{message:e.message,name:e.name,stack:e.stack}}:{isError:!1,value:e},[t,[]]},deserialize(e){if(e.isError)throw Object.assign(new Error(e.value.message),e.value);throw e.value}}]]);function c(e,t=globalThis,n=["*"]){t.addEventListener("message",(function i(o){if(!o||!o.data)return;if(!function(e,t){for(const n of e){if(t===n||"*"===n)return!0;if(n instanceof RegExp&&n.test(t))return!0}return!1}(n,o.origin))return void console.warn(`Invalid origin '${o.origin}' for comlink proxy`);const{id:l,type:u,path:d}=Object.assign({path:[]},o.data),h=(o.data.argumentList||[]).map(w);let p;try{const t=d.slice(0,-1).reduce(((e,t)=>e[t]),e),n=d.reduce(((e,t)=>e[t]),e);switch(u){case"GET":p=n;break;case"SET":t[d.slice(-1)[0]]=w(o.data.value),p=!0;break;case"APPLY":p=n.apply(t,h);break;case"CONSTRUCT":p=function(e){return Object.assign(e,{[r]:!0})}(new n(...h));break;case"ENDPOINT":{const{port1:t,port2:n}=new MessageChannel;c(e,n),p=function(e,t){return b.set(e,t),e}(t,[t])}break;case"RELEASE":p=void 0;break;default:return}}catch(m){p={value:m,[s]:0}}Promise.resolve(p).catch((e=>({value:e,[s]:0}))).then((n=>{const[r,o]=_(n);t.postMessage(Object.assign(Object.assign({},r),{id:l}),o),"RELEASE"===u&&(t.removeEventListener("message",i),f(t),a in e&&"function"==typeof e[a]&&e[a]())})).catch((e=>{const[n,r]=_({value:new TypeError("Unserializable return value"),[s]:0});t.postMessage(Object.assign(Object.assign({},n),{id:l}),r)}))})),t.start&&t.start()}function f(e){(function(e){return"MessagePort"===e.constructor.name})(e)&&e.close()}function d(e,t){const n=new Map;return e.addEventListener("message",(function(e){const{data:t}=e;if(!t||!t.id)return;const r=n.get(t.id);if(r)try{r(t)}finally{n.delete(t.id)}})),v(e,n,[],t)}function h(e){if(e)throw new Error("Proxy has been released and is not useable")}function p(e){return S(e,new Map,{type:"RELEASE"}).then((()=>{f(e)}))}const m=new WeakMap,g="FinalizationRegistry"in globalThis&&new FinalizationRegistry((e=>{const t=(m.get(e)||0)-1;m.set(e,t),0===t&&p(e)}));function v(e,t,n=[],r=function(){}){let a=!1;const s=new Proxy(r,{get(r,i){if(h(a),i===o)return()=>{!function(e){g&&g.unregister(e)}(s),p(e),t.clear(),a=!0};if("then"===i){if(0===n.length)return{then:()=>s};const r=S(e,t,{type:"GET",path:n.map((e=>e.toString()))}).then(w);return r.then.bind(r)}return v(e,t,[...n,i])},set(r,i,o){h(a);const[s,l]=_(o);return S(e,t,{type:"SET",path:[...n,i].map((e=>e.toString())),value:s},l).then(w)},apply(r,o,s){h(a);const l=n[n.length-1];if(l===i)return S(e,t,{type:"ENDPOINT"}).then(w);if("bind"===l)return v(e,t,n.slice(0,-1));const[u,c]=y(s);return S(e,t,{type:"APPLY",path:n.map((e=>e.toString())),argumentList:u},c).then(w)},construct(r,i){h(a);const[o,s]=y(i);return S(e,t,{type:"CONSTRUCT",path:n.map((e=>e.toString())),argumentList:o},s).then(w)}});return function(e,t){const n=(m.get(t)||0)+1;m.set(t,n),g&&g.register(e,t,e)}(s,e),s}function y(e){const t=e.map(_);return[t.map((e=>e[0])),(n=t.map((e=>e[1])),Array.prototype.concat.apply([],n))];var n}const b=new WeakMap;function _(e){for(const[t,n]of u)if(n.canHandle(e)){const[r,i]=n.serialize(e);return[{type:"HANDLER",name:t,value:r},i]}return[{type:"RAW",value:e},b.get(e)||[]]}function w(e){switch(e.type){case"HANDLER":return u.get(e.name).deserialize(e.value);case"RAW":return e.value}}function S(e,t,n,r){return new Promise((i=>{const o=new Array(4).fill(0).map((()=>Math.floor(Math.random()*Number.MAX_SAFE_INTEGER).toString(16))).join("-");t.set(o,i),e.start&&e.start(),e.postMessage(Object.assign({id:o},n),r)}))}},690010:(e,t)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),t._isTypeMatch=t._typeOf=void 0,t._typeOf=function(e){return Array.isArray(e)?"array":typeof e},t._isTypeMatch=function(e,t){const n=e=>Array.isArray(e)?"array":typeof e;return n(e)===n(t)}},709851:(e,t,n)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),t._makeTypedGet=t._mergeOverride=t._makeLayer=t._makeExperiment=t._makeDynamicConfig=t._makeFeatureGate=void 0;const r=()=>n(668024),i=()=>n(690010);function o(e,t,n,r){var i;return{name:e,details:t,ruleID:null!==(i=null==n?void 0:n.rule_id)&&void 0!==i?i:"",__evaluation:n,value:r}}function a(e,t,n){var r;const i=null!==(r=null==n?void 0:n.value)&&void 0!==r?r:{};return Object.assign(Object.assign({},o(e,t,n,i)),{get:s(e,null==n?void 0:n.value)})}function s(e,t,n){return(o,a)=>{var s;const l=null!==(s=null==t?void 0:t[o])&&void 0!==s?s:null;return null==l?null!=a?a:null:null==a||(0,i()._isTypeMatch)(l,a)?(null==n||n(o),l):(r().Log.warn(`Parameter type mismatch. '${e}.${o}' was found to be type '${typeof l}' but fallback/return type is '${typeof a}'. See https://docs.statsig.com/client/javascript-sdk/#typed-getters`),null!=a?a:null)}}t._makeFeatureGate=function(e,t,n){var r;return Object.assign(Object.assign({},o(e,t,n,!0===(null==n?void 0:n.value))),{idType:null!==(r=null==n?void 0:n.id_type)&&void 0!==r?r:null})},t._makeDynamicConfig=a,t._makeExperiment=function(e,t,n){var r;const i=a(e,t,n);return Object.assign(Object.assign({},i),{groupName:null!==(r=null==n?void 0:n.group_name)&&void 0!==r?r:null})},t._makeLayer=function(e,t,n,r){var i,a;return Object.assign(Object.assign({},o(e,t,n,void 0)),{get:s(e,null==n?void 0:n.value,r),groupName:null!==(i=null==n?void 0:n.group_name)&&void 0!==i?i:null,__value:null!==(a=null==n?void 0:n.value)&&void 0!==a?a:{}})},t._mergeOverride=function(e,t,n,r){return Object.assign(Object.assign(Object.assign({},e),t),{get:s(e.name,n,r)})},t._makeTypedGet=s},717344:(e,t,n)=>{"use strict";function r(e,...t){if(!((n=e)instanceof Uint8Array||ArrayBuffer.isView(n)&&"Uint8Array"===n.constructor.name))throw new Error("Uint8Array expected");var n;if(t.length>0&&!t.includes(e.length))throw new Error("Uint8Array expected of length "+t+", got length="+e.length)}function i(e,t=!0){if(e.destroyed)throw new Error("Hash instance has been destroyed");if(t&&e.finished)throw new Error("Hash#digest() has already been called")}function o(e){return new DataView(e.buffer,e.byteOffset,e.byteLength)}function a(e,t){return e<<32-t|e>>>t}n.d(t,{sc:()=>g});function s(e){return"string"==typeof e&&(e=function(e){if("string"!=typeof e)throw new Error("utf8ToBytes expected string, got "+typeof e);return new Uint8Array((new TextEncoder).encode(e))}(e)),r(e),e}class l{clone(){return this._cloneInto()}}function u(e){const t=t=>e().update(s(t)).digest(),n=e();return t.outputLen=n.outputLen,t.blockLen=n.blockLen,t.create=()=>e(),t}function c(e,t,n){return e&t^e&n^t&n}class f extends l{constructor(e,t,n,r){super(),this.blockLen=e,this.outputLen=t,this.padOffset=n,this.isLE=r,this.finished=!1,this.length=0,this.pos=0,this.destroyed=!1,this.buffer=new Uint8Array(e),this.view=o(this.buffer)}update(e){i(this);const{view:t,buffer:n,blockLen:r}=this,a=(e=s(e)).length;for(let i=0;i<a;){const s=Math.min(r-this.pos,a-i);if(s!==r)n.set(e.subarray(i,i+s),this.pos),this.pos+=s,i+=s,this.pos===r&&(this.process(t,0),this.pos=0);else{const t=o(e);for(;r<=a-i;i+=r)this.process(t,i)}}return this.length+=e.length,this.roundClean(),this}digestInto(e){i(this),function(e,t){r(e);const n=t.outputLen;if(e.length<n)throw new Error("digestInto() expects output buffer of length at least "+n)}(e,this),this.finished=!0;const{buffer:t,view:n,blockLen:a,isLE:s}=this;let{pos:l}=this;t[l++]=128,this.buffer.subarray(l).fill(0),this.padOffset>a-l&&(this.process(n,0),l=0);for(let r=l;r<a;r++)t[r]=0;!function(e,t,n,r){if("function"==typeof e.setBigUint64)return e.setBigUint64(t,n,r);const i=BigInt(32),o=BigInt(4294967295),a=Number(n>>i&o),s=Number(n&o),l=r?4:0,u=r?0:4;e.setUint32(t+l,a,r),e.setUint32(t+u,s,r)}(n,a-8,BigInt(8*this.length),s),this.process(n,0);const u=o(e),c=this.outputLen;if(c%4)throw new Error("_sha2: outputLen should be aligned to 32bit");const f=c/4,d=this.get();if(f>d.length)throw new Error("_sha2: outputLen bigger than state");for(let r=0;r<f;r++)u.setUint32(4*r,d[r],s)}digest(){const{buffer:e,outputLen:t}=this;this.digestInto(e);const n=e.slice(0,t);return this.destroy(),n}_cloneInto(e){e||(e=new this.constructor),e.set(...this.get());const{blockLen:t,buffer:n,length:r,finished:i,destroyed:o,pos:a}=this;return e.length=r,e.pos=a,e.finished=i,e.destroyed=o,r%t&&e.buffer.set(n),e}}const d=new Uint32Array([1116352408,1899447441,3049323471,3921009573,961987163,1508970993,2453635748,2870763221,3624381080,310598401,607225278,1426881987,1925078388,2162078206,2614888103,3248222580,3835390401,4022224774,264347078,604807628,770255983,1249150122,1555081692,1996064986,2554220882,2821834349,2952996808,3210313671,3336571891,3584528711,113926993,338241895,666307205,773529912,1294757372,1396182291,1695183700,1986661051,2177026350,2456956037,2730485921,2820302411,3259730800,3345764771,3516065817,3600352804,4094571909,275423344,430227734,506948616,659060556,883997877,958139571,1322822218,1537002063,1747873779,1955562222,2024104815,2227730452,2361852424,2428436474,2756734187,3204031479,3329325298]),h=new Uint32Array([1779033703,3144134277,1013904242,2773480762,1359893119,2600822924,528734635,1541459225]),p=new Uint32Array(64);class m extends f{constructor(){super(64,32,8,!1),this.A=0|h[0],this.B=0|h[1],this.C=0|h[2],this.D=0|h[3],this.E=0|h[4],this.F=0|h[5],this.G=0|h[6],this.H=0|h[7]}get(){const{A:e,B:t,C:n,D:r,E:i,F:o,G:a,H:s}=this;return[e,t,n,r,i,o,a,s]}set(e,t,n,r,i,o,a,s){this.A=0|e,this.B=0|t,this.C=0|n,this.D=0|r,this.E=0|i,this.F=0|o,this.G=0|a,this.H=0|s}process(e,t){for(let a=0;a<16;a++,t+=4)p[a]=e.getUint32(t,!1);for(let c=16;c<64;c++){const e=p[c-15],t=p[c-2],n=a(e,7)^a(e,18)^e>>>3,r=a(t,17)^a(t,19)^t>>>10;p[c]=r+p[c-7]+n+p[c-16]|0}let{A:n,B:r,C:i,D:o,E:s,F:l,G:u,H:f}=this;for(let m=0;m<64;m++){const e=f+(a(s,6)^a(s,11)^a(s,25))+((h=s)&l^~h&u)+d[m]+p[m]|0,t=(a(n,2)^a(n,13)^a(n,22))+c(n,r,i)|0;f=u,u=l,l=s,s=o+e|0,o=i,i=r,r=n,n=e+t|0}var h;n=n+this.A|0,r=r+this.B|0,i=i+this.C|0,o=o+this.D|0,s=s+this.E|0,l=l+this.F|0,u=u+this.G|0,f=f+this.H|0,this.set(n,r,i,o,s,l,u,f)}roundClean(){p.fill(0)}destroy(){this.set(0,0,0,0,0,0,0,0),this.buffer.fill(0)}}const g=u((()=>new m))},722653:function(e,t,n){var r;!function(i,o){"use strict";var a="function",s="undefined",l="object",u="string",c="major",f="model",d="name",h="type",p="vendor",m="version",g="architecture",v="console",y="mobile",b="tablet",_="smarttv",w="wearable",S="embedded",E="Amazon",k="Apple",T="ASUS",x="BlackBerry",O="Browser",C="Chrome",N="Firefox",I="Google",D="Huawei",A="LG",P="Microsoft",L="Motorola",M="Opera",R="Samsung",F="Sharp",U="Sony",j="Xiaomi",B="Zebra",z="Facebook",H="Chromium OS",V="Mac OS",$=function(e){for(var t={},n=0;n<e.length;n++)t[e[n].toUpperCase()]=e[n];return t},G=function(e,t){return typeof e===u&&-1!==q(t).indexOf(q(e))},q=function(e){return e.toLowerCase()},W=function(e,t){if(typeof e===u)return e=e.replace(/^\s\s*/,""),typeof t===s?e:e.substring(0,500)},K=function(e,t){for(var n,r,i,s,u,c,f=0;f<t.length&&!u;){var d=t[f],h=t[f+1];for(n=r=0;n<d.length&&!u&&d[n];)if(u=d[n++].exec(e))for(i=0;i<h.length;i++)c=u[++r],typeof(s=h[i])===l&&s.length>0?2===s.length?typeof s[1]==a?this[s[0]]=s[1].call(this,c):this[s[0]]=s[1]:3===s.length?typeof s[1]!==a||s[1].exec&&s[1].test?this[s[0]]=c?c.replace(s[1],s[2]):o:this[s[0]]=c?s[1].call(this,c,s[2]):o:4===s.length&&(this[s[0]]=c?s[3].call(this,c.replace(s[1],s[2])):o):this[s]=c||o;f+=2}},Z=function(e,t){for(var n in t)if(typeof t[n]===l&&t[n].length>0){for(var r=0;r<t[n].length;r++)if(G(t[n][r],e))return"?"===n?o:n}else if(G(t[n],e))return"?"===n?o:n;return e},J={ME:"4.90","NT 3.11":"NT3.51","NT 4.0":"NT4.0",2e3:"NT 5.0",XP:["NT 5.1","NT 5.2"],Vista:"NT 6.0",7:"NT 6.1",8:"NT 6.2",8.1:"NT 6.3",10:["NT 6.4","NT 10.0"],RT:"ARM"},Y={browser:[[/\b(?:crmo|crios)\/([\w\.]+)/i],[m,[d,"Chrome"]],[/edg(?:e|ios|a)?\/([\w\.]+)/i],[m,[d,"Edge"]],[/(opera mini)\/([-\w\.]+)/i,/(opera [mobiletab]{3,6})\b.+version\/([-\w\.]+)/i,/(opera)(?:.+version\/|[\/ ]+)([\w\.]+)/i],[d,m],[/opios[\/ ]+([\w\.]+)/i],[m,[d,M+" Mini"]],[/\bop(?:rg)?x\/([\w\.]+)/i],[m,[d,M+" GX"]],[/\bopr\/([\w\.]+)/i],[m,[d,M]],[/\bb[ai]*d(?:uhd|[ub]*[aekoprswx]{5,6})[\/ ]?([\w\.]+)/i],[m,[d,"Baidu"]],[/(kindle)\/([\w\.]+)/i,/(lunascape|maxthon|netfront|jasmine|blazer)[\/ ]?([\w\.]*)/i,/(avant|iemobile|slim)\s?(?:browser)?[\/ ]?([\w\.]*)/i,/(?:ms|\()(ie) ([\w\.]+)/i,/(flock|rockmelt|midori|epiphany|silk|skyfire|bolt|iron|vivaldi|iridium|phantomjs|bowser|quark|qupzilla|falkon|rekonq|puffin|brave|whale(?!.+naver)|qqbrowserlite|qq|duckduckgo)\/([-\w\.]+)/i,/(heytap|ovi)browser\/([\d\.]+)/i,/(weibo)__([\d\.]+)/i],[d,m],[/\bddg\/([\w\.]+)/i],[m,[d,"DuckDuckGo"]],[/(?:\buc? ?browser|(?:juc.+)ucweb)[\/ ]?([\w\.]+)/i],[m,[d,"UC"+O]],[/microm.+\bqbcore\/([\w\.]+)/i,/\bqbcore\/([\w\.]+).+microm/i,/micromessenger\/([\w\.]+)/i],[m,[d,"WeChat"]],[/konqueror\/([\w\.]+)/i],[m,[d,"Konqueror"]],[/trident.+rv[: ]([\w\.]{1,9})\b.+like gecko/i],[m,[d,"IE"]],[/ya(?:search)?browser\/([\w\.]+)/i],[m,[d,"Yandex"]],[/slbrowser\/([\w\.]+)/i],[m,[d,"Smart Lenovo "+O]],[/(avast|avg)\/([\w\.]+)/i],[[d,/(.+)/,"$1 Secure "+O],m],[/\bfocus\/([\w\.]+)/i],[m,[d,N+" Focus"]],[/\bopt\/([\w\.]+)/i],[m,[d,M+" Touch"]],[/coc_coc\w+\/([\w\.]+)/i],[m,[d,"Coc Coc"]],[/dolfin\/([\w\.]+)/i],[m,[d,"Dolphin"]],[/coast\/([\w\.]+)/i],[m,[d,M+" Coast"]],[/miuibrowser\/([\w\.]+)/i],[m,[d,"MIUI "+O]],[/fxios\/([-\w\.]+)/i],[m,[d,N]],[/\bqihu|(qi?ho?o?|360)browser/i],[[d,"360 "+O]],[/(oculus|sailfish|huawei|vivo)browser\/([\w\.]+)/i],[[d,/(.+)/,"$1 "+O],m],[/samsungbrowser\/([\w\.]+)/i],[m,[d,R+" Internet"]],[/(comodo_dragon)\/([\w\.]+)/i],[[d,/_/g," "],m],[/metasr[\/ ]?([\d\.]+)/i],[m,[d,"Sogou Explorer"]],[/(sogou)mo\w+\/([\d\.]+)/i],[[d,"Sogou Mobile"],m],[/(electron)\/([\w\.]+) safari/i,/(tesla)(?: qtcarbrowser|\/(20\d\d\.[-\w\.]+))/i,/m?(qqbrowser|2345Explorer)[\/ ]?([\w\.]+)/i],[d,m],[/(lbbrowser)/i,/\[(linkedin)app\]/i],[d],[/((?:fban\/fbios|fb_iab\/fb4a)(?!.+fbav)|;fbav\/([\w\.]+);)/i],[[d,z],m],[/(Klarna)\/([\w\.]+)/i,/(kakao(?:talk|story))[\/ ]([\w\.]+)/i,/(naver)\(.*?(\d+\.[\w\.]+).*\)/i,/safari (line)\/([\w\.]+)/i,/\b(line)\/([\w\.]+)\/iab/i,/(alipay)client\/([\w\.]+)/i,/(twitter)(?:and| f.+e\/([\w\.]+))/i,/(chromium|instagram|snapchat)[\/ ]([-\w\.]+)/i],[d,m],[/\bgsa\/([\w\.]+) .*safari\//i],[m,[d,"GSA"]],[/musical_ly(?:.+app_?version\/|_)([\w\.]+)/i],[m,[d,"TikTok"]],[/headlesschrome(?:\/([\w\.]+)| )/i],[m,[d,C+" Headless"]],[/ wv\).+(chrome)\/([\w\.]+)/i],[[d,C+" WebView"],m],[/droid.+ version\/([\w\.]+)\b.+(?:mobile safari|safari)/i],[m,[d,"Android "+O]],[/(chrome|omniweb|arora|[tizenoka]{5} ?browser)\/v?([\w\.]+)/i],[d,m],[/version\/([\w\.\,]+) .*mobile\/\w+ (safari)/i],[m,[d,"Mobile Safari"]],[/version\/([\w(\.|\,)]+) .*(mobile ?safari|safari)/i],[m,d],[/webkit.+?(mobile ?safari|safari)(\/[\w\.]+)/i],[d,[m,Z,{"1.0":"/8",1.2:"/1",1.3:"/3","2.0":"/412","2.0.2":"/416","2.0.3":"/417","2.0.4":"/419","?":"/"}]],[/(webkit|khtml)\/([\w\.]+)/i],[d,m],[/(navigator|netscape\d?)\/([-\w\.]+)/i],[[d,"Netscape"],m],[/mobile vr; rv:([\w\.]+)\).+firefox/i],[m,[d,N+" Reality"]],[/ekiohf.+(flow)\/([\w\.]+)/i,/(swiftfox)/i,/(icedragon|iceweasel|camino|chimera|fennec|maemo browser|minimo|conkeror|klar)[\/ ]?([\w\.\+]+)/i,/(seamonkey|k-meleon|icecat|iceape|firebird|phoenix|palemoon|basilisk|waterfox)\/([-\w\.]+)$/i,/(firefox)\/([\w\.]+)/i,/(mozilla)\/([\w\.]+) .+rv\:.+gecko\/\d+/i,/(polaris|lynx|dillo|icab|doris|amaya|w3m|netsurf|sleipnir|obigo|mosaic|(?:go|ice|up)[\. ]?browser)[-\/ ]?v?([\w\.]+)/i,/(links) \(([\w\.]+)/i,/panasonic;(viera)/i],[d,m],[/(cobalt)\/([\w\.]+)/i],[d,[m,/master.|lts./,""]]],cpu:[[/(?:(amd|x(?:(?:86|64)[-_])?|wow|win)64)[;\)]/i],[[g,"amd64"]],[/(ia32(?=;))/i],[[g,q]],[/((?:i[346]|x)86)[;\)]/i],[[g,"ia32"]],[/\b(aarch64|arm(v?8e?l?|_?64))\b/i],[[g,"arm64"]],[/\b(arm(?:v[67])?ht?n?[fl]p?)\b/i],[[g,"armhf"]],[/windows (ce|mobile); ppc;/i],[[g,"arm"]],[/((?:ppc|powerpc)(?:64)?)(?: mac|;|\))/i],[[g,/ower/,"",q]],[/(sun4\w)[;\)]/i],[[g,"sparc"]],[/((?:avr32|ia64(?=;))|68k(?=\))|\barm(?=v(?:[1-7]|[5-7]1)l?|;|eabi)|(?=atmel )avr|(?:irix|mips|sparc)(?:64)?\b|pa-risc)/i],[[g,q]]],device:[[/\b(sch-i[89]0\d|shw-m380s|sm-[ptx]\w{2,4}|gt-[pn]\d{2,4}|sgh-t8[56]9|nexus 10)/i],[f,[p,R],[h,b]],[/\b((?:s[cgp]h|gt|sm)-\w+|sc[g-]?[\d]+a?|galaxy nexus)/i,/samsung[- ]([-\w]+)/i,/sec-(sgh\w+)/i],[f,[p,R],[h,y]],[/(?:\/|\()(ip(?:hone|od)[\w, ]*)(?:\/|;)/i],[f,[p,k],[h,y]],[/\((ipad);[-\w\),; ]+apple/i,/applecoremedia\/[\w\.]+ \((ipad)/i,/\b(ipad)\d\d?,\d\d?[;\]].+ios/i],[f,[p,k],[h,b]],[/(macintosh);/i],[f,[p,k]],[/\b(sh-?[altvz]?\d\d[a-ekm]?)/i],[f,[p,F],[h,y]],[/\b((?:ag[rs][23]?|bah2?|sht?|btv)-a?[lw]\d{2})\b(?!.+d\/s)/i],[f,[p,D],[h,b]],[/(?:huawei|honor)([-\w ]+)[;\)]/i,/\b(nexus 6p|\w{2,4}e?-[atu]?[ln][\dx][012359c][adn]?)\b(?!.+d\/s)/i],[f,[p,D],[h,y]],[/\b(poco[\w ]+|m2\d{3}j\d\d[a-z]{2})(?: bui|\))/i,/\b; (\w+) build\/hm\1/i,/\b(hm[-_ ]?note?[_ ]?(?:\d\w)?) bui/i,/\b(redmi[\-_ ]?(?:note|k)?[\w_ ]+)(?: bui|\))/i,/oid[^\)]+; (m?[12][0-389][01]\w{3,6}[c-y])( bui|; wv|\))/i,/\b(mi[-_ ]?(?:a\d|one|one[_ ]plus|note lte|max|cc)?[_ ]?(?:\d?\w?)[_ ]?(?:plus|se|lite)?)(?: bui|\))/i],[[f,/_/g," "],[p,j],[h,y]],[/oid[^\)]+; (2\d{4}(283|rpbf)[cgl])( bui|\))/i,/\b(mi[-_ ]?(?:pad)(?:[\w_ ]+))(?: bui|\))/i],[[f,/_/g," "],[p,j],[h,b]],[/; (\w+) bui.+ oppo/i,/\b(cph[12]\d{3}|p(?:af|c[al]|d\w|e[ar])[mt]\d0|x9007|a101op)\b/i],[f,[p,"OPPO"],[h,y]],[/\b(opd2\d{3}a?) bui/i],[f,[p,"OPPO"],[h,b]],[/vivo (\w+)(?: bui|\))/i,/\b(v[12]\d{3}\w?[at])(?: bui|;)/i],[f,[p,"Vivo"],[h,y]],[/\b(rmx[1-3]\d{3})(?: bui|;|\))/i],[f,[p,"Realme"],[h,y]],[/\b(milestone|droid(?:[2-4x]| (?:bionic|x2|pro|razr))?:?( 4g)?)\b[\w ]+build\//i,/\bmot(?:orola)?[- ](\w*)/i,/((?:moto[\w\(\) ]+|xt\d{3,4}|nexus 6)(?= bui|\)))/i],[f,[p,L],[h,y]],[/\b(mz60\d|xoom[2 ]{0,2}) build\//i],[f,[p,L],[h,b]],[/((?=lg)?[vl]k\-?\d{3}) bui| 3\.[-\w; ]{10}lg?-([06cv9]{3,4})/i],[f,[p,A],[h,b]],[/(lm(?:-?f100[nv]?|-[\w\.]+)(?= bui|\))|nexus [45])/i,/\blg[-e;\/ ]+((?!browser|netcast|android tv)\w+)/i,/\blg-?([\d\w]+) bui/i],[f,[p,A],[h,y]],[/(ideatab[-\w ]+)/i,/lenovo ?(s[56]000[-\w]+|tab(?:[\w ]+)|yt[-\d\w]{6}|tb[-\d\w]{6})/i],[f,[p,"Lenovo"],[h,b]],[/(?:maemo|nokia).*(n900|lumia \d+)/i,/nokia[-_ ]?([-\w\.]*)/i],[[f,/_/g," "],[p,"Nokia"],[h,y]],[/(pixel c)\b/i],[f,[p,I],[h,b]],[/droid.+; (pixel[\daxl ]{0,6})(?: bui|\))/i],[f,[p,I],[h,y]],[/droid.+ (a?\d[0-2]{2}so|[c-g]\d{4}|so[-gl]\w+|xq-a\w[4-7][12])(?= bui|\).+chrome\/(?![1-6]{0,1}\d\.))/i],[f,[p,U],[h,y]],[/sony tablet [ps]/i,/\b(?:sony)?sgp\w+(?: bui|\))/i],[[f,"Xperia Tablet"],[p,U],[h,b]],[/ (kb2005|in20[12]5|be20[12][59])\b/i,/(?:one)?(?:plus)? (a\d0\d\d)(?: b|\))/i],[f,[p,"OnePlus"],[h,y]],[/(alexa)webm/i,/(kf[a-z]{2}wi|aeo[c-r]{2})( bui|\))/i,/(kf[a-z]+)( bui|\)).+silk\//i],[f,[p,E],[h,b]],[/((?:sd|kf)[0349hijorstuw]+)( bui|\)).+silk\//i],[[f,/(.+)/g,"Fire Phone $1"],[p,E],[h,y]],[/(playbook);[-\w\),; ]+(rim)/i],[f,p,[h,b]],[/\b((?:bb[a-f]|st[hv])100-\d)/i,/\(bb10; (\w+)/i],[f,[p,x],[h,y]],[/(?:\b|asus_)(transfo[prime ]{4,10} \w+|eeepc|slider \w+|nexus 7|padfone|p00[cj])/i],[f,[p,T],[h,b]],[/ (z[bes]6[027][012][km][ls]|zenfone \d\w?)\b/i],[f,[p,T],[h,y]],[/(nexus 9)/i],[f,[p,"HTC"],[h,b]],[/(htc)[-;_ ]{1,2}([\w ]+(?=\)| bui)|\w+)/i,/(zte)[- ]([\w ]+?)(?: bui|\/|\))/i,/(alcatel|geeksphone|nexian|panasonic(?!(?:;|\.))|sony(?!-bra))[-_ ]?([-\w]*)/i],[p,[f,/_/g," "],[h,y]],[/droid.+; ([ab][1-7]-?[0178a]\d\d?)/i],[f,[p,"Acer"],[h,b]],[/droid.+; (m[1-5] note) bui/i,/\bmz-([-\w]{2,})/i],[f,[p,"Meizu"],[h,y]],[/; ((?:power )?armor(?:[\w ]{0,8}))(?: bui|\))/i],[f,[p,"Ulefone"],[h,y]],[/(blackberry|benq|palm(?=\-)|sonyericsson|acer|asus|dell|meizu|motorola|polytron|infinix|tecno)[-_ ]?([-\w]*)/i,/(hp) ([\w ]+\w)/i,/(asus)-?(\w+)/i,/(microsoft); (lumia[\w ]+)/i,/(lenovo)[-_ ]?([-\w]+)/i,/(jolla)/i,/(oppo) ?([\w ]+) bui/i],[p,f,[h,y]],[/(kobo)\s(ereader|touch)/i,/(archos) (gamepad2?)/i,/(hp).+(touchpad(?!.+tablet)|tablet)/i,/(kindle)\/([\w\.]+)/i,/(nook)[\w ]+build\/(\w+)/i,/(dell) (strea[kpr\d ]*[\dko])/i,/(le[- ]+pan)[- ]+(\w{1,9}) bui/i,/(trinity)[- ]*(t\d{3}) bui/i,/(gigaset)[- ]+(q\w{1,9}) bui/i,/(vodafone) ([\w ]+)(?:\)| bui)/i],[p,f,[h,b]],[/(surface duo)/i],[f,[p,P],[h,b]],[/droid [\d\.]+; (fp\du?)(?: b|\))/i],[f,[p,"Fairphone"],[h,y]],[/(u304aa)/i],[f,[p,"AT&T"],[h,y]],[/\bsie-(\w*)/i],[f,[p,"Siemens"],[h,y]],[/\b(rct\w+) b/i],[f,[p,"RCA"],[h,b]],[/\b(venue[\d ]{2,7}) b/i],[f,[p,"Dell"],[h,b]],[/\b(q(?:mv|ta)\w+) b/i],[f,[p,"Verizon"],[h,b]],[/\b(?:barnes[& ]+noble |bn[rt])([\w\+ ]*) b/i],[f,[p,"Barnes & Noble"],[h,b]],[/\b(tm\d{3}\w+) b/i],[f,[p,"NuVision"],[h,b]],[/\b(k88) b/i],[f,[p,"ZTE"],[h,b]],[/\b(nx\d{3}j) b/i],[f,[p,"ZTE"],[h,y]],[/\b(gen\d{3}) b.+49h/i],[f,[p,"Swiss"],[h,y]],[/\b(zur\d{3}) b/i],[f,[p,"Swiss"],[h,b]],[/\b((zeki)?tb.*\b) b/i],[f,[p,"Zeki"],[h,b]],[/\b([yr]\d{2}) b/i,/\b(dragon[- ]+touch |dt)(\w{5}) b/i],[[p,"Dragon Touch"],f,[h,b]],[/\b(ns-?\w{0,9}) b/i],[f,[p,"Insignia"],[h,b]],[/\b((nxa|next)-?\w{0,9}) b/i],[f,[p,"NextBook"],[h,b]],[/\b(xtreme\_)?(v(1[045]|2[015]|[3469]0|7[05])) b/i],[[p,"Voice"],f,[h,y]],[/\b(lvtel\-)?(v1[12]) b/i],[[p,"LvTel"],f,[h,y]],[/\b(ph-1) /i],[f,[p,"Essential"],[h,y]],[/\b(v(100md|700na|7011|917g).*\b) b/i],[f,[p,"Envizen"],[h,b]],[/\b(trio[-\w\. ]+) b/i],[f,[p,"MachSpeed"],[h,b]],[/\btu_(1491) b/i],[f,[p,"Rotor"],[h,b]],[/(shield[\w ]+) b/i],[f,[p,"Nvidia"],[h,b]],[/(sprint) (\w+)/i],[p,f,[h,y]],[/(kin\.[onetw]{3})/i],[[f,/\./g," "],[p,P],[h,y]],[/droid.+; (cc6666?|et5[16]|mc[239][23]x?|vc8[03]x?)\)/i],[f,[p,B],[h,b]],[/droid.+; (ec30|ps20|tc[2-8]\d[kx])\)/i],[f,[p,B],[h,y]],[/smart-tv.+(samsung)/i],[p,[h,_]],[/hbbtv.+maple;(\d+)/i],[[f,/^/,"SmartTV"],[p,R],[h,_]],[/(nux; netcast.+smarttv|lg (netcast\.tv-201\d|android tv))/i],[[p,A],[h,_]],[/(apple) ?tv/i],[p,[f,k+" TV"],[h,_]],[/crkey/i],[[f,C+"cast"],[p,I],[h,_]],[/droid.+aft(\w+)( bui|\))/i],[f,[p,E],[h,_]],[/\(dtv[\);].+(aquos)/i,/(aquos-tv[\w ]+)\)/i],[f,[p,F],[h,_]],[/(bravia[\w ]+)( bui|\))/i],[f,[p,U],[h,_]],[/(mitv-\w{5}) bui/i],[f,[p,j],[h,_]],[/Hbbtv.*(technisat) (.*);/i],[p,f,[h,_]],[/\b(roku)[\dx]*[\)\/]((?:dvp-)?[\d\.]*)/i,/hbbtv\/\d+\.\d+\.\d+ +\([\w\+ ]*; *([\w\d][^;]*);([^;]*)/i],[[p,W],[f,W],[h,_]],[/\b(android tv|smart[- ]?tv|opera tv|tv; rv:)\b/i],[[h,_]],[/(ouya)/i,/(nintendo) ([wids3utch]+)/i],[p,f,[h,v]],[/droid.+; (shield) bui/i],[f,[p,"Nvidia"],[h,v]],[/(playstation [345portablevi]+)/i],[f,[p,U],[h,v]],[/\b(xbox(?: one)?(?!; xbox))[\); ]/i],[f,[p,P],[h,v]],[/((pebble))app/i],[p,f,[h,w]],[/(watch)(?: ?os[,\/]|\d,\d\/)[\d\.]+/i],[f,[p,k],[h,w]],[/droid.+; (glass) \d/i],[f,[p,I],[h,w]],[/droid.+; (wt63?0{2,3})\)/i],[f,[p,B],[h,w]],[/(quest( \d| pro)?)/i],[f,[p,z],[h,w]],[/(tesla)(?: qtcarbrowser|\/[-\w\.]+)/i],[p,[h,S]],[/(aeobc)\b/i],[f,[p,E],[h,S]],[/droid .+?; ([^;]+?)(?: bui|; wv\)|\) applew).+? mobile safari/i],[f,[h,y]],[/droid .+?; ([^;]+?)(?: bui|\) applew).+?(?! mobile) safari/i],[f,[h,b]],[/\b((tablet|tab)[;\/]|focus\/\d(?!.+mobile))/i],[[h,b]],[/(phone|mobile(?:[;\/]| [ \w\/\.]*safari)|pda(?=.+windows ce))/i],[[h,y]],[/(android[-\w\. ]{0,9});.+buil/i],[f,[p,"Generic"]]],engine:[[/windows.+ edge\/([\w\.]+)/i],[m,[d,"EdgeHTML"]],[/webkit\/537\.36.+chrome\/(?!27)([\w\.]+)/i],[m,[d,"Blink"]],[/(presto)\/([\w\.]+)/i,/(webkit|trident|netfront|netsurf|amaya|lynx|w3m|goanna)\/([\w\.]+)/i,/ekioh(flow)\/([\w\.]+)/i,/(khtml|tasman|links)[\/ ]\(?([\w\.]+)/i,/(icab)[\/ ]([23]\.[\d\.]+)/i,/\b(libweb)/i],[d,m],[/rv\:([\w\.]{1,9})\b.+(gecko)/i],[m,d]],os:[[/microsoft (windows) (vista|xp)/i],[d,m],[/(windows (?:phone(?: os)?|mobile))[\/ ]?([\d\.\w ]*)/i],[d,[m,Z,J]],[/windows nt 6\.2; (arm)/i,/windows[\/ ]?([ntce\d\. ]+\w)(?!.+xbox)/i,/(?:win(?=3|9|n)|win 9x )([nt\d\.]+)/i],[[m,Z,J],[d,"Windows"]],[/ip[honead]{2,4}\b(?:.*os ([\w]+) like mac|; opera)/i,/(?:ios;fbsv\/|iphone.+ios[\/ ])([\d\.]+)/i,/cfnetwork\/.+darwin/i],[[m,/_/g,"."],[d,"iOS"]],[/(mac os x) ?([\w\. ]*)/i,/(macintosh|mac_powerpc\b)(?!.+haiku)/i],[[d,V],[m,/_/g,"."]],[/droid ([\w\.]+)\b.+(android[- ]x86|harmonyos)/i],[m,d],[/(android|webos|qnx|bada|rim tablet os|maemo|meego|sailfish)[-\/ ]?([\w\.]*)/i,/(blackberry)\w*\/([\w\.]*)/i,/(tizen|kaios)[\/ ]([\w\.]+)/i,/\((series40);/i],[d,m],[/\(bb(10);/i],[m,[d,x]],[/(?:symbian ?os|symbos|s60(?=;)|series60)[-\/ ]?([\w\.]*)/i],[m,[d,"Symbian"]],[/mozilla\/[\d\.]+ \((?:mobile|tablet|tv|mobile; [\w ]+); rv:.+ gecko\/([\w\.]+)/i],[m,[d,N+" OS"]],[/web0s;.+rt(tv)/i,/\b(?:hp)?wos(?:browser)?\/([\w\.]+)/i],[m,[d,"webOS"]],[/watch(?: ?os[,\/]|\d,\d\/)([\d\.]+)/i],[m,[d,"watchOS"]],[/crkey\/([\d\.]+)/i],[m,[d,C+"cast"]],[/(cros) [\w]+(?:\)| ([\w\.]+)\b)/i],[[d,H],m],[/panasonic;(viera)/i,/(netrange)mmh/i,/(nettv)\/(\d+\.[\w\.]+)/i,/(nintendo|playstation) ([wids345portablevuch]+)/i,/(xbox); +xbox ([^\);]+)/i,/\b(joli|palm)\b ?(?:os)?\/?([\w\.]*)/i,/(mint)[\/\(\) ]?(\w*)/i,/(mageia|vectorlinux)[; ]/i,/([kxln]?ubuntu|debian|suse|opensuse|gentoo|arch(?= linux)|slackware|fedora|mandriva|centos|pclinuxos|red ?hat|zenwalk|linpus|raspbian|plan 9|minix|risc os|contiki|deepin|manjaro|elementary os|sabayon|linspire)(?: gnu\/linux)?(?: enterprise)?(?:[- ]linux)?(?:-gnu)?[-\/ ]?(?!chrom|package)([-\w\.]*)/i,/(hurd|linux) ?([\w\.]*)/i,/(gnu) ?([\w\.]*)/i,/\b([-frentopcghs]{0,5}bsd|dragonfly)[\/ ]?(?!amd|[ix346]{1,2}86)([\w\.]*)/i,/(haiku) (\w+)/i],[d,m],[/(sunos) ?([\w\.\d]*)/i],[[d,"Solaris"],m],[/((?:open)?solaris)[-\/ ]?([\w\.]*)/i,/(aix) ((\d)(?=\.|\)| )[\w\.])*/i,/\b(beos|os\/2|amigaos|morphos|openvms|fuchsia|hp-ux|serenityos)/i,/(unix) ?([\w\.]*)/i],[d,m]]},Q=function(e,t){if(typeof e===l&&(t=e,e=o),!(this instanceof Q))return new Q(e,t).getResult();var n=typeof i!==s&&i.navigator?i.navigator:o,r=e||(n&&n.userAgent?n.userAgent:""),v=n&&n.userAgentData?n.userAgentData:o,_=t?function(e,t){var n={};for(var r in e)t[r]&&t[r].length%2==0?n[r]=t[r].concat(e[r]):n[r]=e[r];return n}(Y,t):Y,w=n&&n.userAgent==r;return this.getBrowser=function(){var e,t={};return t[d]=o,t[m]=o,K.call(t,r,_.browser),t[c]=typeof(e=t[m])===u?e.replace(/[^\d\.]/g,"").split(".")[0]:o,w&&n&&n.brave&&typeof n.brave.isBrave==a&&(t[d]="Brave"),t},this.getCPU=function(){var e={};return e[g]=o,K.call(e,r,_.cpu),e},this.getDevice=function(){var e={};return e[p]=o,e[f]=o,e[h]=o,K.call(e,r,_.device),w&&!e[h]&&v&&v.mobile&&(e[h]=y),w&&"Macintosh"==e[f]&&n&&typeof n.standalone!==s&&n.maxTouchPoints&&n.maxTouchPoints>2&&(e[f]="iPad",e[h]=b),e},this.getEngine=function(){var e={};return e[d]=o,e[m]=o,K.call(e,r,_.engine),e},this.getOS=function(){var e={};return e[d]=o,e[m]=o,K.call(e,r,_.os),w&&!e[d]&&v&&v.platform&&"Unknown"!=v.platform&&(e[d]=v.platform.replace(/chrome os/i,H).replace(/macos/i,V)),e},this.getResult=function(){return{ua:this.getUA(),browser:this.getBrowser(),engine:this.getEngine(),os:this.getOS(),device:this.getDevice(),cpu:this.getCPU()}},this.getUA=function(){return r},this.setUA=function(e){return r=typeof e===u&&e.length>500?W(e,500):e,this},this.setUA(r),this};Q.VERSION="1.0.38",Q.BROWSER=$([d,m,c]),Q.CPU=$([g]),Q.DEVICE=$([f,p,h,v,y,_,b,w,S]),Q.ENGINE=Q.OS=$([d,m]),typeof t!==s?(e.exports&&(t=e.exports=Q),t.UAParser=Q):n.amdO?(r=function(){return Q}.call(t,n,t,e))===o||(e.exports=r):typeof i!==s&&(i.UAParser=Q);var X=typeof i!==s&&(i.jQuery||i.Zepto);if(X&&!X.ua){var ee=new Q;X.ua=ee.getResult(),X.ua.get=function(){return ee.getUA()},X.ua.set=function(e){ee.setUA(e);var t=ee.getResult();for(var n in t)X.ua[n]=t[n]}}}("object"==typeof window?window:this)},729094:(e,t,n)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),t.SpecStore=void 0;function r(e){return(0,n(636978)._typedJsonParse)(e,"has_updates","DownloadConfigSpecsResponse")}t.SpecStore=class{constructor(){this._rawValues=null,this._values=null,this._source="Uninitialized",this._lcut=0,this._receivedAt=0,this._defaultEnvironment=null}getValues(){return this._rawValues?r(this._rawValues):null}getSource(){return this._source}getDefaultEnvironment(){return this._defaultEnvironment}setValuesFromDataAdapter(e){var t;if(!e)return;const n=r(e.data);!0===(null==n?void 0:n.has_updates)&&(this._lcut=n.time,this._receivedAt=e.receivedAt,this._source=e.source,this._values=n,this._rawValues=e.data,this._defaultEnvironment=null!==(t=n.default_environment)&&void 0!==t?t:null)}reset(){this._values=null,this._rawValues=null,this._source="Loading"}finalize(){this._values||(this._source="NoValues")}getSpecAndSourceInfo(e,t){var n;const r=this._getSpecs(e);return{spec:null!==(n=null==r?void 0:r.find((e=>e.name===t)))&&void 0!==n?n:null,source:this._source,lcut:this._lcut,receivedAt:this._receivedAt}}getParamStoreAndSourceInfo(e){var t,n,r;const i=null===(t=this._values)||void 0===t?void 0:t.param_stores;return{paramStoreConfig:null!==(r=null===(n=null==i?void 0:i[e])||void 0===n?void 0:n.parameters)&&void 0!==r?r:null,source:this._source,lcut:this._lcut,receivedAt:this._receivedAt}}_getSpecs(e){var t,n,r;switch(e){case"gate":return null===(t=this._values)||void 0===t?void 0:t.feature_gates;case"config":return null===(n=this._values)||void 0===n?void 0:n.dynamic_configs;case"layer":return null===(r=this._values)||void 0===r?void 0:r.layer_configs}}}},732268:e=>{"use strict";e.exports={isString:function(e){return"string"==typeof e},isObject:function(e){return"object"==typeof e&&null!==e},isNull:function(e){return null===e},isNullOrUndefined:function(e){return null==e}}},738221:(e,t,n)=>{var r=()=>n(10124),i=()=>n(399374),o=Math.max,a=Math.min;e.exports=function(e,t,s){var l,u,c,f,d,h,p=0,m=!1,g=!1,v=!0;if("function"!=typeof e)throw new TypeError("Expected a function");function y(t){var n=l,r=u;return l=u=void 0,p=t,f=e.apply(r,n)}function b(e){var n=e-h;return void 0===h||n>=t||n<0||g&&e-p>=c}function _(){var e=r()();if(b(e))return w(e);d=setTimeout(_,function(e){var n=t-(e-h);return g?a(n,c-(e-p)):n}(e))}function w(e){return d=void 0,v&&l?y(e):(l=u=void 0,f)}function S(){var e=r()(),n=b(e);if(l=arguments,u=this,h=e,n){if(void 0===d)return function(e){return p=e,d=setTimeout(_,t),m?y(e):f}(h);if(g)return clearTimeout(d),d=setTimeout(_,t),y(h)}return void 0===d&&(d=setTimeout(_,t)),f}return t=i()(t)||0,n(223805)(s)&&(m=!!s.leading,c=(g="maxWait"in s)?o(i()(s.maxWait)||0,t):c,v="trailing"in s?!!s.trailing:v),S.cancel=function(){void 0!==d&&clearTimeout(d),p=0,l=h=u=d=void 0},S.flush=function(){return void 0===d?f:w(r()())},S}},747186:(e,t,n)=>{"use strict";t.decode=t.parse=n(611630),t.encode=t.stringify=n(959106)},748140:(e,t,n)=>{"use strict";var r=n(794644),i=n(326198),o=n(991291),a=r.aTypedArray;(0,r.exportTypedArrayMethod)("at",(function(e){var t=a(this),n=i(t),r=o(e),s=r>=0?r:n+r;return s<0||s>=n?void 0:t[s]}))},754819:(e,t,n)=>{"use strict";function r(e,t){var n=t&&t.cache?t.cache:c,r=t&&t.serializer?t.serializer:l;return(t&&t.strategy?t.strategy:s)(e,{cache:n,serializer:r})}function i(e,t,n,r){var i,o=null==(i=r)||"number"==typeof i||"boolean"==typeof i?r:n(r),a=t.get(o);return void 0===a&&(a=e.call(this,r),t.set(o,a)),a}function o(e,t,n){var r=Array.prototype.slice.call(arguments,3),i=n(r),o=t.get(i);return void 0===o&&(o=e.apply(this,r),t.set(i,o)),o}function a(e,t,n,r,i){return n.bind(t,e,r,i)}function s(e,t){return a(e,this,1===e.length?i:o,t.cache.create(),t.serializer)}n.d(t,{B:()=>r,W:()=>f});var l=function(){return JSON.stringify(arguments)};function u(){this.cache=Object.create(null)}u.prototype.get=function(e){return this.cache[e]},u.prototype.set=function(e,t){this.cache[e]=t};var c={create:function(){return new u}},f={variadic:function(e,t){return a(e,this,o,t.cache.create(),t.serializer)},monadic:function(e,t){return a(e,this,i,t.cache.create(),t.serializer)}}},755776:e=>{"use strict";var t,n,r,i={'"':'"',"\\":"\\","/":"/",b:"\b",f:"\f",n:"\n",r:"\r",t:"\t"};function o(e){throw{name:"SyntaxError",message:e,at:t,text:r}}function a(e){return e&&e!==n&&o("Expected '"+e+"' instead of '"+n+"'"),n=r.charAt(t),t+=1,n}function s(){var e,t="";for("-"===n&&(t="-",a("-"));n>="0"&&n<="9";)t+=n,a();if("."===n)for(t+=".";a()&&n>="0"&&n<="9";)t+=n;if("e"===n||"E"===n)for(t+=n,a(),"-"!==n&&"+"!==n||(t+=n,a());n>="0"&&n<="9";)t+=n,a();return e=Number(t),isFinite(e)||o("Bad number"),e}function l(){var e,t,r,s="";if('"'===n)for(;a();){if('"'===n)return a(),s;if("\\"===n)if(a(),"u"===n){for(r=0,t=0;t<4&&(e=parseInt(a(),16),isFinite(e));t+=1)r=16*r+e;s+=String.fromCharCode(r)}else{if("string"!=typeof i[n])break;s+=i[n]}else s+=n}o("Bad string")}function u(){for(;n&&n<=" ";)a()}function c(){switch(u(),n){case"{":return function(){var e,t={};if("{"===n){if(a("{"),u(),"}"===n)return a("}"),t;for(;n;){if(e=l(),u(),a(":"),Object.prototype.hasOwnProperty.call(t,e)&&o('Duplicate key "'+e+'"'),t[e]=c(),u(),"}"===n)return a("}"),t;a(","),u()}}o("Bad object")}();case"[":return function(){var e=[];if("["===n){if(a("["),u(),"]"===n)return a("]"),e;for(;n;){if(e.push(c()),u(),"]"===n)return a("]"),e;a(","),u()}}o("Bad array")}();case'"':return l();case"-":return s();default:return n>="0"&&n<="9"?s():function(){switch(n){case"t":return a("t"),a("r"),a("u"),a("e"),!0;case"f":return a("f"),a("a"),a("l"),a("s"),a("e"),!1;case"n":return a("n"),a("u"),a("l"),a("l"),null;default:o("Unexpected '"+n+"'")}}()}}e.exports=function(e,i){var a;return r=e,t=0,n=" ",a=c(),u(),n&&o("Syntax error"),"function"==typeof i?function e(t,n){var r,o,a=t[n];if(a&&"object"==typeof a)for(r in c)Object.prototype.hasOwnProperty.call(a,r)&&(void 0===(o=e(a,r))?delete a[r]:a[r]=o);return i.call(t,n,a)}({"":a},""):a}},756170:(e,t,n)=>{e.exports=n(400912)},757061:(e,t)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0})},763375:(e,t,n)=>{e.exports=function(e){return e&&e.length?n(855765)(e):[]}},766944:(e,t,n)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),t.SHA256=void 0;Object.defineProperty(t,"SHA256",{enumerable:!0,get:function(){return n(272669).SHA256}})},767394:(e,t,n)=>{"use strict";var r=n(444576),i=n(146706),o=n(544576),a=r.ArrayBuffer,s=r.TypeError;e.exports=a&&i(a.prototype,"byteLength","get")||function(e){if("ArrayBuffer"!==o(e))throw new s("ArrayBuffer expected");return e.byteLength}},775084:(e,t)=>{"use strict";t.wE=function(e,t={}){const{encode:r=encodeURIComponent,delimiter:i=n}=t,o=d((e instanceof c?e:f(e,t)).tokens,i,r);return function(e={}){const[t,...n]=o(e);if(n.length)throw new TypeError(`Missing parameters: ${n.join(", ")}`);return t}},t.YW=function(e,t={}){const{decode:i=decodeURIComponent,delimiter:o=n}=t,{regexp:a,keys:s}=h(e,t),l=s.map((e=>!1===i?r:"param"===e.type?i:e=>e.split(o).map(i)));return function(e){const t=a.exec(e);if(!t)return!1;const n=t[0],r=Object.create(null);for(let i=1;i<t.length;i++){if(void 0===t[i])continue;const e=s[i-1],n=l[i-1];r[e.name]=n(t[i])}return{path:n,params:r}}};const n="/",r=e=>e,i=/^[$_\p{ID_Start}]$/u,o=/^[$\u200c\u200d\p{ID_Continue}]$/u,a="https://git.new/pathToRegexpError",s={"{":"{","}":"}","(":"(",")":")","[":"[","]":"]","+":"+","?":"?","!":"!"};function l(e){return e.replace(/[.+*?^${}()[\]|/\\]/g,"\\$&")}class u{constructor(e){this.tokens=e}peek(){if(!this._peek){const e=this.tokens.next();this._peek=e.value}return this._peek}tryConsume(e){const t=this.peek();if(t.type===e)return this._peek=void 0,t.value}consume(e){const t=this.tryConsume(e);if(void 0!==t)return t;const{type:n,index:r}=this.peek();throw new TypeError(`Unexpected ${n} at ${r}, expected ${e}: ${a}`)}text(){let e,t="";for(;e=this.tryConsume("CHAR")||this.tryConsume("ESCAPED");)t+=e;return t}}class c{constructor(e){this.tokens=e}}function f(e,t={}){const{encodePath:n=r}=t,l=new u(function*(e){const t=[...e];let n=0;function r(){let e="";if(i.test(t[++n]))for(e+=t[n];o.test(t[++n]);)e+=t[n];else if('"'===t[n]){let r=n;for(;n<t.length;){if('"'===t[++n]){n++,r=0;break}e+="\\"===t[n]?t[++n]:t[n]}if(r)throw new TypeError(`Unterminated quote at ${r}: ${a}`)}if(!e)throw new TypeError(`Missing parameter name at ${n}: ${a}`);return e}for(;n<t.length;){const e=t[n],i=s[e];if(i)yield{type:i,index:n++,value:e};else if("\\"===e)yield{type:"ESCAPED",index:n++,value:t[n++]};else if(":"===e){const e=r();yield{type:"PARAM",index:n,value:e}}else if("*"===e){const e=r();yield{type:"WILDCARD",index:n,value:e}}else yield{type:"CHAR",index:n,value:t[n++]}}return{type:"END",index:n,value:""}}(e));const f=function e(t){const r=[];for(;;){const i=l.text();i&&r.push({type:"text",value:n(i)});const o=l.tryConsume("PARAM");if(o){r.push({type:"param",name:o});continue}const a=l.tryConsume("WILDCARD");if(a){r.push({type:"wildcard",name:a});continue}if(!l.tryConsume("{"))return l.consume(t),r;r.push({type:"group",tokens:e("}")})}}("END");return new c(f)}function d(e,t,n){const i=e.map((e=>function(e,t,n){if("text"===e.type)return()=>[e.value];if("group"===e.type){const r=d(e.tokens,t,n);return e=>{const[t,...n]=r(e);return n.length?[""]:[t]}}const i=n||r;if("wildcard"===e.type&&!1!==n)return n=>{const r=n[e.name];if(null==r)return["",e.name];if(!Array.isArray(r)||0===r.length)throw new TypeError(`Expected "${e.name}" to be a non-empty array`);return[r.map(((t,n)=>{if("string"!=typeof t)throw new TypeError(`Expected "${e.name}/${n}" to be a string`);return i(t)})).join(t)]};return t=>{const n=t[e.name];if(null==n)return["",e.name];if("string"!=typeof n)throw new TypeError(`Expected "${e.name}" to be a string`);return[i(n)]}}(e,t,n)));return e=>{const t=[""];for(const n of i){const[r,...i]=n(e);t[0]+=r,t.push(...i)}return t}}function h(e,t={}){const{delimiter:r=n,end:i=!0,sensitive:o=!1,trailing:a=!0}=t,s=[],u=[],d=o?"":"i",h=(Array.isArray(e)?e:[e]).map((e=>e instanceof c?e:f(e,t)));for(const{tokens:n}of h)for(const e of p(n,0,[])){const t=m(e,r,s);u.push(t)}let g=`^(?:${u.join("|")})`;a&&(g+=`(?:${l(r)}$)?`),g+=i?"$":`(?=${l(r)}|$)`;return{regexp:new RegExp(g,d),keys:s}}function*p(e,t,n){if(t===e.length)return yield n;const r=e[t];if("group"===r.type){const i=n.slice();for(const n of p(r.tokens,0,i))yield*p(e,t+1,n)}else n.push(r);yield*p(e,t+1,n)}function m(e,t,n){let r="",i="",o=!0;for(let s=0;s<e.length;s++){const u=e[s];if("text"!==u.type)if("param"!==u.type&&"wildcard"!==u.type);else{if(!o&&!i)throw new TypeError(`Missing text after "${u.name}": ${a}`);"param"===u.type?r+=`(${g(t,o?"":i)}+)`:r+="([\\s\\S]+)",n.push(u),i="",o=!1}else r+=l(u.value),i+=u.value,o||(o=u.value.includes(t))}return r}function g(e,t){return t.length<2?e.length<2?`[^${l(e+t)}]`:`(?:(?!${l(e)})[^${l(t)}])`:e.length<2?`(?:(?!${l(t)})[^${l(e)}])`:`(?:(?!${l(t)}|${l(e)})[\\s\\S])`}},782944:function(e,t,n){"use strict";var r=this&&this.__awaiter||function(e,t,n,r){return new(n||(n=Promise))((function(i,o){function a(e){try{l(r.next(e))}catch(t){o(t)}}function s(e){try{l(r.throw(e))}catch(t){o(t)}}function l(e){var t;e.done?i(e.value):(t=e.value,t instanceof n?t:new n((function(e){e(t)}))).then(a,s)}l((r=r.apply(e,t||[])).next())}))};Object.defineProperty(t,"__esModule",{value:!0});const i=()=>n(636978);class o extends i().NetworkCore{constructor(e,t){super(e,t);const n=null==e?void 0:e.networkConfig;this._option=e,this._initializeUrlConfig=new(i().UrlConfiguration)(i().Endpoint._initialize,null==n?void 0:n.initializeUrl,null==n?void 0:n.api,null==n?void 0:n.initializeFallbackUrls)}fetchEvaluations(e,t,n,o,a){var s,l,u,c,f,d;return r(this,void 0,void 0,(function*(){const r=t?(0,i()._typedJsonParse)(t,"has_updates","InitializeResponse"):null;let h={user:o,hash:null!==(u=null===(l=null===(s=this._option)||void 0===s?void 0:s.networkConfig)||void 0===l?void 0:l.initializeHashAlgorithm)&&void 0!==u?u:"djb2",deltasResponseRequested:!1,full_checksum:null};if(null==r?void 0:r.has_updates){const e=(null==r?void 0:r.hash_used)!==(null!==(d=null===(f=null===(c=this._option)||void 0===c?void 0:c.networkConfig)||void 0===f?void 0:f.initializeHashAlgorithm)&&void 0!==d?d:"djb2");h=Object.assign(Object.assign({},h),{sinceTime:a&&!e?r.time:0,previousDerivedFields:"derived_fields"in r&&a?r.derived_fields:{},deltasResponseRequested:!0,full_checksum:r.full_checksum,partialUserMatchSinceTime:e?0:r.time})}return this._fetchEvaluations(e,r,h,n)}))}_fetchEvaluations(e,t,i,o){var a,s;return r(this,void 0,void 0,(function*(){const r=yield this.post({sdkKey:e,urlConfig:this._initializeUrlConfig,data:i,retries:2,isStatsigEncodable:!0,priority:o});if(204===(null==r?void 0:r.code))return'{"has_updates": false}';if(200!==(null==r?void 0:r.code))return null!==(a=null==r?void 0:r.body)&&void 0!==a?a:null;if(!0!==(null==t?void 0:t.has_updates)||!0!==(null===(s=r.body)||void 0===s?void 0:s.includes('"is_delta":true'))||!0!==i.deltasResponseRequested)return r.body;const l=(0,n(365694)._resolveDeltasResponse)(t,r.body);return"string"==typeof l?l:this._fetchEvaluations(e,t,Object.assign(Object.assign(Object.assign({},i),l),{deltasResponseRequested:!1}),o)}))}}t.default=o},788238:function(e,t,n){"use strict";var r=this&&this.__createBinding||(Object.create?function(e,t,n,r){void 0===r&&(r=n);var i=Object.getOwnPropertyDescriptor(t,n);i&&!("get"in i?!t.__esModule:i.writable||i.configurable)||(i={enumerable:!0,get:function(){return t[n]}}),Object.defineProperty(e,r,i)}:function(e,t,n,r){void 0===r&&(r=n),e[r]=t[n]}),i=this&&this.__exportStar||function(e,t){for(var n in e)"default"===n||Object.prototype.hasOwnProperty.call(t,n)||r(t,e,n)};Object.defineProperty(t,"__esModule",{value:!0}),i(n(539950),t)},789429:(e,t,n)=>{"use strict";var r=n(444576),i=n(216193);e.exports=function(e){if(i){try{return r.process.getBuiltinModule(e)}catch(t){}try{return Function('return require("'+e+'")')()}catch(t){}}}},794644:(e,t,n)=>{"use strict";var r,i,o,a=n(977811),s=n(743724),l=n(444576),u=n(194901),c=n(820034),f=n(39297),d=n(136955),h=n(116823),p=n(266699),m=n(436840),g=n(562106),v=n(401625),y=n(142787),b=n(152967),_=n(978227),w=n(733392),S=n(591181),E=S.enforce,k=S.get,T=l.Int8Array,x=T&&T.prototype,O=l.Uint8ClampedArray,C=O&&O.prototype,N=T&&y(T),I=x&&y(x),D=Object.prototype,A=l.TypeError,P=_("toStringTag"),L=w("TYPED_ARRAY_TAG"),M="TypedArrayConstructor",R=a&&!!b&&"Opera"!==d(l.opera),F=!1,U={Int8Array:1,Uint8Array:1,Uint8ClampedArray:1,Int16Array:2,Uint16Array:2,Int32Array:4,Uint32Array:4,Float32Array:4,Float64Array:8},j={BigInt64Array:8,BigUint64Array:8},B=function(e){var t=y(e);if(c(t)){var n=k(t);return n&&f(n,M)?n[M]:B(t)}},z=function(e){if(!c(e))return!1;var t=d(e);return f(U,t)||f(j,t)};for(r in U)(o=(i=l[r])&&i.prototype)?E(o)[M]=i:R=!1;for(r in j)(o=(i=l[r])&&i.prototype)&&(E(o)[M]=i);if((!R||!u(N)||N===Function.prototype)&&(N=function(){throw new A("Incorrect invocation")},R))for(r in U)l[r]&&b(l[r],N);if((!R||!I||I===D)&&(I=N.prototype,R))for(r in U)l[r]&&b(l[r].prototype,I);if(R&&y(C)!==I&&b(C,I),s&&!f(I,P))for(r in F=!0,g(I,P,{configurable:!0,get:function(){return c(this)?this[L]:void 0}}),U)l[r]&&p(l[r],L,r);e.exports={NATIVE_ARRAY_BUFFER_VIEWS:R,TYPED_ARRAY_TAG:F&&L,aTypedArray:function(e){if(z(e))return e;throw new A("Target is not a typed array")},aTypedArrayConstructor:function(e){if(u(e)&&(!b||v(N,e)))return e;throw new A(h(e)+" is not a typed array constructor")},exportTypedArrayMethod:function(e,t,n,r){if(s){if(n)for(var i in U){var o=l[i];if(o&&f(o.prototype,e))try{delete o.prototype[e]}catch(a){try{o.prototype[e]=t}catch(u){}}}I[e]&&!n||m(I,e,n?t:R&&x[e]||t,r)}},exportTypedArrayStaticMethod:function(e,t,n){var r,i;if(s){if(b){if(n)for(r in U)if((i=l[r])&&f(i,e))try{delete i[e]}catch(o){}if(N[e]&&!n)return;try{return m(N,e,n?t:R&&N[e]||t)}catch(o){}}for(r in U)!(i=l[r])||i[e]&&!n||m(i,e,t)}},getTypedArrayConstructor:B,isView:function(e){if(!c(e))return!1;var t=d(e);return"DataView"===t||f(U,t)||f(j,t)},isTypedArray:z,TypedArray:N,TypedArrayPrototype:I}},803949:(e,t,n)=>{"use strict";var r=n(746518),i=n(72652),o=n(479306),a=n(28551),s=n(301767);r({target:"Iterator",proto:!0,real:!0},{forEach:function(e){a(this),o(e);var t=s(this),n=0;i(t,(function(t){e(t,n++)}),{IS_RECORD:!0})}})},804106:(e,t,n)=>{"use strict";n.d(t,{XU:()=>f,dT:()=>h,Gr:()=>d});var r,i,o=()=>n(331635),a=n(296540),s=n(327025);!function(e){e.formatDate="FormattedDate",e.formatTime="FormattedTime",e.formatNumber="FormattedNumber",e.formatList="FormattedList",e.formatDisplayName="FormattedDisplayName"}(r||(r={})),function(e){e.formatDate="FormattedDateParts",e.formatTime="FormattedTimeParts",e.formatNumber="FormattedNumberParts",e.formatList="FormattedListParts"}(i||(i={}));var l=function(e){var t=(0,s.A)(),n=e.value,r=e.children,i=(0,o().__rest)(e,["value","children"]);return r(t.formatNumberToParts(n,i))};l.displayName="FormattedNumberParts";function u(e){var t=function(t){var n=(0,s.A)(),r=t.value,i=t.children,a=(0,o().__rest)(t,["value","children"]),l="string"==typeof r?new Date(r||0):r;return i("formatDate"===e?n.formatDateToParts(l,a):n.formatTimeToParts(l,a))};return t.displayName=i[e],t}function c(e){var t=function(t){var n=(0,s.A)(),r=t.value,i=t.children,l=(0,o().__rest)(t,["value","children"]),u=n[e](r,l);if("function"==typeof i)return i(u);var c=n.textComponent||a.Fragment;return a.createElement(c,null,u)};return t.displayName=r[e],t}l.displayName="FormattedNumberParts";var f=c("formatDate"),d=(c("formatTime"),c("formatNumber")),h=c("formatList");c("formatDisplayName"),u("formatDate"),u("formatTime")},810686:(e,t)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),t.SDKType=void 0;const n={};let r;t.SDKType={_get:e=>{var t;return(null!==(t=n[e])&&void 0!==t?t:"js-mono")+(null!=r?r:"")},_setClientType(e,t){n[e]=t},_setBindingType(e){r&&"-react"!==r||(r="-"+e)}}},814603:(e,t,n)=>{"use strict";var r=n(436840),i=n(179504),o=n(500655),a=n(422812),s=URLSearchParams,l=s.prototype,u=i(l.append),c=i(l.delete),f=i(l.forEach),d=i([].push),h=new s("a=1&a=2&b=3");h.delete("a",1),h.delete("b",void 0),h+""!="a=2"&&r(l,"delete",(function(e){var t=arguments.length,n=t<2?void 0:arguments[1];if(t&&void 0===n)return c(this,e);var r=[];f(this,(function(e,t){d(r,{key:t,value:e})})),a(t,1);for(var i,s=o(e),l=o(n),h=0,p=0,m=!1,g=r.length;h<g;)i=r[h++],m||i.key===s?(m=!0,c(this,i.key)):p++;for(;p<g;)(i=r[p++]).key===s&&i.value===l||u(this,i.key,i.value)}),{enumerable:!0,unsafe:!0})},814628:(e,t,n)=>{"use strict";var r=n(746518),i=n(836043);r({target:"Promise",stat:!0},{withResolvers:function(){var e=i.f(this);return{promise:e.promise,resolve:e.resolve,reject:e.reject}}})},815287:(e,t)=>{"use strict";var n=Symbol.for("react.element"),r=Symbol.for("react.portal"),i=Symbol.for("react.fragment"),o=Symbol.for("react.strict_mode"),a=Symbol.for("react.profiler"),s=Symbol.for("react.provider"),l=Symbol.for("react.context"),u=Symbol.for("react.forward_ref"),c=Symbol.for("react.suspense"),f=Symbol.for("react.memo"),d=Symbol.for("react.lazy"),h=Symbol.iterator;var p={isMounted:function(){return!1},enqueueForceUpdate:function(){},enqueueReplaceState:function(){},enqueueSetState:function(){}},m=Object.assign,g={};function v(e,t,n){this.props=e,this.context=t,this.refs=g,this.updater=n||p}function y(){}function b(e,t,n){this.props=e,this.context=t,this.refs=g,this.updater=n||p}v.prototype.isReactComponent={},v.prototype.setState=function(e,t){if("object"!=typeof e&&"function"!=typeof e&&null!=e)throw Error("setState(...): takes an object of state variables to update or a function which returns an object of state variables.");this.updater.enqueueSetState(this,e,t,"setState")},v.prototype.forceUpdate=function(e){this.updater.enqueueForceUpdate(this,e,"forceUpdate")},y.prototype=v.prototype;var _=b.prototype=new y;_.constructor=b,m(_,v.prototype),_.isPureReactComponent=!0;var w=Array.isArray,S=Object.prototype.hasOwnProperty,E={current:null},k={key:!0,ref:!0,__self:!0,__source:!0};function T(e,t,r){var i,o={},a=null,s=null;if(null!=t)for(i in void 0!==t.ref&&(s=t.ref),void 0!==t.key&&(a=""+t.key),t)S.call(t,i)&&!k.hasOwnProperty(i)&&(o[i]=t[i]);var l=arguments.length-2;if(1===l)o.children=r;else if(1<l){for(var u=Array(l),c=0;c<l;c++)u[c]=arguments[c+2];o.children=u}if(e&&e.defaultProps)for(i in l=e.defaultProps)void 0===o[i]&&(o[i]=l[i]);return{$$typeof:n,type:e,key:a,ref:s,props:o,_owner:E.current}}function x(e){return"object"==typeof e&&null!==e&&e.$$typeof===n}var O=/\/+/g;function C(e,t){return"object"==typeof e&&null!==e&&null!=e.key?function(e){var t={"=":"=0",":":"=2"};return"$"+e.replace(/[=:]/g,(function(e){return t[e]}))}(""+e.key):t.toString(36)}function N(e,t,i,o,a){var s=typeof e;"undefined"!==s&&"boolean"!==s||(e=null);var l=!1;if(null===e)l=!0;else switch(s){case"string":case"number":l=!0;break;case"object":switch(e.$$typeof){case n:case r:l=!0}}if(l)return a=a(l=e),e=""===o?"."+C(l,0):o,w(a)?(i="",null!=e&&(i=e.replace(O,"$&/")+"/"),N(a,t,i,"",(function(e){return e}))):null!=a&&(x(a)&&(a=function(e,t){return{$$typeof:n,type:e.type,key:t,ref:e.ref,props:e.props,_owner:e._owner}}(a,i+(!a.key||l&&l.key===a.key?"":(""+a.key).replace(O,"$&/")+"/")+e)),t.push(a)),1;if(l=0,o=""===o?".":o+":",w(e))for(var u=0;u<e.length;u++){var c=o+C(s=e[u],u);l+=N(s,t,i,c,a)}else if(c=function(e){return null===e||"object"!=typeof e?null:"function"==typeof(e=h&&e[h]||e["@@iterator"])?e:null}(e),"function"==typeof c)for(e=c.call(e),u=0;!(s=e.next()).done;)l+=N(s=s.value,t,i,c=o+C(s,u++),a);else if("object"===s)throw t=String(e),Error("Objects are not valid as a React child (found: "+("[object Object]"===t?"object with keys {"+Object.keys(e).join(", ")+"}":t)+"). If you meant to render a collection of children, use an array instead.");return l}function I(e,t,n){if(null==e)return e;var r=[],i=0;return N(e,r,"","",(function(e){return t.call(n,e,i++)})),r}function D(e){if(-1===e._status){var t=e._result;(t=t()).then((function(t){0!==e._status&&-1!==e._status||(e._status=1,e._result=t)}),(function(t){0!==e._status&&-1!==e._status||(e._status=2,e._result=t)})),-1===e._status&&(e._status=0,e._result=t)}if(1===e._status)return e._result.default;throw e._result}var A={current:null},P={transition:null},L={ReactCurrentDispatcher:A,ReactCurrentBatchConfig:P,ReactCurrentOwner:E};t.Children={map:I,forEach:function(e,t,n){I(e,(function(){t.apply(this,arguments)}),n)},count:function(e){var t=0;return I(e,(function(){t++})),t},toArray:function(e){return I(e,(function(e){return e}))||[]},only:function(e){if(!x(e))throw Error("React.Children.only expected to receive a single React element child.");return e}},t.Component=v,t.Fragment=i,t.Profiler=a,t.PureComponent=b,t.StrictMode=o,t.Suspense=c,t.__SECRET_INTERNALS_DO_NOT_USE_OR_YOU_WILL_BE_FIRED=L,t.cloneElement=function(e,t,r){if(null==e)throw Error("React.cloneElement(...): The argument must be a React element, but you passed "+e+".");var i=m({},e.props),o=e.key,a=e.ref,s=e._owner;if(null!=t){if(void 0!==t.ref&&(a=t.ref,s=E.current),void 0!==t.key&&(o=""+t.key),e.type&&e.type.defaultProps)var l=e.type.defaultProps;for(u in t)S.call(t,u)&&!k.hasOwnProperty(u)&&(i[u]=void 0===t[u]&&void 0!==l?l[u]:t[u])}var u=arguments.length-2;if(1===u)i.children=r;else if(1<u){l=Array(u);for(var c=0;c<u;c++)l[c]=arguments[c+2];i.children=l}return{$$typeof:n,type:e.type,key:o,ref:a,props:i,_owner:s}},t.createContext=function(e){return(e={$$typeof:l,_currentValue:e,_currentValue2:e,_threadCount:0,Provider:null,Consumer:null,_defaultValue:null,_globalName:null}).Provider={$$typeof:s,_context:e},e.Consumer=e},t.createElement=T,t.createFactory=function(e){var t=T.bind(null,e);return t.type=e,t},t.createRef=function(){return{current:null}},t.forwardRef=function(e){return{$$typeof:u,render:e}},t.isValidElement=x,t.lazy=function(e){return{$$typeof:d,_payload:{_status:-1,_result:e},_init:D}},t.memo=function(e,t){return{$$typeof:f,type:e,compare:void 0===t?null:t}},t.startTransition=function(e){var t=P.transition;P.transition={};try{e()}finally{P.transition=t}},t.unstable_act=function(){throw Error("act(...) is not supported in production builds of React.")},t.useCallback=function(e,t){return A.current.useCallback(e,t)},t.useContext=function(e){return A.current.useContext(e)},t.useDebugValue=function(){},t.useDeferredValue=function(e){return A.current.useDeferredValue(e)},t.useEffect=function(e,t){return A.current.useEffect(e,t)},t.useId=function(){return A.current.useId()},t.useImperativeHandle=function(e,t,n){return A.current.useImperativeHandle(e,t,n)},t.useInsertionEffect=function(e,t){return A.current.useInsertionEffect(e,t)},t.useLayoutEffect=function(e,t){return A.current.useLayoutEffect(e,t)},t.useMemo=function(e,t){return A.current.useMemo(e,t)},t.useReducer=function(e,t,n){return A.current.useReducer(e,t,n)},t.useRef=function(e){return A.current.useRef(e)},t.useState=function(e){return A.current.useState(e)},t.useSyncExternalStore=function(e,t,n){return A.current.useSyncExternalStore(e,t,n)},t.useTransition=function(){return A.current.useTransition()},t.version="18.2.0"},816573:(e,t,n)=>{"use strict";var r=n(743724),i=n(562106),o=n(903238),a=ArrayBuffer.prototype;r&&!("detached"in a)&&i(a,"detached",{configurable:!0,get:function(){return o(this)}})},821903:(e,t,n)=>{"use strict";var r=n(794644),i=n(143839).findLast,o=r.aTypedArray;(0,r.exportTypedArrayMethod)("findLast",(function(e){return i(o(this),e,arguments.length>1?arguments[1]:void 0)}))},822015:(e,t,n)=>{"use strict";n.d(t,{S:()=>d});var r,i=()=>n(331635),o=n(892677),a=n(754819),s=n(215067);function l(e){return"function"==typeof e}function u(e,t,n,i,a,c,f){if(1===e.length&&(0,o.eW)(e[0]))return[{type:r.literal,value:e[0].value}];for(var d=[],h=0,p=e;h<p.length;h++){var m=p[h];if((0,o.eW)(m))d.push({type:r.literal,value:m.value});else if((0,o.jA)(m))"number"==typeof c&&d.push({type:r.literal,value:n.getNumberFormat(t).format(c)});else{var g=m.value;if(!a||!(g in a))throw new s.Ei(g,f);var v=a[g];if((0,o.Im)(m))v&&"string"!=typeof v&&"number"!=typeof v||(v="string"==typeof v||"number"==typeof v?String(v):""),d.push({type:"string"==typeof v?r.literal:r.object,value:v});else if((0,o.tv)(m)){var y="string"==typeof m.style?i.date[m.style]:(0,o.Tu)(m.style)?m.style.parsedOptions:void 0;d.push({type:r.literal,value:n.getDateTimeFormat(t,y).format(v)})}else if((0,o.Qh)(m)){y="string"==typeof m.style?i.time[m.style]:(0,o.Tu)(m.style)?m.style.parsedOptions:i.time.medium;d.push({type:r.literal,value:n.getDateTimeFormat(t,y).format(v)})}else if((0,o.oF)(m)){(y="string"==typeof m.style?i.number[m.style]:(0,o.N1)(m.style)?m.style.parsedOptions:void 0)&&y.scale&&(v*=y.scale||1),d.push({type:r.literal,value:n.getNumberFormat(t,y).format(v)})}else{if((0,o.xm)(m)){var b=m.children,_=m.value,w=a[_];if(!l(w))throw new s.Zo(_,"function",f);var S=w(u(b,t,n,i,a,c).map((function(e){return e.value})));Array.isArray(S)||(S=[S]),d.push.apply(d,S.map((function(e){return{type:"string"==typeof e?r.literal:r.object,value:e}})))}if((0,o.Jp)(m)){if(!(E=m.options[v]||m.options.other))throw new s.$x(m.value,v,Object.keys(m.options),f);d.push.apply(d,u(E.value,t,n,i,a))}else if((0,o.N6)(m)){var E;if(!(E=m.options["=".concat(v)])){if(!Intl.PluralRules)throw new s.IF('Intl.PluralRules is not available in this environment.\nTry polyfilling it using "@formatjs/intl-pluralrules"\n',s.O4.MISSING_INTL_API,f);var k=n.getPluralRules(t,{type:m.pluralType}).select(v-(m.offset||0));E=m.options[k]||m.options.other}if(!E)throw new s.$x(m.value,v,Object.keys(m.options),f);d.push.apply(d,u(E.value,t,n,i,a,v-(m.offset||0)))}else;}}}return function(e){return e.length<2?e:e.reduce((function(e,t){var n=e[e.length-1];return n&&n.type===r.literal&&t.type===r.literal?n.value+=t.value:e.push(t),e}),[])}(d)}function c(e,t){return t?Object.keys(e).reduce((function(n,r){var o,a;return n[r]=(o=e[r],(a=t[r])?(0,i().__assign)((0,i().__assign)((0,i().__assign)({},o||{}),a||{}),Object.keys(o).reduce((function(e,t){return e[t]=(0,i().__assign)((0,i().__assign)({},o[t]),a[t]||{}),e}),{})):o),n}),(0,i().__assign)({},e)):e}function f(e){return{create:function(){return{get:function(t){return e[t]},set:function(t,n){e[t]=n}}}}}!function(e){e[e.literal=0]="literal",e[e.object=1]="object"}(r||(r={}));var d=function(){function e(t,n,o,s){var l,d=this;if(void 0===n&&(n=e.defaultLocale),this.formatterCache={number:{},dateTime:{},pluralRules:{}},this.format=function(e){var t=d.formatToParts(e);if(1===t.length)return t[0].value;var n=t.reduce((function(e,t){return e.length&&t.type===r.literal&&"string"==typeof e[e.length-1]?e[e.length-1]+=t.value:e.push(t.value),e}),[]);return n.length<=1?n[0]||"":n},this.formatToParts=function(e){return u(d.ast,d.locales,d.formatters,d.formats,e,void 0,d.message)},this.resolvedOptions=function(){var e;return{locale:(null===(e=d.resolvedLocale)||void 0===e?void 0:e.toString())||Intl.NumberFormat.supportedLocalesOf(d.locales)[0]}},this.getAst=function(){return d.ast},this.locales=n,this.resolvedLocale=e.resolveLocale(n),"string"==typeof t){if(this.message=t,!e.__parse)throw new TypeError("IntlMessageFormat.__parse must be set to process `message` of type `string`");var h=s||{},p=(h.formatters,(0,i().__rest)(h,["formatters"]));this.ast=e.__parse(t,(0,i().__assign)((0,i().__assign)({},p),{locale:this.resolvedLocale}))}else this.ast=t;if(!Array.isArray(this.ast))throw new TypeError("A message must be provided as a String or AST.");this.formats=c(e.formats,o),this.formatters=s&&s.formatters||(void 0===(l=this.formatterCache)&&(l={number:{},dateTime:{},pluralRules:{}}),{getNumberFormat:(0,a.B)((function(){for(var e,t=[],n=0;n<arguments.length;n++)t[n]=arguments[n];return new((e=Intl.NumberFormat).bind.apply(e,(0,i().__spreadArray)([void 0],t,!1)))}),{cache:f(l.number),strategy:a.W.variadic}),getDateTimeFormat:(0,a.B)((function(){for(var e,t=[],n=0;n<arguments.length;n++)t[n]=arguments[n];return new((e=Intl.DateTimeFormat).bind.apply(e,(0,i().__spreadArray)([void 0],t,!1)))}),{cache:f(l.dateTime),strategy:a.W.variadic}),getPluralRules:(0,a.B)((function(){for(var e,t=[],n=0;n<arguments.length;n++)t[n]=arguments[n];return new((e=Intl.PluralRules).bind.apply(e,(0,i().__spreadArray)([void 0],t,!1)))}),{cache:f(l.pluralRules),strategy:a.W.variadic})})}return Object.defineProperty(e,"defaultLocale",{get:function(){return e.memoizedDefaultLocale||(e.memoizedDefaultLocale=(new Intl.NumberFormat).resolvedOptions().locale),e.memoizedDefaultLocale},enumerable:!1,configurable:!0}),e.memoizedDefaultLocale=null,e.resolveLocale=function(e){if(void 0!==Intl.Locale){var t=Intl.NumberFormat.supportedLocalesOf(e);return t.length>0?new Intl.Locale(t[0]):new Intl.Locale("string"==typeof e?e:e[0])}},e.__parse=o.qg,e.formats={number:{integer:{maximumFractionDigits:0},currency:{style:"currency"},percent:{style:"percent"}},date:{short:{month:"numeric",day:"numeric",year:"2-digit"},medium:{month:"short",day:"numeric",year:"numeric"},long:{month:"long",day:"numeric",year:"numeric"},full:{weekday:"long",month:"long",day:"numeric",year:"numeric"}},time:{short:{hour:"numeric",minute:"numeric"},medium:{hour:"numeric",minute:"numeric",second:"numeric"},long:{hour:"numeric",minute:"numeric",second:"numeric",timeZoneName:"short"},full:{hour:"numeric",minute:"numeric",second:"numeric",timeZoneName:"short"}}},e}()},822551:(e,t,n)=>{"use strict";var r=n(296540),i=n(920194);function o(e){for(var t="https://reactjs.org/docs/error-decoder.html?invariant="+e,n=1;n<arguments.length;n++)t+="&args[]="+encodeURIComponent(arguments[n]);return"Minified React error #"+e+"; visit "+t+" for the full message or use the non-minified dev environment for full errors and additional helpful warnings."}var a=new Set,s={};function l(e,t){u(e,t),u(e+"Capture",t)}function u(e,t){for(s[e]=t,e=0;e<t.length;e++)a.add(t[e])}var c=!("undefined"==typeof window||void 0===window.document||void 0===window.document.createElement),f=Object.prototype.hasOwnProperty,d=/^[:A-Z_a-z\u00C0-\u00D6\u00D8-\u00F6\u00F8-\u02FF\u0370-\u037D\u037F-\u1FFF\u200C-\u200D\u2070-\u218F\u2C00-\u2FEF\u3001-\uD7FF\uF900-\uFDCF\uFDF0-\uFFFD][:A-Z_a-z\u00C0-\u00D6\u00D8-\u00F6\u00F8-\u02FF\u0370-\u037D\u037F-\u1FFF\u200C-\u200D\u2070-\u218F\u2C00-\u2FEF\u3001-\uD7FF\uF900-\uFDCF\uFDF0-\uFFFD\-.0-9\u00B7\u0300-\u036F\u203F-\u2040]*$/,h={},p={};function m(e,t,n,r,i,o,a){this.acceptsBooleans=2===t||3===t||4===t,this.attributeName=r,this.attributeNamespace=i,this.mustUseProperty=n,this.propertyName=e,this.type=t,this.sanitizeURL=o,this.removeEmptyString=a}var g={};"children dangerouslySetInnerHTML defaultValue defaultChecked innerHTML suppressContentEditableWarning suppressHydrationWarning style".split(" ").forEach((function(e){g[e]=new m(e,0,!1,e,null,!1,!1)})),[["acceptCharset","accept-charset"],["className","class"],["htmlFor","for"],["httpEquiv","http-equiv"]].forEach((function(e){var t=e[0];g[t]=new m(t,1,!1,e[1],null,!1,!1)})),["contentEditable","draggable","spellCheck","value"].forEach((function(e){g[e]=new m(e,2,!1,e.toLowerCase(),null,!1,!1)})),["autoReverse","externalResourcesRequired","focusable","preserveAlpha"].forEach((function(e){g[e]=new m(e,2,!1,e,null,!1,!1)})),"allowFullScreen async autoFocus autoPlay controls default defer disabled disablePictureInPicture disableRemotePlayback formNoValidate hidden loop noModule noValidate open playsInline readOnly required reversed scoped seamless itemScope".split(" ").forEach((function(e){g[e]=new m(e,3,!1,e.toLowerCase(),null,!1,!1)})),["checked","multiple","muted","selected"].forEach((function(e){g[e]=new m(e,3,!0,e,null,!1,!1)})),["capture","download"].forEach((function(e){g[e]=new m(e,4,!1,e,null,!1,!1)})),["cols","rows","size","span"].forEach((function(e){g[e]=new m(e,6,!1,e,null,!1,!1)})),["rowSpan","start"].forEach((function(e){g[e]=new m(e,5,!1,e.toLowerCase(),null,!1,!1)}));var v=/[\-:]([a-z])/g;function y(e){return e[1].toUpperCase()}function b(e,t,n,r){var i=g.hasOwnProperty(t)?g[t]:null;(null!==i?0!==i.type:r||!(2<t.length)||"o"!==t[0]&&"O"!==t[0]||"n"!==t[1]&&"N"!==t[1])&&(function(e,t,n,r){if(null==t||function(e,t,n,r){if(null!==n&&0===n.type)return!1;switch(typeof t){case"function":case"symbol":return!0;case"boolean":return!r&&(null!==n?!n.acceptsBooleans:"data-"!==(e=e.toLowerCase().slice(0,5))&&"aria-"!==e);default:return!1}}(e,t,n,r))return!0;if(r)return!1;if(null!==n)switch(n.type){case 3:return!t;case 4:return!1===t;case 5:return isNaN(t);case 6:return isNaN(t)||1>t}return!1}(t,n,i,r)&&(n=null),r||null===i?function(e){return!!f.call(p,e)||!f.call(h,e)&&(d.test(e)?p[e]=!0:(h[e]=!0,!1))}(t)&&(null===n?e.removeAttribute(t):e.setAttribute(t,""+n)):i.mustUseProperty?e[i.propertyName]=null===n?3!==i.type&&"":n:(t=i.attributeName,r=i.attributeNamespace,null===n?e.removeAttribute(t):(n=3===(i=i.type)||4===i&&!0===n?"":""+n,r?e.setAttributeNS(r,t,n):e.setAttribute(t,n))))}"accent-height alignment-baseline arabic-form baseline-shift cap-height clip-path clip-rule color-interpolation color-interpolation-filters color-profile color-rendering dominant-baseline enable-background fill-opacity fill-rule flood-color flood-opacity font-family font-size font-size-adjust font-stretch font-style font-variant font-weight glyph-name glyph-orientation-horizontal glyph-orientation-vertical horiz-adv-x horiz-origin-x image-rendering letter-spacing lighting-color marker-end marker-mid marker-start overline-position overline-thickness paint-order panose-1 pointer-events rendering-intent shape-rendering stop-color stop-opacity strikethrough-position strikethrough-thickness stroke-dasharray stroke-dashoffset stroke-linecap stroke-linejoin stroke-miterlimit stroke-opacity stroke-width text-anchor text-decoration text-rendering underline-position underline-thickness unicode-bidi unicode-range units-per-em v-alphabetic v-hanging v-ideographic v-mathematical vector-effect vert-adv-y vert-origin-x vert-origin-y word-spacing writing-mode xmlns:xlink x-height".split(" ").forEach((function(e){var t=e.replace(v,y);g[t]=new m(t,1,!1,e,null,!1,!1)})),"xlink:actuate xlink:arcrole xlink:role xlink:show xlink:title xlink:type".split(" ").forEach((function(e){var t=e.replace(v,y);g[t]=new m(t,1,!1,e,"http://www.w3.org/1999/xlink",!1,!1)})),["xml:base","xml:lang","xml:space"].forEach((function(e){var t=e.replace(v,y);g[t]=new m(t,1,!1,e,"http://www.w3.org/XML/1998/namespace",!1,!1)})),["tabIndex","crossOrigin"].forEach((function(e){g[e]=new m(e,1,!1,e.toLowerCase(),null,!1,!1)})),g.xlinkHref=new m("xlinkHref",1,!1,"xlink:href","http://www.w3.org/1999/xlink",!0,!1),["src","href","action","formAction"].forEach((function(e){g[e]=new m(e,1,!1,e.toLowerCase(),null,!0,!0)}));var _=r.__SECRET_INTERNALS_DO_NOT_USE_OR_YOU_WILL_BE_FIRED,w=Symbol.for("react.element"),S=Symbol.for("react.portal"),E=Symbol.for("react.fragment"),k=Symbol.for("react.strict_mode"),T=Symbol.for("react.profiler"),x=Symbol.for("react.provider"),O=Symbol.for("react.context"),C=Symbol.for("react.forward_ref"),N=Symbol.for("react.suspense"),I=Symbol.for("react.suspense_list"),D=Symbol.for("react.memo"),A=Symbol.for("react.lazy");Symbol.for("react.scope"),Symbol.for("react.debug_trace_mode");var P=Symbol.for("react.offscreen");Symbol.for("react.legacy_hidden"),Symbol.for("react.cache"),Symbol.for("react.tracing_marker");var L=Symbol.iterator;function M(e){return null===e||"object"!=typeof e?null:"function"==typeof(e=L&&e[L]||e["@@iterator"])?e:null}var R,F=Object.assign;function U(e){if(void 0===R)try{throw Error()}catch(n){var t=n.stack.trim().match(/\n( *(at )?)/);R=t&&t[1]||""}return"\n"+R+e}var j=!1;function B(e,t){if(!e||j)return"";j=!0;var n=Error.prepareStackTrace;Error.prepareStackTrace=void 0;try{if(t)if(t=function(){throw Error()},Object.defineProperty(t.prototype,"props",{set:function(){throw Error()}}),"object"==typeof Reflect&&Reflect.construct){try{Reflect.construct(t,[])}catch(u){var r=u}Reflect.construct(e,[],t)}else{try{t.call()}catch(u){r=u}e.call(t.prototype)}else{try{throw Error()}catch(u){r=u}e()}}catch(u){if(u&&r&&"string"==typeof u.stack){for(var i=u.stack.split("\n"),o=r.stack.split("\n"),a=i.length-1,s=o.length-1;1<=a&&0<=s&&i[a]!==o[s];)s--;for(;1<=a&&0<=s;a--,s--)if(i[a]!==o[s]){if(1!==a||1!==s)do{if(a--,0>--s||i[a]!==o[s]){var l="\n"+i[a].replace(" at new "," at ");return e.displayName&&l.includes("<anonymous>")&&(l=l.replace("<anonymous>",e.displayName)),l}}while(1<=a&&0<=s);break}}}finally{j=!1,Error.prepareStackTrace=n}return(e=e?e.displayName||e.name:"")?U(e):""}function z(e){switch(e.tag){case 5:return U(e.type);case 16:return U("Lazy");case 13:return U("Suspense");case 19:return U("SuspenseList");case 0:case 2:case 15:return e=B(e.type,!1);case 11:return e=B(e.type.render,!1);case 1:return e=B(e.type,!0);default:return""}}function H(e){if(null==e)return null;if("function"==typeof e)return e.displayName||e.name||null;if("string"==typeof e)return e;switch(e){case E:return"Fragment";case S:return"Portal";case T:return"Profiler";case k:return"StrictMode";case N:return"Suspense";case I:return"SuspenseList"}if("object"==typeof e)switch(e.$$typeof){case O:return(e.displayName||"Context")+".Consumer";case x:return(e._context.displayName||"Context")+".Provider";case C:var t=e.render;return(e=e.displayName)||(e=""!==(e=t.displayName||t.name||"")?"ForwardRef("+e+")":"ForwardRef"),e;case D:return null!==(t=e.displayName||null)?t:H(e.type)||"Memo";case A:t=e._payload,e=e._init;try{return H(e(t))}catch(n){}}return null}function V(e){var t=e.type;switch(e.tag){case 24:return"Cache";case 9:return(t.displayName||"Context")+".Consumer";case 10:return(t._context.displayName||"Context")+".Provider";case 18:return"DehydratedFragment";case 11:return e=(e=t.render).displayName||e.name||"",t.displayName||(""!==e?"ForwardRef("+e+")":"ForwardRef");case 7:return"Fragment";case 5:return t;case 4:return"Portal";case 3:return"Root";case 6:return"Text";case 16:return H(t);case 8:return t===k?"StrictMode":"Mode";case 22:return"Offscreen";case 12:return"Profiler";case 21:return"Scope";case 13:return"Suspense";case 19:return"SuspenseList";case 25:return"TracingMarker";case 1:case 0:case 17:case 2:case 14:case 15:if("function"==typeof t)return t.displayName||t.name||null;if("string"==typeof t)return t}return null}function $(e){switch(typeof e){case"boolean":case"number":case"string":case"undefined":case"object":return e;default:return""}}function G(e){var t=e.type;return(e=e.nodeName)&&"input"===e.toLowerCase()&&("checkbox"===t||"radio"===t)}function q(e){e._valueTracker||(e._valueTracker=function(e){var t=G(e)?"checked":"value",n=Object.getOwnPropertyDescriptor(e.constructor.prototype,t),r=""+e[t];if(!e.hasOwnProperty(t)&&void 0!==n&&"function"==typeof n.get&&"function"==typeof n.set){var i=n.get,o=n.set;return Object.defineProperty(e,t,{configurable:!0,get:function(){return i.call(this)},set:function(e){r=""+e,o.call(this,e)}}),Object.defineProperty(e,t,{enumerable:n.enumerable}),{getValue:function(){return r},setValue:function(e){r=""+e},stopTracking:function(){e._valueTracker=null,delete e[t]}}}}(e))}function W(e){if(!e)return!1;var t=e._valueTracker;if(!t)return!0;var n=t.getValue(),r="";return e&&(r=G(e)?e.checked?"true":"false":e.value),(e=r)!==n&&(t.setValue(e),!0)}function K(e){if(void 0===(e=e||("undefined"!=typeof document?document:void 0)))return null;try{return e.activeElement||e.body}catch(t){return e.body}}function Z(e,t){var n=t.checked;return F({},t,{defaultChecked:void 0,defaultValue:void 0,value:void 0,checked:null!=n?n:e._wrapperState.initialChecked})}function J(e,t){var n=null==t.defaultValue?"":t.defaultValue,r=null!=t.checked?t.checked:t.defaultChecked;n=$(null!=t.value?t.value:n),e._wrapperState={initialChecked:r,initialValue:n,controlled:"checkbox"===t.type||"radio"===t.type?null!=t.checked:null!=t.value}}function Y(e,t){null!=(t=t.checked)&&b(e,"checked",t,!1)}function Q(e,t){Y(e,t);var n=$(t.value),r=t.type;if(null!=n)"number"===r?(0===n&&""===e.value||e.value!=n)&&(e.value=""+n):e.value!==""+n&&(e.value=""+n);else if("submit"===r||"reset"===r)return void e.removeAttribute("value");t.hasOwnProperty("value")?ee(e,t.type,n):t.hasOwnProperty("defaultValue")&&ee(e,t.type,$(t.defaultValue)),null==t.checked&&null!=t.defaultChecked&&(e.defaultChecked=!!t.defaultChecked)}function X(e,t,n){if(t.hasOwnProperty("value")||t.hasOwnProperty("defaultValue")){var r=t.type;if(!("submit"!==r&&"reset"!==r||void 0!==t.value&&null!==t.value))return;t=""+e._wrapperState.initialValue,n||t===e.value||(e.value=t),e.defaultValue=t}""!==(n=e.name)&&(e.name=""),e.defaultChecked=!!e._wrapperState.initialChecked,""!==n&&(e.name=n)}function ee(e,t,n){"number"===t&&K(e.ownerDocument)===e||(null==n?e.defaultValue=""+e._wrapperState.initialValue:e.defaultValue!==""+n&&(e.defaultValue=""+n))}var te=Array.isArray;function ne(e,t,n,r){if(e=e.options,t){t={};for(var i=0;i<n.length;i++)t["$"+n[i]]=!0;for(n=0;n<e.length;n++)i=t.hasOwnProperty("$"+e[n].value),e[n].selected!==i&&(e[n].selected=i),i&&r&&(e[n].defaultSelected=!0)}else{for(n=""+$(n),t=null,i=0;i<e.length;i++){if(e[i].value===n)return e[i].selected=!0,void(r&&(e[i].defaultSelected=!0));null!==t||e[i].disabled||(t=e[i])}null!==t&&(t.selected=!0)}}function re(e,t){if(null!=t.dangerouslySetInnerHTML)throw Error(o(91));return F({},t,{value:void 0,defaultValue:void 0,children:""+e._wrapperState.initialValue})}function ie(e,t){var n=t.value;if(null==n){if(n=t.children,t=t.defaultValue,null!=n){if(null!=t)throw Error(o(92));if(te(n)){if(1<n.length)throw Error(o(93));n=n[0]}t=n}null==t&&(t=""),n=t}e._wrapperState={initialValue:$(n)}}function oe(e,t){var n=$(t.value),r=$(t.defaultValue);null!=n&&((n=""+n)!==e.value&&(e.value=n),null==t.defaultValue&&e.defaultValue!==n&&(e.defaultValue=n)),null!=r&&(e.defaultValue=""+r)}function ae(e){var t=e.textContent;t===e._wrapperState.initialValue&&""!==t&&null!==t&&(e.value=t)}function se(e){switch(e){case"svg":return"http://www.w3.org/2000/svg";case"math":return"http://www.w3.org/1998/Math/MathML";default:return"http://www.w3.org/1999/xhtml"}}function le(e,t){return null==e||"http://www.w3.org/1999/xhtml"===e?se(t):"http://www.w3.org/2000/svg"===e&&"foreignObject"===t?"http://www.w3.org/1999/xhtml":e}var ue,ce,fe=(ce=function(e,t){if("http://www.w3.org/2000/svg"!==e.namespaceURI||"innerHTML"in e)e.innerHTML=t;else{for((ue=ue||document.createElement("div")).innerHTML="<svg>"+t.valueOf().toString()+"</svg>",t=ue.firstChild;e.firstChild;)e.removeChild(e.firstChild);for(;t.firstChild;)e.appendChild(t.firstChild)}},"undefined"!=typeof MSApp&&MSApp.execUnsafeLocalFunction?function(e,t,n,r){MSApp.execUnsafeLocalFunction((function(){return ce(e,t)}))}:ce);function de(e,t){if(t){var n=e.firstChild;if(n&&n===e.lastChild&&3===n.nodeType)return void(n.nodeValue=t)}e.textContent=t}var he={animationIterationCount:!0,aspectRatio:!0,borderImageOutset:!0,borderImageSlice:!0,borderImageWidth:!0,boxFlex:!0,boxFlexGroup:!0,boxOrdinalGroup:!0,columnCount:!0,columns:!0,flex:!0,flexGrow:!0,flexPositive:!0,flexShrink:!0,flexNegative:!0,flexOrder:!0,gridArea:!0,gridRow:!0,gridRowEnd:!0,gridRowSpan:!0,gridRowStart:!0,gridColumn:!0,gridColumnEnd:!0,gridColumnSpan:!0,gridColumnStart:!0,fontWeight:!0,lineClamp:!0,lineHeight:!0,opacity:!0,order:!0,orphans:!0,tabSize:!0,widows:!0,zIndex:!0,zoom:!0,fillOpacity:!0,floodOpacity:!0,stopOpacity:!0,strokeDasharray:!0,strokeDashoffset:!0,strokeMiterlimit:!0,strokeOpacity:!0,strokeWidth:!0},pe=["Webkit","ms","Moz","O"];function me(e,t,n){return null==t||"boolean"==typeof t||""===t?"":n||"number"!=typeof t||0===t||he.hasOwnProperty(e)&&he[e]?(""+t).trim():t+"px"}function ge(e,t){for(var n in e=e.style,t)if(t.hasOwnProperty(n)){var r=0===n.indexOf("--"),i=me(n,t[n],r);"float"===n&&(n="cssFloat"),r?e.setProperty(n,i):e[n]=i}}Object.keys(he).forEach((function(e){pe.forEach((function(t){t=t+e.charAt(0).toUpperCase()+e.substring(1),he[t]=he[e]}))}));var ve=F({menuitem:!0},{area:!0,base:!0,br:!0,col:!0,embed:!0,hr:!0,img:!0,input:!0,keygen:!0,link:!0,meta:!0,param:!0,source:!0,track:!0,wbr:!0});function ye(e,t){if(t){if(ve[e]&&(null!=t.children||null!=t.dangerouslySetInnerHTML))throw Error(o(137,e));if(null!=t.dangerouslySetInnerHTML){if(null!=t.children)throw Error(o(60));if("object"!=typeof t.dangerouslySetInnerHTML||!("__html"in t.dangerouslySetInnerHTML))throw Error(o(61))}if(null!=t.style&&"object"!=typeof t.style)throw Error(o(62))}}function be(e,t){if(-1===e.indexOf("-"))return"string"==typeof t.is;switch(e){case"annotation-xml":case"color-profile":case"font-face":case"font-face-src":case"font-face-uri":case"font-face-format":case"font-face-name":case"missing-glyph":return!1;default:return!0}}var _e=null;function we(e){return(e=e.target||e.srcElement||window).correspondingUseElement&&(e=e.correspondingUseElement),3===e.nodeType?e.parentNode:e}var Se=null,Ee=null,ke=null;function Te(e){if(e=bi(e)){if("function"!=typeof Se)throw Error(o(280));var t=e.stateNode;t&&(t=wi(t),Se(e.stateNode,e.type,t))}}function xe(e){Ee?ke?ke.push(e):ke=[e]:Ee=e}function Oe(){if(Ee){var e=Ee,t=ke;if(ke=Ee=null,Te(e),t)for(e=0;e<t.length;e++)Te(t[e])}}function Ce(e,t){return e(t)}function Ne(){}var Ie=!1;function De(e,t,n){if(Ie)return e(t,n);Ie=!0;try{return Ce(e,t,n)}finally{Ie=!1,(null!==Ee||null!==ke)&&(Ne(),Oe())}}function Ae(e,t){var n=e.stateNode;if(null===n)return null;var r=wi(n);if(null===r)return null;n=r[t];e:switch(t){case"onClick":case"onClickCapture":case"onDoubleClick":case"onDoubleClickCapture":case"onMouseDown":case"onMouseDownCapture":case"onMouseMove":case"onMouseMoveCapture":case"onMouseUp":case"onMouseUpCapture":case"onMouseEnter":(r=!r.disabled)||(r=!("button"===(e=e.type)||"input"===e||"select"===e||"textarea"===e)),e=!r;break e;default:e=!1}if(e)return null;if(n&&"function"!=typeof n)throw Error(o(231,t,typeof n));return n}var Pe=!1;if(c)try{var Le={};Object.defineProperty(Le,"passive",{get:function(){Pe=!0}}),window.addEventListener("test",Le,Le),window.removeEventListener("test",Le,Le)}catch(ce){Pe=!1}function Me(e,t,n,r,i,o,a,s,l){var u=Array.prototype.slice.call(arguments,3);try{t.apply(n,u)}catch(c){this.onError(c)}}var Re=!1,Fe=null,Ue=!1,je=null,Be={onError:function(e){Re=!0,Fe=e}};function ze(e,t,n,r,i,o,a,s,l){Re=!1,Fe=null,Me.apply(Be,arguments)}function He(e){var t=e,n=e;if(e.alternate)for(;t.return;)t=t.return;else{e=t;do{0!=(4098&(t=e).flags)&&(n=t.return),e=t.return}while(e)}return 3===t.tag?n:null}function Ve(e){if(13===e.tag){var t=e.memoizedState;if(null===t&&(null!==(e=e.alternate)&&(t=e.memoizedState)),null!==t)return t.dehydrated}return null}function $e(e){if(He(e)!==e)throw Error(o(188))}function Ge(e){return null!==(e=function(e){var t=e.alternate;if(!t){if(null===(t=He(e)))throw Error(o(188));return t!==e?null:e}for(var n=e,r=t;;){var i=n.return;if(null===i)break;var a=i.alternate;if(null===a){if(null!==(r=i.return)){n=r;continue}break}if(i.child===a.child){for(a=i.child;a;){if(a===n)return $e(i),e;if(a===r)return $e(i),t;a=a.sibling}throw Error(o(188))}if(n.return!==r.return)n=i,r=a;else{for(var s=!1,l=i.child;l;){if(l===n){s=!0,n=i,r=a;break}if(l===r){s=!0,r=i,n=a;break}l=l.sibling}if(!s){for(l=a.child;l;){if(l===n){s=!0,n=a,r=i;break}if(l===r){s=!0,r=a,n=i;break}l=l.sibling}if(!s)throw Error(o(189))}}if(n.alternate!==r)throw Error(o(190))}if(3!==n.tag)throw Error(o(188));return n.stateNode.current===n?e:t}(e))?qe(e):null}function qe(e){if(5===e.tag||6===e.tag)return e;for(e=e.child;null!==e;){var t=qe(e);if(null!==t)return t;e=e.sibling}return null}var We=i.unstable_scheduleCallback,Ke=i.unstable_cancelCallback,Ze=i.unstable_shouldYield,Je=i.unstable_requestPaint,Ye=i.unstable_now,Qe=i.unstable_getCurrentPriorityLevel,Xe=i.unstable_ImmediatePriority,et=i.unstable_UserBlockingPriority,tt=i.unstable_NormalPriority,nt=i.unstable_LowPriority,rt=i.unstable_IdlePriority,it=null,ot=null;var at=Math.clz32?Math.clz32:function(e){return e>>>=0,0===e?32:31-(st(e)/lt|0)|0},st=Math.log,lt=Math.LN2;var ut=64,ct=4194304;function ft(e){switch(e&-e){case 1:return 1;case 2:return 2;case 4:return 4;case 8:return 8;case 16:return 16;case 32:return 32;case 64:case 128:case 256:case 512:case 1024:case 2048:case 4096:case 8192:case 16384:case 32768:case 65536:case 131072:case 262144:case 524288:case 1048576:case 2097152:return 4194240&e;case 4194304:case 8388608:case 16777216:case 33554432:case 67108864:return 130023424&e;case 134217728:return 134217728;case 268435456:return 268435456;case 536870912:return 536870912;case 1073741824:return 1073741824;default:return e}}function dt(e,t){var n=e.pendingLanes;if(0===n)return 0;var r=0,i=e.suspendedLanes,o=e.pingedLanes,a=268435455&n;if(0!==a){var s=a&~i;0!==s?r=ft(s):0!==(o&=a)&&(r=ft(o))}else 0!==(a=n&~i)?r=ft(a):0!==o&&(r=ft(o));if(0===r)return 0;if(0!==t&&t!==r&&0==(t&i)&&((i=r&-r)>=(o=t&-t)||16===i&&0!=(4194240&o)))return t;if(0!=(4&r)&&(r|=16&n),0!==(t=e.entangledLanes))for(e=e.entanglements,t&=r;0<t;)i=1<<(n=31-at(t)),r|=e[n],t&=~i;return r}function ht(e,t){switch(e){case 1:case 2:case 4:return t+250;case 8:case 16:case 32:case 64:case 128:case 256:case 512:case 1024:case 2048:case 4096:case 8192:case 16384:case 32768:case 65536:case 131072:case 262144:case 524288:case 1048576:case 2097152:return t+5e3;default:return-1}}function pt(e){return 0!==(e=-1073741825&e.pendingLanes)?e:1073741824&e?1073741824:0}function mt(){var e=ut;return 0==(4194240&(ut<<=1))&&(ut=64),e}function gt(e){for(var t=[],n=0;31>n;n++)t.push(e);return t}function vt(e,t,n){e.pendingLanes|=t,536870912!==t&&(e.suspendedLanes=0,e.pingedLanes=0),(e=e.eventTimes)[t=31-at(t)]=n}function yt(e,t){var n=e.entangledLanes|=t;for(e=e.entanglements;n;){var r=31-at(n),i=1<<r;i&t|e[r]&t&&(e[r]|=t),n&=~i}}var bt=0;function _t(e){return 1<(e&=-e)?4<e?0!=(268435455&e)?16:536870912:4:1}var wt,St,Et,kt,Tt,xt=!1,Ot=[],Ct=null,Nt=null,It=null,Dt=new Map,At=new Map,Pt=[],Lt="mousedown mouseup touchcancel touchend touchstart auxclick dblclick pointercancel pointerdown pointerup dragend dragstart drop compositionend compositionstart keydown keypress keyup input textInput copy cut paste click change contextmenu reset submit".split(" ");function Mt(e,t){switch(e){case"focusin":case"focusout":Ct=null;break;case"dragenter":case"dragleave":Nt=null;break;case"mouseover":case"mouseout":It=null;break;case"pointerover":case"pointerout":Dt.delete(t.pointerId);break;case"gotpointercapture":case"lostpointercapture":At.delete(t.pointerId)}}function Rt(e,t,n,r,i,o){return null===e||e.nativeEvent!==o?(e={blockedOn:t,domEventName:n,eventSystemFlags:r,nativeEvent:o,targetContainers:[i]},null!==t&&(null!==(t=bi(t))&&St(t)),e):(e.eventSystemFlags|=r,t=e.targetContainers,null!==i&&-1===t.indexOf(i)&&t.push(i),e)}function Ft(e){var t=yi(e.target);if(null!==t){var n=He(t);if(null!==n)if(13===(t=n.tag)){if(null!==(t=Ve(n)))return e.blockedOn=t,void Tt(e.priority,(function(){Et(n)}))}else if(3===t&&n.stateNode.current.memoizedState.isDehydrated)return void(e.blockedOn=3===n.tag?n.stateNode.containerInfo:null)}e.blockedOn=null}function Ut(e){if(null!==e.blockedOn)return!1;for(var t=e.targetContainers;0<t.length;){var n=Zt(e.domEventName,e.eventSystemFlags,t[0],e.nativeEvent);if(null!==n)return null!==(t=bi(n))&&St(t),e.blockedOn=n,!1;var r=new(n=e.nativeEvent).constructor(n.type,n);_e=r,n.target.dispatchEvent(r),_e=null,t.shift()}return!0}function jt(e,t,n){Ut(e)&&n.delete(t)}function Bt(){xt=!1,null!==Ct&&Ut(Ct)&&(Ct=null),null!==Nt&&Ut(Nt)&&(Nt=null),null!==It&&Ut(It)&&(It=null),Dt.forEach(jt),At.forEach(jt)}function zt(e,t){e.blockedOn===t&&(e.blockedOn=null,xt||(xt=!0,i.unstable_scheduleCallback(i.unstable_NormalPriority,Bt)))}function Ht(e){function t(t){return zt(t,e)}if(0<Ot.length){zt(Ot[0],e);for(var n=1;n<Ot.length;n++){var r=Ot[n];r.blockedOn===e&&(r.blockedOn=null)}}for(null!==Ct&&zt(Ct,e),null!==Nt&&zt(Nt,e),null!==It&&zt(It,e),Dt.forEach(t),At.forEach(t),n=0;n<Pt.length;n++)(r=Pt[n]).blockedOn===e&&(r.blockedOn=null);for(;0<Pt.length&&null===(n=Pt[0]).blockedOn;)Ft(n),null===n.blockedOn&&Pt.shift()}var Vt=_.ReactCurrentBatchConfig,$t=!0;function Gt(e,t,n,r){var i=bt,o=Vt.transition;Vt.transition=null;try{bt=1,Wt(e,t,n,r)}finally{bt=i,Vt.transition=o}}function qt(e,t,n,r){var i=bt,o=Vt.transition;Vt.transition=null;try{bt=4,Wt(e,t,n,r)}finally{bt=i,Vt.transition=o}}function Wt(e,t,n,r){if($t){var i=Zt(e,t,n,r);if(null===i)$r(e,t,r,Kt,n),Mt(e,r);else if(function(e,t,n,r,i){switch(t){case"focusin":return Ct=Rt(Ct,e,t,n,r,i),!0;case"dragenter":return Nt=Rt(Nt,e,t,n,r,i),!0;case"mouseover":return It=Rt(It,e,t,n,r,i),!0;case"pointerover":var o=i.pointerId;return Dt.set(o,Rt(Dt.get(o)||null,e,t,n,r,i)),!0;case"gotpointercapture":return o=i.pointerId,At.set(o,Rt(At.get(o)||null,e,t,n,r,i)),!0}return!1}(i,e,t,n,r))r.stopPropagation();else if(Mt(e,r),4&t&&-1<Lt.indexOf(e)){for(;null!==i;){var o=bi(i);if(null!==o&&wt(o),null===(o=Zt(e,t,n,r))&&$r(e,t,r,Kt,n),o===i)break;i=o}null!==i&&r.stopPropagation()}else $r(e,t,r,null,n)}}var Kt=null;function Zt(e,t,n,r){if(Kt=null,null!==(e=yi(e=we(r))))if(null===(t=He(e)))e=null;else if(13===(n=t.tag)){if(null!==(e=Ve(t)))return e;e=null}else if(3===n){if(t.stateNode.current.memoizedState.isDehydrated)return 3===t.tag?t.stateNode.containerInfo:null;e=null}else t!==e&&(e=null);return Kt=e,null}function Jt(e){switch(e){case"cancel":case"click":case"close":case"contextmenu":case"copy":case"cut":case"auxclick":case"dblclick":case"dragend":case"dragstart":case"drop":case"focusin":case"focusout":case"input":case"invalid":case"keydown":case"keypress":case"keyup":case"mousedown":case"mouseup":case"paste":case"pause":case"play":case"pointercancel":case"pointerdown":case"pointerup":case"ratechange":case"reset":case"resize":case"seeked":case"submit":case"touchcancel":case"touchend":case"touchstart":case"volumechange":case"change":case"selectionchange":case"textInput":case"compositionstart":case"compositionend":case"compositionupdate":case"beforeblur":case"afterblur":case"beforeinput":case"blur":case"fullscreenchange":case"focus":case"hashchange":case"popstate":case"select":case"selectstart":return 1;case"drag":case"dragenter":case"dragexit":case"dragleave":case"dragover":case"mousemove":case"mouseout":case"mouseover":case"pointermove":case"pointerout":case"pointerover":case"scroll":case"toggle":case"touchmove":case"wheel":case"mouseenter":case"mouseleave":case"pointerenter":case"pointerleave":return 4;case"message":switch(Qe()){case Xe:return 1;case et:return 4;case tt:case nt:return 16;case rt:return 536870912;default:return 16}default:return 16}}var Yt=null,Qt=null,Xt=null;function en(){if(Xt)return Xt;var e,t,n=Qt,r=n.length,i="value"in Yt?Yt.value:Yt.textContent,o=i.length;for(e=0;e<r&&n[e]===i[e];e++);var a=r-e;for(t=1;t<=a&&n[r-t]===i[o-t];t++);return Xt=i.slice(e,1<t?1-t:void 0)}function tn(e){var t=e.keyCode;return"charCode"in e?0===(e=e.charCode)&&13===t&&(e=13):e=t,10===e&&(e=13),32<=e||13===e?e:0}function nn(){return!0}function rn(){return!1}function on(e){function t(t,n,r,i,o){for(var a in this._reactName=t,this._targetInst=r,this.type=n,this.nativeEvent=i,this.target=o,this.currentTarget=null,e)e.hasOwnProperty(a)&&(t=e[a],this[a]=t?t(i):i[a]);return this.isDefaultPrevented=(null!=i.defaultPrevented?i.defaultPrevented:!1===i.returnValue)?nn:rn,this.isPropagationStopped=rn,this}return F(t.prototype,{preventDefault:function(){this.defaultPrevented=!0;var e=this.nativeEvent;e&&(e.preventDefault?e.preventDefault():"unknown"!=typeof e.returnValue&&(e.returnValue=!1),this.isDefaultPrevented=nn)},stopPropagation:function(){var e=this.nativeEvent;e&&(e.stopPropagation?e.stopPropagation():"unknown"!=typeof e.cancelBubble&&(e.cancelBubble=!0),this.isPropagationStopped=nn)},persist:function(){},isPersistent:nn}),t}var an,sn,ln,un={eventPhase:0,bubbles:0,cancelable:0,timeStamp:function(e){return e.timeStamp||Date.now()},defaultPrevented:0,isTrusted:0},cn=on(un),fn=F({},un,{view:0,detail:0}),dn=on(fn),hn=F({},fn,{screenX:0,screenY:0,clientX:0,clientY:0,pageX:0,pageY:0,ctrlKey:0,shiftKey:0,altKey:0,metaKey:0,getModifierState:Tn,button:0,buttons:0,relatedTarget:function(e){return void 0===e.relatedTarget?e.fromElement===e.srcElement?e.toElement:e.fromElement:e.relatedTarget},movementX:function(e){return"movementX"in e?e.movementX:(e!==ln&&(ln&&"mousemove"===e.type?(an=e.screenX-ln.screenX,sn=e.screenY-ln.screenY):sn=an=0,ln=e),an)},movementY:function(e){return"movementY"in e?e.movementY:sn}}),pn=on(hn),mn=on(F({},hn,{dataTransfer:0})),gn=on(F({},fn,{relatedTarget:0})),vn=on(F({},un,{animationName:0,elapsedTime:0,pseudoElement:0})),yn=F({},un,{clipboardData:function(e){return"clipboardData"in e?e.clipboardData:window.clipboardData}}),bn=on(yn),_n=on(F({},un,{data:0})),wn={Esc:"Escape",Spacebar:" ",Left:"ArrowLeft",Up:"ArrowUp",Right:"ArrowRight",Down:"ArrowDown",Del:"Delete",Win:"OS",Menu:"ContextMenu",Apps:"ContextMenu",Scroll:"ScrollLock",MozPrintableKey:"Unidentified"},Sn={8:"Backspace",9:"Tab",12:"Clear",13:"Enter",16:"Shift",17:"Control",18:"Alt",19:"Pause",20:"CapsLock",27:"Escape",32:" ",33:"PageUp",34:"PageDown",35:"End",36:"Home",37:"ArrowLeft",38:"ArrowUp",39:"ArrowRight",40:"ArrowDown",45:"Insert",46:"Delete",112:"F1",113:"F2",114:"F3",115:"F4",116:"F5",117:"F6",118:"F7",119:"F8",120:"F9",121:"F10",122:"F11",123:"F12",144:"NumLock",145:"ScrollLock",224:"Meta"},En={Alt:"altKey",Control:"ctrlKey",Meta:"metaKey",Shift:"shiftKey"};function kn(e){var t=this.nativeEvent;return t.getModifierState?t.getModifierState(e):!!(e=En[e])&&!!t[e]}function Tn(){return kn}var xn=F({},fn,{key:function(e){if(e.key){var t=wn[e.key]||e.key;if("Unidentified"!==t)return t}return"keypress"===e.type?13===(e=tn(e))?"Enter":String.fromCharCode(e):"keydown"===e.type||"keyup"===e.type?Sn[e.keyCode]||"Unidentified":""},code:0,location:0,ctrlKey:0,shiftKey:0,altKey:0,metaKey:0,repeat:0,locale:0,getModifierState:Tn,charCode:function(e){return"keypress"===e.type?tn(e):0},keyCode:function(e){return"keydown"===e.type||"keyup"===e.type?e.keyCode:0},which:function(e){return"keypress"===e.type?tn(e):"keydown"===e.type||"keyup"===e.type?e.keyCode:0}}),On=on(xn),Cn=on(F({},hn,{pointerId:0,width:0,height:0,pressure:0,tangentialPressure:0,tiltX:0,tiltY:0,twist:0,pointerType:0,isPrimary:0})),Nn=on(F({},fn,{touches:0,targetTouches:0,changedTouches:0,altKey:0,metaKey:0,ctrlKey:0,shiftKey:0,getModifierState:Tn})),In=on(F({},un,{propertyName:0,elapsedTime:0,pseudoElement:0})),Dn=F({},hn,{deltaX:function(e){return"deltaX"in e?e.deltaX:"wheelDeltaX"in e?-e.wheelDeltaX:0},deltaY:function(e){return"deltaY"in e?e.deltaY:"wheelDeltaY"in e?-e.wheelDeltaY:"wheelDelta"in e?-e.wheelDelta:0},deltaZ:0,deltaMode:0}),An=on(Dn),Pn=[9,13,27,32],Ln=c&&"CompositionEvent"in window,Mn=null;c&&"documentMode"in document&&(Mn=document.documentMode);var Rn=c&&"TextEvent"in window&&!Mn,Fn=c&&(!Ln||Mn&&8<Mn&&11>=Mn),Un=String.fromCharCode(32),jn=!1;function Bn(e,t){switch(e){case"keyup":return-1!==Pn.indexOf(t.keyCode);case"keydown":return 229!==t.keyCode;case"keypress":case"mousedown":case"focusout":return!0;default:return!1}}function zn(e){return"object"==typeof(e=e.detail)&&"data"in e?e.data:null}var Hn=!1;var Vn={color:!0,date:!0,datetime:!0,"datetime-local":!0,email:!0,month:!0,number:!0,password:!0,range:!0,search:!0,tel:!0,text:!0,time:!0,url:!0,week:!0};function $n(e){var t=e&&e.nodeName&&e.nodeName.toLowerCase();return"input"===t?!!Vn[e.type]:"textarea"===t}function Gn(e,t,n,r){xe(r),0<(t=qr(t,"onChange")).length&&(n=new cn("onChange","change",null,n,r),e.push({event:n,listeners:t}))}var qn=null,Wn=null;function Kn(e){Ur(e,0)}function Zn(e){if(W(_i(e)))return e}function Jn(e,t){if("change"===e)return t}var Yn=!1;if(c){var Qn;if(c){var Xn="oninput"in document;if(!Xn){var er=document.createElement("div");er.setAttribute("oninput","return;"),Xn="function"==typeof er.oninput}Qn=Xn}else Qn=!1;Yn=Qn&&(!document.documentMode||9<document.documentMode)}function tr(){qn&&(qn.detachEvent("onpropertychange",nr),Wn=qn=null)}function nr(e){if("value"===e.propertyName&&Zn(Wn)){var t=[];Gn(t,Wn,e,we(e)),De(Kn,t)}}function rr(e,t,n){"focusin"===e?(tr(),Wn=n,(qn=t).attachEvent("onpropertychange",nr)):"focusout"===e&&tr()}function ir(e){if("selectionchange"===e||"keyup"===e||"keydown"===e)return Zn(Wn)}function or(e,t){if("click"===e)return Zn(t)}function ar(e,t){if("input"===e||"change"===e)return Zn(t)}var sr="function"==typeof Object.is?Object.is:function(e,t){return e===t&&(0!==e||1/e==1/t)||e!=e&&t!=t};function lr(e,t){if(sr(e,t))return!0;if("object"!=typeof e||null===e||"object"!=typeof t||null===t)return!1;var n=Object.keys(e),r=Object.keys(t);if(n.length!==r.length)return!1;for(r=0;r<n.length;r++){var i=n[r];if(!f.call(t,i)||!sr(e[i],t[i]))return!1}return!0}function ur(e){for(;e&&e.firstChild;)e=e.firstChild;return e}function cr(e,t){var n,r=ur(e);for(e=0;r;){if(3===r.nodeType){if(n=e+r.textContent.length,e<=t&&n>=t)return{node:r,offset:t-e};e=n}e:{for(;r;){if(r.nextSibling){r=r.nextSibling;break e}r=r.parentNode}r=void 0}r=ur(r)}}function fr(e,t){return!(!e||!t)&&(e===t||(!e||3!==e.nodeType)&&(t&&3===t.nodeType?fr(e,t.parentNode):"contains"in e?e.contains(t):!!e.compareDocumentPosition&&!!(16&e.compareDocumentPosition(t))))}function dr(){for(var e=window,t=K();t instanceof e.HTMLIFrameElement;){try{var n="string"==typeof t.contentWindow.location.href}catch(r){n=!1}if(!n)break;t=K((e=t.contentWindow).document)}return t}function hr(e){var t=e&&e.nodeName&&e.nodeName.toLowerCase();return t&&("input"===t&&("text"===e.type||"search"===e.type||"tel"===e.type||"url"===e.type||"password"===e.type)||"textarea"===t||"true"===e.contentEditable)}function pr(e){var t=dr(),n=e.focusedElem,r=e.selectionRange;if(t!==n&&n&&n.ownerDocument&&fr(n.ownerDocument.documentElement,n)){if(null!==r&&hr(n))if(t=r.start,void 0===(e=r.end)&&(e=t),"selectionStart"in n)n.selectionStart=t,n.selectionEnd=Math.min(e,n.value.length);else if((e=(t=n.ownerDocument||document)&&t.defaultView||window).getSelection){e=e.getSelection();var i=n.textContent.length,o=Math.min(r.start,i);r=void 0===r.end?o:Math.min(r.end,i),!e.extend&&o>r&&(i=r,r=o,o=i),i=cr(n,o);var a=cr(n,r);i&&a&&(1!==e.rangeCount||e.anchorNode!==i.node||e.anchorOffset!==i.offset||e.focusNode!==a.node||e.focusOffset!==a.offset)&&((t=t.createRange()).setStart(i.node,i.offset),e.removeAllRanges(),o>r?(e.addRange(t),e.extend(a.node,a.offset)):(t.setEnd(a.node,a.offset),e.addRange(t)))}for(t=[],e=n;e=e.parentNode;)1===e.nodeType&&t.push({element:e,left:e.scrollLeft,top:e.scrollTop});for("function"==typeof n.focus&&n.focus(),n=0;n<t.length;n++)(e=t[n]).element.scrollLeft=e.left,e.element.scrollTop=e.top}}var mr=c&&"documentMode"in document&&11>=document.documentMode,gr=null,vr=null,yr=null,br=!1;function _r(e,t,n){var r=n.window===n?n.document:9===n.nodeType?n:n.ownerDocument;br||null==gr||gr!==K(r)||("selectionStart"in(r=gr)&&hr(r)?r={start:r.selectionStart,end:r.selectionEnd}:r={anchorNode:(r=(r.ownerDocument&&r.ownerDocument.defaultView||window).getSelection()).anchorNode,anchorOffset:r.anchorOffset,focusNode:r.focusNode,focusOffset:r.focusOffset},yr&&lr(yr,r)||(yr=r,0<(r=qr(vr,"onSelect")).length&&(t=new cn("onSelect","select",null,t,n),e.push({event:t,listeners:r}),t.target=gr)))}function wr(e,t){var n={};return n[e.toLowerCase()]=t.toLowerCase(),n["Webkit"+e]="webkit"+t,n["Moz"+e]="moz"+t,n}var Sr={animationend:wr("Animation","AnimationEnd"),animationiteration:wr("Animation","AnimationIteration"),animationstart:wr("Animation","AnimationStart"),transitionend:wr("Transition","TransitionEnd")},Er={},kr={};function Tr(e){if(Er[e])return Er[e];if(!Sr[e])return e;var t,n=Sr[e];for(t in n)if(n.hasOwnProperty(t)&&t in kr)return Er[e]=n[t];return e}c&&(kr=document.createElement("div").style,"AnimationEvent"in window||(delete Sr.animationend.animation,delete Sr.animationiteration.animation,delete Sr.animationstart.animation),"TransitionEvent"in window||delete Sr.transitionend.transition);var xr=Tr("animationend"),Or=Tr("animationiteration"),Cr=Tr("animationstart"),Nr=Tr("transitionend"),Ir=new Map,Dr="abort auxClick cancel canPlay canPlayThrough click close contextMenu copy cut drag dragEnd dragEnter dragExit dragLeave dragOver dragStart drop durationChange emptied encrypted ended error gotPointerCapture input invalid keyDown keyPress keyUp load loadedData loadedMetadata loadStart lostPointerCapture mouseDown mouseMove mouseOut mouseOver mouseUp paste pause play playing pointerCancel pointerDown pointerMove pointerOut pointerOver pointerUp progress rateChange reset resize seeked seeking stalled submit suspend timeUpdate touchCancel touchEnd touchStart volumeChange scroll toggle touchMove waiting wheel".split(" ");function Ar(e,t){Ir.set(e,t),l(t,[e])}for(var Pr=0;Pr<Dr.length;Pr++){var Lr=Dr[Pr];Ar(Lr.toLowerCase(),"on"+(Lr[0].toUpperCase()+Lr.slice(1)))}Ar(xr,"onAnimationEnd"),Ar(Or,"onAnimationIteration"),Ar(Cr,"onAnimationStart"),Ar("dblclick","onDoubleClick"),Ar("focusin","onFocus"),Ar("focusout","onBlur"),Ar(Nr,"onTransitionEnd"),u("onMouseEnter",["mouseout","mouseover"]),u("onMouseLeave",["mouseout","mouseover"]),u("onPointerEnter",["pointerout","pointerover"]),u("onPointerLeave",["pointerout","pointerover"]),l("onChange","change click focusin focusout input keydown keyup selectionchange".split(" ")),l("onSelect","focusout contextmenu dragend focusin keydown keyup mousedown mouseup selectionchange".split(" ")),l("onBeforeInput",["compositionend","keypress","textInput","paste"]),l("onCompositionEnd","compositionend focusout keydown keypress keyup mousedown".split(" ")),l("onCompositionStart","compositionstart focusout keydown keypress keyup mousedown".split(" ")),l("onCompositionUpdate","compositionupdate focusout keydown keypress keyup mousedown".split(" "));var Mr="abort canplay canplaythrough durationchange emptied encrypted ended error loadeddata loadedmetadata loadstart pause play playing progress ratechange resize seeked seeking stalled suspend timeupdate volumechange waiting".split(" "),Rr=new Set("cancel close invalid load scroll toggle".split(" ").concat(Mr));function Fr(e,t,n){var r=e.type||"unknown-event";e.currentTarget=n,function(e,t,n,r,i,a,s,l,u){if(ze.apply(this,arguments),Re){if(!Re)throw Error(o(198));var c=Fe;Re=!1,Fe=null,Ue||(Ue=!0,je=c)}}(r,t,void 0,e),e.currentTarget=null}function Ur(e,t){t=0!=(4&t);for(var n=0;n<e.length;n++){var r=e[n],i=r.event;r=r.listeners;e:{var o=void 0;if(t)for(var a=r.length-1;0<=a;a--){var s=r[a],l=s.instance,u=s.currentTarget;if(s=s.listener,l!==o&&i.isPropagationStopped())break e;Fr(i,s,u),o=l}else for(a=0;a<r.length;a++){if(l=(s=r[a]).instance,u=s.currentTarget,s=s.listener,l!==o&&i.isPropagationStopped())break e;Fr(i,s,u),o=l}}}if(Ue)throw e=je,Ue=!1,je=null,e}function jr(e,t){var n=t[mi];void 0===n&&(n=t[mi]=new Set);var r=e+"__bubble";n.has(r)||(Vr(t,e,2,!1),n.add(r))}function Br(e,t,n){var r=0;t&&(r|=4),Vr(n,e,r,t)}var zr="_reactListening"+Math.random().toString(36).slice(2);function Hr(e){if(!e[zr]){e[zr]=!0,a.forEach((function(t){"selectionchange"!==t&&(Rr.has(t)||Br(t,!1,e),Br(t,!0,e))}));var t=9===e.nodeType?e:e.ownerDocument;null===t||t[zr]||(t[zr]=!0,Br("selectionchange",!1,t))}}function Vr(e,t,n,r){switch(Jt(t)){case 1:var i=Gt;break;case 4:i=qt;break;default:i=Wt}n=i.bind(null,t,n,e),i=void 0,!Pe||"touchstart"!==t&&"touchmove"!==t&&"wheel"!==t||(i=!0),r?void 0!==i?e.addEventListener(t,n,{capture:!0,passive:i}):e.addEventListener(t,n,!0):void 0!==i?e.addEventListener(t,n,{passive:i}):e.addEventListener(t,n,!1)}function $r(e,t,n,r,i){var o=r;if(0==(1&t)&&0==(2&t)&&null!==r)e:for(;;){if(null===r)return;var a=r.tag;if(3===a||4===a){var s=r.stateNode.containerInfo;if(s===i||8===s.nodeType&&s.parentNode===i)break;if(4===a)for(a=r.return;null!==a;){var l=a.tag;if((3===l||4===l)&&((l=a.stateNode.containerInfo)===i||8===l.nodeType&&l.parentNode===i))return;a=a.return}for(;null!==s;){if(null===(a=yi(s)))return;if(5===(l=a.tag)||6===l){r=o=a;continue e}s=s.parentNode}}r=r.return}De((function(){var r=o,i=we(n),a=[];e:{var s=Ir.get(e);if(void 0!==s){var l=cn,u=e;switch(e){case"keypress":if(0===tn(n))break e;case"keydown":case"keyup":l=On;break;case"focusin":u="focus",l=gn;break;case"focusout":u="blur",l=gn;break;case"beforeblur":case"afterblur":l=gn;break;case"click":if(2===n.button)break e;case"auxclick":case"dblclick":case"mousedown":case"mousemove":case"mouseup":case"mouseout":case"mouseover":case"contextmenu":l=pn;break;case"drag":case"dragend":case"dragenter":case"dragexit":case"dragleave":case"dragover":case"dragstart":case"drop":l=mn;break;case"touchcancel":case"touchend":case"touchmove":case"touchstart":l=Nn;break;case xr:case Or:case Cr:l=vn;break;case Nr:l=In;break;case"scroll":l=dn;break;case"wheel":l=An;break;case"copy":case"cut":case"paste":l=bn;break;case"gotpointercapture":case"lostpointercapture":case"pointercancel":case"pointerdown":case"pointermove":case"pointerout":case"pointerover":case"pointerup":l=Cn}var c=0!=(4&t),f=!c&&"scroll"===e,d=c?null!==s?s+"Capture":null:s;c=[];for(var h,p=r;null!==p;){var m=(h=p).stateNode;if(5===h.tag&&null!==m&&(h=m,null!==d&&(null!=(m=Ae(p,d))&&c.push(Gr(p,m,h)))),f)break;p=p.return}0<c.length&&(s=new l(s,u,null,n,i),a.push({event:s,listeners:c}))}}if(0==(7&t)){if(l="mouseout"===e||"pointerout"===e,(!(s="mouseover"===e||"pointerover"===e)||n===_e||!(u=n.relatedTarget||n.fromElement)||!yi(u)&&!u[pi])&&(l||s)&&(s=i.window===i?i:(s=i.ownerDocument)?s.defaultView||s.parentWindow:window,l?(l=r,null!==(u=(u=n.relatedTarget||n.toElement)?yi(u):null)&&(u!==(f=He(u))||5!==u.tag&&6!==u.tag)&&(u=null)):(l=null,u=r),l!==u)){if(c=pn,m="onMouseLeave",d="onMouseEnter",p="mouse","pointerout"!==e&&"pointerover"!==e||(c=Cn,m="onPointerLeave",d="onPointerEnter",p="pointer"),f=null==l?s:_i(l),h=null==u?s:_i(u),(s=new c(m,p+"leave",l,n,i)).target=f,s.relatedTarget=h,m=null,yi(i)===r&&((c=new c(d,p+"enter",u,n,i)).target=h,c.relatedTarget=f,m=c),f=m,l&&u)e:{for(d=u,p=0,h=c=l;h;h=Wr(h))p++;for(h=0,m=d;m;m=Wr(m))h++;for(;0<p-h;)c=Wr(c),p--;for(;0<h-p;)d=Wr(d),h--;for(;p--;){if(c===d||null!==d&&c===d.alternate)break e;c=Wr(c),d=Wr(d)}c=null}else c=null;null!==l&&Kr(a,s,l,c,!1),null!==u&&null!==f&&Kr(a,f,u,c,!0)}if("select"===(l=(s=r?_i(r):window).nodeName&&s.nodeName.toLowerCase())||"input"===l&&"file"===s.type)var g=Jn;else if($n(s))if(Yn)g=ar;else{g=ir;var v=rr}else(l=s.nodeName)&&"input"===l.toLowerCase()&&("checkbox"===s.type||"radio"===s.type)&&(g=or);switch(g&&(g=g(e,r))?Gn(a,g,n,i):(v&&v(e,s,r),"focusout"===e&&(v=s._wrapperState)&&v.controlled&&"number"===s.type&&ee(s,"number",s.value)),v=r?_i(r):window,e){case"focusin":($n(v)||"true"===v.contentEditable)&&(gr=v,vr=r,yr=null);break;case"focusout":yr=vr=gr=null;break;case"mousedown":br=!0;break;case"contextmenu":case"mouseup":case"dragend":br=!1,_r(a,n,i);break;case"selectionchange":if(mr)break;case"keydown":case"keyup":_r(a,n,i)}var y;if(Ln)e:{switch(e){case"compositionstart":var b="onCompositionStart";break e;case"compositionend":b="onCompositionEnd";break e;case"compositionupdate":b="onCompositionUpdate";break e}b=void 0}else Hn?Bn(e,n)&&(b="onCompositionEnd"):"keydown"===e&&229===n.keyCode&&(b="onCompositionStart");b&&(Fn&&"ko"!==n.locale&&(Hn||"onCompositionStart"!==b?"onCompositionEnd"===b&&Hn&&(y=en()):(Qt="value"in(Yt=i)?Yt.value:Yt.textContent,Hn=!0)),0<(v=qr(r,b)).length&&(b=new _n(b,e,null,n,i),a.push({event:b,listeners:v}),y?b.data=y:null!==(y=zn(n))&&(b.data=y))),(y=Rn?function(e,t){switch(e){case"compositionend":return zn(t);case"keypress":return 32!==t.which?null:(jn=!0,Un);case"textInput":return(e=t.data)===Un&&jn?null:e;default:return null}}(e,n):function(e,t){if(Hn)return"compositionend"===e||!Ln&&Bn(e,t)?(e=en(),Xt=Qt=Yt=null,Hn=!1,e):null;switch(e){case"paste":default:return null;case"keypress":if(!(t.ctrlKey||t.altKey||t.metaKey)||t.ctrlKey&&t.altKey){if(t.char&&1<t.char.length)return t.char;if(t.which)return String.fromCharCode(t.which)}return null;case"compositionend":return Fn&&"ko"!==t.locale?null:t.data}}(e,n))&&(0<(r=qr(r,"onBeforeInput")).length&&(i=new _n("onBeforeInput","beforeinput",null,n,i),a.push({event:i,listeners:r}),i.data=y))}Ur(a,t)}))}function Gr(e,t,n){return{instance:e,listener:t,currentTarget:n}}function qr(e,t){for(var n=t+"Capture",r=[];null!==e;){var i=e,o=i.stateNode;5===i.tag&&null!==o&&(i=o,null!=(o=Ae(e,n))&&r.unshift(Gr(e,o,i)),null!=(o=Ae(e,t))&&r.push(Gr(e,o,i))),e=e.return}return r}function Wr(e){if(null===e)return null;do{e=e.return}while(e&&5!==e.tag);return e||null}function Kr(e,t,n,r,i){for(var o=t._reactName,a=[];null!==n&&n!==r;){var s=n,l=s.alternate,u=s.stateNode;if(null!==l&&l===r)break;5===s.tag&&null!==u&&(s=u,i?null!=(l=Ae(n,o))&&a.unshift(Gr(n,l,s)):i||null!=(l=Ae(n,o))&&a.push(Gr(n,l,s))),n=n.return}0!==a.length&&e.push({event:t,listeners:a})}var Zr=/\r\n?/g,Jr=/\u0000|\uFFFD/g;function Yr(e){return("string"==typeof e?e:""+e).replace(Zr,"\n").replace(Jr,"")}function Qr(e,t,n){if(t=Yr(t),Yr(e)!==t&&n)throw Error(o(425))}function Xr(){}var ei=null,ti=null;function ni(e,t){return"textarea"===e||"noscript"===e||"string"==typeof t.children||"number"==typeof t.children||"object"==typeof t.dangerouslySetInnerHTML&&null!==t.dangerouslySetInnerHTML&&null!=t.dangerouslySetInnerHTML.__html}var ri="function"==typeof setTimeout?setTimeout:void 0,ii="function"==typeof clearTimeout?clearTimeout:void 0,oi="function"==typeof Promise?Promise:void 0,ai="function"==typeof queueMicrotask?queueMicrotask:void 0!==oi?function(e){return oi.resolve(null).then(e).catch(si)}:ri;function si(e){setTimeout((function(){throw e}))}function li(e,t){var n=t,r=0;do{var i=n.nextSibling;if(e.removeChild(n),i&&8===i.nodeType)if("/$"===(n=i.data)){if(0===r)return e.removeChild(i),void Ht(t);r--}else"$"!==n&&"$?"!==n&&"$!"!==n||r++;n=i}while(n);Ht(t)}function ui(e){for(;null!=e;e=e.nextSibling){var t=e.nodeType;if(1===t||3===t)break;if(8===t){if("$"===(t=e.data)||"$!"===t||"$?"===t)break;if("/$"===t)return null}}return e}function ci(e){e=e.previousSibling;for(var t=0;e;){if(8===e.nodeType){var n=e.data;if("$"===n||"$!"===n||"$?"===n){if(0===t)return e;t--}else"/$"===n&&t++}e=e.previousSibling}return null}var fi=Math.random().toString(36).slice(2),di="__reactFiber$"+fi,hi="__reactProps$"+fi,pi="__reactContainer$"+fi,mi="__reactEvents$"+fi,gi="__reactListeners$"+fi,vi="__reactHandles$"+fi;function yi(e){var t=e[di];if(t)return t;for(var n=e.parentNode;n;){if(t=n[pi]||n[di]){if(n=t.alternate,null!==t.child||null!==n&&null!==n.child)for(e=ci(e);null!==e;){if(n=e[di])return n;e=ci(e)}return t}n=(e=n).parentNode}return null}function bi(e){return!(e=e[di]||e[pi])||5!==e.tag&&6!==e.tag&&13!==e.tag&&3!==e.tag?null:e}function _i(e){if(5===e.tag||6===e.tag)return e.stateNode;throw Error(o(33))}function wi(e){return e[hi]||null}var Si=[],Ei=-1;function ki(e){return{current:e}}function Ti(e){0>Ei||(e.current=Si[Ei],Si[Ei]=null,Ei--)}function xi(e,t){Ei++,Si[Ei]=e.current,e.current=t}var Oi={},Ci=ki(Oi),Ni=ki(!1),Ii=Oi;function Di(e,t){var n=e.type.contextTypes;if(!n)return Oi;var r=e.stateNode;if(r&&r.__reactInternalMemoizedUnmaskedChildContext===t)return r.__reactInternalMemoizedMaskedChildContext;var i,o={};for(i in n)o[i]=t[i];return r&&((e=e.stateNode).__reactInternalMemoizedUnmaskedChildContext=t,e.__reactInternalMemoizedMaskedChildContext=o),o}function Ai(e){return null!=(e=e.childContextTypes)}function Pi(){Ti(Ni),Ti(Ci)}function Li(e,t,n){if(Ci.current!==Oi)throw Error(o(168));xi(Ci,t),xi(Ni,n)}function Mi(e,t,n){var r=e.stateNode;if(t=t.childContextTypes,"function"!=typeof r.getChildContext)return n;for(var i in r=r.getChildContext())if(!(i in t))throw Error(o(108,V(e)||"Unknown",i));return F({},n,r)}function Ri(e){return e=(e=e.stateNode)&&e.__reactInternalMemoizedMergedChildContext||Oi,Ii=Ci.current,xi(Ci,e),xi(Ni,Ni.current),!0}function Fi(e,t,n){var r=e.stateNode;if(!r)throw Error(o(169));n?(e=Mi(e,t,Ii),r.__reactInternalMemoizedMergedChildContext=e,Ti(Ni),Ti(Ci),xi(Ci,e)):Ti(Ni),xi(Ni,n)}var Ui=null,ji=!1,Bi=!1;function zi(e){null===Ui?Ui=[e]:Ui.push(e)}function Hi(){if(!Bi&&null!==Ui){Bi=!0;var e=0,t=bt;try{var n=Ui;for(bt=1;e<n.length;e++){var r=n[e];do{r=r(!0)}while(null!==r)}Ui=null,ji=!1}catch(i){throw null!==Ui&&(Ui=Ui.slice(e+1)),We(Xe,Hi),i}finally{bt=t,Bi=!1}}return null}var Vi=[],$i=0,Gi=null,qi=0,Wi=[],Ki=0,Zi=null,Ji=1,Yi="";function Qi(e,t){Vi[$i++]=qi,Vi[$i++]=Gi,Gi=e,qi=t}function Xi(e,t,n){Wi[Ki++]=Ji,Wi[Ki++]=Yi,Wi[Ki++]=Zi,Zi=e;var r=Ji;e=Yi;var i=32-at(r)-1;r&=~(1<<i),n+=1;var o=32-at(t)+i;if(30<o){var a=i-i%5;o=(r&(1<<a)-1).toString(32),r>>=a,i-=a,Ji=1<<32-at(t)+i|n<<i|r,Yi=o+e}else Ji=1<<o|n<<i|r,Yi=e}function eo(e){null!==e.return&&(Qi(e,1),Xi(e,1,0))}function to(e){for(;e===Gi;)Gi=Vi[--$i],Vi[$i]=null,qi=Vi[--$i],Vi[$i]=null;for(;e===Zi;)Zi=Wi[--Ki],Wi[Ki]=null,Yi=Wi[--Ki],Wi[Ki]=null,Ji=Wi[--Ki],Wi[Ki]=null}var no=null,ro=null,io=!1,oo=null;function ao(e,t){var n=Au(5,null,null,0);n.elementType="DELETED",n.stateNode=t,n.return=e,null===(t=e.deletions)?(e.deletions=[n],e.flags|=16):t.push(n)}function so(e,t){switch(e.tag){case 5:var n=e.type;return null!==(t=1!==t.nodeType||n.toLowerCase()!==t.nodeName.toLowerCase()?null:t)&&(e.stateNode=t,no=e,ro=ui(t.firstChild),!0);case 6:return null!==(t=""===e.pendingProps||3!==t.nodeType?null:t)&&(e.stateNode=t,no=e,ro=null,!0);case 13:return null!==(t=8!==t.nodeType?null:t)&&(n=null!==Zi?{id:Ji,overflow:Yi}:null,e.memoizedState={dehydrated:t,treeContext:n,retryLane:1073741824},(n=Au(18,null,null,0)).stateNode=t,n.return=e,e.child=n,no=e,ro=null,!0);default:return!1}}function lo(e){return 0!=(1&e.mode)&&0==(128&e.flags)}function uo(e){if(io){var t=ro;if(t){var n=t;if(!so(e,t)){if(lo(e))throw Error(o(418));t=ui(n.nextSibling);var r=no;t&&so(e,t)?ao(r,n):(e.flags=-4097&e.flags|2,io=!1,no=e)}}else{if(lo(e))throw Error(o(418));e.flags=-4097&e.flags|2,io=!1,no=e}}}function co(e){for(e=e.return;null!==e&&5!==e.tag&&3!==e.tag&&13!==e.tag;)e=e.return;no=e}function fo(e){if(e!==no)return!1;if(!io)return co(e),io=!0,!1;var t;if((t=3!==e.tag)&&!(t=5!==e.tag)&&(t="head"!==(t=e.type)&&"body"!==t&&!ni(e.type,e.memoizedProps)),t&&(t=ro)){if(lo(e))throw ho(),Error(o(418));for(;t;)ao(e,t),t=ui(t.nextSibling)}if(co(e),13===e.tag){if(!(e=null!==(e=e.memoizedState)?e.dehydrated:null))throw Error(o(317));e:{for(e=e.nextSibling,t=0;e;){if(8===e.nodeType){var n=e.data;if("/$"===n){if(0===t){ro=ui(e.nextSibling);break e}t--}else"$"!==n&&"$!"!==n&&"$?"!==n||t++}e=e.nextSibling}ro=null}}else ro=no?ui(e.stateNode.nextSibling):null;return!0}function ho(){for(var e=ro;e;)e=ui(e.nextSibling)}function po(){ro=no=null,io=!1}function mo(e){null===oo?oo=[e]:oo.push(e)}var go=_.ReactCurrentBatchConfig;function vo(e,t){if(e&&e.defaultProps){for(var n in t=F({},t),e=e.defaultProps)void 0===t[n]&&(t[n]=e[n]);return t}return t}var yo=ki(null),bo=null,_o=null,wo=null;function So(){wo=_o=bo=null}function Eo(e){var t=yo.current;Ti(yo),e._currentValue=t}function ko(e,t,n){for(;null!==e;){var r=e.alternate;if((e.childLanes&t)!==t?(e.childLanes|=t,null!==r&&(r.childLanes|=t)):null!==r&&(r.childLanes&t)!==t&&(r.childLanes|=t),e===n)break;e=e.return}}function To(e,t){bo=e,wo=_o=null,null!==(e=e.dependencies)&&null!==e.firstContext&&(0!=(e.lanes&t)&&(_s=!0),e.firstContext=null)}function xo(e){var t=e._currentValue;if(wo!==e)if(e={context:e,memoizedValue:t,next:null},null===_o){if(null===bo)throw Error(o(308));_o=e,bo.dependencies={lanes:0,firstContext:e}}else _o=_o.next=e;return t}var Oo=null;function Co(e){null===Oo?Oo=[e]:Oo.push(e)}function No(e,t,n,r){var i=t.interleaved;return null===i?(n.next=n,Co(t)):(n.next=i.next,i.next=n),t.interleaved=n,Io(e,r)}function Io(e,t){e.lanes|=t;var n=e.alternate;for(null!==n&&(n.lanes|=t),n=e,e=e.return;null!==e;)e.childLanes|=t,null!==(n=e.alternate)&&(n.childLanes|=t),n=e,e=e.return;return 3===n.tag?n.stateNode:null}var Do=!1;function Ao(e){e.updateQueue={baseState:e.memoizedState,firstBaseUpdate:null,lastBaseUpdate:null,shared:{pending:null,interleaved:null,lanes:0},effects:null}}function Po(e,t){e=e.updateQueue,t.updateQueue===e&&(t.updateQueue={baseState:e.baseState,firstBaseUpdate:e.firstBaseUpdate,lastBaseUpdate:e.lastBaseUpdate,shared:e.shared,effects:e.effects})}function Lo(e,t){return{eventTime:e,lane:t,tag:0,payload:null,callback:null,next:null}}function Mo(e,t,n){var r=e.updateQueue;if(null===r)return null;if(r=r.shared,0!=(2&Nl)){var i=r.pending;return null===i?t.next=t:(t.next=i.next,i.next=t),r.pending=t,Io(e,n)}return null===(i=r.interleaved)?(t.next=t,Co(r)):(t.next=i.next,i.next=t),r.interleaved=t,Io(e,n)}function Ro(e,t,n){if(null!==(t=t.updateQueue)&&(t=t.shared,0!=(4194240&n))){var r=t.lanes;n|=r&=e.pendingLanes,t.lanes=n,yt(e,n)}}function Fo(e,t){var n=e.updateQueue,r=e.alternate;if(null!==r&&n===(r=r.updateQueue)){var i=null,o=null;if(null!==(n=n.firstBaseUpdate)){do{var a={eventTime:n.eventTime,lane:n.lane,tag:n.tag,payload:n.payload,callback:n.callback,next:null};null===o?i=o=a:o=o.next=a,n=n.next}while(null!==n);null===o?i=o=t:o=o.next=t}else i=o=t;return n={baseState:r.baseState,firstBaseUpdate:i,lastBaseUpdate:o,shared:r.shared,effects:r.effects},void(e.updateQueue=n)}null===(e=n.lastBaseUpdate)?n.firstBaseUpdate=t:e.next=t,n.lastBaseUpdate=t}function Uo(e,t,n,r){var i=e.updateQueue;Do=!1;var o=i.firstBaseUpdate,a=i.lastBaseUpdate,s=i.shared.pending;if(null!==s){i.shared.pending=null;var l=s,u=l.next;l.next=null,null===a?o=u:a.next=u,a=l;var c=e.alternate;null!==c&&((s=(c=c.updateQueue).lastBaseUpdate)!==a&&(null===s?c.firstBaseUpdate=u:s.next=u,c.lastBaseUpdate=l))}if(null!==o){var f=i.baseState;for(a=0,c=u=l=null,s=o;;){var d=s.lane,h=s.eventTime;if((r&d)===d){null!==c&&(c=c.next={eventTime:h,lane:0,tag:s.tag,payload:s.payload,callback:s.callback,next:null});e:{var p=e,m=s;switch(d=t,h=n,m.tag){case 1:if("function"==typeof(p=m.payload)){f=p.call(h,f,d);break e}f=p;break e;case 3:p.flags=-65537&p.flags|128;case 0:if(null==(d="function"==typeof(p=m.payload)?p.call(h,f,d):p))break e;f=F({},f,d);break e;case 2:Do=!0}}null!==s.callback&&0!==s.lane&&(e.flags|=64,null===(d=i.effects)?i.effects=[s]:d.push(s))}else h={eventTime:h,lane:d,tag:s.tag,payload:s.payload,callback:s.callback,next:null},null===c?(u=c=h,l=f):c=c.next=h,a|=d;if(null===(s=s.next)){if(null===(s=i.shared.pending))break;s=(d=s).next,d.next=null,i.lastBaseUpdate=d,i.shared.pending=null}}if(null===c&&(l=f),i.baseState=l,i.firstBaseUpdate=u,i.lastBaseUpdate=c,null!==(t=i.shared.interleaved)){i=t;do{a|=i.lane,i=i.next}while(i!==t)}else null===o&&(i.shared.lanes=0);Fl|=a,e.lanes=a,e.memoizedState=f}}function jo(e,t,n){if(e=t.effects,t.effects=null,null!==e)for(t=0;t<e.length;t++){var r=e[t],i=r.callback;if(null!==i){if(r.callback=null,r=n,"function"!=typeof i)throw Error(o(191,i));i.call(r)}}}var Bo=(new r.Component).refs;function zo(e,t,n,r){n=null==(n=n(r,t=e.memoizedState))?t:F({},t,n),e.memoizedState=n,0===e.lanes&&(e.updateQueue.baseState=n)}var Ho={isMounted:function(e){return!!(e=e._reactInternals)&&He(e)===e},enqueueSetState:function(e,t,n){e=e._reactInternals;var r=tu(),i=nu(e),o=Lo(r,i);o.payload=t,null!=n&&(o.callback=n),null!==(t=Mo(e,o,i))&&(ru(t,e,i,r),Ro(t,e,i))},enqueueReplaceState:function(e,t,n){e=e._reactInternals;var r=tu(),i=nu(e),o=Lo(r,i);o.tag=1,o.payload=t,null!=n&&(o.callback=n),null!==(t=Mo(e,o,i))&&(ru(t,e,i,r),Ro(t,e,i))},enqueueForceUpdate:function(e,t){e=e._reactInternals;var n=tu(),r=nu(e),i=Lo(n,r);i.tag=2,null!=t&&(i.callback=t),null!==(t=Mo(e,i,r))&&(ru(t,e,r,n),Ro(t,e,r))}};function Vo(e,t,n,r,i,o,a){return"function"==typeof(e=e.stateNode).shouldComponentUpdate?e.shouldComponentUpdate(r,o,a):!t.prototype||!t.prototype.isPureReactComponent||(!lr(n,r)||!lr(i,o))}function $o(e,t,n){var r=!1,i=Oi,o=t.contextType;return"object"==typeof o&&null!==o?o=xo(o):(i=Ai(t)?Ii:Ci.current,o=(r=null!=(r=t.contextTypes))?Di(e,i):Oi),t=new t(n,o),e.memoizedState=null!==t.state&&void 0!==t.state?t.state:null,t.updater=Ho,e.stateNode=t,t._reactInternals=e,r&&((e=e.stateNode).__reactInternalMemoizedUnmaskedChildContext=i,e.__reactInternalMemoizedMaskedChildContext=o),t}function Go(e,t,n,r){e=t.state,"function"==typeof t.componentWillReceiveProps&&t.componentWillReceiveProps(n,r),"function"==typeof t.UNSAFE_componentWillReceiveProps&&t.UNSAFE_componentWillReceiveProps(n,r),t.state!==e&&Ho.enqueueReplaceState(t,t.state,null)}function qo(e,t,n,r){var i=e.stateNode;i.props=n,i.state=e.memoizedState,i.refs=Bo,Ao(e);var o=t.contextType;"object"==typeof o&&null!==o?i.context=xo(o):(o=Ai(t)?Ii:Ci.current,i.context=Di(e,o)),i.state=e.memoizedState,"function"==typeof(o=t.getDerivedStateFromProps)&&(zo(e,t,o,n),i.state=e.memoizedState),"function"==typeof t.getDerivedStateFromProps||"function"==typeof i.getSnapshotBeforeUpdate||"function"!=typeof i.UNSAFE_componentWillMount&&"function"!=typeof i.componentWillMount||(t=i.state,"function"==typeof i.componentWillMount&&i.componentWillMount(),"function"==typeof i.UNSAFE_componentWillMount&&i.UNSAFE_componentWillMount(),t!==i.state&&Ho.enqueueReplaceState(i,i.state,null),Uo(e,n,i,r),i.state=e.memoizedState),"function"==typeof i.componentDidMount&&(e.flags|=4194308)}function Wo(e,t,n){if(null!==(e=n.ref)&&"function"!=typeof e&&"object"!=typeof e){if(n._owner){if(n=n._owner){if(1!==n.tag)throw Error(o(309));var r=n.stateNode}if(!r)throw Error(o(147,e));var i=r,a=""+e;return null!==t&&null!==t.ref&&"function"==typeof t.ref&&t.ref._stringRef===a?t.ref:(t=function(e){var t=i.refs;t===Bo&&(t=i.refs={}),null===e?delete t[a]:t[a]=e},t._stringRef=a,t)}if("string"!=typeof e)throw Error(o(284));if(!n._owner)throw Error(o(290,e))}return e}function Ko(e,t){throw e=Object.prototype.toString.call(t),Error(o(31,"[object Object]"===e?"object with keys {"+Object.keys(t).join(", ")+"}":e))}function Zo(e){return(0,e._init)(e._payload)}function Jo(e){function t(t,n){if(e){var r=t.deletions;null===r?(t.deletions=[n],t.flags|=16):r.push(n)}}function n(n,r){if(!e)return null;for(;null!==r;)t(n,r),r=r.sibling;return null}function r(e,t){for(e=new Map;null!==t;)null!==t.key?e.set(t.key,t):e.set(t.index,t),t=t.sibling;return e}function i(e,t){return(e=Lu(e,t)).index=0,e.sibling=null,e}function a(t,n,r){return t.index=r,e?null!==(r=t.alternate)?(r=r.index)<n?(t.flags|=2,n):r:(t.flags|=2,n):(t.flags|=1048576,n)}function s(t){return e&&null===t.alternate&&(t.flags|=2),t}function l(e,t,n,r){return null===t||6!==t.tag?((t=Uu(n,e.mode,r)).return=e,t):((t=i(t,n)).return=e,t)}function u(e,t,n,r){var o=n.type;return o===E?f(e,t,n.props.children,r,n.key):null!==t&&(t.elementType===o||"object"==typeof o&&null!==o&&o.$$typeof===A&&Zo(o)===t.type)?((r=i(t,n.props)).ref=Wo(e,t,n),r.return=e,r):((r=Mu(n.type,n.key,n.props,null,e.mode,r)).ref=Wo(e,t,n),r.return=e,r)}function c(e,t,n,r){return null===t||4!==t.tag||t.stateNode.containerInfo!==n.containerInfo||t.stateNode.implementation!==n.implementation?((t=ju(n,e.mode,r)).return=e,t):((t=i(t,n.children||[])).return=e,t)}function f(e,t,n,r,o){return null===t||7!==t.tag?((t=Ru(n,e.mode,r,o)).return=e,t):((t=i(t,n)).return=e,t)}function d(e,t,n){if("string"==typeof t&&""!==t||"number"==typeof t)return(t=Uu(""+t,e.mode,n)).return=e,t;if("object"==typeof t&&null!==t){switch(t.$$typeof){case w:return(n=Mu(t.type,t.key,t.props,null,e.mode,n)).ref=Wo(e,null,t),n.return=e,n;case S:return(t=ju(t,e.mode,n)).return=e,t;case A:return d(e,(0,t._init)(t._payload),n)}if(te(t)||M(t))return(t=Ru(t,e.mode,n,null)).return=e,t;Ko(e,t)}return null}function h(e,t,n,r){var i=null!==t?t.key:null;if("string"==typeof n&&""!==n||"number"==typeof n)return null!==i?null:l(e,t,""+n,r);if("object"==typeof n&&null!==n){switch(n.$$typeof){case w:return n.key===i?u(e,t,n,r):null;case S:return n.key===i?c(e,t,n,r):null;case A:return h(e,t,(i=n._init)(n._payload),r)}if(te(n)||M(n))return null!==i?null:f(e,t,n,r,null);Ko(e,n)}return null}function p(e,t,n,r,i){if("string"==typeof r&&""!==r||"number"==typeof r)return l(t,e=e.get(n)||null,""+r,i);if("object"==typeof r&&null!==r){switch(r.$$typeof){case w:return u(t,e=e.get(null===r.key?n:r.key)||null,r,i);case S:return c(t,e=e.get(null===r.key?n:r.key)||null,r,i);case A:return p(e,t,n,(0,r._init)(r._payload),i)}if(te(r)||M(r))return f(t,e=e.get(n)||null,r,i,null);Ko(t,r)}return null}function m(i,o,s,l){for(var u=null,c=null,f=o,m=o=0,g=null;null!==f&&m<s.length;m++){f.index>m?(g=f,f=null):g=f.sibling;var v=h(i,f,s[m],l);if(null===v){null===f&&(f=g);break}e&&f&&null===v.alternate&&t(i,f),o=a(v,o,m),null===c?u=v:c.sibling=v,c=v,f=g}if(m===s.length)return n(i,f),io&&Qi(i,m),u;if(null===f){for(;m<s.length;m++)null!==(f=d(i,s[m],l))&&(o=a(f,o,m),null===c?u=f:c.sibling=f,c=f);return io&&Qi(i,m),u}for(f=r(i,f);m<s.length;m++)null!==(g=p(f,i,m,s[m],l))&&(e&&null!==g.alternate&&f.delete(null===g.key?m:g.key),o=a(g,o,m),null===c?u=g:c.sibling=g,c=g);return e&&f.forEach((function(e){return t(i,e)})),io&&Qi(i,m),u}function g(i,s,l,u){var c=M(l);if("function"!=typeof c)throw Error(o(150));if(null==(l=c.call(l)))throw Error(o(151));for(var f=c=null,m=s,g=s=0,v=null,y=l.next();null!==m&&!y.done;g++,y=l.next()){m.index>g?(v=m,m=null):v=m.sibling;var b=h(i,m,y.value,u);if(null===b){null===m&&(m=v);break}e&&m&&null===b.alternate&&t(i,m),s=a(b,s,g),null===f?c=b:f.sibling=b,f=b,m=v}if(y.done)return n(i,m),io&&Qi(i,g),c;if(null===m){for(;!y.done;g++,y=l.next())null!==(y=d(i,y.value,u))&&(s=a(y,s,g),null===f?c=y:f.sibling=y,f=y);return io&&Qi(i,g),c}for(m=r(i,m);!y.done;g++,y=l.next())null!==(y=p(m,i,g,y.value,u))&&(e&&null!==y.alternate&&m.delete(null===y.key?g:y.key),s=a(y,s,g),null===f?c=y:f.sibling=y,f=y);return e&&m.forEach((function(e){return t(i,e)})),io&&Qi(i,g),c}return function e(r,o,a,l){if("object"==typeof a&&null!==a&&a.type===E&&null===a.key&&(a=a.props.children),"object"==typeof a&&null!==a){switch(a.$$typeof){case w:e:{for(var u=a.key,c=o;null!==c;){if(c.key===u){if((u=a.type)===E){if(7===c.tag){n(r,c.sibling),(o=i(c,a.props.children)).return=r,r=o;break e}}else if(c.elementType===u||"object"==typeof u&&null!==u&&u.$$typeof===A&&Zo(u)===c.type){n(r,c.sibling),(o=i(c,a.props)).ref=Wo(r,c,a),o.return=r,r=o;break e}n(r,c);break}t(r,c),c=c.sibling}a.type===E?((o=Ru(a.props.children,r.mode,l,a.key)).return=r,r=o):((l=Mu(a.type,a.key,a.props,null,r.mode,l)).ref=Wo(r,o,a),l.return=r,r=l)}return s(r);case S:e:{for(c=a.key;null!==o;){if(o.key===c){if(4===o.tag&&o.stateNode.containerInfo===a.containerInfo&&o.stateNode.implementation===a.implementation){n(r,o.sibling),(o=i(o,a.children||[])).return=r,r=o;break e}n(r,o);break}t(r,o),o=o.sibling}(o=ju(a,r.mode,l)).return=r,r=o}return s(r);case A:return e(r,o,(c=a._init)(a._payload),l)}if(te(a))return m(r,o,a,l);if(M(a))return g(r,o,a,l);Ko(r,a)}return"string"==typeof a&&""!==a||"number"==typeof a?(a=""+a,null!==o&&6===o.tag?(n(r,o.sibling),(o=i(o,a)).return=r,r=o):(n(r,o),(o=Uu(a,r.mode,l)).return=r,r=o),s(r)):n(r,o)}}var Yo=Jo(!0),Qo=Jo(!1),Xo={},ea=ki(Xo),ta=ki(Xo),na=ki(Xo);function ra(e){if(e===Xo)throw Error(o(174));return e}function ia(e,t){switch(xi(na,t),xi(ta,e),xi(ea,Xo),e=t.nodeType){case 9:case 11:t=(t=t.documentElement)?t.namespaceURI:le(null,"");break;default:t=le(t=(e=8===e?t.parentNode:t).namespaceURI||null,e=e.tagName)}Ti(ea),xi(ea,t)}function oa(){Ti(ea),Ti(ta),Ti(na)}function aa(e){ra(na.current);var t=ra(ea.current),n=le(t,e.type);t!==n&&(xi(ta,e),xi(ea,n))}function sa(e){ta.current===e&&(Ti(ea),Ti(ta))}var la=ki(0);function ua(e){for(var t=e;null!==t;){if(13===t.tag){var n=t.memoizedState;if(null!==n&&(null===(n=n.dehydrated)||"$?"===n.data||"$!"===n.data))return t}else if(19===t.tag&&void 0!==t.memoizedProps.revealOrder){if(0!=(128&t.flags))return t}else if(null!==t.child){t.child.return=t,t=t.child;continue}if(t===e)break;for(;null===t.sibling;){if(null===t.return||t.return===e)return null;t=t.return}t.sibling.return=t.return,t=t.sibling}return null}var ca=[];function fa(){for(var e=0;e<ca.length;e++)ca[e]._workInProgressVersionPrimary=null;ca.length=0}var da=_.ReactCurrentDispatcher,ha=_.ReactCurrentBatchConfig,pa=0,ma=null,ga=null,va=null,ya=!1,ba=!1,_a=0,wa=0;function Sa(){throw Error(o(321))}function Ea(e,t){if(null===t)return!1;for(var n=0;n<t.length&&n<e.length;n++)if(!sr(e[n],t[n]))return!1;return!0}function ka(e,t,n,r,i,a){if(pa=a,ma=t,t.memoizedState=null,t.updateQueue=null,t.lanes=0,da.current=null===e||null===e.memoizedState?ss:ls,e=n(r,i),ba){a=0;do{if(ba=!1,_a=0,25<=a)throw Error(o(301));a+=1,va=ga=null,t.updateQueue=null,da.current=us,e=n(r,i)}while(ba)}if(da.current=as,t=null!==ga&&null!==ga.next,pa=0,va=ga=ma=null,ya=!1,t)throw Error(o(300));return e}function Ta(){var e=0!==_a;return _a=0,e}function xa(){var e={memoizedState:null,baseState:null,baseQueue:null,queue:null,next:null};return null===va?ma.memoizedState=va=e:va=va.next=e,va}function Oa(){if(null===ga){var e=ma.alternate;e=null!==e?e.memoizedState:null}else e=ga.next;var t=null===va?ma.memoizedState:va.next;if(null!==t)va=t,ga=e;else{if(null===e)throw Error(o(310));e={memoizedState:(ga=e).memoizedState,baseState:ga.baseState,baseQueue:ga.baseQueue,queue:ga.queue,next:null},null===va?ma.memoizedState=va=e:va=va.next=e}return va}function Ca(e,t){return"function"==typeof t?t(e):t}function Na(e){var t=Oa(),n=t.queue;if(null===n)throw Error(o(311));n.lastRenderedReducer=e;var r=ga,i=r.baseQueue,a=n.pending;if(null!==a){if(null!==i){var s=i.next;i.next=a.next,a.next=s}r.baseQueue=i=a,n.pending=null}if(null!==i){a=i.next,r=r.baseState;var l=s=null,u=null,c=a;do{var f=c.lane;if((pa&f)===f)null!==u&&(u=u.next={lane:0,action:c.action,hasEagerState:c.hasEagerState,eagerState:c.eagerState,next:null}),r=c.hasEagerState?c.eagerState:e(r,c.action);else{var d={lane:f,action:c.action,hasEagerState:c.hasEagerState,eagerState:c.eagerState,next:null};null===u?(l=u=d,s=r):u=u.next=d,ma.lanes|=f,Fl|=f}c=c.next}while(null!==c&&c!==a);null===u?s=r:u.next=l,sr(r,t.memoizedState)||(_s=!0),t.memoizedState=r,t.baseState=s,t.baseQueue=u,n.lastRenderedState=r}if(null!==(e=n.interleaved)){i=e;do{a=i.lane,ma.lanes|=a,Fl|=a,i=i.next}while(i!==e)}else null===i&&(n.lanes=0);return[t.memoizedState,n.dispatch]}function Ia(e){var t=Oa(),n=t.queue;if(null===n)throw Error(o(311));n.lastRenderedReducer=e;var r=n.dispatch,i=n.pending,a=t.memoizedState;if(null!==i){n.pending=null;var s=i=i.next;do{a=e(a,s.action),s=s.next}while(s!==i);sr(a,t.memoizedState)||(_s=!0),t.memoizedState=a,null===t.baseQueue&&(t.baseState=a),n.lastRenderedState=a}return[a,r]}function Da(){}function Aa(e,t){var n=ma,r=Oa(),i=t(),a=!sr(r.memoizedState,i);if(a&&(r.memoizedState=i,_s=!0),r=r.queue,$a(Ma.bind(null,n,r,e),[e]),r.getSnapshot!==t||a||null!==va&&1&va.memoizedState.tag){if(n.flags|=2048,ja(9,La.bind(null,n,r,i,t),void 0,null),null===Il)throw Error(o(349));0!=(30&pa)||Pa(n,t,i)}return i}function Pa(e,t,n){e.flags|=16384,e={getSnapshot:t,value:n},null===(t=ma.updateQueue)?(t={lastEffect:null,stores:null},ma.updateQueue=t,t.stores=[e]):null===(n=t.stores)?t.stores=[e]:n.push(e)}function La(e,t,n,r){t.value=n,t.getSnapshot=r,Ra(t)&&Fa(e)}function Ma(e,t,n){return n((function(){Ra(t)&&Fa(e)}))}function Ra(e){var t=e.getSnapshot;e=e.value;try{var n=t();return!sr(e,n)}catch(r){return!0}}function Fa(e){var t=Io(e,1);null!==t&&ru(t,e,1,-1)}function Ua(e){var t=xa();return"function"==typeof e&&(e=e()),t.memoizedState=t.baseState=e,e={pending:null,interleaved:null,lanes:0,dispatch:null,lastRenderedReducer:Ca,lastRenderedState:e},t.queue=e,e=e.dispatch=ns.bind(null,ma,e),[t.memoizedState,e]}function ja(e,t,n,r){return e={tag:e,create:t,destroy:n,deps:r,next:null},null===(t=ma.updateQueue)?(t={lastEffect:null,stores:null},ma.updateQueue=t,t.lastEffect=e.next=e):null===(n=t.lastEffect)?t.lastEffect=e.next=e:(r=n.next,n.next=e,e.next=r,t.lastEffect=e),e}function Ba(){return Oa().memoizedState}function za(e,t,n,r){var i=xa();ma.flags|=e,i.memoizedState=ja(1|t,n,void 0,void 0===r?null:r)}function Ha(e,t,n,r){var i=Oa();r=void 0===r?null:r;var o=void 0;if(null!==ga){var a=ga.memoizedState;if(o=a.destroy,null!==r&&Ea(r,a.deps))return void(i.memoizedState=ja(t,n,o,r))}ma.flags|=e,i.memoizedState=ja(1|t,n,o,r)}function Va(e,t){return za(8390656,8,e,t)}function $a(e,t){return Ha(2048,8,e,t)}function Ga(e,t){return Ha(4,2,e,t)}function qa(e,t){return Ha(4,4,e,t)}function Wa(e,t){return"function"==typeof t?(e=e(),t(e),function(){t(null)}):null!=t?(e=e(),t.current=e,function(){t.current=null}):void 0}function Ka(e,t,n){return n=null!=n?n.concat([e]):null,Ha(4,4,Wa.bind(null,t,e),n)}function Za(){}function Ja(e,t){var n=Oa();t=void 0===t?null:t;var r=n.memoizedState;return null!==r&&null!==t&&Ea(t,r[1])?r[0]:(n.memoizedState=[e,t],e)}function Ya(e,t){var n=Oa();t=void 0===t?null:t;var r=n.memoizedState;return null!==r&&null!==t&&Ea(t,r[1])?r[0]:(e=e(),n.memoizedState=[e,t],e)}function Qa(e,t,n){return 0==(21&pa)?(e.baseState&&(e.baseState=!1,_s=!0),e.memoizedState=n):(sr(n,t)||(n=mt(),ma.lanes|=n,Fl|=n,e.baseState=!0),t)}function Xa(e,t){var n=bt;bt=0!==n&&4>n?n:4,e(!0);var r=ha.transition;ha.transition={};try{e(!1),t()}finally{bt=n,ha.transition=r}}function es(){return Oa().memoizedState}function ts(e,t,n){var r=nu(e);if(n={lane:r,action:n,hasEagerState:!1,eagerState:null,next:null},rs(e))is(t,n);else if(null!==(n=No(e,t,n,r))){ru(n,e,r,tu()),os(n,t,r)}}function ns(e,t,n){var r=nu(e),i={lane:r,action:n,hasEagerState:!1,eagerState:null,next:null};if(rs(e))is(t,i);else{var o=e.alternate;if(0===e.lanes&&(null===o||0===o.lanes)&&null!==(o=t.lastRenderedReducer))try{var a=t.lastRenderedState,s=o(a,n);if(i.hasEagerState=!0,i.eagerState=s,sr(s,a)){var l=t.interleaved;return null===l?(i.next=i,Co(t)):(i.next=l.next,l.next=i),void(t.interleaved=i)}}catch(u){}null!==(n=No(e,t,i,r))&&(ru(n,e,r,i=tu()),os(n,t,r))}}function rs(e){var t=e.alternate;return e===ma||null!==t&&t===ma}function is(e,t){ba=ya=!0;var n=e.pending;null===n?t.next=t:(t.next=n.next,n.next=t),e.pending=t}function os(e,t,n){if(0!=(4194240&n)){var r=t.lanes;n|=r&=e.pendingLanes,t.lanes=n,yt(e,n)}}var as={readContext:xo,useCallback:Sa,useContext:Sa,useEffect:Sa,useImperativeHandle:Sa,useInsertionEffect:Sa,useLayoutEffect:Sa,useMemo:Sa,useReducer:Sa,useRef:Sa,useState:Sa,useDebugValue:Sa,useDeferredValue:Sa,useTransition:Sa,useMutableSource:Sa,useSyncExternalStore:Sa,useId:Sa,unstable_isNewReconciler:!1},ss={readContext:xo,useCallback:function(e,t){return xa().memoizedState=[e,void 0===t?null:t],e},useContext:xo,useEffect:Va,useImperativeHandle:function(e,t,n){return n=null!=n?n.concat([e]):null,za(4194308,4,Wa.bind(null,t,e),n)},useLayoutEffect:function(e,t){return za(4194308,4,e,t)},useInsertionEffect:function(e,t){return za(4,2,e,t)},useMemo:function(e,t){var n=xa();return t=void 0===t?null:t,e=e(),n.memoizedState=[e,t],e},useReducer:function(e,t,n){var r=xa();return t=void 0!==n?n(t):t,r.memoizedState=r.baseState=t,e={pending:null,interleaved:null,lanes:0,dispatch:null,lastRenderedReducer:e,lastRenderedState:t},r.queue=e,e=e.dispatch=ts.bind(null,ma,e),[r.memoizedState,e]},useRef:function(e){return e={current:e},xa().memoizedState=e},useState:Ua,useDebugValue:Za,useDeferredValue:function(e){return xa().memoizedState=e},useTransition:function(){var e=Ua(!1),t=e[0];return e=Xa.bind(null,e[1]),xa().memoizedState=e,[t,e]},useMutableSource:function(){},useSyncExternalStore:function(e,t,n){var r=ma,i=xa();if(io){if(void 0===n)throw Error(o(407));n=n()}else{if(n=t(),null===Il)throw Error(o(349));0!=(30&pa)||Pa(r,t,n)}i.memoizedState=n;var a={value:n,getSnapshot:t};return i.queue=a,Va(Ma.bind(null,r,a,e),[e]),r.flags|=2048,ja(9,La.bind(null,r,a,n,t),void 0,null),n},useId:function(){var e=xa(),t=Il.identifierPrefix;if(io){var n=Yi;t=":"+t+"R"+(n=(Ji&~(1<<32-at(Ji)-1)).toString(32)+n),0<(n=_a++)&&(t+="H"+n.toString(32)),t+=":"}else t=":"+t+"r"+(n=wa++).toString(32)+":";return e.memoizedState=t},unstable_isNewReconciler:!1},ls={readContext:xo,useCallback:Ja,useContext:xo,useEffect:$a,useImperativeHandle:Ka,useInsertionEffect:Ga,useLayoutEffect:qa,useMemo:Ya,useReducer:Na,useRef:Ba,useState:function(){return Na(Ca)},useDebugValue:Za,useDeferredValue:function(e){return Qa(Oa(),ga.memoizedState,e)},useTransition:function(){return[Na(Ca)[0],Oa().memoizedState]},useMutableSource:Da,useSyncExternalStore:Aa,useId:es,unstable_isNewReconciler:!1},us={readContext:xo,useCallback:Ja,useContext:xo,useEffect:$a,useImperativeHandle:Ka,useInsertionEffect:Ga,useLayoutEffect:qa,useMemo:Ya,useReducer:Ia,useRef:Ba,useState:function(){return Ia(Ca)},useDebugValue:Za,useDeferredValue:function(e){var t=Oa();return null===ga?t.memoizedState=e:Qa(t,ga.memoizedState,e)},useTransition:function(){return[Ia(Ca)[0],Oa().memoizedState]},useMutableSource:Da,useSyncExternalStore:Aa,useId:es,unstable_isNewReconciler:!1};function cs(e,t){try{var n="",r=t;do{n+=z(r),r=r.return}while(r);var i=n}catch(o){i="\nError generating stack: "+o.message+"\n"+o.stack}return{value:e,source:t,stack:i,digest:null}}function fs(e,t,n){return{value:e,source:null,stack:null!=n?n:null,digest:null!=t?t:null}}function ds(e,t){try{console.error(t.value)}catch(n){setTimeout((function(){throw n}))}}var hs="function"==typeof WeakMap?WeakMap:Map;function ps(e,t,n){(n=Lo(-1,n)).tag=3,n.payload={element:null};var r=t.value;return n.callback=function(){Gl||(Gl=!0,ql=r),ds(0,t)},n}function ms(e,t,n){(n=Lo(-1,n)).tag=3;var r=e.type.getDerivedStateFromError;if("function"==typeof r){var i=t.value;n.payload=function(){return r(i)},n.callback=function(){ds(0,t)}}var o=e.stateNode;return null!==o&&"function"==typeof o.componentDidCatch&&(n.callback=function(){ds(0,t),"function"!=typeof r&&(null===Wl?Wl=new Set([this]):Wl.add(this));var e=t.stack;this.componentDidCatch(t.value,{componentStack:null!==e?e:""})}),n}function gs(e,t,n){var r=e.pingCache;if(null===r){r=e.pingCache=new hs;var i=new Set;r.set(t,i)}else void 0===(i=r.get(t))&&(i=new Set,r.set(t,i));i.has(n)||(i.add(n),e=xu.bind(null,e,t,n),t.then(e,e))}function vs(e){do{var t;if((t=13===e.tag)&&(t=null===(t=e.memoizedState)||null!==t.dehydrated),t)return e;e=e.return}while(null!==e);return null}function ys(e,t,n,r,i){return 0==(1&e.mode)?(e===t?e.flags|=65536:(e.flags|=128,n.flags|=131072,n.flags&=-52805,1===n.tag&&(null===n.alternate?n.tag=17:((t=Lo(-1,1)).tag=2,Mo(n,t,1))),n.lanes|=1),e):(e.flags|=65536,e.lanes=i,e)}var bs=_.ReactCurrentOwner,_s=!1;function ws(e,t,n,r){t.child=null===e?Qo(t,null,n,r):Yo(t,e.child,n,r)}function Ss(e,t,n,r,i){n=n.render;var o=t.ref;return To(t,i),r=ka(e,t,n,r,o,i),n=Ta(),null===e||_s?(io&&n&&eo(t),t.flags|=1,ws(e,t,r,i),t.child):(t.updateQueue=e.updateQueue,t.flags&=-2053,e.lanes&=~i,Gs(e,t,i))}function Es(e,t,n,r,i){if(null===e){var o=n.type;return"function"!=typeof o||Pu(o)||void 0!==o.defaultProps||null!==n.compare||void 0!==n.defaultProps?((e=Mu(n.type,null,r,t,t.mode,i)).ref=t.ref,e.return=t,t.child=e):(t.tag=15,t.type=o,ks(e,t,o,r,i))}if(o=e.child,0==(e.lanes&i)){var a=o.memoizedProps;if((n=null!==(n=n.compare)?n:lr)(a,r)&&e.ref===t.ref)return Gs(e,t,i)}return t.flags|=1,(e=Lu(o,r)).ref=t.ref,e.return=t,t.child=e}function ks(e,t,n,r,i){if(null!==e){var o=e.memoizedProps;if(lr(o,r)&&e.ref===t.ref){if(_s=!1,t.pendingProps=r=o,0==(e.lanes&i))return t.lanes=e.lanes,Gs(e,t,i);0!=(131072&e.flags)&&(_s=!0)}}return Os(e,t,n,r,i)}function Ts(e,t,n){var r=t.pendingProps,i=r.children,o=null!==e?e.memoizedState:null;if("hidden"===r.mode)if(0==(1&t.mode))t.memoizedState={baseLanes:0,cachePool:null,transitions:null},xi(Ll,Pl),Pl|=n;else{if(0==(1073741824&n))return e=null!==o?o.baseLanes|n:n,t.lanes=t.childLanes=1073741824,t.memoizedState={baseLanes:e,cachePool:null,transitions:null},t.updateQueue=null,xi(Ll,Pl),Pl|=e,null;t.memoizedState={baseLanes:0,cachePool:null,transitions:null},r=null!==o?o.baseLanes:n,xi(Ll,Pl),Pl|=r}else null!==o?(r=o.baseLanes|n,t.memoizedState=null):r=n,xi(Ll,Pl),Pl|=r;return ws(e,t,i,n),t.child}function xs(e,t){var n=t.ref;(null===e&&null!==n||null!==e&&e.ref!==n)&&(t.flags|=512,t.flags|=2097152)}function Os(e,t,n,r,i){var o=Ai(n)?Ii:Ci.current;return o=Di(t,o),To(t,i),n=ka(e,t,n,r,o,i),r=Ta(),null===e||_s?(io&&r&&eo(t),t.flags|=1,ws(e,t,n,i),t.child):(t.updateQueue=e.updateQueue,t.flags&=-2053,e.lanes&=~i,Gs(e,t,i))}function Cs(e,t,n,r,i){if(Ai(n)){var o=!0;Ri(t)}else o=!1;if(To(t,i),null===t.stateNode)$s(e,t),$o(t,n,r),qo(t,n,r,i),r=!0;else if(null===e){var a=t.stateNode,s=t.memoizedProps;a.props=s;var l=a.context,u=n.contextType;"object"==typeof u&&null!==u?u=xo(u):u=Di(t,u=Ai(n)?Ii:Ci.current);var c=n.getDerivedStateFromProps,f="function"==typeof c||"function"==typeof a.getSnapshotBeforeUpdate;f||"function"!=typeof a.UNSAFE_componentWillReceiveProps&&"function"!=typeof a.componentWillReceiveProps||(s!==r||l!==u)&&Go(t,a,r,u),Do=!1;var d=t.memoizedState;a.state=d,Uo(t,r,a,i),l=t.memoizedState,s!==r||d!==l||Ni.current||Do?("function"==typeof c&&(zo(t,n,c,r),l=t.memoizedState),(s=Do||Vo(t,n,s,r,d,l,u))?(f||"function"!=typeof a.UNSAFE_componentWillMount&&"function"!=typeof a.componentWillMount||("function"==typeof a.componentWillMount&&a.componentWillMount(),"function"==typeof a.UNSAFE_componentWillMount&&a.UNSAFE_componentWillMount()),"function"==typeof a.componentDidMount&&(t.flags|=4194308)):("function"==typeof a.componentDidMount&&(t.flags|=4194308),t.memoizedProps=r,t.memoizedState=l),a.props=r,a.state=l,a.context=u,r=s):("function"==typeof a.componentDidMount&&(t.flags|=4194308),r=!1)}else{a=t.stateNode,Po(e,t),s=t.memoizedProps,u=t.type===t.elementType?s:vo(t.type,s),a.props=u,f=t.pendingProps,d=a.context,"object"==typeof(l=n.contextType)&&null!==l?l=xo(l):l=Di(t,l=Ai(n)?Ii:Ci.current);var h=n.getDerivedStateFromProps;(c="function"==typeof h||"function"==typeof a.getSnapshotBeforeUpdate)||"function"!=typeof a.UNSAFE_componentWillReceiveProps&&"function"!=typeof a.componentWillReceiveProps||(s!==f||d!==l)&&Go(t,a,r,l),Do=!1,d=t.memoizedState,a.state=d,Uo(t,r,a,i);var p=t.memoizedState;s!==f||d!==p||Ni.current||Do?("function"==typeof h&&(zo(t,n,h,r),p=t.memoizedState),(u=Do||Vo(t,n,u,r,d,p,l)||!1)?(c||"function"!=typeof a.UNSAFE_componentWillUpdate&&"function"!=typeof a.componentWillUpdate||("function"==typeof a.componentWillUpdate&&a.componentWillUpdate(r,p,l),"function"==typeof a.UNSAFE_componentWillUpdate&&a.UNSAFE_componentWillUpdate(r,p,l)),"function"==typeof a.componentDidUpdate&&(t.flags|=4),"function"==typeof a.getSnapshotBeforeUpdate&&(t.flags|=1024)):("function"!=typeof a.componentDidUpdate||s===e.memoizedProps&&d===e.memoizedState||(t.flags|=4),"function"!=typeof a.getSnapshotBeforeUpdate||s===e.memoizedProps&&d===e.memoizedState||(t.flags|=1024),t.memoizedProps=r,t.memoizedState=p),a.props=r,a.state=p,a.context=l,r=u):("function"!=typeof a.componentDidUpdate||s===e.memoizedProps&&d===e.memoizedState||(t.flags|=4),"function"!=typeof a.getSnapshotBeforeUpdate||s===e.memoizedProps&&d===e.memoizedState||(t.flags|=1024),r=!1)}return Ns(e,t,n,r,o,i)}function Ns(e,t,n,r,i,o){xs(e,t);var a=0!=(128&t.flags);if(!r&&!a)return i&&Fi(t,n,!1),Gs(e,t,o);r=t.stateNode,bs.current=t;var s=a&&"function"!=typeof n.getDerivedStateFromError?null:r.render();return t.flags|=1,null!==e&&a?(t.child=Yo(t,e.child,null,o),t.child=Yo(t,null,s,o)):ws(e,t,s,o),t.memoizedState=r.state,i&&Fi(t,n,!0),t.child}function Is(e){var t=e.stateNode;t.pendingContext?Li(0,t.pendingContext,t.pendingContext!==t.context):t.context&&Li(0,t.context,!1),ia(e,t.containerInfo)}function Ds(e,t,n,r,i){return po(),mo(i),t.flags|=256,ws(e,t,n,r),t.child}var As,Ps,Ls,Ms,Rs={dehydrated:null,treeContext:null,retryLane:0};function Fs(e){return{baseLanes:e,cachePool:null,transitions:null}}function Us(e,t,n){var r,i=t.pendingProps,a=la.current,s=!1,l=0!=(128&t.flags);if((r=l)||(r=(null===e||null!==e.memoizedState)&&0!=(2&a)),r?(s=!0,t.flags&=-129):null!==e&&null===e.memoizedState||(a|=1),xi(la,1&a),null===e)return uo(t),null!==(e=t.memoizedState)&&null!==(e=e.dehydrated)?(0==(1&t.mode)?t.lanes=1:"$!"===e.data?t.lanes=8:t.lanes=1073741824,null):(l=i.children,e=i.fallback,s?(i=t.mode,s=t.child,l={mode:"hidden",children:l},0==(1&i)&&null!==s?(s.childLanes=0,s.pendingProps=l):s=Fu(l,i,0,null),e=Ru(e,i,n,null),s.return=t,e.return=t,s.sibling=e,t.child=s,t.child.memoizedState=Fs(n),t.memoizedState=Rs,e):js(t,l));if(null!==(a=e.memoizedState)&&null!==(r=a.dehydrated))return function(e,t,n,r,i,a,s){if(n)return 256&t.flags?(t.flags&=-257,Bs(e,t,s,r=fs(Error(o(422))))):null!==t.memoizedState?(t.child=e.child,t.flags|=128,null):(a=r.fallback,i=t.mode,r=Fu({mode:"visible",children:r.children},i,0,null),(a=Ru(a,i,s,null)).flags|=2,r.return=t,a.return=t,r.sibling=a,t.child=r,0!=(1&t.mode)&&Yo(t,e.child,null,s),t.child.memoizedState=Fs(s),t.memoizedState=Rs,a);if(0==(1&t.mode))return Bs(e,t,s,null);if("$!"===i.data){if(r=i.nextSibling&&i.nextSibling.dataset)var l=r.dgst;return r=l,Bs(e,t,s,r=fs(a=Error(o(419)),r,void 0))}if(l=0!=(s&e.childLanes),_s||l){if(null!==(r=Il)){switch(s&-s){case 4:i=2;break;case 16:i=8;break;case 64:case 128:case 256:case 512:case 1024:case 2048:case 4096:case 8192:case 16384:case 32768:case 65536:case 131072:case 262144:case 524288:case 1048576:case 2097152:case 4194304:case 8388608:case 16777216:case 33554432:case 67108864:i=32;break;case 536870912:i=268435456;break;default:i=0}0!==(i=0!=(i&(r.suspendedLanes|s))?0:i)&&i!==a.retryLane&&(a.retryLane=i,Io(e,i),ru(r,e,i,-1))}return gu(),Bs(e,t,s,r=fs(Error(o(421))))}return"$?"===i.data?(t.flags|=128,t.child=e.child,t=Cu.bind(null,e),i._reactRetry=t,null):(e=a.treeContext,ro=ui(i.nextSibling),no=t,io=!0,oo=null,null!==e&&(Wi[Ki++]=Ji,Wi[Ki++]=Yi,Wi[Ki++]=Zi,Ji=e.id,Yi=e.overflow,Zi=t),t=js(t,r.children),t.flags|=4096,t)}(e,t,l,i,r,a,n);if(s){s=i.fallback,l=t.mode,r=(a=e.child).sibling;var u={mode:"hidden",children:i.children};return 0==(1&l)&&t.child!==a?((i=t.child).childLanes=0,i.pendingProps=u,t.deletions=null):(i=Lu(a,u)).subtreeFlags=14680064&a.subtreeFlags,null!==r?s=Lu(r,s):(s=Ru(s,l,n,null)).flags|=2,s.return=t,i.return=t,i.sibling=s,t.child=i,i=s,s=t.child,l=null===(l=e.child.memoizedState)?Fs(n):{baseLanes:l.baseLanes|n,cachePool:null,transitions:l.transitions},s.memoizedState=l,s.childLanes=e.childLanes&~n,t.memoizedState=Rs,i}return e=(s=e.child).sibling,i=Lu(s,{mode:"visible",children:i.children}),0==(1&t.mode)&&(i.lanes=n),i.return=t,i.sibling=null,null!==e&&(null===(n=t.deletions)?(t.deletions=[e],t.flags|=16):n.push(e)),t.child=i,t.memoizedState=null,i}function js(e,t){return(t=Fu({mode:"visible",children:t},e.mode,0,null)).return=e,e.child=t}function Bs(e,t,n,r){return null!==r&&mo(r),Yo(t,e.child,null,n),(e=js(t,t.pendingProps.children)).flags|=2,t.memoizedState=null,e}function zs(e,t,n){e.lanes|=t;var r=e.alternate;null!==r&&(r.lanes|=t),ko(e.return,t,n)}function Hs(e,t,n,r,i){var o=e.memoizedState;null===o?e.memoizedState={isBackwards:t,rendering:null,renderingStartTime:0,last:r,tail:n,tailMode:i}:(o.isBackwards=t,o.rendering=null,o.renderingStartTime=0,o.last=r,o.tail=n,o.tailMode=i)}function Vs(e,t,n){var r=t.pendingProps,i=r.revealOrder,o=r.tail;if(ws(e,t,r.children,n),0!=(2&(r=la.current)))r=1&r|2,t.flags|=128;else{if(null!==e&&0!=(128&e.flags))e:for(e=t.child;null!==e;){if(13===e.tag)null!==e.memoizedState&&zs(e,n,t);else if(19===e.tag)zs(e,n,t);else if(null!==e.child){e.child.return=e,e=e.child;continue}if(e===t)break e;for(;null===e.sibling;){if(null===e.return||e.return===t)break e;e=e.return}e.sibling.return=e.return,e=e.sibling}r&=1}if(xi(la,r),0==(1&t.mode))t.memoizedState=null;else switch(i){case"forwards":for(n=t.child,i=null;null!==n;)null!==(e=n.alternate)&&null===ua(e)&&(i=n),n=n.sibling;null===(n=i)?(i=t.child,t.child=null):(i=n.sibling,n.sibling=null),Hs(t,!1,i,n,o);break;case"backwards":for(n=null,i=t.child,t.child=null;null!==i;){if(null!==(e=i.alternate)&&null===ua(e)){t.child=i;break}e=i.sibling,i.sibling=n,n=i,i=e}Hs(t,!0,n,null,o);break;case"together":Hs(t,!1,null,null,void 0);break;default:t.memoizedState=null}return t.child}function $s(e,t){0==(1&t.mode)&&null!==e&&(e.alternate=null,t.alternate=null,t.flags|=2)}function Gs(e,t,n){if(null!==e&&(t.dependencies=e.dependencies),Fl|=t.lanes,0==(n&t.childLanes))return null;if(null!==e&&t.child!==e.child)throw Error(o(153));if(null!==t.child){for(n=Lu(e=t.child,e.pendingProps),t.child=n,n.return=t;null!==e.sibling;)e=e.sibling,(n=n.sibling=Lu(e,e.pendingProps)).return=t;n.sibling=null}return t.child}function qs(e,t){if(!io)switch(e.tailMode){case"hidden":t=e.tail;for(var n=null;null!==t;)null!==t.alternate&&(n=t),t=t.sibling;null===n?e.tail=null:n.sibling=null;break;case"collapsed":n=e.tail;for(var r=null;null!==n;)null!==n.alternate&&(r=n),n=n.sibling;null===r?t||null===e.tail?e.tail=null:e.tail.sibling=null:r.sibling=null}}function Ws(e){var t=null!==e.alternate&&e.alternate.child===e.child,n=0,r=0;if(t)for(var i=e.child;null!==i;)n|=i.lanes|i.childLanes,r|=14680064&i.subtreeFlags,r|=14680064&i.flags,i.return=e,i=i.sibling;else for(i=e.child;null!==i;)n|=i.lanes|i.childLanes,r|=i.subtreeFlags,r|=i.flags,i.return=e,i=i.sibling;return e.subtreeFlags|=r,e.childLanes=n,t}function Ks(e,t,n){var r=t.pendingProps;switch(to(t),t.tag){case 2:case 16:case 15:case 0:case 11:case 7:case 8:case 12:case 9:case 14:return Ws(t),null;case 1:case 17:return Ai(t.type)&&Pi(),Ws(t),null;case 3:return r=t.stateNode,oa(),Ti(Ni),Ti(Ci),fa(),r.pendingContext&&(r.context=r.pendingContext,r.pendingContext=null),null!==e&&null!==e.child||(fo(t)?t.flags|=4:null===e||e.memoizedState.isDehydrated&&0==(256&t.flags)||(t.flags|=1024,null!==oo&&(su(oo),oo=null))),Ps(e,t),Ws(t),null;case 5:sa(t);var i=ra(na.current);if(n=t.type,null!==e&&null!=t.stateNode)Ls(e,t,n,r,i),e.ref!==t.ref&&(t.flags|=512,t.flags|=2097152);else{if(!r){if(null===t.stateNode)throw Error(o(166));return Ws(t),null}if(e=ra(ea.current),fo(t)){r=t.stateNode,n=t.type;var a=t.memoizedProps;switch(r[di]=t,r[hi]=a,e=0!=(1&t.mode),n){case"dialog":jr("cancel",r),jr("close",r);break;case"iframe":case"object":case"embed":jr("load",r);break;case"video":case"audio":for(i=0;i<Mr.length;i++)jr(Mr[i],r);break;case"source":jr("error",r);break;case"img":case"image":case"link":jr("error",r),jr("load",r);break;case"details":jr("toggle",r);break;case"input":J(r,a),jr("invalid",r);break;case"select":r._wrapperState={wasMultiple:!!a.multiple},jr("invalid",r);break;case"textarea":ie(r,a),jr("invalid",r)}for(var l in ye(n,a),i=null,a)if(a.hasOwnProperty(l)){var u=a[l];"children"===l?"string"==typeof u?r.textContent!==u&&(!0!==a.suppressHydrationWarning&&Qr(r.textContent,u,e),i=["children",u]):"number"==typeof u&&r.textContent!==""+u&&(!0!==a.suppressHydrationWarning&&Qr(r.textContent,u,e),i=["children",""+u]):s.hasOwnProperty(l)&&null!=u&&"onScroll"===l&&jr("scroll",r)}switch(n){case"input":q(r),X(r,a,!0);break;case"textarea":q(r),ae(r);break;case"select":case"option":break;default:"function"==typeof a.onClick&&(r.onclick=Xr)}r=i,t.updateQueue=r,null!==r&&(t.flags|=4)}else{l=9===i.nodeType?i:i.ownerDocument,"http://www.w3.org/1999/xhtml"===e&&(e=se(n)),"http://www.w3.org/1999/xhtml"===e?"script"===n?((e=l.createElement("div")).innerHTML="<script><\/script>",e=e.removeChild(e.firstChild)):"string"==typeof r.is?e=l.createElement(n,{is:r.is}):(e=l.createElement(n),"select"===n&&(l=e,r.multiple?l.multiple=!0:r.size&&(l.size=r.size))):e=l.createElementNS(e,n),e[di]=t,e[hi]=r,As(e,t,!1,!1),t.stateNode=e;e:{switch(l=be(n,r),n){case"dialog":jr("cancel",e),jr("close",e),i=r;break;case"iframe":case"object":case"embed":jr("load",e),i=r;break;case"video":case"audio":for(i=0;i<Mr.length;i++)jr(Mr[i],e);i=r;break;case"source":jr("error",e),i=r;break;case"img":case"image":case"link":jr("error",e),jr("load",e),i=r;break;case"details":jr("toggle",e),i=r;break;case"input":J(e,r),i=Z(e,r),jr("invalid",e);break;case"option":default:i=r;break;case"select":e._wrapperState={wasMultiple:!!r.multiple},i=F({},r,{value:void 0}),jr("invalid",e);break;case"textarea":ie(e,r),i=re(e,r),jr("invalid",e)}for(a in ye(n,i),u=i)if(u.hasOwnProperty(a)){var c=u[a];"style"===a?ge(e,c):"dangerouslySetInnerHTML"===a?null!=(c=c?c.__html:void 0)&&fe(e,c):"children"===a?"string"==typeof c?("textarea"!==n||""!==c)&&de(e,c):"number"==typeof c&&de(e,""+c):"suppressContentEditableWarning"!==a&&"suppressHydrationWarning"!==a&&"autoFocus"!==a&&(s.hasOwnProperty(a)?null!=c&&"onScroll"===a&&jr("scroll",e):null!=c&&b(e,a,c,l))}switch(n){case"input":q(e),X(e,r,!1);break;case"textarea":q(e),ae(e);break;case"option":null!=r.value&&e.setAttribute("value",""+$(r.value));break;case"select":e.multiple=!!r.multiple,null!=(a=r.value)?ne(e,!!r.multiple,a,!1):null!=r.defaultValue&&ne(e,!!r.multiple,r.defaultValue,!0);break;default:"function"==typeof i.onClick&&(e.onclick=Xr)}switch(n){case"button":case"input":case"select":case"textarea":r=!!r.autoFocus;break e;case"img":r=!0;break e;default:r=!1}}r&&(t.flags|=4)}null!==t.ref&&(t.flags|=512,t.flags|=2097152)}return Ws(t),null;case 6:if(e&&null!=t.stateNode)Ms(e,t,e.memoizedProps,r);else{if("string"!=typeof r&&null===t.stateNode)throw Error(o(166));if(n=ra(na.current),ra(ea.current),fo(t)){if(r=t.stateNode,n=t.memoizedProps,r[di]=t,(a=r.nodeValue!==n)&&null!==(e=no))switch(e.tag){case 3:Qr(r.nodeValue,n,0!=(1&e.mode));break;case 5:!0!==e.memoizedProps.suppressHydrationWarning&&Qr(r.nodeValue,n,0!=(1&e.mode))}a&&(t.flags|=4)}else(r=(9===n.nodeType?n:n.ownerDocument).createTextNode(r))[di]=t,t.stateNode=r}return Ws(t),null;case 13:if(Ti(la),r=t.memoizedState,null===e||null!==e.memoizedState&&null!==e.memoizedState.dehydrated){if(io&&null!==ro&&0!=(1&t.mode)&&0==(128&t.flags))ho(),po(),t.flags|=98560,a=!1;else if(a=fo(t),null!==r&&null!==r.dehydrated){if(null===e){if(!a)throw Error(o(318));if(!(a=null!==(a=t.memoizedState)?a.dehydrated:null))throw Error(o(317));a[di]=t}else po(),0==(128&t.flags)&&(t.memoizedState=null),t.flags|=4;Ws(t),a=!1}else null!==oo&&(su(oo),oo=null),a=!0;if(!a)return 65536&t.flags?t:null}return 0!=(128&t.flags)?(t.lanes=n,t):((r=null!==r)!==(null!==e&&null!==e.memoizedState)&&r&&(t.child.flags|=8192,0!=(1&t.mode)&&(null===e||0!=(1&la.current)?0===Ml&&(Ml=3):gu())),null!==t.updateQueue&&(t.flags|=4),Ws(t),null);case 4:return oa(),Ps(e,t),null===e&&Hr(t.stateNode.containerInfo),Ws(t),null;case 10:return Eo(t.type._context),Ws(t),null;case 19:if(Ti(la),null===(a=t.memoizedState))return Ws(t),null;if(r=0!=(128&t.flags),null===(l=a.rendering))if(r)qs(a,!1);else{if(0!==Ml||null!==e&&0!=(128&e.flags))for(e=t.child;null!==e;){if(null!==(l=ua(e))){for(t.flags|=128,qs(a,!1),null!==(r=l.updateQueue)&&(t.updateQueue=r,t.flags|=4),t.subtreeFlags=0,r=n,n=t.child;null!==n;)e=r,(a=n).flags&=14680066,null===(l=a.alternate)?(a.childLanes=0,a.lanes=e,a.child=null,a.subtreeFlags=0,a.memoizedProps=null,a.memoizedState=null,a.updateQueue=null,a.dependencies=null,a.stateNode=null):(a.childLanes=l.childLanes,a.lanes=l.lanes,a.child=l.child,a.subtreeFlags=0,a.deletions=null,a.memoizedProps=l.memoizedProps,a.memoizedState=l.memoizedState,a.updateQueue=l.updateQueue,a.type=l.type,e=l.dependencies,a.dependencies=null===e?null:{lanes:e.lanes,firstContext:e.firstContext}),n=n.sibling;return xi(la,1&la.current|2),t.child}e=e.sibling}null!==a.tail&&Ye()>Vl&&(t.flags|=128,r=!0,qs(a,!1),t.lanes=4194304)}else{if(!r)if(null!==(e=ua(l))){if(t.flags|=128,r=!0,null!==(n=e.updateQueue)&&(t.updateQueue=n,t.flags|=4),qs(a,!0),null===a.tail&&"hidden"===a.tailMode&&!l.alternate&&!io)return Ws(t),null}else 2*Ye()-a.renderingStartTime>Vl&&1073741824!==n&&(t.flags|=128,r=!0,qs(a,!1),t.lanes=4194304);a.isBackwards?(l.sibling=t.child,t.child=l):(null!==(n=a.last)?n.sibling=l:t.child=l,a.last=l)}return null!==a.tail?(t=a.tail,a.rendering=t,a.tail=t.sibling,a.renderingStartTime=Ye(),t.sibling=null,n=la.current,xi(la,r?1&n|2:1&n),t):(Ws(t),null);case 22:case 23:return du(),r=null!==t.memoizedState,null!==e&&null!==e.memoizedState!==r&&(t.flags|=8192),r&&0!=(1&t.mode)?0!=(1073741824&Pl)&&(Ws(t),6&t.subtreeFlags&&(t.flags|=8192)):Ws(t),null;case 24:case 25:return null}throw Error(o(156,t.tag))}function Zs(e,t){switch(to(t),t.tag){case 1:return Ai(t.type)&&Pi(),65536&(e=t.flags)?(t.flags=-65537&e|128,t):null;case 3:return oa(),Ti(Ni),Ti(Ci),fa(),0!=(65536&(e=t.flags))&&0==(128&e)?(t.flags=-65537&e|128,t):null;case 5:return sa(t),null;case 13:if(Ti(la),null!==(e=t.memoizedState)&&null!==e.dehydrated){if(null===t.alternate)throw Error(o(340));po()}return 65536&(e=t.flags)?(t.flags=-65537&e|128,t):null;case 19:return Ti(la),null;case 4:return oa(),null;case 10:return Eo(t.type._context),null;case 22:case 23:return du(),null;default:return null}}As=function(e,t){for(var n=t.child;null!==n;){if(5===n.tag||6===n.tag)e.appendChild(n.stateNode);else if(4!==n.tag&&null!==n.child){n.child.return=n,n=n.child;continue}if(n===t)break;for(;null===n.sibling;){if(null===n.return||n.return===t)return;n=n.return}n.sibling.return=n.return,n=n.sibling}},Ps=function(){},Ls=function(e,t,n,r){var i=e.memoizedProps;if(i!==r){e=t.stateNode,ra(ea.current);var o,a=null;switch(n){case"input":i=Z(e,i),r=Z(e,r),a=[];break;case"select":i=F({},i,{value:void 0}),r=F({},r,{value:void 0}),a=[];break;case"textarea":i=re(e,i),r=re(e,r),a=[];break;default:"function"!=typeof i.onClick&&"function"==typeof r.onClick&&(e.onclick=Xr)}for(c in ye(n,r),n=null,i)if(!r.hasOwnProperty(c)&&i.hasOwnProperty(c)&&null!=i[c])if("style"===c){var l=i[c];for(o in l)l.hasOwnProperty(o)&&(n||(n={}),n[o]="")}else"dangerouslySetInnerHTML"!==c&&"children"!==c&&"suppressContentEditableWarning"!==c&&"suppressHydrationWarning"!==c&&"autoFocus"!==c&&(s.hasOwnProperty(c)?a||(a=[]):(a=a||[]).push(c,null));for(c in r){var u=r[c];if(l=null!=i?i[c]:void 0,r.hasOwnProperty(c)&&u!==l&&(null!=u||null!=l))if("style"===c)if(l){for(o in l)!l.hasOwnProperty(o)||u&&u.hasOwnProperty(o)||(n||(n={}),n[o]="");for(o in u)u.hasOwnProperty(o)&&l[o]!==u[o]&&(n||(n={}),n[o]=u[o])}else n||(a||(a=[]),a.push(c,n)),n=u;else"dangerouslySetInnerHTML"===c?(u=u?u.__html:void 0,l=l?l.__html:void 0,null!=u&&l!==u&&(a=a||[]).push(c,u)):"children"===c?"string"!=typeof u&&"number"!=typeof u||(a=a||[]).push(c,""+u):"suppressContentEditableWarning"!==c&&"suppressHydrationWarning"!==c&&(s.hasOwnProperty(c)?(null!=u&&"onScroll"===c&&jr("scroll",e),a||l===u||(a=[])):(a=a||[]).push(c,u))}n&&(a=a||[]).push("style",n);var c=a;(t.updateQueue=c)&&(t.flags|=4)}},Ms=function(e,t,n,r){n!==r&&(t.flags|=4)};var Js=!1,Ys=!1,Qs="function"==typeof WeakSet?WeakSet:Set,Xs=null;function el(e,t){var n=e.ref;if(null!==n)if("function"==typeof n)try{n(null)}catch(r){Tu(e,t,r)}else n.current=null}function tl(e,t,n){try{n()}catch(r){Tu(e,t,r)}}var nl=!1;function rl(e,t,n){var r=t.updateQueue;if(null!==(r=null!==r?r.lastEffect:null)){var i=r=r.next;do{if((i.tag&e)===e){var o=i.destroy;i.destroy=void 0,void 0!==o&&tl(t,n,o)}i=i.next}while(i!==r)}}function il(e,t){if(null!==(t=null!==(t=t.updateQueue)?t.lastEffect:null)){var n=t=t.next;do{if((n.tag&e)===e){var r=n.create;n.destroy=r()}n=n.next}while(n!==t)}}function ol(e){var t=e.ref;if(null!==t){var n=e.stateNode;e.tag,e=n,"function"==typeof t?t(e):t.current=e}}function al(e){var t=e.alternate;null!==t&&(e.alternate=null,al(t)),e.child=null,e.deletions=null,e.sibling=null,5===e.tag&&(null!==(t=e.stateNode)&&(delete t[di],delete t[hi],delete t[mi],delete t[gi],delete t[vi])),e.stateNode=null,e.return=null,e.dependencies=null,e.memoizedProps=null,e.memoizedState=null,e.pendingProps=null,e.stateNode=null,e.updateQueue=null}function sl(e){return 5===e.tag||3===e.tag||4===e.tag}function ll(e){e:for(;;){for(;null===e.sibling;){if(null===e.return||sl(e.return))return null;e=e.return}for(e.sibling.return=e.return,e=e.sibling;5!==e.tag&&6!==e.tag&&18!==e.tag;){if(2&e.flags)continue e;if(null===e.child||4===e.tag)continue e;e.child.return=e,e=e.child}if(!(2&e.flags))return e.stateNode}}function ul(e,t,n){var r=e.tag;if(5===r||6===r)e=e.stateNode,t?8===n.nodeType?n.parentNode.insertBefore(e,t):n.insertBefore(e,t):(8===n.nodeType?(t=n.parentNode).insertBefore(e,n):(t=n).appendChild(e),null!=(n=n._reactRootContainer)||null!==t.onclick||(t.onclick=Xr));else if(4!==r&&null!==(e=e.child))for(ul(e,t,n),e=e.sibling;null!==e;)ul(e,t,n),e=e.sibling}function cl(e,t,n){var r=e.tag;if(5===r||6===r)e=e.stateNode,t?n.insertBefore(e,t):n.appendChild(e);else if(4!==r&&null!==(e=e.child))for(cl(e,t,n),e=e.sibling;null!==e;)cl(e,t,n),e=e.sibling}var fl=null,dl=!1;function hl(e,t,n){for(n=n.child;null!==n;)pl(e,t,n),n=n.sibling}function pl(e,t,n){if(ot&&"function"==typeof ot.onCommitFiberUnmount)try{ot.onCommitFiberUnmount(it,n)}catch(s){}switch(n.tag){case 5:Ys||el(n,t);case 6:var r=fl,i=dl;fl=null,hl(e,t,n),dl=i,null!==(fl=r)&&(dl?(e=fl,n=n.stateNode,8===e.nodeType?e.parentNode.removeChild(n):e.removeChild(n)):fl.removeChild(n.stateNode));break;case 18:null!==fl&&(dl?(e=fl,n=n.stateNode,8===e.nodeType?li(e.parentNode,n):1===e.nodeType&&li(e,n),Ht(e)):li(fl,n.stateNode));break;case 4:r=fl,i=dl,fl=n.stateNode.containerInfo,dl=!0,hl(e,t,n),fl=r,dl=i;break;case 0:case 11:case 14:case 15:if(!Ys&&(null!==(r=n.updateQueue)&&null!==(r=r.lastEffect))){i=r=r.next;do{var o=i,a=o.destroy;o=o.tag,void 0!==a&&(0!=(2&o)||0!=(4&o))&&tl(n,t,a),i=i.next}while(i!==r)}hl(e,t,n);break;case 1:if(!Ys&&(el(n,t),"function"==typeof(r=n.stateNode).componentWillUnmount))try{r.props=n.memoizedProps,r.state=n.memoizedState,r.componentWillUnmount()}catch(s){Tu(n,t,s)}hl(e,t,n);break;case 21:hl(e,t,n);break;case 22:1&n.mode?(Ys=(r=Ys)||null!==n.memoizedState,hl(e,t,n),Ys=r):hl(e,t,n);break;default:hl(e,t,n)}}function ml(e){var t=e.updateQueue;if(null!==t){e.updateQueue=null;var n=e.stateNode;null===n&&(n=e.stateNode=new Qs),t.forEach((function(t){var r=Nu.bind(null,e,t);n.has(t)||(n.add(t),t.then(r,r))}))}}function gl(e,t){var n=t.deletions;if(null!==n)for(var r=0;r<n.length;r++){var i=n[r];try{var a=e,s=t,l=s;e:for(;null!==l;){switch(l.tag){case 5:fl=l.stateNode,dl=!1;break e;case 3:case 4:fl=l.stateNode.containerInfo,dl=!0;break e}l=l.return}if(null===fl)throw Error(o(160));pl(a,s,i),fl=null,dl=!1;var u=i.alternate;null!==u&&(u.return=null),i.return=null}catch(c){Tu(i,t,c)}}if(12854&t.subtreeFlags)for(t=t.child;null!==t;)vl(t,e),t=t.sibling}function vl(e,t){var n=e.alternate,r=e.flags;switch(e.tag){case 0:case 11:case 14:case 15:if(gl(t,e),yl(e),4&r){try{rl(3,e,e.return),il(3,e)}catch(g){Tu(e,e.return,g)}try{rl(5,e,e.return)}catch(g){Tu(e,e.return,g)}}break;case 1:gl(t,e),yl(e),512&r&&null!==n&&el(n,n.return);break;case 5:if(gl(t,e),yl(e),512&r&&null!==n&&el(n,n.return),32&e.flags){var i=e.stateNode;try{de(i,"")}catch(g){Tu(e,e.return,g)}}if(4&r&&null!=(i=e.stateNode)){var a=e.memoizedProps,s=null!==n?n.memoizedProps:a,l=e.type,u=e.updateQueue;if(e.updateQueue=null,null!==u)try{"input"===l&&"radio"===a.type&&null!=a.name&&Y(i,a),be(l,s);var c=be(l,a);for(s=0;s<u.length;s+=2){var f=u[s],d=u[s+1];"style"===f?ge(i,d):"dangerouslySetInnerHTML"===f?fe(i,d):"children"===f?de(i,d):b(i,f,d,c)}switch(l){case"input":Q(i,a);break;case"textarea":oe(i,a);break;case"select":var h=i._wrapperState.wasMultiple;i._wrapperState.wasMultiple=!!a.multiple;var p=a.value;null!=p?ne(i,!!a.multiple,p,!1):h!==!!a.multiple&&(null!=a.defaultValue?ne(i,!!a.multiple,a.defaultValue,!0):ne(i,!!a.multiple,a.multiple?[]:"",!1))}i[hi]=a}catch(g){Tu(e,e.return,g)}}break;case 6:if(gl(t,e),yl(e),4&r){if(null===e.stateNode)throw Error(o(162));i=e.stateNode,a=e.memoizedProps;try{i.nodeValue=a}catch(g){Tu(e,e.return,g)}}break;case 3:if(gl(t,e),yl(e),4&r&&null!==n&&n.memoizedState.isDehydrated)try{Ht(t.containerInfo)}catch(g){Tu(e,e.return,g)}break;case 4:default:gl(t,e),yl(e);break;case 13:gl(t,e),yl(e),8192&(i=e.child).flags&&(a=null!==i.memoizedState,i.stateNode.isHidden=a,!a||null!==i.alternate&&null!==i.alternate.memoizedState||(Hl=Ye())),4&r&&ml(e);break;case 22:if(f=null!==n&&null!==n.memoizedState,1&e.mode?(Ys=(c=Ys)||f,gl(t,e),Ys=c):gl(t,e),yl(e),8192&r){if(c=null!==e.memoizedState,(e.stateNode.isHidden=c)&&!f&&0!=(1&e.mode))for(Xs=e,f=e.child;null!==f;){for(d=Xs=f;null!==Xs;){switch(p=(h=Xs).child,h.tag){case 0:case 11:case 14:case 15:rl(4,h,h.return);break;case 1:el(h,h.return);var m=h.stateNode;if("function"==typeof m.componentWillUnmount){r=h,n=h.return;try{t=r,m.props=t.memoizedProps,m.state=t.memoizedState,m.componentWillUnmount()}catch(g){Tu(r,n,g)}}break;case 5:el(h,h.return);break;case 22:if(null!==h.memoizedState){Sl(d);continue}}null!==p?(p.return=h,Xs=p):Sl(d)}f=f.sibling}e:for(f=null,d=e;;){if(5===d.tag){if(null===f){f=d;try{i=d.stateNode,c?"function"==typeof(a=i.style).setProperty?a.setProperty("display","none","important"):a.display="none":(l=d.stateNode,s=null!=(u=d.memoizedProps.style)&&u.hasOwnProperty("display")?u.display:null,l.style.display=me("display",s))}catch(g){Tu(e,e.return,g)}}}else if(6===d.tag){if(null===f)try{d.stateNode.nodeValue=c?"":d.memoizedProps}catch(g){Tu(e,e.return,g)}}else if((22!==d.tag&&23!==d.tag||null===d.memoizedState||d===e)&&null!==d.child){d.child.return=d,d=d.child;continue}if(d===e)break e;for(;null===d.sibling;){if(null===d.return||d.return===e)break e;f===d&&(f=null),d=d.return}f===d&&(f=null),d.sibling.return=d.return,d=d.sibling}}break;case 19:gl(t,e),yl(e),4&r&&ml(e);case 21:}}function yl(e){var t=e.flags;if(2&t){try{e:{for(var n=e.return;null!==n;){if(sl(n)){var r=n;break e}n=n.return}throw Error(o(160))}switch(r.tag){case 5:var i=r.stateNode;32&r.flags&&(de(i,""),r.flags&=-33),cl(e,ll(e),i);break;case 3:case 4:var a=r.stateNode.containerInfo;ul(e,ll(e),a);break;default:throw Error(o(161))}}catch(s){Tu(e,e.return,s)}e.flags&=-3}4096&t&&(e.flags&=-4097)}function bl(e,t,n){Xs=e,_l(e,t,n)}function _l(e,t,n){for(var r=0!=(1&e.mode);null!==Xs;){var i=Xs,o=i.child;if(22===i.tag&&r){var a=null!==i.memoizedState||Js;if(!a){var s=i.alternate,l=null!==s&&null!==s.memoizedState||Ys;s=Js;var u=Ys;if(Js=a,(Ys=l)&&!u)for(Xs=i;null!==Xs;)l=(a=Xs).child,22===a.tag&&null!==a.memoizedState?El(i):null!==l?(l.return=a,Xs=l):El(i);for(;null!==o;)Xs=o,_l(o,t,n),o=o.sibling;Xs=i,Js=s,Ys=u}wl(e)}else 0!=(8772&i.subtreeFlags)&&null!==o?(o.return=i,Xs=o):wl(e)}}function wl(e){for(;null!==Xs;){var t=Xs;if(0!=(8772&t.flags)){var n=t.alternate;try{if(0!=(8772&t.flags))switch(t.tag){case 0:case 11:case 15:Ys||il(5,t);break;case 1:var r=t.stateNode;if(4&t.flags&&!Ys)if(null===n)r.componentDidMount();else{var i=t.elementType===t.type?n.memoizedProps:vo(t.type,n.memoizedProps);r.componentDidUpdate(i,n.memoizedState,r.__reactInternalSnapshotBeforeUpdate)}var a=t.updateQueue;null!==a&&jo(t,a,r);break;case 3:var s=t.updateQueue;if(null!==s){if(n=null,null!==t.child)switch(t.child.tag){case 5:case 1:n=t.child.stateNode}jo(t,s,n)}break;case 5:var l=t.stateNode;if(null===n&&4&t.flags){n=l;var u=t.memoizedProps;switch(t.type){case"button":case"input":case"select":case"textarea":u.autoFocus&&n.focus();break;case"img":u.src&&(n.src=u.src)}}break;case 6:case 4:case 12:case 19:case 17:case 21:case 22:case 23:case 25:break;case 13:if(null===t.memoizedState){var c=t.alternate;if(null!==c){var f=c.memoizedState;if(null!==f){var d=f.dehydrated;null!==d&&Ht(d)}}}break;default:throw Error(o(163))}Ys||512&t.flags&&ol(t)}catch(h){Tu(t,t.return,h)}}if(t===e){Xs=null;break}if(null!==(n=t.sibling)){n.return=t.return,Xs=n;break}Xs=t.return}}function Sl(e){for(;null!==Xs;){var t=Xs;if(t===e){Xs=null;break}var n=t.sibling;if(null!==n){n.return=t.return,Xs=n;break}Xs=t.return}}function El(e){for(;null!==Xs;){var t=Xs;try{switch(t.tag){case 0:case 11:case 15:var n=t.return;try{il(4,t)}catch(l){Tu(t,n,l)}break;case 1:var r=t.stateNode;if("function"==typeof r.componentDidMount){var i=t.return;try{r.componentDidMount()}catch(l){Tu(t,i,l)}}var o=t.return;try{ol(t)}catch(l){Tu(t,o,l)}break;case 5:var a=t.return;try{ol(t)}catch(l){Tu(t,a,l)}}}catch(l){Tu(t,t.return,l)}if(t===e){Xs=null;break}var s=t.sibling;if(null!==s){s.return=t.return,Xs=s;break}Xs=t.return}}var kl,Tl=Math.ceil,xl=_.ReactCurrentDispatcher,Ol=_.ReactCurrentOwner,Cl=_.ReactCurrentBatchConfig,Nl=0,Il=null,Dl=null,Al=0,Pl=0,Ll=ki(0),Ml=0,Rl=null,Fl=0,Ul=0,jl=0,Bl=null,zl=null,Hl=0,Vl=1/0,$l=null,Gl=!1,ql=null,Wl=null,Kl=!1,Zl=null,Jl=0,Yl=0,Ql=null,Xl=-1,eu=0;function tu(){return 0!=(6&Nl)?Ye():-1!==Xl?Xl:Xl=Ye()}function nu(e){return 0==(1&e.mode)?1:0!=(2&Nl)&&0!==Al?Al&-Al:null!==go.transition?(0===eu&&(eu=mt()),eu):0!==(e=bt)?e:e=void 0===(e=window.event)?16:Jt(e.type)}function ru(e,t,n,r){if(50<Yl)throw Yl=0,Ql=null,Error(o(185));vt(e,n,r),0!=(2&Nl)&&e===Il||(e===Il&&(0==(2&Nl)&&(Ul|=n),4===Ml&&lu(e,Al)),iu(e,r),1===n&&0===Nl&&0==(1&t.mode)&&(Vl=Ye()+500,ji&&Hi()))}function iu(e,t){var n=e.callbackNode;!function(e,t){for(var n=e.suspendedLanes,r=e.pingedLanes,i=e.expirationTimes,o=e.pendingLanes;0<o;){var a=31-at(o),s=1<<a,l=i[a];-1===l?0!=(s&n)&&0==(s&r)||(i[a]=ht(s,t)):l<=t&&(e.expiredLanes|=s),o&=~s}}(e,t);var r=dt(e,e===Il?Al:0);if(0===r)null!==n&&Ke(n),e.callbackNode=null,e.callbackPriority=0;else if(t=r&-r,e.callbackPriority!==t){if(null!=n&&Ke(n),1===t)0===e.tag?function(e){ji=!0,zi(e)}(uu.bind(null,e)):zi(uu.bind(null,e)),ai((function(){0==(6&Nl)&&Hi()})),n=null;else{switch(_t(r)){case 1:n=Xe;break;case 4:n=et;break;case 16:default:n=tt;break;case 536870912:n=rt}n=Iu(n,ou.bind(null,e))}e.callbackPriority=t,e.callbackNode=n}}function ou(e,t){if(Xl=-1,eu=0,0!=(6&Nl))throw Error(o(327));var n=e.callbackNode;if(Eu()&&e.callbackNode!==n)return null;var r=dt(e,e===Il?Al:0);if(0===r)return null;if(0!=(30&r)||0!=(r&e.expiredLanes)||t)t=vu(e,r);else{t=r;var i=Nl;Nl|=2;var a=mu();for(Il===e&&Al===t||($l=null,Vl=Ye()+500,hu(e,t));;)try{bu();break}catch(l){pu(e,l)}So(),xl.current=a,Nl=i,null!==Dl?t=0:(Il=null,Al=0,t=Ml)}if(0!==t){if(2===t&&(0!==(i=pt(e))&&(r=i,t=au(e,i))),1===t)throw n=Rl,hu(e,0),lu(e,r),iu(e,Ye()),n;if(6===t)lu(e,r);else{if(i=e.current.alternate,0==(30&r)&&!function(e){for(var t=e;;){if(16384&t.flags){var n=t.updateQueue;if(null!==n&&null!==(n=n.stores))for(var r=0;r<n.length;r++){var i=n[r],o=i.getSnapshot;i=i.value;try{if(!sr(o(),i))return!1}catch(s){return!1}}}if(n=t.child,16384&t.subtreeFlags&&null!==n)n.return=t,t=n;else{if(t===e)break;for(;null===t.sibling;){if(null===t.return||t.return===e)return!0;t=t.return}t.sibling.return=t.return,t=t.sibling}}return!0}(i)&&(2===(t=vu(e,r))&&(0!==(a=pt(e))&&(r=a,t=au(e,a))),1===t))throw n=Rl,hu(e,0),lu(e,r),iu(e,Ye()),n;switch(e.finishedWork=i,e.finishedLanes=r,t){case 0:case 1:throw Error(o(345));case 2:case 5:Su(e,zl,$l);break;case 3:if(lu(e,r),(130023424&r)===r&&10<(t=Hl+500-Ye())){if(0!==dt(e,0))break;if(((i=e.suspendedLanes)&r)!==r){tu(),e.pingedLanes|=e.suspendedLanes&i;break}e.timeoutHandle=ri(Su.bind(null,e,zl,$l),t);break}Su(e,zl,$l);break;case 4:if(lu(e,r),(4194240&r)===r)break;for(t=e.eventTimes,i=-1;0<r;){var s=31-at(r);a=1<<s,(s=t[s])>i&&(i=s),r&=~a}if(r=i,10<(r=(120>(r=Ye()-r)?120:480>r?480:1080>r?1080:1920>r?1920:3e3>r?3e3:4320>r?4320:1960*Tl(r/1960))-r)){e.timeoutHandle=ri(Su.bind(null,e,zl,$l),r);break}Su(e,zl,$l);break;default:throw Error(o(329))}}}return iu(e,Ye()),e.callbackNode===n?ou.bind(null,e):null}function au(e,t){var n=Bl;return e.current.memoizedState.isDehydrated&&(hu(e,t).flags|=256),2!==(e=vu(e,t))&&(t=zl,zl=n,null!==t&&su(t)),e}function su(e){null===zl?zl=e:zl.push.apply(zl,e)}function lu(e,t){for(t&=~jl,t&=~Ul,e.suspendedLanes|=t,e.pingedLanes&=~t,e=e.expirationTimes;0<t;){var n=31-at(t),r=1<<n;e[n]=-1,t&=~r}}function uu(e){if(0!=(6&Nl))throw Error(o(327));Eu();var t=dt(e,0);if(0==(1&t))return iu(e,Ye()),null;var n=vu(e,t);if(0!==e.tag&&2===n){var r=pt(e);0!==r&&(t=r,n=au(e,r))}if(1===n)throw n=Rl,hu(e,0),lu(e,t),iu(e,Ye()),n;if(6===n)throw Error(o(345));return e.finishedWork=e.current.alternate,e.finishedLanes=t,Su(e,zl,$l),iu(e,Ye()),null}function cu(e,t){var n=Nl;Nl|=1;try{return e(t)}finally{0===(Nl=n)&&(Vl=Ye()+500,ji&&Hi())}}function fu(e){null!==Zl&&0===Zl.tag&&0==(6&Nl)&&Eu();var t=Nl;Nl|=1;var n=Cl.transition,r=bt;try{if(Cl.transition=null,bt=1,e)return e()}finally{bt=r,Cl.transition=n,0==(6&(Nl=t))&&Hi()}}function du(){Pl=Ll.current,Ti(Ll)}function hu(e,t){e.finishedWork=null,e.finishedLanes=0;var n=e.timeoutHandle;if(-1!==n&&(e.timeoutHandle=-1,ii(n)),null!==Dl)for(n=Dl.return;null!==n;){var r=n;switch(to(r),r.tag){case 1:null!=(r=r.type.childContextTypes)&&Pi();break;case 3:oa(),Ti(Ni),Ti(Ci),fa();break;case 5:sa(r);break;case 4:oa();break;case 13:case 19:Ti(la);break;case 10:Eo(r.type._context);break;case 22:case 23:du()}n=n.return}if(Il=e,Dl=e=Lu(e.current,null),Al=Pl=t,Ml=0,Rl=null,jl=Ul=Fl=0,zl=Bl=null,null!==Oo){for(t=0;t<Oo.length;t++)if(null!==(r=(n=Oo[t]).interleaved)){n.interleaved=null;var i=r.next,o=n.pending;if(null!==o){var a=o.next;o.next=i,r.next=a}n.pending=r}Oo=null}return e}function pu(e,t){for(;;){var n=Dl;try{if(So(),da.current=as,ya){for(var r=ma.memoizedState;null!==r;){var i=r.queue;null!==i&&(i.pending=null),r=r.next}ya=!1}if(pa=0,va=ga=ma=null,ba=!1,_a=0,Ol.current=null,null===n||null===n.return){Ml=1,Rl=t,Dl=null;break}e:{var a=e,s=n.return,l=n,u=t;if(t=Al,l.flags|=32768,null!==u&&"object"==typeof u&&"function"==typeof u.then){var c=u,f=l,d=f.tag;if(0==(1&f.mode)&&(0===d||11===d||15===d)){var h=f.alternate;h?(f.updateQueue=h.updateQueue,f.memoizedState=h.memoizedState,f.lanes=h.lanes):(f.updateQueue=null,f.memoizedState=null)}var p=vs(s);if(null!==p){p.flags&=-257,ys(p,s,l,0,t),1&p.mode&&gs(a,c,t),u=c;var m=(t=p).updateQueue;if(null===m){var g=new Set;g.add(u),t.updateQueue=g}else m.add(u);break e}if(0==(1&t)){gs(a,c,t),gu();break e}u=Error(o(426))}else if(io&&1&l.mode){var v=vs(s);if(null!==v){0==(65536&v.flags)&&(v.flags|=256),ys(v,s,l,0,t),mo(cs(u,l));break e}}a=u=cs(u,l),4!==Ml&&(Ml=2),null===Bl?Bl=[a]:Bl.push(a),a=s;do{switch(a.tag){case 3:a.flags|=65536,t&=-t,a.lanes|=t,Fo(a,ps(0,u,t));break e;case 1:l=u;var y=a.type,b=a.stateNode;if(0==(128&a.flags)&&("function"==typeof y.getDerivedStateFromError||null!==b&&"function"==typeof b.componentDidCatch&&(null===Wl||!Wl.has(b)))){a.flags|=65536,t&=-t,a.lanes|=t,Fo(a,ms(a,l,t));break e}}a=a.return}while(null!==a)}wu(n)}catch(_){t=_,Dl===n&&null!==n&&(Dl=n=n.return);continue}break}}function mu(){var e=xl.current;return xl.current=as,null===e?as:e}function gu(){0!==Ml&&3!==Ml&&2!==Ml||(Ml=4),null===Il||0==(268435455&Fl)&&0==(268435455&Ul)||lu(Il,Al)}function vu(e,t){var n=Nl;Nl|=2;var r=mu();for(Il===e&&Al===t||($l=null,hu(e,t));;)try{yu();break}catch(i){pu(e,i)}if(So(),Nl=n,xl.current=r,null!==Dl)throw Error(o(261));return Il=null,Al=0,Ml}function yu(){for(;null!==Dl;)_u(Dl)}function bu(){for(;null!==Dl&&!Ze();)_u(Dl)}function _u(e){var t=kl(e.alternate,e,Pl);e.memoizedProps=e.pendingProps,null===t?wu(e):Dl=t,Ol.current=null}function wu(e){var t=e;do{var n=t.alternate;if(e=t.return,0==(32768&t.flags)){if(null!==(n=Ks(n,t,Pl)))return void(Dl=n)}else{if(null!==(n=Zs(n,t)))return n.flags&=32767,void(Dl=n);if(null===e)return Ml=6,void(Dl=null);e.flags|=32768,e.subtreeFlags=0,e.deletions=null}if(null!==(t=t.sibling))return void(Dl=t);Dl=t=e}while(null!==t);0===Ml&&(Ml=5)}function Su(e,t,n){var r=bt,i=Cl.transition;try{Cl.transition=null,bt=1,function(e,t,n,r){do{Eu()}while(null!==Zl);if(0!=(6&Nl))throw Error(o(327));n=e.finishedWork;var i=e.finishedLanes;if(null===n)return null;if(e.finishedWork=null,e.finishedLanes=0,n===e.current)throw Error(o(177));e.callbackNode=null,e.callbackPriority=0;var a=n.lanes|n.childLanes;if(function(e,t){var n=e.pendingLanes&~t;e.pendingLanes=t,e.suspendedLanes=0,e.pingedLanes=0,e.expiredLanes&=t,e.mutableReadLanes&=t,e.entangledLanes&=t,t=e.entanglements;var r=e.eventTimes;for(e=e.expirationTimes;0<n;){var i=31-at(n),o=1<<i;t[i]=0,r[i]=-1,e[i]=-1,n&=~o}}(e,a),e===Il&&(Dl=Il=null,Al=0),0==(2064&n.subtreeFlags)&&0==(2064&n.flags)||Kl||(Kl=!0,Iu(tt,(function(){return Eu(),null}))),a=0!=(15990&n.flags),0!=(15990&n.subtreeFlags)||a){a=Cl.transition,Cl.transition=null;var s=bt;bt=1;var l=Nl;Nl|=4,Ol.current=null,function(e,t){if(ei=$t,hr(e=dr())){if("selectionStart"in e)var n={start:e.selectionStart,end:e.selectionEnd};else e:{var r=(n=(n=e.ownerDocument)&&n.defaultView||window).getSelection&&n.getSelection();if(r&&0!==r.rangeCount){n=r.anchorNode;var i=r.anchorOffset,a=r.focusNode;r=r.focusOffset;try{n.nodeType,a.nodeType}catch(w){n=null;break e}var s=0,l=-1,u=-1,c=0,f=0,d=e,h=null;t:for(;;){for(var p;d!==n||0!==i&&3!==d.nodeType||(l=s+i),d!==a||0!==r&&3!==d.nodeType||(u=s+r),3===d.nodeType&&(s+=d.nodeValue.length),null!==(p=d.firstChild);)h=d,d=p;for(;;){if(d===e)break t;if(h===n&&++c===i&&(l=s),h===a&&++f===r&&(u=s),null!==(p=d.nextSibling))break;h=(d=h).parentNode}d=p}n=-1===l||-1===u?null:{start:l,end:u}}else n=null}n=n||{start:0,end:0}}else n=null;for(ti={focusedElem:e,selectionRange:n},$t=!1,Xs=t;null!==Xs;)if(e=(t=Xs).child,0!=(1028&t.subtreeFlags)&&null!==e)e.return=t,Xs=e;else for(;null!==Xs;){t=Xs;try{var m=t.alternate;if(0!=(1024&t.flags))switch(t.tag){case 0:case 11:case 15:case 5:case 6:case 4:case 17:break;case 1:if(null!==m){var g=m.memoizedProps,v=m.memoizedState,y=t.stateNode,b=y.getSnapshotBeforeUpdate(t.elementType===t.type?g:vo(t.type,g),v);y.__reactInternalSnapshotBeforeUpdate=b}break;case 3:var _=t.stateNode.containerInfo;1===_.nodeType?_.textContent="":9===_.nodeType&&_.documentElement&&_.removeChild(_.documentElement);break;default:throw Error(o(163))}}catch(w){Tu(t,t.return,w)}if(null!==(e=t.sibling)){e.return=t.return,Xs=e;break}Xs=t.return}m=nl,nl=!1}(e,n),vl(n,e),pr(ti),$t=!!ei,ti=ei=null,e.current=n,bl(n,e,i),Je(),Nl=l,bt=s,Cl.transition=a}else e.current=n;if(Kl&&(Kl=!1,Zl=e,Jl=i),a=e.pendingLanes,0===a&&(Wl=null),function(e){if(ot&&"function"==typeof ot.onCommitFiberRoot)try{ot.onCommitFiberRoot(it,e,void 0,128==(128&e.current.flags))}catch(t){}}(n.stateNode),iu(e,Ye()),null!==t)for(r=e.onRecoverableError,n=0;n<t.length;n++)i=t[n],r(i.value,{componentStack:i.stack,digest:i.digest});if(Gl)throw Gl=!1,e=ql,ql=null,e;0!=(1&Jl)&&0!==e.tag&&Eu(),a=e.pendingLanes,0!=(1&a)?e===Ql?Yl++:(Yl=0,Ql=e):Yl=0,Hi()}(e,t,n,r)}finally{Cl.transition=i,bt=r}return null}function Eu(){if(null!==Zl){var e=_t(Jl),t=Cl.transition,n=bt;try{if(Cl.transition=null,bt=16>e?16:e,null===Zl)var r=!1;else{if(e=Zl,Zl=null,Jl=0,0!=(6&Nl))throw Error(o(331));var i=Nl;for(Nl|=4,Xs=e.current;null!==Xs;){var a=Xs,s=a.child;if(0!=(16&Xs.flags)){var l=a.deletions;if(null!==l){for(var u=0;u<l.length;u++){var c=l[u];for(Xs=c;null!==Xs;){var f=Xs;switch(f.tag){case 0:case 11:case 15:rl(8,f,a)}var d=f.child;if(null!==d)d.return=f,Xs=d;else for(;null!==Xs;){var h=(f=Xs).sibling,p=f.return;if(al(f),f===c){Xs=null;break}if(null!==h){h.return=p,Xs=h;break}Xs=p}}}var m=a.alternate;if(null!==m){var g=m.child;if(null!==g){m.child=null;do{var v=g.sibling;g.sibling=null,g=v}while(null!==g)}}Xs=a}}if(0!=(2064&a.subtreeFlags)&&null!==s)s.return=a,Xs=s;else e:for(;null!==Xs;){if(0!=(2048&(a=Xs).flags))switch(a.tag){case 0:case 11:case 15:rl(9,a,a.return)}var y=a.sibling;if(null!==y){y.return=a.return,Xs=y;break e}Xs=a.return}}var b=e.current;for(Xs=b;null!==Xs;){var _=(s=Xs).child;if(0!=(2064&s.subtreeFlags)&&null!==_)_.return=s,Xs=_;else e:for(s=b;null!==Xs;){if(0!=(2048&(l=Xs).flags))try{switch(l.tag){case 0:case 11:case 15:il(9,l)}}catch(S){Tu(l,l.return,S)}if(l===s){Xs=null;break e}var w=l.sibling;if(null!==w){w.return=l.return,Xs=w;break e}Xs=l.return}}if(Nl=i,Hi(),ot&&"function"==typeof ot.onPostCommitFiberRoot)try{ot.onPostCommitFiberRoot(it,e)}catch(S){}r=!0}return r}finally{bt=n,Cl.transition=t}}return!1}function ku(e,t,n){e=Mo(e,t=ps(0,t=cs(n,t),1),1),t=tu(),null!==e&&(vt(e,1,t),iu(e,t))}function Tu(e,t,n){if(3===e.tag)ku(e,e,n);else for(;null!==t;){if(3===t.tag){ku(t,e,n);break}if(1===t.tag){var r=t.stateNode;if("function"==typeof t.type.getDerivedStateFromError||"function"==typeof r.componentDidCatch&&(null===Wl||!Wl.has(r))){t=Mo(t,e=ms(t,e=cs(n,e),1),1),e=tu(),null!==t&&(vt(t,1,e),iu(t,e));break}}t=t.return}}function xu(e,t,n){var r=e.pingCache;null!==r&&r.delete(t),t=tu(),e.pingedLanes|=e.suspendedLanes&n,Il===e&&(Al&n)===n&&(4===Ml||3===Ml&&(130023424&Al)===Al&&500>Ye()-Hl?hu(e,0):jl|=n),iu(e,t)}function Ou(e,t){0===t&&(0==(1&e.mode)?t=1:(t=ct,0==(130023424&(ct<<=1))&&(ct=4194304)));var n=tu();null!==(e=Io(e,t))&&(vt(e,t,n),iu(e,n))}function Cu(e){var t=e.memoizedState,n=0;null!==t&&(n=t.retryLane),Ou(e,n)}function Nu(e,t){var n=0;switch(e.tag){case 13:var r=e.stateNode,i=e.memoizedState;null!==i&&(n=i.retryLane);break;case 19:r=e.stateNode;break;default:throw Error(o(314))}null!==r&&r.delete(t),Ou(e,n)}function Iu(e,t){return We(e,t)}function Du(e,t,n,r){this.tag=e,this.key=n,this.sibling=this.child=this.return=this.stateNode=this.type=this.elementType=null,this.index=0,this.ref=null,this.pendingProps=t,this.dependencies=this.memoizedState=this.updateQueue=this.memoizedProps=null,this.mode=r,this.subtreeFlags=this.flags=0,this.deletions=null,this.childLanes=this.lanes=0,this.alternate=null}function Au(e,t,n,r){return new Du(e,t,n,r)}function Pu(e){return!(!(e=e.prototype)||!e.isReactComponent)}function Lu(e,t){var n=e.alternate;return null===n?((n=Au(e.tag,t,e.key,e.mode)).elementType=e.elementType,n.type=e.type,n.stateNode=e.stateNode,n.alternate=e,e.alternate=n):(n.pendingProps=t,n.type=e.type,n.flags=0,n.subtreeFlags=0,n.deletions=null),n.flags=14680064&e.flags,n.childLanes=e.childLanes,n.lanes=e.lanes,n.child=e.child,n.memoizedProps=e.memoizedProps,n.memoizedState=e.memoizedState,n.updateQueue=e.updateQueue,t=e.dependencies,n.dependencies=null===t?null:{lanes:t.lanes,firstContext:t.firstContext},n.sibling=e.sibling,n.index=e.index,n.ref=e.ref,n}function Mu(e,t,n,r,i,a){var s=2;if(r=e,"function"==typeof e)Pu(e)&&(s=1);else if("string"==typeof e)s=5;else e:switch(e){case E:return Ru(n.children,i,a,t);case k:s=8,i|=8;break;case T:return(e=Au(12,n,t,2|i)).elementType=T,e.lanes=a,e;case N:return(e=Au(13,n,t,i)).elementType=N,e.lanes=a,e;case I:return(e=Au(19,n,t,i)).elementType=I,e.lanes=a,e;case P:return Fu(n,i,a,t);default:if("object"==typeof e&&null!==e)switch(e.$$typeof){case x:s=10;break e;case O:s=9;break e;case C:s=11;break e;case D:s=14;break e;case A:s=16,r=null;break e}throw Error(o(130,null==e?e:typeof e,""))}return(t=Au(s,n,t,i)).elementType=e,t.type=r,t.lanes=a,t}function Ru(e,t,n,r){return(e=Au(7,e,r,t)).lanes=n,e}function Fu(e,t,n,r){return(e=Au(22,e,r,t)).elementType=P,e.lanes=n,e.stateNode={isHidden:!1},e}function Uu(e,t,n){return(e=Au(6,e,null,t)).lanes=n,e}function ju(e,t,n){return(t=Au(4,null!==e.children?e.children:[],e.key,t)).lanes=n,t.stateNode={containerInfo:e.containerInfo,pendingChildren:null,implementation:e.implementation},t}function Bu(e,t,n,r,i){this.tag=t,this.containerInfo=e,this.finishedWork=this.pingCache=this.current=this.pendingChildren=null,this.timeoutHandle=-1,this.callbackNode=this.pendingContext=this.context=null,this.callbackPriority=0,this.eventTimes=gt(0),this.expirationTimes=gt(-1),this.entangledLanes=this.finishedLanes=this.mutableReadLanes=this.expiredLanes=this.pingedLanes=this.suspendedLanes=this.pendingLanes=0,this.entanglements=gt(0),this.identifierPrefix=r,this.onRecoverableError=i,this.mutableSourceEagerHydrationData=null}function zu(e,t,n,r,i,o,a,s,l){return e=new Bu(e,t,n,s,l),1===t?(t=1,!0===o&&(t|=8)):t=0,o=Au(3,null,null,t),e.current=o,o.stateNode=e,o.memoizedState={element:r,isDehydrated:n,cache:null,transitions:null,pendingSuspenseBoundaries:null},Ao(o),e}function Hu(e){if(!e)return Oi;e:{if(He(e=e._reactInternals)!==e||1!==e.tag)throw Error(o(170));var t=e;do{switch(t.tag){case 3:t=t.stateNode.context;break e;case 1:if(Ai(t.type)){t=t.stateNode.__reactInternalMemoizedMergedChildContext;break e}}t=t.return}while(null!==t);throw Error(o(171))}if(1===e.tag){var n=e.type;if(Ai(n))return Mi(e,n,t)}return t}function Vu(e,t,n,r,i,o,a,s,l){return(e=zu(n,r,!0,e,0,o,0,s,l)).context=Hu(null),n=e.current,(o=Lo(r=tu(),i=nu(n))).callback=null!=t?t:null,Mo(n,o,i),e.current.lanes=i,vt(e,i,r),iu(e,r),e}function $u(e,t,n,r){var i=t.current,o=tu(),a=nu(i);return n=Hu(n),null===t.context?t.context=n:t.pendingContext=n,(t=Lo(o,a)).payload={element:e},null!==(r=void 0===r?null:r)&&(t.callback=r),null!==(e=Mo(i,t,a))&&(ru(e,i,a,o),Ro(e,i,a)),a}function Gu(e){return(e=e.current).child?(e.child.tag,e.child.stateNode):null}function qu(e,t){if(null!==(e=e.memoizedState)&&null!==e.dehydrated){var n=e.retryLane;e.retryLane=0!==n&&n<t?n:t}}function Wu(e,t){qu(e,t),(e=e.alternate)&&qu(e,t)}kl=function(e,t,n){if(null!==e)if(e.memoizedProps!==t.pendingProps||Ni.current)_s=!0;else{if(0==(e.lanes&n)&&0==(128&t.flags))return _s=!1,function(e,t,n){switch(t.tag){case 3:Is(t),po();break;case 5:aa(t);break;case 1:Ai(t.type)&&Ri(t);break;case 4:ia(t,t.stateNode.containerInfo);break;case 10:var r=t.type._context,i=t.memoizedProps.value;xi(yo,r._currentValue),r._currentValue=i;break;case 13:if(null!==(r=t.memoizedState))return null!==r.dehydrated?(xi(la,1&la.current),t.flags|=128,null):0!=(n&t.child.childLanes)?Us(e,t,n):(xi(la,1&la.current),null!==(e=Gs(e,t,n))?e.sibling:null);xi(la,1&la.current);break;case 19:if(r=0!=(n&t.childLanes),0!=(128&e.flags)){if(r)return Vs(e,t,n);t.flags|=128}if(null!==(i=t.memoizedState)&&(i.rendering=null,i.tail=null,i.lastEffect=null),xi(la,la.current),r)break;return null;case 22:case 23:return t.lanes=0,Ts(e,t,n)}return Gs(e,t,n)}(e,t,n);_s=0!=(131072&e.flags)}else _s=!1,io&&0!=(1048576&t.flags)&&Xi(t,qi,t.index);switch(t.lanes=0,t.tag){case 2:var r=t.type;$s(e,t),e=t.pendingProps;var i=Di(t,Ci.current);To(t,n),i=ka(null,t,r,e,i,n);var a=Ta();return t.flags|=1,"object"==typeof i&&null!==i&&"function"==typeof i.render&&void 0===i.$$typeof?(t.tag=1,t.memoizedState=null,t.updateQueue=null,Ai(r)?(a=!0,Ri(t)):a=!1,t.memoizedState=null!==i.state&&void 0!==i.state?i.state:null,Ao(t),i.updater=Ho,t.stateNode=i,i._reactInternals=t,qo(t,r,e,n),t=Ns(null,t,r,!0,a,n)):(t.tag=0,io&&a&&eo(t),ws(null,t,i,n),t=t.child),t;case 16:r=t.elementType;e:{switch($s(e,t),e=t.pendingProps,r=(i=r._init)(r._payload),t.type=r,i=t.tag=function(e){if("function"==typeof e)return Pu(e)?1:0;if(null!=e){if((e=e.$$typeof)===C)return 11;if(e===D)return 14}return 2}(r),e=vo(r,e),i){case 0:t=Os(null,t,r,e,n);break e;case 1:t=Cs(null,t,r,e,n);break e;case 11:t=Ss(null,t,r,e,n);break e;case 14:t=Es(null,t,r,vo(r.type,e),n);break e}throw Error(o(306,r,""))}return t;case 0:return r=t.type,i=t.pendingProps,Os(e,t,r,i=t.elementType===r?i:vo(r,i),n);case 1:return r=t.type,i=t.pendingProps,Cs(e,t,r,i=t.elementType===r?i:vo(r,i),n);case 3:e:{if(Is(t),null===e)throw Error(o(387));r=t.pendingProps,i=(a=t.memoizedState).element,Po(e,t),Uo(t,r,null,n);var s=t.memoizedState;if(r=s.element,a.isDehydrated){if(a={element:r,isDehydrated:!1,cache:s.cache,pendingSuspenseBoundaries:s.pendingSuspenseBoundaries,transitions:s.transitions},t.updateQueue.baseState=a,t.memoizedState=a,256&t.flags){t=Ds(e,t,r,n,i=cs(Error(o(423)),t));break e}if(r!==i){t=Ds(e,t,r,n,i=cs(Error(o(424)),t));break e}for(ro=ui(t.stateNode.containerInfo.firstChild),no=t,io=!0,oo=null,n=Qo(t,null,r,n),t.child=n;n;)n.flags=-3&n.flags|4096,n=n.sibling}else{if(po(),r===i){t=Gs(e,t,n);break e}ws(e,t,r,n)}t=t.child}return t;case 5:return aa(t),null===e&&uo(t),r=t.type,i=t.pendingProps,a=null!==e?e.memoizedProps:null,s=i.children,ni(r,i)?s=null:null!==a&&ni(r,a)&&(t.flags|=32),xs(e,t),ws(e,t,s,n),t.child;case 6:return null===e&&uo(t),null;case 13:return Us(e,t,n);case 4:return ia(t,t.stateNode.containerInfo),r=t.pendingProps,null===e?t.child=Yo(t,null,r,n):ws(e,t,r,n),t.child;case 11:return r=t.type,i=t.pendingProps,Ss(e,t,r,i=t.elementType===r?i:vo(r,i),n);case 7:return ws(e,t,t.pendingProps,n),t.child;case 8:case 12:return ws(e,t,t.pendingProps.children,n),t.child;case 10:e:{if(r=t.type._context,i=t.pendingProps,a=t.memoizedProps,s=i.value,xi(yo,r._currentValue),r._currentValue=s,null!==a)if(sr(a.value,s)){if(a.children===i.children&&!Ni.current){t=Gs(e,t,n);break e}}else for(null!==(a=t.child)&&(a.return=t);null!==a;){var l=a.dependencies;if(null!==l){s=a.child;for(var u=l.firstContext;null!==u;){if(u.context===r){if(1===a.tag){(u=Lo(-1,n&-n)).tag=2;var c=a.updateQueue;if(null!==c){var f=(c=c.shared).pending;null===f?u.next=u:(u.next=f.next,f.next=u),c.pending=u}}a.lanes|=n,null!==(u=a.alternate)&&(u.lanes|=n),ko(a.return,n,t),l.lanes|=n;break}u=u.next}}else if(10===a.tag)s=a.type===t.type?null:a.child;else if(18===a.tag){if(null===(s=a.return))throw Error(o(341));s.lanes|=n,null!==(l=s.alternate)&&(l.lanes|=n),ko(s,n,t),s=a.sibling}else s=a.child;if(null!==s)s.return=a;else for(s=a;null!==s;){if(s===t){s=null;break}if(null!==(a=s.sibling)){a.return=s.return,s=a;break}s=s.return}a=s}ws(e,t,i.children,n),t=t.child}return t;case 9:return i=t.type,r=t.pendingProps.children,To(t,n),r=r(i=xo(i)),t.flags|=1,ws(e,t,r,n),t.child;case 14:return i=vo(r=t.type,t.pendingProps),Es(e,t,r,i=vo(r.type,i),n);case 15:return ks(e,t,t.type,t.pendingProps,n);case 17:return r=t.type,i=t.pendingProps,i=t.elementType===r?i:vo(r,i),$s(e,t),t.tag=1,Ai(r)?(e=!0,Ri(t)):e=!1,To(t,n),$o(t,r,i),qo(t,r,i,n),Ns(null,t,r,!0,e,n);case 19:return Vs(e,t,n);case 22:return Ts(e,t,n)}throw Error(o(156,t.tag))};var Ku="function"==typeof reportError?reportError:function(e){console.error(e)};function Zu(e){this._internalRoot=e}function Ju(e){this._internalRoot=e}function Yu(e){return!(!e||1!==e.nodeType&&9!==e.nodeType&&11!==e.nodeType)}function Qu(e){return!(!e||1!==e.nodeType&&9!==e.nodeType&&11!==e.nodeType&&(8!==e.nodeType||" react-mount-point-unstable "!==e.nodeValue))}function Xu(){}function ec(e,t,n,r,i){var o=n._reactRootContainer;if(o){var a=o;if("function"==typeof i){var s=i;i=function(){var e=Gu(a);s.call(e)}}$u(t,a,e,i)}else a=function(e,t,n,r,i){if(i){if("function"==typeof r){var o=r;r=function(){var e=Gu(a);o.call(e)}}var a=Vu(t,r,e,0,null,!1,0,"",Xu);return e._reactRootContainer=a,e[pi]=a.current,Hr(8===e.nodeType?e.parentNode:e),fu(),a}for(;i=e.lastChild;)e.removeChild(i);if("function"==typeof r){var s=r;r=function(){var e=Gu(l);s.call(e)}}var l=zu(e,0,!1,null,0,!1,0,"",Xu);return e._reactRootContainer=l,e[pi]=l.current,Hr(8===e.nodeType?e.parentNode:e),fu((function(){$u(t,l,n,r)})),l}(n,t,e,i,r);return Gu(a)}Ju.prototype.render=Zu.prototype.render=function(e){var t=this._internalRoot;if(null===t)throw Error(o(409));$u(e,t,null,null)},Ju.prototype.unmount=Zu.prototype.unmount=function(){var e=this._internalRoot;if(null!==e){this._internalRoot=null;var t=e.containerInfo;fu((function(){$u(null,e,null,null)})),t[pi]=null}},Ju.prototype.unstable_scheduleHydration=function(e){if(e){var t=kt();e={blockedOn:null,target:e,priority:t};for(var n=0;n<Pt.length&&0!==t&&t<Pt[n].priority;n++);Pt.splice(n,0,e),0===n&&Ft(e)}},wt=function(e){switch(e.tag){case 3:var t=e.stateNode;if(t.current.memoizedState.isDehydrated){var n=ft(t.pendingLanes);0!==n&&(yt(t,1|n),iu(t,Ye()),0==(6&Nl)&&(Vl=Ye()+500,Hi()))}break;case 13:fu((function(){var t=Io(e,1);if(null!==t){var n=tu();ru(t,e,1,n)}})),Wu(e,1)}},St=function(e){if(13===e.tag){var t=Io(e,134217728);if(null!==t)ru(t,e,134217728,tu());Wu(e,134217728)}},Et=function(e){if(13===e.tag){var t=nu(e),n=Io(e,t);if(null!==n)ru(n,e,t,tu());Wu(e,t)}},kt=function(){return bt},Tt=function(e,t){var n=bt;try{return bt=e,t()}finally{bt=n}},Se=function(e,t,n){switch(t){case"input":if(Q(e,n),t=n.name,"radio"===n.type&&null!=t){for(n=e;n.parentNode;)n=n.parentNode;for(n=n.querySelectorAll("input[name="+JSON.stringify(""+t)+'][type="radio"]'),t=0;t<n.length;t++){var r=n[t];if(r!==e&&r.form===e.form){var i=wi(r);if(!i)throw Error(o(90));W(r),Q(r,i)}}}break;case"textarea":oe(e,n);break;case"select":null!=(t=n.value)&&ne(e,!!n.multiple,t,!1)}},Ce=cu,Ne=fu;var tc={usingClientEntryPoint:!1,Events:[bi,_i,wi,xe,Oe,cu]},nc={findFiberByHostInstance:yi,bundleType:0,version:"18.2.0",rendererPackageName:"react-dom"},rc={bundleType:nc.bundleType,version:nc.version,rendererPackageName:nc.rendererPackageName,rendererConfig:nc.rendererConfig,overrideHookState:null,overrideHookStateDeletePath:null,overrideHookStateRenamePath:null,overrideProps:null,overridePropsDeletePath:null,overridePropsRenamePath:null,setErrorHandler:null,setSuspenseHandler:null,scheduleUpdate:null,currentDispatcherRef:_.ReactCurrentDispatcher,findHostInstanceByFiber:function(e){return null===(e=Ge(e))?null:e.stateNode},findFiberByHostInstance:nc.findFiberByHostInstance||function(){return null},findHostInstancesForRefresh:null,scheduleRefresh:null,scheduleRoot:null,setRefreshHandler:null,getCurrentFiber:null,reconcilerVersion:"18.2.0-next-9e3b772b8-20220608"};if("undefined"!=typeof __REACT_DEVTOOLS_GLOBAL_HOOK__){var ic=__REACT_DEVTOOLS_GLOBAL_HOOK__;if(!ic.isDisabled&&ic.supportsFiber)try{it=ic.inject(rc),ot=ic}catch(ce){}}t.__SECRET_INTERNALS_DO_NOT_USE_OR_YOU_WILL_BE_FIRED=tc,t.createPortal=function(e,t){var n=2<arguments.length&&void 0!==arguments[2]?arguments[2]:null;if(!Yu(t))throw Error(o(200));return function(e,t,n){var r=3<arguments.length&&void 0!==arguments[3]?arguments[3]:null;return{$$typeof:S,key:null==r?null:""+r,children:e,containerInfo:t,implementation:n}}(e,t,null,n)},t.createRoot=function(e,t){if(!Yu(e))throw Error(o(299));var n=!1,r="",i=Ku;return null!=t&&(!0===t.unstable_strictMode&&(n=!0),void 0!==t.identifierPrefix&&(r=t.identifierPrefix),void 0!==t.onRecoverableError&&(i=t.onRecoverableError)),t=zu(e,1,!1,null,0,n,0,r,i),e[pi]=t.current,Hr(8===e.nodeType?e.parentNode:e),new Zu(t)},t.findDOMNode=function(e){if(null==e)return null;if(1===e.nodeType)return e;var t=e._reactInternals;if(void 0===t){if("function"==typeof e.render)throw Error(o(188));throw e=Object.keys(e).join(","),Error(o(268,e))}return e=null===(e=Ge(t))?null:e.stateNode},t.flushSync=function(e){return fu(e)},t.hydrate=function(e,t,n){if(!Qu(t))throw Error(o(200));return ec(null,e,t,!0,n)},t.hydrateRoot=function(e,t,n){if(!Yu(e))throw Error(o(405));var r=null!=n&&n.hydratedSources||null,i=!1,a="",s=Ku;if(null!=n&&(!0===n.unstable_strictMode&&(i=!0),void 0!==n.identifierPrefix&&(a=n.identifierPrefix),void 0!==n.onRecoverableError&&(s=n.onRecoverableError)),t=Vu(t,null,e,1,null!=n?n:null,i,0,a,s),e[pi]=t.current,Hr(e),r)for(e=0;e<r.length;e++)i=(i=(n=r[e])._getVersion)(n._source),null==t.mutableSourceEagerHydrationData?t.mutableSourceEagerHydrationData=[n,i]:t.mutableSourceEagerHydrationData.push(n,i);return new Ju(t)},t.render=function(e,t,n){if(!Qu(t))throw Error(o(200));return ec(null,e,t,!1,n)},t.unmountComponentAtNode=function(e){if(!Qu(e))throw Error(o(40));return!!e._reactRootContainer&&(fu((function(){ec(null,null,e,!1,(function(){e._reactRootContainer=null,e[pi]=null}))})),!0)},t.unstable_batchedUpdates=cu,t.unstable_renderSubtreeIntoContainer=function(e,t,n,r){if(!Qu(n))throw Error(o(200));if(null==e||void 0===e._reactInternals)throw Error(o(38));return ec(e,t,n,!1,r)},t.version="18.2.0-next-9e3b772b8-20220608"},823215:(e,t,n)=>{"use strict";var r=n(746518),i=n(72652),o=n(479306),a=n(28551),s=n(301767);r({target:"Iterator",proto:!0,real:!0},{every:function(e){a(this),o(e);var t=s(this),n=0;return!i(t,(function(t,r){if(!e(t,n++))return r()}),{IS_RECORD:!0,INTERRUPTED:!0}).stopped}})},836043:(e,t,n)=>{"use strict";var r=n(479306),i=TypeError,o=function(e){var t,n;this.promise=new e((function(e,r){if(void 0!==t||void 0!==n)throw new i("Bad Promise constructor");t=e,n=r})),this.resolve=r(t),this.reject=r(n)};e.exports.f=function(e){return new o(e)}},842451:function(e,t,n){"use strict";var r=this&&this.__awaiter||function(e,t,n,r){return new(n||(n=Promise))((function(i,o){function a(e){try{l(r.next(e))}catch(t){o(t)}}function s(e){try{l(r.throw(e))}catch(t){o(t)}}function l(e){var t;e.done?i(e.value):(t=e.value,t instanceof n?t:new n((function(e){e(t)}))).then(a,s)}l((r=r.apply(e,t||[])).next())}))};Object.defineProperty(t,"__esModule",{value:!0}),t.StatsigEvaluationsDataAdapter=void 0;const i=()=>n(636978),o=()=>n(782944);class a extends i().DataAdapterCore{constructor(){super("EvaluationsDataAdapter","evaluations"),this._network=null,this._options=null}attach(e,t,n){super.attach(e,t,n),null!==n&&n instanceof o().default?this._network=n:this._network=new(o().default)(null!=t?t:{})}getDataAsync(e,t,n){return this._getDataAsyncImpl(e,(0,i()._normalizeUser)(t,this._options),n)}prefetchData(e,t){return this._prefetchDataImpl(e,t)}setData(e){const t=(0,i()._typedJsonParse)(e,"has_updates","data");t&&"user"in t?super.setData(e,t.user):i().Log.error("StatsigUser not found. You may be using an older server SDK version. Please upgrade your SDK or use setDataLegacy.")}setDataLegacy(e,t){super.setData(e,t)}_fetchFromNetwork(e,t,n,i){var o;return r(this,void 0,void 0,(function*(){const r=yield null===(o=this._network)||void 0===o?void 0:o.fetchEvaluations(this._getSdkKey(),e,null==n?void 0:n.priority,t,i);return null!=r?r:null}))}_getCacheKey(e){var t;const n=(0,i()._getStorageKey)(this._getSdkKey(),e,null===(t=this._options)||void 0===t?void 0:t.customUserCacheKeyFunc);return`${i().DataAdapterCachePrefix}.${this._cacheSuffix}.${n}`}_isCachedResultValidFor204(e,t){return null!=e.fullUserHash&&e.fullUserHash===(0,i()._getFullUserHash)(t)}}t.StatsigEvaluationsDataAdapter=a},843509:(e,t,n)=>{"use strict";var r,i,o;Object.defineProperty(t,"__esModule",{value:!0}),t._getInstance=t._getStatsigGlobalFlag=t._getStatsigGlobal=void 0;t._getStatsigGlobal=()=>{try{return"undefined"!=typeof __STATSIG__?__STATSIG__:c}catch(e){return c}};t._getStatsigGlobalFlag=e=>(0,t._getStatsigGlobal)()[e];t._getInstance=e=>{const r=(0,t._getStatsigGlobal)();return e?r.instances&&r.instances[e]:(r.instances&&Object.keys(r.instances).length>1&&n(668024).Log.warn("Call made to Statsig global instance without an SDK key but there is more than one client instance. If you are using mulitple clients, please specify the SDK key."),r.firstInstance)};const a="__STATSIG__",s="undefined"!=typeof window?window:{},l=void 0!==n.g?n.g:{},u="undefined"!=typeof globalThis?globalThis:{},c=null!==(o=null!==(i=null!==(r=s[a])&&void 0!==r?r:l[a])&&void 0!==i?i:u[a])&&void 0!==o?o:{instance:t._getInstance};s[a]=c,l[a]=c,u[a]=c},863024:(e,t)=>{function n(e,t){return e===t?0:e>t?1:-1}function r(e,t,r){r||(r=n);var o=i(e,t,r);return-1===o?-1:0===r(e[o],t)?o:-1}function i(e,t,r){r||(r=n);for(var i=e.length,o=i-1,a=0,s=-1;o>=a&&a>=0&&o<i;){var l=r(e[s=Math.floor((o+a)/2)],t);if(0===l)return s;l>=0?o=s-1:a=s+1}return s}t.add=function(e,t,r){r||(r=n);var i=e.push(t)-1;for(;i;){if(r(t,e[i-1])>0)return;e[i]=e[i-1],e[i-1]=t,i--}},t.remove=function(e,t,n){var i=r(e,t,n);return-1!==i&&(e.splice(i,1),!0)},t.has=function(e,t,n){return r(e,t,n)>-1},t.eq=r,t.lte=function(e,t,r){r||(r=n);var o=i(e,t,r);if(-1===o)return-1;for(;o>=0;o--){if(r(e[o],t)<=0)return o}return-1},t.lt=function(e,t,r){r||(r=n);var o=i(e,t,r);if(-1===o)return-1;for(;o>=0;o--){if(r(e[o],t)<0)return o}return-1},t.gte=function(e,t,r){r||(r=n);var o=i(e,t,r);if(-1===o)return-1;for(;o<e.length;o++){if(r(e[o],t)>=0)return o}return-1},t.gt=function(e,t,r){r||(r=n);var o=i(e,t,r);if(-1===o)return-1;for(;o<e.length;o++){if(r(e[o],t)>0)return o}return-1}},871386:(e,t,n)=>{"use strict";n.d(t,{A:()=>s});const r={randomUUID:"undefined"!=typeof crypto&&crypto.randomUUID&&crypto.randomUUID.bind(crypto)};let i;const o=new Uint8Array(16);function a(){if(!i&&(i="undefined"!=typeof crypto&&crypto.getRandomValues&&crypto.getRandomValues.bind(crypto),!i))throw new Error("crypto.getRandomValues() not supported. See https://github.com/uuidjs/uuid#getrandomvalues-not-supported");return i(o)}const s=function(e,t,i){if(r.randomUUID&&!t&&!e)return r.randomUUID();const o=(e=e||{}).random||(e.rng||a)();if(o[6]=15&o[6]|64,o[8]=63&o[8]|128,t){i=i||0;for(let e=0;e<16;++e)t[i+e]=o[e];return t}return(0,n(338823).k)(o)}},878100:(e,t,n)=>{"use strict";var r=n(746518),i=n(595636);i&&r({target:"ArrayBuffer",proto:!0},{transfer:function(){return i(this,arguments.length?arguments[0]:void 0,!0)}})},882509:(e,t)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0})},883503:(e,t,n)=>{var r,i,o,a;r=()=>n(3939),i=n(392151).utf8,o=n(392151).bin,(a=function(e,t){e.constructor==String?e=t&&"binary"===t.encoding?o.stringToBytes(e):i.stringToBytes(e):n(587206)(e)?e=Array.prototype.slice.call(e,0):Array.isArray(e)||(e=e.toString());for(var s=r().bytesToWords(e),l=8*e.length,u=1732584193,c=-271733879,f=-1732584194,d=271733878,h=0;h<s.length;h++)s[h]=16711935&(s[h]<<8|s[h]>>>24)|4278255360&(s[h]<<24|s[h]>>>8);s[l>>>5]|=128<<l%32,s[14+(l+64>>>9<<4)]=l;var p=a._ff,m=a._gg,g=a._hh,v=a._ii;for(h=0;h<s.length;h+=16){var y=u,b=c,_=f,w=d;u=p(u,c,f,d,s[h+0],7,-680876936),d=p(d,u,c,f,s[h+1],12,-389564586),f=p(f,d,u,c,s[h+2],17,606105819),c=p(c,f,d,u,s[h+3],22,-1044525330),u=p(u,c,f,d,s[h+4],7,-176418897),d=p(d,u,c,f,s[h+5],12,1200080426),f=p(f,d,u,c,s[h+6],17,-1473231341),c=p(c,f,d,u,s[h+7],22,-45705983),u=p(u,c,f,d,s[h+8],7,1770035416),d=p(d,u,c,f,s[h+9],12,-1958414417),f=p(f,d,u,c,s[h+10],17,-42063),c=p(c,f,d,u,s[h+11],22,-1990404162),u=p(u,c,f,d,s[h+12],7,1804603682),d=p(d,u,c,f,s[h+13],12,-40341101),f=p(f,d,u,c,s[h+14],17,-1502002290),u=m(u,c=p(c,f,d,u,s[h+15],22,1236535329),f,d,s[h+1],5,-165796510),d=m(d,u,c,f,s[h+6],9,-1069501632),f=m(f,d,u,c,s[h+11],14,643717713),c=m(c,f,d,u,s[h+0],20,-373897302),u=m(u,c,f,d,s[h+5],5,-701558691),d=m(d,u,c,f,s[h+10],9,38016083),f=m(f,d,u,c,s[h+15],14,-660478335),c=m(c,f,d,u,s[h+4],20,-405537848),u=m(u,c,f,d,s[h+9],5,568446438),d=m(d,u,c,f,s[h+14],9,-1019803690),f=m(f,d,u,c,s[h+3],14,-187363961),c=m(c,f,d,u,s[h+8],20,1163531501),u=m(u,c,f,d,s[h+13],5,-1444681467),d=m(d,u,c,f,s[h+2],9,-51403784),f=m(f,d,u,c,s[h+7],14,1735328473),u=g(u,c=m(c,f,d,u,s[h+12],20,-1926607734),f,d,s[h+5],4,-378558),d=g(d,u,c,f,s[h+8],11,-2022574463),f=g(f,d,u,c,s[h+11],16,1839030562),c=g(c,f,d,u,s[h+14],23,-35309556),u=g(u,c,f,d,s[h+1],4,-1530992060),d=g(d,u,c,f,s[h+4],11,1272893353),f=g(f,d,u,c,s[h+7],16,-155497632),c=g(c,f,d,u,s[h+10],23,-1094730640),u=g(u,c,f,d,s[h+13],4,681279174),d=g(d,u,c,f,s[h+0],11,-358537222),f=g(f,d,u,c,s[h+3],16,-722521979),c=g(c,f,d,u,s[h+6],23,76029189),u=g(u,c,f,d,s[h+9],4,-640364487),d=g(d,u,c,f,s[h+12],11,-421815835),f=g(f,d,u,c,s[h+15],16,530742520),u=v(u,c=g(c,f,d,u,s[h+2],23,-995338651),f,d,s[h+0],6,-198630844),d=v(d,u,c,f,s[h+7],10,1126891415),f=v(f,d,u,c,s[h+14],15,-1416354905),c=v(c,f,d,u,s[h+5],21,-57434055),u=v(u,c,f,d,s[h+12],6,1700485571),d=v(d,u,c,f,s[h+3],10,-1894986606),f=v(f,d,u,c,s[h+10],15,-1051523),c=v(c,f,d,u,s[h+1],21,-2054922799),u=v(u,c,f,d,s[h+8],6,1873313359),d=v(d,u,c,f,s[h+15],10,-30611744),f=v(f,d,u,c,s[h+6],15,-1560198380),c=v(c,f,d,u,s[h+13],21,1309151649),u=v(u,c,f,d,s[h+4],6,-145523070),d=v(d,u,c,f,s[h+11],10,-1120210379),f=v(f,d,u,c,s[h+2],15,718787259),c=v(c,f,d,u,s[h+9],21,-343485551),u=u+y>>>0,c=c+b>>>0,f=f+_>>>0,d=d+w>>>0}return r().endian([u,c,f,d])})._ff=function(e,t,n,r,i,o,a){var s=e+(t&n|~t&r)+(i>>>0)+a;return(s<<o|s>>>32-o)+t},a._gg=function(e,t,n,r,i,o,a){var s=e+(t&r|n&~r)+(i>>>0)+a;return(s<<o|s>>>32-o)+t},a._hh=function(e,t,n,r,i,o,a){var s=e+(t^n^r)+(i>>>0)+a;return(s<<o|s>>>32-o)+t},a._ii=function(e,t,n,r,i,o,a){var s=e+(n^(t|~r))+(i>>>0)+a;return(s<<o|s>>>32-o)+t},a._blocksize=16,a._digestsize=16,e.exports=function(e,t){if(null==e)throw new Error("Illegal argument "+e);var n=r().wordsToBytes(a(e,t));return t&&t.asBytes?n:t&&t.asString?o.bytesToString(n):r().bytesToHex(n)}},892677:(e,t,n)=>{"use strict";n.d(t,{ZE:()=>i,Im:()=>l,tv:()=>c,Tu:()=>v,eW:()=>s,oF:()=>u,N1:()=>g,N6:()=>h,jA:()=>p,Jp:()=>d,xm:()=>m,Qh:()=>f,qg:()=>oe});var r,i,o,a=()=>n(331635);function s(e){return e.type===i.literal}function l(e){return e.type===i.argument}function u(e){return e.type===i.number}function c(e){return e.type===i.date}function f(e){return e.type===i.time}function d(e){return e.type===i.select}function h(e){return e.type===i.plural}function p(e){return e.type===i.pound}function m(e){return e.type===i.tag}function g(e){return!(!e||"object"!=typeof e||e.type!==o.number)}function v(e){return!(!e||"object"!=typeof e||e.type!==o.dateTime)}!function(e){e[e.EXPECT_ARGUMENT_CLOSING_BRACE=1]="EXPECT_ARGUMENT_CLOSING_BRACE",e[e.EMPTY_ARGUMENT=2]="EMPTY_ARGUMENT",e[e.MALFORMED_ARGUMENT=3]="MALFORMED_ARGUMENT",e[e.EXPECT_ARGUMENT_TYPE=4]="EXPECT_ARGUMENT_TYPE",e[e.INVALID_ARGUMENT_TYPE=5]="INVALID_ARGUMENT_TYPE",e[e.EXPECT_ARGUMENT_STYLE=6]="EXPECT_ARGUMENT_STYLE",e[e.INVALID_NUMBER_SKELETON=7]="INVALID_NUMBER_SKELETON",e[e.INVALID_DATE_TIME_SKELETON=8]="INVALID_DATE_TIME_SKELETON",e[e.EXPECT_NUMBER_SKELETON=9]="EXPECT_NUMBER_SKELETON",e[e.EXPECT_DATE_TIME_SKELETON=10]="EXPECT_DATE_TIME_SKELETON",e[e.UNCLOSED_QUOTE_IN_ARGUMENT_STYLE=11]="UNCLOSED_QUOTE_IN_ARGUMENT_STYLE",e[e.EXPECT_SELECT_ARGUMENT_OPTIONS=12]="EXPECT_SELECT_ARGUMENT_OPTIONS",e[e.EXPECT_PLURAL_ARGUMENT_OFFSET_VALUE=13]="EXPECT_PLURAL_ARGUMENT_OFFSET_VALUE",e[e.INVALID_PLURAL_ARGUMENT_OFFSET_VALUE=14]="INVALID_PLURAL_ARGUMENT_OFFSET_VALUE",e[e.EXPECT_SELECT_ARGUMENT_SELECTOR=15]="EXPECT_SELECT_ARGUMENT_SELECTOR",e[e.EXPECT_PLURAL_ARGUMENT_SELECTOR=16]="EXPECT_PLURAL_ARGUMENT_SELECTOR",e[e.EXPECT_SELECT_ARGUMENT_SELECTOR_FRAGMENT=17]="EXPECT_SELECT_ARGUMENT_SELECTOR_FRAGMENT",e[e.EXPECT_PLURAL_ARGUMENT_SELECTOR_FRAGMENT=18]="EXPECT_PLURAL_ARGUMENT_SELECTOR_FRAGMENT",e[e.INVALID_PLURAL_ARGUMENT_SELECTOR=19]="INVALID_PLURAL_ARGUMENT_SELECTOR",e[e.DUPLICATE_PLURAL_ARGUMENT_SELECTOR=20]="DUPLICATE_PLURAL_ARGUMENT_SELECTOR",e[e.DUPLICATE_SELECT_ARGUMENT_SELECTOR=21]="DUPLICATE_SELECT_ARGUMENT_SELECTOR",e[e.MISSING_OTHER_CLAUSE=22]="MISSING_OTHER_CLAUSE",e[e.INVALID_TAG=23]="INVALID_TAG",e[e.INVALID_TAG_NAME=25]="INVALID_TAG_NAME",e[e.UNMATCHED_CLOSING_TAG=26]="UNMATCHED_CLOSING_TAG",e[e.UNCLOSED_TAG=27]="UNCLOSED_TAG"}(r||(r={})),function(e){e[e.literal=0]="literal",e[e.argument=1]="argument",e[e.number=2]="number",e[e.date=3]="date",e[e.time=4]="time",e[e.select=5]="select",e[e.plural=6]="plural",e[e.pound=7]="pound",e[e.tag=8]="tag"}(i||(i={})),function(e){e[e.number=0]="number",e[e.dateTime=1]="dateTime"}(o||(o={}));var y=/[ \xA0\u1680\u2000-\u200A\u202F\u205F\u3000]/,b=/(?:[Eec]{1,6}|G{1,5}|[Qq]{1,5}|(?:[yYur]+|U{1,5})|[ML]{1,5}|d{1,2}|D{1,3}|F{1}|[abB]{1,5}|[hkHK]{1,2}|w{1,2}|W{1}|m{1,2}|s{1,2}|[zZOvVxX]{1,4})(?=([^']*'[^']*')*[^']*$)/g;function _(e){var t={};return e.replace(b,(function(e){var n=e.length;switch(e[0]){case"G":t.era=4===n?"long":5===n?"narrow":"short";break;case"y":t.year=2===n?"2-digit":"numeric";break;case"Y":case"u":case"U":case"r":throw new RangeError("`Y/u/U/r` (year) patterns are not supported, use `y` instead");case"q":case"Q":throw new RangeError("`q/Q` (quarter) patterns are not supported");case"M":case"L":t.month=["numeric","2-digit","short","long","narrow"][n-1];break;case"w":case"W":throw new RangeError("`w/W` (week) patterns are not supported");case"d":t.day=["numeric","2-digit"][n-1];break;case"D":case"F":case"g":throw new RangeError("`D/F/g` (day) patterns are not supported, use `d` instead");case"E":t.weekday=4===n?"long":5===n?"narrow":"short";break;case"e":if(n<4)throw new RangeError("`e..eee` (weekday) patterns are not supported");t.weekday=["short","long","narrow","short"][n-4];break;case"c":if(n<4)throw new RangeError("`c..ccc` (weekday) patterns are not supported");t.weekday=["short","long","narrow","short"][n-4];break;case"a":t.hour12=!0;break;case"b":case"B":throw new RangeError("`b/B` (period) patterns are not supported, use `a` instead");case"h":t.hourCycle="h12",t.hour=["numeric","2-digit"][n-1];break;case"H":t.hourCycle="h23",t.hour=["numeric","2-digit"][n-1];break;case"K":t.hourCycle="h11",t.hour=["numeric","2-digit"][n-1];break;case"k":t.hourCycle="h24",t.hour=["numeric","2-digit"][n-1];break;case"j":case"J":case"C":throw new RangeError("`j/J/C` (hour) patterns are not supported, use `h/H/K/k` instead");case"m":t.minute=["numeric","2-digit"][n-1];break;case"s":t.second=["numeric","2-digit"][n-1];break;case"S":case"A":throw new RangeError("`S/A` (second) patterns are not supported, use `s` instead");case"z":t.timeZoneName=n<4?"short":"long";break;case"Z":case"O":case"v":case"V":case"X":case"x":throw new RangeError("`Z/O/v/V/X/x` (timeZone) patterns are not supported, use `z` instead")}return""})),t}var w=/[\t-\r \x85\u200E\u200F\u2028\u2029]/i;var S=/^\.(?:(0+)(\*)?|(#+)|(0+)(#+))$/g,E=/^(@+)?(\+|#+)?[rs]?$/g,k=/(\*)(0+)|(#+)(0+)|(0+)/g,T=/^(0+)$/;function x(e){var t={};return"r"===e[e.length-1]?t.roundingPriority="morePrecision":"s"===e[e.length-1]&&(t.roundingPriority="lessPrecision"),e.replace(E,(function(e,n,r){return"string"!=typeof r?(t.minimumSignificantDigits=n.length,t.maximumSignificantDigits=n.length):"+"===r?t.minimumSignificantDigits=n.length:"#"===n[0]?t.maximumSignificantDigits=n.length:(t.minimumSignificantDigits=n.length,t.maximumSignificantDigits=n.length+("string"==typeof r?r.length:0)),""})),t}function O(e){switch(e){case"sign-auto":return{signDisplay:"auto"};case"sign-accounting":case"()":return{currencySign:"accounting"};case"sign-always":case"+!":return{signDisplay:"always"};case"sign-accounting-always":case"()!":return{signDisplay:"always",currencySign:"accounting"};case"sign-except-zero":case"+?":return{signDisplay:"exceptZero"};case"sign-accounting-except-zero":case"()?":return{signDisplay:"exceptZero",currencySign:"accounting"};case"sign-never":case"+_":return{signDisplay:"never"}}}function C(e){var t;if("E"===e[0]&&"E"===e[1]?(t={notation:"engineering"},e=e.slice(2)):"E"===e[0]&&(t={notation:"scientific"},e=e.slice(1)),t){var n=e.slice(0,2);if("+!"===n?(t.signDisplay="always",e=e.slice(2)):"+?"===n&&(t.signDisplay="exceptZero",e=e.slice(2)),!T.test(e))throw new Error("Malformed concise eng/scientific notation");t.minimumIntegerDigits=e.length}return t}function N(e){var t=O(e);return t||{}}function I(e){for(var t={},n=0,r=e;n<r.length;n++){var i=r[n];switch(i.stem){case"percent":case"%":t.style="percent";continue;case"%x100":t.style="percent",t.scale=100;continue;case"currency":t.style="currency",t.currency=i.options[0];continue;case"group-off":case",_":t.useGrouping=!1;continue;case"precision-integer":case".":t.maximumFractionDigits=0;continue;case"measure-unit":case"unit":t.style="unit",t.unit=i.options[0].replace(/^(.*?)-/,"");continue;case"compact-short":case"K":t.notation="compact",t.compactDisplay="short";continue;case"compact-long":case"KK":t.notation="compact",t.compactDisplay="long";continue;case"scientific":t=(0,a().__assign)((0,a().__assign)((0,a().__assign)({},t),{notation:"scientific"}),i.options.reduce((function(e,t){return(0,a().__assign)((0,a().__assign)({},e),N(t))}),{}));continue;case"engineering":t=(0,a().__assign)((0,a().__assign)((0,a().__assign)({},t),{notation:"engineering"}),i.options.reduce((function(e,t){return(0,a().__assign)((0,a().__assign)({},e),N(t))}),{}));continue;case"notation-simple":t.notation="standard";continue;case"unit-width-narrow":t.currencyDisplay="narrowSymbol",t.unitDisplay="narrow";continue;case"unit-width-short":t.currencyDisplay="code",t.unitDisplay="short";continue;case"unit-width-full-name":t.currencyDisplay="name",t.unitDisplay="long";continue;case"unit-width-iso-code":t.currencyDisplay="symbol";continue;case"scale":t.scale=parseFloat(i.options[0]);continue;case"rounding-mode-floor":t.roundingMode="floor";continue;case"rounding-mode-ceiling":t.roundingMode="ceil";continue;case"rounding-mode-down":t.roundingMode="trunc";continue;case"rounding-mode-up":t.roundingMode="expand";continue;case"rounding-mode-half-even":t.roundingMode="halfEven";continue;case"rounding-mode-half-down":t.roundingMode="halfTrunc";continue;case"rounding-mode-half-up":t.roundingMode="halfExpand";continue;case"integer-width":if(i.options.length>1)throw new RangeError("integer-width stems only accept a single optional option");i.options[0].replace(k,(function(e,n,r,i,o,a){if(n)t.minimumIntegerDigits=r.length;else{if(i&&o)throw new Error("We currently do not support maximum integer digits");if(a)throw new Error("We currently do not support exact integer digits")}return""}));continue}if(T.test(i.stem))t.minimumIntegerDigits=i.stem.length;else if(S.test(i.stem)){if(i.options.length>1)throw new RangeError("Fraction-precision stems only accept a single optional option");i.stem.replace(S,(function(e,n,r,i,o,a){return"*"===r?t.minimumFractionDigits=n.length:i&&"#"===i[0]?t.maximumFractionDigits=i.length:o&&a?(t.minimumFractionDigits=o.length,t.maximumFractionDigits=o.length+a.length):(t.minimumFractionDigits=n.length,t.maximumFractionDigits=n.length),""}));var o=i.options[0];"w"===o?t=(0,a().__assign)((0,a().__assign)({},t),{trailingZeroDisplay:"stripIfInteger"}):o&&(t=(0,a().__assign)((0,a().__assign)({},t),x(o)))}else if(E.test(i.stem))t=(0,a().__assign)((0,a().__assign)({},t),x(i.stem));else{var s=O(i.stem);s&&(t=(0,a().__assign)((0,a().__assign)({},t),s));var l=C(i.stem);l&&(t=(0,a().__assign)((0,a().__assign)({},t),l))}}return t}var D,A={"001":["H","h"],AC:["H","h","hb","hB"],AD:["H","hB"],AE:["h","hB","hb","H"],AF:["H","hb","hB","h"],AG:["h","hb","H","hB"],AI:["H","h","hb","hB"],AL:["h","H","hB"],AM:["H","hB"],AO:["H","hB"],AR:["H","h","hB","hb"],AS:["h","H"],AT:["H","hB"],AU:["h","hb","H","hB"],AW:["H","hB"],AX:["H"],AZ:["H","hB","h"],BA:["H","hB","h"],BB:["h","hb","H","hB"],BD:["h","hB","H"],BE:["H","hB"],BF:["H","hB"],BG:["H","hB","h"],BH:["h","hB","hb","H"],BI:["H","h"],BJ:["H","hB"],BL:["H","hB"],BM:["h","hb","H","hB"],BN:["hb","hB","h","H"],BO:["H","hB","h","hb"],BQ:["H"],BR:["H","hB"],BS:["h","hb","H","hB"],BT:["h","H"],BW:["H","h","hb","hB"],BY:["H","h"],BZ:["H","h","hb","hB"],CA:["h","hb","H","hB"],CC:["H","h","hb","hB"],CD:["hB","H"],CF:["H","h","hB"],CG:["H","hB"],CH:["H","hB","h"],CI:["H","hB"],CK:["H","h","hb","hB"],CL:["H","h","hB","hb"],CM:["H","h","hB"],CN:["H","hB","hb","h"],CO:["h","H","hB","hb"],CP:["H"],CR:["H","h","hB","hb"],CU:["H","h","hB","hb"],CV:["H","hB"],CW:["H","hB"],CX:["H","h","hb","hB"],CY:["h","H","hb","hB"],CZ:["H"],DE:["H","hB"],DG:["H","h","hb","hB"],DJ:["h","H"],DK:["H"],DM:["h","hb","H","hB"],DO:["h","H","hB","hb"],DZ:["h","hB","hb","H"],EA:["H","h","hB","hb"],EC:["H","hB","h","hb"],EE:["H","hB"],EG:["h","hB","hb","H"],EH:["h","hB","hb","H"],ER:["h","H"],ES:["H","hB","h","hb"],ET:["hB","hb","h","H"],FI:["H"],FJ:["h","hb","H","hB"],FK:["H","h","hb","hB"],FM:["h","hb","H","hB"],FO:["H","h"],FR:["H","hB"],GA:["H","hB"],GB:["H","h","hb","hB"],GD:["h","hb","H","hB"],GE:["H","hB","h"],GF:["H","hB"],GG:["H","h","hb","hB"],GH:["h","H"],GI:["H","h","hb","hB"],GL:["H","h"],GM:["h","hb","H","hB"],GN:["H","hB"],GP:["H","hB"],GQ:["H","hB","h","hb"],GR:["h","H","hb","hB"],GT:["H","h","hB","hb"],GU:["h","hb","H","hB"],GW:["H","hB"],GY:["h","hb","H","hB"],HK:["h","hB","hb","H"],HN:["H","h","hB","hb"],HR:["H","hB"],HU:["H","h"],IC:["H","h","hB","hb"],ID:["H"],IE:["H","h","hb","hB"],IL:["H","hB"],IM:["H","h","hb","hB"],IN:["h","H"],IO:["H","h","hb","hB"],IQ:["h","hB","hb","H"],IR:["hB","H"],IS:["H"],IT:["H","hB"],JE:["H","h","hb","hB"],JM:["h","hb","H","hB"],JO:["h","hB","hb","H"],JP:["H","K","h"],KE:["hB","hb","H","h"],KG:["H","h","hB","hb"],KH:["hB","h","H","hb"],KI:["h","hb","H","hB"],KM:["H","h","hB","hb"],KN:["h","hb","H","hB"],KP:["h","H","hB","hb"],KR:["h","H","hB","hb"],KW:["h","hB","hb","H"],KY:["h","hb","H","hB"],KZ:["H","hB"],LA:["H","hb","hB","h"],LB:["h","hB","hb","H"],LC:["h","hb","H","hB"],LI:["H","hB","h"],LK:["H","h","hB","hb"],LR:["h","hb","H","hB"],LS:["h","H"],LT:["H","h","hb","hB"],LU:["H","h","hB"],LV:["H","hB","hb","h"],LY:["h","hB","hb","H"],MA:["H","h","hB","hb"],MC:["H","hB"],MD:["H","hB"],ME:["H","hB","h"],MF:["H","hB"],MG:["H","h"],MH:["h","hb","H","hB"],MK:["H","h","hb","hB"],ML:["H"],MM:["hB","hb","H","h"],MN:["H","h","hb","hB"],MO:["h","hB","hb","H"],MP:["h","hb","H","hB"],MQ:["H","hB"],MR:["h","hB","hb","H"],MS:["H","h","hb","hB"],MT:["H","h"],MU:["H","h"],MV:["H","h"],MW:["h","hb","H","hB"],MX:["H","h","hB","hb"],MY:["hb","hB","h","H"],MZ:["H","hB"],NA:["h","H","hB","hb"],NC:["H","hB"],NE:["H"],NF:["H","h","hb","hB"],NG:["H","h","hb","hB"],NI:["H","h","hB","hb"],NL:["H","hB"],NO:["H","h"],NP:["H","h","hB"],NR:["H","h","hb","hB"],NU:["H","h","hb","hB"],NZ:["h","hb","H","hB"],OM:["h","hB","hb","H"],PA:["h","H","hB","hb"],PE:["H","hB","h","hb"],PF:["H","h","hB"],PG:["h","H"],PH:["h","hB","hb","H"],PK:["h","hB","H"],PL:["H","h"],PM:["H","hB"],PN:["H","h","hb","hB"],PR:["h","H","hB","hb"],PS:["h","hB","hb","H"],PT:["H","hB"],PW:["h","H"],PY:["H","h","hB","hb"],QA:["h","hB","hb","H"],RE:["H","hB"],RO:["H","hB"],RS:["H","hB","h"],RU:["H"],RW:["H","h"],SA:["h","hB","hb","H"],SB:["h","hb","H","hB"],SC:["H","h","hB"],SD:["h","hB","hb","H"],SE:["H"],SG:["h","hb","H","hB"],SH:["H","h","hb","hB"],SI:["H","hB"],SJ:["H"],SK:["H"],SL:["h","hb","H","hB"],SM:["H","h","hB"],SN:["H","h","hB"],SO:["h","H"],SR:["H","hB"],SS:["h","hb","H","hB"],ST:["H","hB"],SV:["H","h","hB","hb"],SX:["H","h","hb","hB"],SY:["h","hB","hb","H"],SZ:["h","hb","H","hB"],TA:["H","h","hb","hB"],TC:["h","hb","H","hB"],TD:["h","H","hB"],TF:["H","h","hB"],TG:["H","hB"],TH:["H","h"],TJ:["H","h"],TL:["H","hB","hb","h"],TM:["H","h"],TN:["h","hB","hb","H"],TO:["h","H"],TR:["H","hB"],TT:["h","hb","H","hB"],TW:["hB","hb","h","H"],TZ:["hB","hb","H","h"],UA:["H","hB","h"],UG:["hB","hb","H","h"],UM:["h","hb","H","hB"],US:["h","hb","H","hB"],UY:["H","h","hB","hb"],UZ:["H","hB","h"],VA:["H","h","hB"],VC:["h","hb","H","hB"],VE:["h","H","hB","hb"],VG:["h","hb","H","hB"],VI:["h","hb","H","hB"],VN:["H","h"],VU:["h","H"],WF:["H","hB"],WS:["h","H"],XK:["H","hB","h"],YE:["h","hB","hb","H"],YT:["H","hB"],ZA:["H","h","hb","hB"],ZM:["h","hb","H","hB"],ZW:["H","h"],"af-ZA":["H","h","hB","hb"],"ar-001":["h","hB","hb","H"],"ca-ES":["H","h","hB"],"en-001":["h","hb","H","hB"],"es-BO":["H","h","hB","hb"],"es-BR":["H","h","hB","hb"],"es-EC":["H","h","hB","hb"],"es-ES":["H","h","hB","hb"],"es-GQ":["H","h","hB","hb"],"es-PE":["H","h","hB","hb"],"fr-CA":["H","h","hB"],"gl-ES":["H","h","hB"],"gu-IN":["hB","hb","h","H"],"hi-IN":["hB","h","H"],"it-CH":["H","h","hB"],"it-IT":["H","h","hB"],"kn-IN":["hB","h","H"],"ml-IN":["hB","h","H"],"mr-IN":["hB","hb","h","H"],"pa-IN":["hB","hb","h","H"],"ta-IN":["hB","h","hb","H"],"te-IN":["hB","h","H"],"zu-ZA":["H","hB","hb","h"]};function P(e){var t=e.hourCycle;if(void 0===t&&e.hourCycles&&e.hourCycles.length&&(t=e.hourCycles[0]),t)switch(t){case"h24":return"k";case"h23":return"H";case"h12":return"h";case"h11":return"K";default:throw new Error("Invalid hourCycle")}var n,r=e.language;return"root"!==r&&(n=e.maximize().region),(A[n||""]||A[r||""]||A["".concat(r,"-001")]||A["001"])[0]}var L=new RegExp("^".concat(y.source,"*")),M=new RegExp("".concat(y.source,"*$"));function R(e,t){return{start:e,end:t}}var F=!!String.prototype.startsWith&&"_a".startsWith("a",1),U=!!String.fromCodePoint,j=!!Object.fromEntries,B=!!String.prototype.codePointAt,z=!!String.prototype.trimStart,H=!!String.prototype.trimEnd,V=!!Number.isSafeInteger?Number.isSafeInteger:function(e){return"number"==typeof e&&isFinite(e)&&Math.floor(e)===e&&Math.abs(e)<=9007199254740991},$=!0;try{$="a"===(null===(D=Q("([^\\p{White_Space}\\p{Pattern_Syntax}]*)","yu").exec("a"))||void 0===D?void 0:D[0])}catch(ae){$=!1}var G,q=F?function(e,t,n){return e.startsWith(t,n)}:function(e,t,n){return e.slice(n,n+t.length)===t},W=U?String.fromCodePoint:function(){for(var e=[],t=0;t<arguments.length;t++)e[t]=arguments[t];for(var n,r="",i=e.length,o=0;i>o;){if((n=e[o++])>1114111)throw RangeError(n+" is not a valid code point");r+=n<65536?String.fromCharCode(n):String.fromCharCode(55296+((n-=65536)>>10),n%1024+56320)}return r},K=j?Object.fromEntries:function(e){for(var t={},n=0,r=e;n<r.length;n++){var i=r[n],o=i[0],a=i[1];t[o]=a}return t},Z=B?function(e,t){return e.codePointAt(t)}:function(e,t){var n=e.length;if(!(t<0||t>=n)){var r,i=e.charCodeAt(t);return i<55296||i>56319||t+1===n||(r=e.charCodeAt(t+1))<56320||r>57343?i:r-56320+(i-55296<<10)+65536}},J=z?function(e){return e.trimStart()}:function(e){return e.replace(L,"")},Y=H?function(e){return e.trimEnd()}:function(e){return e.replace(M,"")};function Q(e,t){return new RegExp(e,t)}if($){var X=Q("([^\\p{White_Space}\\p{Pattern_Syntax}]*)","yu");G=function(e,t){var n;return X.lastIndex=t,null!==(n=X.exec(e)[1])&&void 0!==n?n:""}}else G=function(e,t){for(var n=[];;){var r=Z(e,t);if(void 0===r||ne(r)||re(r))break;n.push(r),t+=r>=65536?2:1}return W.apply(void 0,n)};var ee=function(){function e(e,t){void 0===t&&(t={}),this.message=e,this.position={offset:0,line:1,column:1},this.ignoreTag=!!t.ignoreTag,this.locale=t.locale,this.requiresOtherClause=!!t.requiresOtherClause,this.shouldParseSkeletons=!!t.shouldParseSkeletons}return e.prototype.parse=function(){if(0!==this.offset())throw Error("parser can only be used once");return this.parseMessage(0,"",!1)},e.prototype.parseMessage=function(e,t,n){for(var o=[];!this.isEOF();){var a=this.char();if(123===a){if((s=this.parseArgument(e,n)).err)return s;o.push(s.val)}else{if(125===a&&e>0)break;if(35!==a||"plural"!==t&&"selectordinal"!==t){if(60===a&&!this.ignoreTag&&47===this.peek()){if(n)break;return this.error(r.UNMATCHED_CLOSING_TAG,R(this.clonePosition(),this.clonePosition()))}if(60===a&&!this.ignoreTag&&te(this.peek()||0)){if((s=this.parseTag(e,t)).err)return s;o.push(s.val)}else{var s;if((s=this.parseLiteral(e,t)).err)return s;o.push(s.val)}}else{var l=this.clonePosition();this.bump(),o.push({type:i.pound,location:R(l,this.clonePosition())})}}}return{val:o,err:null}},e.prototype.parseTag=function(e,t){var n=this.clonePosition();this.bump();var o=this.parseTagName();if(this.bumpSpace(),this.bumpIf("/>"))return{val:{type:i.literal,value:"<".concat(o,"/>"),location:R(n,this.clonePosition())},err:null};if(this.bumpIf(">")){var a=this.parseMessage(e+1,t,!0);if(a.err)return a;var s=a.val,l=this.clonePosition();if(this.bumpIf("</")){if(this.isEOF()||!te(this.char()))return this.error(r.INVALID_TAG,R(l,this.clonePosition()));var u=this.clonePosition();return o!==this.parseTagName()?this.error(r.UNMATCHED_CLOSING_TAG,R(u,this.clonePosition())):(this.bumpSpace(),this.bumpIf(">")?{val:{type:i.tag,value:o,children:s,location:R(n,this.clonePosition())},err:null}:this.error(r.INVALID_TAG,R(l,this.clonePosition())))}return this.error(r.UNCLOSED_TAG,R(n,this.clonePosition()))}return this.error(r.INVALID_TAG,R(n,this.clonePosition()))},e.prototype.parseTagName=function(){var e,t=this.offset();for(this.bump();!this.isEOF()&&(45===(e=this.char())||46===e||e>=48&&e<=57||95===e||e>=97&&e<=122||e>=65&&e<=90||183==e||e>=192&&e<=214||e>=216&&e<=246||e>=248&&e<=893||e>=895&&e<=8191||e>=8204&&e<=8205||e>=8255&&e<=8256||e>=8304&&e<=8591||e>=11264&&e<=12271||e>=12289&&e<=55295||e>=63744&&e<=64975||e>=65008&&e<=65533||e>=65536&&e<=983039);)this.bump();return this.message.slice(t,this.offset())},e.prototype.parseLiteral=function(e,t){for(var n=this.clonePosition(),r="";;){var o=this.tryParseQuote(t);if(o)r+=o;else{var a=this.tryParseUnquoted(e,t);if(a)r+=a;else{var s=this.tryParseLeftAngleBracket();if(!s)break;r+=s}}}var l=R(n,this.clonePosition());return{val:{type:i.literal,value:r,location:l},err:null}},e.prototype.tryParseLeftAngleBracket=function(){return this.isEOF()||60!==this.char()||!this.ignoreTag&&(te(e=this.peek()||0)||47===e)?null:(this.bump(),"<");var e},e.prototype.tryParseQuote=function(e){if(this.isEOF()||39!==this.char())return null;switch(this.peek()){case 39:return this.bump(),this.bump(),"'";case 123:case 60:case 62:case 125:break;case 35:if("plural"===e||"selectordinal"===e)break;return null;default:return null}this.bump();var t=[this.char()];for(this.bump();!this.isEOF();){var n=this.char();if(39===n){if(39!==this.peek()){this.bump();break}t.push(39),this.bump()}else t.push(n);this.bump()}return W.apply(void 0,t)},e.prototype.tryParseUnquoted=function(e,t){if(this.isEOF())return null;var n=this.char();return 60===n||123===n||35===n&&("plural"===t||"selectordinal"===t)||125===n&&e>0?null:(this.bump(),W(n))},e.prototype.parseArgument=function(e,t){var n=this.clonePosition();if(this.bump(),this.bumpSpace(),this.isEOF())return this.error(r.EXPECT_ARGUMENT_CLOSING_BRACE,R(n,this.clonePosition()));if(125===this.char())return this.bump(),this.error(r.EMPTY_ARGUMENT,R(n,this.clonePosition()));var o=this.parseIdentifierIfPossible().value;if(!o)return this.error(r.MALFORMED_ARGUMENT,R(n,this.clonePosition()));if(this.bumpSpace(),this.isEOF())return this.error(r.EXPECT_ARGUMENT_CLOSING_BRACE,R(n,this.clonePosition()));switch(this.char()){case 125:return this.bump(),{val:{type:i.argument,value:o,location:R(n,this.clonePosition())},err:null};case 44:return this.bump(),this.bumpSpace(),this.isEOF()?this.error(r.EXPECT_ARGUMENT_CLOSING_BRACE,R(n,this.clonePosition())):this.parseArgumentOptions(e,t,o,n);default:return this.error(r.MALFORMED_ARGUMENT,R(n,this.clonePosition()))}},e.prototype.parseIdentifierIfPossible=function(){var e=this.clonePosition(),t=this.offset(),n=G(this.message,t),r=t+n.length;return this.bumpTo(r),{value:n,location:R(e,this.clonePosition())}},e.prototype.parseArgumentOptions=function(e,t,n,s){var l,u=this.clonePosition(),c=this.parseIdentifierIfPossible().value,f=this.clonePosition();switch(c){case"":return this.error(r.EXPECT_ARGUMENT_TYPE,R(u,f));case"number":case"date":case"time":this.bumpSpace();var d=null;if(this.bumpIf(",")){this.bumpSpace();var h=this.clonePosition();if((S=this.parseSimpleArgStyleIfPossible()).err)return S;if(0===(v=Y(S.val)).length)return this.error(r.EXPECT_ARGUMENT_STYLE,R(this.clonePosition(),this.clonePosition()));d={style:v,styleLocation:R(h,this.clonePosition())}}if((E=this.tryParseArgumentClose(s)).err)return E;var p=R(s,this.clonePosition());if(d&&q(null==d?void 0:d.style,"::",0)){var m=J(d.style.slice(2));if("number"===c)return(S=this.parseNumberSkeletonFromString(m,d.styleLocation)).err?S:{val:{type:i.number,value:n,location:p,style:S.val},err:null};if(0===m.length)return this.error(r.EXPECT_DATE_TIME_SKELETON,p);var g=m;this.locale&&(g=function(e,t){for(var n="",r=0;r<e.length;r++){var i=e.charAt(r);if("j"===i){for(var o=0;r+1<e.length&&e.charAt(r+1)===i;)o++,r++;var a=1+(1&o),s=o<2?1:3+(o>>1),l=P(t);for("H"!=l&&"k"!=l||(s=0);s-- >0;)n+="a";for(;a-- >0;)n=l+n}else n+="J"===i?"H":i}return n}(m,this.locale));var v={type:o.dateTime,pattern:g,location:d.styleLocation,parsedOptions:this.shouldParseSkeletons?_(g):{}};return{val:{type:"date"===c?i.date:i.time,value:n,location:p,style:v},err:null}}return{val:{type:"number"===c?i.number:"date"===c?i.date:i.time,value:n,location:p,style:null!==(l=null==d?void 0:d.style)&&void 0!==l?l:null},err:null};case"plural":case"selectordinal":case"select":var y=this.clonePosition();if(this.bumpSpace(),!this.bumpIf(","))return this.error(r.EXPECT_SELECT_ARGUMENT_OPTIONS,R(y,(0,a().__assign)({},y)));this.bumpSpace();var b=this.parseIdentifierIfPossible(),w=0;if("select"!==c&&"offset"===b.value){if(!this.bumpIf(":"))return this.error(r.EXPECT_PLURAL_ARGUMENT_OFFSET_VALUE,R(this.clonePosition(),this.clonePosition()));var S;if(this.bumpSpace(),(S=this.tryParseDecimalInteger(r.EXPECT_PLURAL_ARGUMENT_OFFSET_VALUE,r.INVALID_PLURAL_ARGUMENT_OFFSET_VALUE)).err)return S;this.bumpSpace(),b=this.parseIdentifierIfPossible(),w=S.val}var E,k=this.tryParsePluralOrSelectOptions(e,c,t,b);if(k.err)return k;if((E=this.tryParseArgumentClose(s)).err)return E;var T=R(s,this.clonePosition());return"select"===c?{val:{type:i.select,value:n,options:K(k.val),location:T},err:null}:{val:{type:i.plural,value:n,options:K(k.val),offset:w,pluralType:"plural"===c?"cardinal":"ordinal",location:T},err:null};default:return this.error(r.INVALID_ARGUMENT_TYPE,R(u,f))}},e.prototype.tryParseArgumentClose=function(e){return this.isEOF()||125!==this.char()?this.error(r.EXPECT_ARGUMENT_CLOSING_BRACE,R(e,this.clonePosition())):(this.bump(),{val:!0,err:null})},e.prototype.parseSimpleArgStyleIfPossible=function(){for(var e=0,t=this.clonePosition();!this.isEOF();){switch(this.char()){case 39:this.bump();var n=this.clonePosition();if(!this.bumpUntil("'"))return this.error(r.UNCLOSED_QUOTE_IN_ARGUMENT_STYLE,R(n,this.clonePosition()));this.bump();break;case 123:e+=1,this.bump();break;case 125:if(!(e>0))return{val:this.message.slice(t.offset,this.offset()),err:null};e-=1;break;default:this.bump()}}return{val:this.message.slice(t.offset,this.offset()),err:null}},e.prototype.parseNumberSkeletonFromString=function(e,t){var n=[];try{n=function(e){if(0===e.length)throw new Error("Number skeleton cannot be empty");for(var t=[],n=0,r=e.split(w).filter((function(e){return e.length>0}));n<r.length;n++){var i=r[n].split("/");if(0===i.length)throw new Error("Invalid number skeleton");for(var o=i[0],a=i.slice(1),s=0,l=a;s<l.length;s++)if(0===l[s].length)throw new Error("Invalid number skeleton");t.push({stem:o,options:a})}return t}(e)}catch(i){return this.error(r.INVALID_NUMBER_SKELETON,t)}return{val:{type:o.number,tokens:n,location:t,parsedOptions:this.shouldParseSkeletons?I(n):{}},err:null}},e.prototype.tryParsePluralOrSelectOptions=function(e,t,n,i){for(var o,a=!1,s=[],l=new Set,u=i.value,c=i.location;;){if(0===u.length){var f=this.clonePosition();if("select"===t||!this.bumpIf("="))break;var d=this.tryParseDecimalInteger(r.EXPECT_PLURAL_ARGUMENT_SELECTOR,r.INVALID_PLURAL_ARGUMENT_SELECTOR);if(d.err)return d;c=R(f,this.clonePosition()),u=this.message.slice(f.offset,this.offset())}if(l.has(u))return this.error("select"===t?r.DUPLICATE_SELECT_ARGUMENT_SELECTOR:r.DUPLICATE_PLURAL_ARGUMENT_SELECTOR,c);"other"===u&&(a=!0),this.bumpSpace();var h=this.clonePosition();if(!this.bumpIf("{"))return this.error("select"===t?r.EXPECT_SELECT_ARGUMENT_SELECTOR_FRAGMENT:r.EXPECT_PLURAL_ARGUMENT_SELECTOR_FRAGMENT,R(this.clonePosition(),this.clonePosition()));var p=this.parseMessage(e+1,t,n);if(p.err)return p;var m=this.tryParseArgumentClose(h);if(m.err)return m;s.push([u,{value:p.val,location:R(h,this.clonePosition())}]),l.add(u),this.bumpSpace(),u=(o=this.parseIdentifierIfPossible()).value,c=o.location}return 0===s.length?this.error("select"===t?r.EXPECT_SELECT_ARGUMENT_SELECTOR:r.EXPECT_PLURAL_ARGUMENT_SELECTOR,R(this.clonePosition(),this.clonePosition())):this.requiresOtherClause&&!a?this.error(r.MISSING_OTHER_CLAUSE,R(this.clonePosition(),this.clonePosition())):{val:s,err:null}},e.prototype.tryParseDecimalInteger=function(e,t){var n=1,r=this.clonePosition();this.bumpIf("+")||this.bumpIf("-")&&(n=-1);for(var i=!1,o=0;!this.isEOF();){var a=this.char();if(!(a>=48&&a<=57))break;i=!0,o=10*o+(a-48),this.bump()}var s=R(r,this.clonePosition());return i?V(o*=n)?{val:o,err:null}:this.error(t,s):this.error(e,s)},e.prototype.offset=function(){return this.position.offset},e.prototype.isEOF=function(){return this.offset()===this.message.length},e.prototype.clonePosition=function(){return{offset:this.position.offset,line:this.position.line,column:this.position.column}},e.prototype.char=function(){var e=this.position.offset;if(e>=this.message.length)throw Error("out of bound");var t=Z(this.message,e);if(void 0===t)throw Error("Offset ".concat(e," is at invalid UTF-16 code unit boundary"));return t},e.prototype.error=function(e,t){return{val:null,err:{kind:e,message:this.message,location:t}}},e.prototype.bump=function(){if(!this.isEOF()){var e=this.char();10===e?(this.position.line+=1,this.position.column=1,this.position.offset+=1):(this.position.column+=1,this.position.offset+=e<65536?1:2)}},e.prototype.bumpIf=function(e){if(q(this.message,e,this.offset())){for(var t=0;t<e.length;t++)this.bump();return!0}return!1},e.prototype.bumpUntil=function(e){var t=this.offset(),n=this.message.indexOf(e,t);return n>=0?(this.bumpTo(n),!0):(this.bumpTo(this.message.length),!1)},e.prototype.bumpTo=function(e){if(this.offset()>e)throw Error("targetOffset ".concat(e," must be greater than or equal to the current offset ").concat(this.offset()));for(e=Math.min(e,this.message.length);;){var t=this.offset();if(t===e)break;if(t>e)throw Error("targetOffset ".concat(e," is at invalid UTF-16 code unit boundary"));if(this.bump(),this.isEOF())break}},e.prototype.bumpSpace=function(){for(;!this.isEOF()&&ne(this.char());)this.bump()},e.prototype.peek=function(){if(this.isEOF())return null;var e=this.char(),t=this.offset(),n=this.message.charCodeAt(t+(e>=65536?2:1));return null!=n?n:null},e}();function te(e){return e>=97&&e<=122||e>=65&&e<=90}function ne(e){return e>=9&&e<=13||32===e||133===e||e>=8206&&e<=8207||8232===e||8233===e}function re(e){return e>=33&&e<=35||36===e||e>=37&&e<=39||40===e||41===e||42===e||43===e||44===e||45===e||e>=46&&e<=47||e>=58&&e<=59||e>=60&&e<=62||e>=63&&e<=64||91===e||92===e||93===e||94===e||96===e||123===e||124===e||125===e||126===e||161===e||e>=162&&e<=165||166===e||167===e||169===e||171===e||172===e||174===e||176===e||177===e||182===e||187===e||191===e||215===e||247===e||e>=8208&&e<=8213||e>=8214&&e<=8215||8216===e||8217===e||8218===e||e>=8219&&e<=8220||8221===e||8222===e||8223===e||e>=8224&&e<=8231||e>=8240&&e<=8248||8249===e||8250===e||e>=8251&&e<=8254||e>=8257&&e<=8259||8260===e||8261===e||8262===e||e>=8263&&e<=8273||8274===e||8275===e||e>=8277&&e<=8286||e>=8592&&e<=8596||e>=8597&&e<=8601||e>=8602&&e<=8603||e>=8604&&e<=8607||8608===e||e>=8609&&e<=8610||8611===e||e>=8612&&e<=8613||8614===e||e>=8615&&e<=8621||8622===e||e>=8623&&e<=8653||e>=8654&&e<=8655||e>=8656&&e<=8657||8658===e||8659===e||8660===e||e>=8661&&e<=8691||e>=8692&&e<=8959||e>=8960&&e<=8967||8968===e||8969===e||8970===e||8971===e||e>=8972&&e<=8991||e>=8992&&e<=8993||e>=8994&&e<=9e3||9001===e||9002===e||e>=9003&&e<=9083||9084===e||e>=9085&&e<=9114||e>=9115&&e<=9139||e>=9140&&e<=9179||e>=9180&&e<=9185||e>=9186&&e<=9254||e>=9255&&e<=9279||e>=9280&&e<=9290||e>=9291&&e<=9311||e>=9472&&e<=9654||9655===e||e>=9656&&e<=9664||9665===e||e>=9666&&e<=9719||e>=9720&&e<=9727||e>=9728&&e<=9838||9839===e||e>=9840&&e<=10087||10088===e||10089===e||10090===e||10091===e||10092===e||10093===e||10094===e||10095===e||10096===e||10097===e||10098===e||10099===e||10100===e||10101===e||e>=10132&&e<=10175||e>=10176&&e<=10180||10181===e||10182===e||e>=10183&&e<=10213||10214===e||10215===e||10216===e||10217===e||10218===e||10219===e||10220===e||10221===e||10222===e||10223===e||e>=10224&&e<=10239||e>=10240&&e<=10495||e>=10496&&e<=10626||10627===e||10628===e||10629===e||10630===e||10631===e||10632===e||10633===e||10634===e||10635===e||10636===e||10637===e||10638===e||10639===e||10640===e||10641===e||10642===e||10643===e||10644===e||10645===e||10646===e||10647===e||10648===e||e>=10649&&e<=10711||10712===e||10713===e||10714===e||10715===e||e>=10716&&e<=10747||10748===e||10749===e||e>=10750&&e<=11007||e>=11008&&e<=11055||e>=11056&&e<=11076||e>=11077&&e<=11078||e>=11079&&e<=11084||e>=11085&&e<=11123||e>=11124&&e<=11125||e>=11126&&e<=11157||11158===e||e>=11159&&e<=11263||e>=11776&&e<=11777||11778===e||11779===e||11780===e||11781===e||e>=11782&&e<=11784||11785===e||11786===e||11787===e||11788===e||11789===e||e>=11790&&e<=11798||11799===e||e>=11800&&e<=11801||11802===e||11803===e||11804===e||11805===e||e>=11806&&e<=11807||11808===e||11809===e||11810===e||11811===e||11812===e||11813===e||11814===e||11815===e||11816===e||11817===e||e>=11818&&e<=11822||11823===e||e>=11824&&e<=11833||e>=11834&&e<=11835||e>=11836&&e<=11839||11840===e||11841===e||11842===e||e>=11843&&e<=11855||e>=11856&&e<=11857||11858===e||e>=11859&&e<=11903||e>=12289&&e<=12291||12296===e||12297===e||12298===e||12299===e||12300===e||12301===e||12302===e||12303===e||12304===e||12305===e||e>=12306&&e<=12307||12308===e||12309===e||12310===e||12311===e||12312===e||12313===e||12314===e||12315===e||12316===e||12317===e||e>=12318&&e<=12319||12320===e||12336===e||64830===e||64831===e||e>=65093&&e<=65094}function ie(e){e.forEach((function(e){if(delete e.location,d(e)||h(e))for(var t in e.options)delete e.options[t].location,ie(e.options[t].value);else u(e)&&g(e.style)||(c(e)||f(e))&&v(e.style)?delete e.style.location:m(e)&&ie(e.children)}))}function oe(e,t){void 0===t&&(t={}),t=(0,a().__assign)({shouldParseSkeletons:!0,requiresOtherClause:!0},t);var n=new ee(e,t).parse();if(n.err){var i=SyntaxError(r[n.err.kind]);throw i.location=n.err.location,i.originalMessage=n.err.message,i}return(null==t?void 0:t.captureLocation)||ie(n.val),n.val}},894483:(e,t,n)=>{"use strict";var r,i,o,a,s=n(444576),l=n(789429),u=n(301548),c=s.structuredClone,f=s.ArrayBuffer,d=s.MessageChannel,h=!1;if(u)h=function(e){c(e,{transfer:[e]})};else if(f)try{d||(r=l("worker_threads"))&&(d=r.MessageChannel),d&&(i=new d,o=new f(2),a=function(e){i.port1.postMessage(null,[e])},2===o.byteLength&&(a(o),0===o.byteLength&&(h=a)))}catch(p){}e.exports=h},894681:(e,t,n)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),t._getStorageKey=t._getUserStorageKey=void 0;const r=()=>n(483918);function i(e,t,n){var i;if(n)return n(e,t);const o=t&&t.customIDs?t.customIDs:{},a=[`uid:${null!==(i=null==t?void 0:t.userID)&&void 0!==i?i:""}`,`cids:${Object.keys(o).sort(((e,t)=>e.localeCompare(t))).map((e=>`${e}-${o[e]}`)).join(",")}`,`k:${e}`];return(0,r()._DJB2)(a.join("|"))}t._getUserStorageKey=i,t._getStorageKey=function(e,t,n){return t?i(e,t,n):(0,r()._DJB2)(`k:${e}`)}},895747:(e,t)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),t.SDKFlags=void 0;const n={};t.SDKFlags={setFlags:(e,t)=>{n[e]=t},get:(e,t)=>{var r,i;return null!==(i=null===(r=n[e])||void 0===r?void 0:r[t])&&void 0!==i&&i}}},903238:(e,t,n)=>{"use strict";var r=n(444576),i=n(227476),o=n(767394),a=r.ArrayBuffer,s=a&&a.prototype,l=s&&i(s.slice);e.exports=function(e){if(0!==o(e))return!1;if(!l)return!1;try{return l(e,0,0),!1}catch(t){return!0}}},905025:(e,t)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0})},907686:function(e,t,n){"use strict";var r=this&&this.__awaiter||function(e,t,n,r){return new(n||(n=Promise))((function(i,o){function a(e){try{l(r.next(e))}catch(t){o(t)}}function s(e){try{l(r.throw(e))}catch(t){o(t)}}function l(e){var t;e.done?i(e.value):(t=e.value,t instanceof n?t:new n((function(e){e(t)}))).then(a,s)}l((r=r.apply(e,t||[])).next())}))};Object.defineProperty(t,"__esModule",{value:!0});const i=()=>n(636978),o=()=>n(79999);class a extends i().StatsigClientBase{static instance(e){const t=(0,i()._getStatsigGlobal)().instance(e);return t instanceof a?t:(i().Log.warn((0,i()._isServerEnv)()?"StatsigClient.instance is not supported in server environments":"Unable to find StatsigClient instance"),new a(null!=e?e:"",{}))}constructor(e,t,r=null){var o,a;i().SDKType._setClientType(e,"javascript-client");const s=new(n(782944).default)(r,(e=>{this.$emt(e)}));super(e,null!==(o=null==r?void 0:r.dataAdapter)&&void 0!==o?o:new(n(842451).StatsigEvaluationsDataAdapter),s,r),this.getFeatureGate=this._memoize(i().MemoPrefix._gate,this._getFeatureGateImpl.bind(this)),this.getDynamicConfig=this._memoize(i().MemoPrefix._dynamicConfig,this._getDynamicConfigImpl.bind(this)),this.getExperiment=this._memoize(i().MemoPrefix._experiment,this._getExperimentImpl.bind(this)),this.getLayer=this._memoize(i().MemoPrefix._layer,this._getLayerImpl.bind(this)),this.getParameterStore=this._memoize(i().MemoPrefix._paramStore,this._getParameterStoreImpl.bind(this)),this._store=new(n(87677).default)(e),this._network=s,this._user=this._configureUser(t,r);const l=null!==(a=null==r?void 0:r.plugins)&&void 0!==a?a:[];for(const n of l)n.bind(this)}initializeSync(e){var t;return"Uninitialized"!==this.loadingStatus?(0,i().createUpdateDetails)(!0,this._store.getSource(),-1,null,null,["MultipleInitializations",...null!==(t=this._store.getWarnings())&&void 0!==t?t:[]]):(this._logger.start(),this.updateUserSync(this._user,e))}initializeAsync(e){return r(this,void 0,void 0,(function*(){return this._initializePromise||(this._initializePromise=this._initializeAsyncImpl(e)),this._initializePromise}))}updateUserSync(e,t){var n;const r=performance.now(),o=[...null!==(n=this._store.getWarnings())&&void 0!==n?n:[]];this._resetForUser(e);const a=this.dataAdapter.getDataSync(this._user);null==a&&o.push("NoCachedValues"),this._store.setValues(a,this._user),this._finalizeUpdate(a);const s=null==t?void 0:t.disableBackgroundCacheRefresh;return!0===s||null==s&&"Bootstrap"===(null==a?void 0:a.source)||this._runPostUpdate(null!=a?a:null,this._user),(0,i().createUpdateDetails)(!0,this._store.getSource(),performance.now()-r,this._errorBoundary.getLastSeenErrorAndReset(),this._network.getLastUsedInitUrlAndReset(),o)}updateUserAsync(e,t){return r(this,void 0,void 0,(function*(){this._resetForUser(e);const n=this._user;i().Diagnostics._markInitOverallStart(this._sdkKey);let r=this.dataAdapter.getDataSync(n);if(this._store.setValues(r,this._user),this._setStatus("Loading",r),r=yield this.dataAdapter.getDataAsync(r,n,t),n!==this._user)return(0,i().createUpdateDetails)(!1,this._store.getSource(),-1,new Error("User changed during update"),this._network.getLastUsedInitUrlAndReset());let o=!1;null!=r&&(i().Diagnostics._markInitProcessStart(this._sdkKey),o=this._store.setValues(r,this._user),i().Diagnostics._markInitProcessEnd(this._sdkKey,{success:o})),this._finalizeUpdate(r),o||(this._errorBoundary.attachErrorIfNoneExists(i().UPDATE_DETAIL_ERROR_MESSAGES.NO_NETWORK_DATA),this.$emt({name:"initialization_failure"})),i().Diagnostics._markInitOverallEnd(this._sdkKey,o,this._store.getCurrentSourceDetails());const a=i().Diagnostics._enqueueDiagnosticsEvent(this._user,this._logger,this._sdkKey,this._options);return(0,i().createUpdateDetails)(o,this._store.getSource(),a,this._errorBoundary.getLastSeenErrorAndReset(),this._network.getLastUsedInitUrlAndReset(),this._store.getWarnings())}))}getContext(){return{sdkKey:this._sdkKey,options:this._options,values:this._store.getValues(),user:JSON.parse(JSON.stringify(this._user)),errorBoundary:this._errorBoundary,session:i().StatsigSession.get(this._sdkKey),stableID:i().StableID.get(this._sdkKey)}}checkGate(e,t){return this.getFeatureGate(e,t).value}logEvent(e,t,n){const r="string"==typeof e?{eventName:e,value:t,metadata:n}:e;this.$emt({name:"log_event_called",event:r}),this._logger.enqueue(Object.assign(Object.assign({},r),{user:this._user,time:Date.now()}))}_primeReadyRipcord(){this.$on("error",(()=>{"Loading"===this.loadingStatus&&this._finalizeUpdate(null)}))}_initializeAsyncImpl(e){return r(this,void 0,void 0,(function*(){return i().Storage.isReady()||(yield i().Storage.isReadyResolver()),this._logger.start(),this.updateUserAsync(this._user,e)}))}_finalizeUpdate(e){this._store.finalize(),this._setStatus("Ready",e)}_runPostUpdate(e,t){this.dataAdapter.getDataAsync(e,t,{priority:"low"}).catch((e=>{i().Log.error("An error occurred after update.",e)}))}_resetForUser(e){this._logger.reset(),this._store.reset(),this._user=this._configureUser(e,this._options)}_configureUser(e,t){var n;const r=(0,i()._normalizeUser)(e,t),o=null===(n=r.customIDs)||void 0===n?void 0:n.stableID;return o&&i().StableID.setOverride(o,this._sdkKey),r}_getFeatureGateImpl(e,t){var n,r;const{result:o,details:a}=this._store.getGate(e),s=(0,i()._makeFeatureGate)(e,a,o),l=null===(r=null===(n=this.overrideAdapter)||void 0===n?void 0:n.getGateOverride)||void 0===r?void 0:r.call(n,s,this._user,t),u=null!=l?l:s;return this._enqueueExposure(e,(0,i()._createGateExposure)(this._user,u,this._store.getExposureMapping()),t),this.$emt({name:"gate_evaluation",gate:u}),u}_getDynamicConfigImpl(e,t){var n,r;const{result:o,details:a}=this._store.getConfig(e),s=(0,i()._makeDynamicConfig)(e,a,o),l=null===(r=null===(n=this.overrideAdapter)||void 0===n?void 0:n.getDynamicConfigOverride)||void 0===r?void 0:r.call(n,s,this._user,t),u=null!=l?l:s;return this._enqueueExposure(e,(0,i()._createConfigExposure)(this._user,u,this._store.getExposureMapping()),t),this.$emt({name:"dynamic_config_evaluation",dynamicConfig:u}),u}_getExperimentImpl(e,t){var n,r,o,a;const{result:s,details:l}=this._store.getConfig(e),u=(0,i()._makeExperiment)(e,l,s);null!=u.__evaluation&&(u.__evaluation.secondary_exposures=(0,i()._mapExposures)(null!==(r=null===(n=u.__evaluation)||void 0===n?void 0:n.secondary_exposures)&&void 0!==r?r:[],this._store.getExposureMapping()));const c=null===(a=null===(o=this.overrideAdapter)||void 0===o?void 0:o.getExperimentOverride)||void 0===a?void 0:a.call(o,u,this._user,t),f=null!=c?c:u;return this._enqueueExposure(e,(0,i()._createConfigExposure)(this._user,f,this._store.getExposureMapping()),t),this.$emt({name:"experiment_evaluation",experiment:f}),f}_getLayerImpl(e,t){var n,r,o;const{result:a,details:s}=this._store.getLayer(e),l=(0,i()._makeLayer)(e,s,a),u=null===(r=null===(n=this.overrideAdapter)||void 0===n?void 0:n.getLayerOverride)||void 0===r?void 0:r.call(n,l,this._user,t);(null==t?void 0:t.disableExposureLog)&&this._logger.incrementNonExposureCount(e);const c=(0,i()._mergeOverride)(l,u,null!==(o=null==u?void 0:u.__value)&&void 0!==o?o:l.__value,(n=>{(null==t?void 0:t.disableExposureLog)||this._enqueueExposure(e,(0,i()._createLayerParameterExposure)(this._user,c,n,this._store.getExposureMapping()),t)}));return this.$emt({name:"layer_evaluation",layer:c}),c}_getParameterStoreImpl(e,t){var n,r;const{result:i,details:a}=this._store.getParamStore(e);this._logger.incrementNonExposureCount(e);const s={name:e,details:a,__configuration:i,get:(0,o()._makeParamStoreGetter)(this,i,t)},l=null===(r=null===(n=this.overrideAdapter)||void 0===n?void 0:n.getParamStoreOverride)||void 0===r?void 0:r.call(n,s,t);return null!=l&&(s.__configuration=l.config,s.details=l.details,s.get=(0,o()._makeParamStoreGetter)(this,l.config,t)),s}}t.default=a},908128:(e,t,n)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),t.OnDeviceEvalAdapter=void 0;const r=()=>n(636978),i=()=>n(982026);t.OnDeviceEvalAdapter=class{constructor(e){this._store=new(i().SpecStore),this._evaluator=new(i().Evaluator)(this._store),null!=e&&this.setData(e)}setData(e){const t=(0,r()._makeDataAdapterResult)("Bootstrap",e,null);this._store.setValuesFromDataAdapter(t)}getGateOverride(e,t,n){return this._evaluate(e,t,this._evaluator.evaluateGate.bind(this._evaluator),r()._makeFeatureGate)}getDynamicConfigOverride(e,t,n){return this._evaluate(e,t,this._evaluator.evaluateConfig.bind(this._evaluator),r()._makeDynamicConfig)}getExperimentOverride(e,t,n){return this._evaluate(e,t,this._evaluator.evaluateConfig.bind(this._evaluator),r()._makeExperiment)}getLayerOverride(e,t,n){return this._evaluate(e,t,this._evaluator.evaluateLayer.bind(this._evaluator),r()._makeLayer)}getParamStoreOverride(e,t){if(!this._shouldTryOnDeviceEval(e.details))return null;const{config:n,details:r}=this._evaluator.getParamStoreConfig(e.name);return r.reason="[OnDevice]"+r.reason,{config:null!=n?n:{},details:r}}_evaluate(e,t,n,r){if(!this._shouldTryOnDeviceEval(e.details))return null;const i=e.name,{evaluation:o,details:a}=n(i,t);return a.reason="[OnDevice]"+a.reason,r(i,a,o)}_shouldTryOnDeviceEval(e){const t=this._store.getValues();return null!=t&&(!e.lcut||t.time>e.lcut)}}},920194:(e,t,n)=>{"use strict";e.exports=n(498731)},950014:(e,t,n)=>{e.exports=function(e,t){return e&&e.length?n(855765)(e,n(315389)(t,2)):[]}},952224:e=>{"use strict";e.exports=function(e){return String(e).replace(n,r).replace(t,encodeURI)};var t=/(?:[^\x21\x25\x26-\x3B\x3D\x3F-\x5B\x5D\x5F\x61-\x7A\x7E]|%(?:[^0-9A-Fa-f]|[0-9A-Fa-f][^0-9A-Fa-f]|$))+/g,n=/(^|[^\uD800-\uDBFF])[\uDC00-\uDFFF]|[\uD800-\uDBFF]([^\uDC00-\uDFFF]|$)/g,r="$1�$2"},953705:(e,t,n)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),t._typedJsonParse=void 0;t._typedJsonParse=function(e,t,r){try{const n=JSON.parse(e);if(n&&"object"==typeof n&&t in n)return n}catch(i){}return n(668024).Log.error(`Failed to parse ${r}`),null}},955002:e=>{"use strict";e.exports={IndexSizeError:{s:"INDEX_SIZE_ERR",c:1,m:1},DOMStringSizeError:{s:"DOMSTRING_SIZE_ERR",c:2,m:0},HierarchyRequestError:{s:"HIERARCHY_REQUEST_ERR",c:3,m:1},WrongDocumentError:{s:"WRONG_DOCUMENT_ERR",c:4,m:1},InvalidCharacterError:{s:"INVALID_CHARACTER_ERR",c:5,m:1},NoDataAllowedError:{s:"NO_DATA_ALLOWED_ERR",c:6,m:0},NoModificationAllowedError:{s:"NO_MODIFICATION_ALLOWED_ERR",c:7,m:1},NotFoundError:{s:"NOT_FOUND_ERR",c:8,m:1},NotSupportedError:{s:"NOT_SUPPORTED_ERR",c:9,m:1},InUseAttributeError:{s:"INUSE_ATTRIBUTE_ERR",c:10,m:1},InvalidStateError:{s:"INVALID_STATE_ERR",c:11,m:1},SyntaxError:{s:"SYNTAX_ERR",c:12,m:1},InvalidModificationError:{s:"INVALID_MODIFICATION_ERR",c:13,m:1},NamespaceError:{s:"NAMESPACE_ERR",c:14,m:1},InvalidAccessError:{s:"INVALID_ACCESS_ERR",c:15,m:1},ValidationError:{s:"VALIDATION_ERR",c:16,m:0},TypeMismatchError:{s:"TYPE_MISMATCH_ERR",c:17,m:1},SecurityError:{s:"SECURITY_ERR",c:18,m:1},NetworkError:{s:"NETWORK_ERR",c:19,m:1},AbortError:{s:"ABORT_ERR",c:20,m:1},URLMismatchError:{s:"URL_MISMATCH_ERR",c:21,m:1},QuotaExceededError:{s:"QUOTA_EXCEEDED_ERR",c:22,m:1},TimeoutError:{s:"TIMEOUT_ERR",c:23,m:1},InvalidNodeTypeError:{s:"INVALID_NODE_TYPE_ERR",c:24,m:1},DataCloneError:{s:"DATA_CLONE_ERR",c:25,m:1}}},957696:(e,t,n)=>{"use strict";var r=n(991291),i=n(218014),o=RangeError;e.exports=function(e){if(void 0===e)return 0;var t=r(e),n=i(t);if(t!==n)throw new o("Wrong length or index");return n}},958229:(e,t,n)=>{"use strict";var r=n(899590),i=RangeError;e.exports=function(e,t){var n=r(e);if(n%t)throw new i("Wrong offset");return n}},959106:e=>{"use strict";var t=function(e){switch(typeof e){case"string":return e;case"boolean":return e?"true":"false";case"number":return isFinite(e)?e:"";default:return""}};e.exports=function(e,n,r,i){return n=n||"&",r=r||"=",null===e&&(e=void 0),"object"==typeof e?Object.keys(e).map((function(i){var o=encodeURIComponent(t(i))+r;return Array.isArray(e[i])?e[i].map((function(e){return o+encodeURIComponent(t(e))})).join(n):o+encodeURIComponent(t(e[i]))})).join(n):i?encodeURIComponent(t(i))+r+encodeURIComponent(t(e)):""}},960709:(e,t,n)=>{"use strict";n.d(t,{Ho:()=>c,OC:()=>a,hr:()=>l,pg:()=>u,sb:()=>f,uo:()=>s});var r,i=()=>n(331635);!function(e){e.FORMAT_ERROR="FORMAT_ERROR",e.UNSUPPORTED_FORMATTER="UNSUPPORTED_FORMATTER",e.INVALID_CONFIG="INVALID_CONFIG",e.MISSING_DATA="MISSING_DATA",e.MISSING_TRANSLATION="MISSING_TRANSLATION"}(r||(r={}));var o=function(e){function t(n,r,i){var o=this,a=i?i instanceof Error?i:new Error(String(i)):void 0;return(o=e.call(this,"[@formatjs/intl Error ".concat(n,"] ").concat(r,"\n").concat(a?"\n".concat(a.message,"\n").concat(a.stack):""))||this).code=n,"function"==typeof Error.captureStackTrace&&Error.captureStackTrace(o,t),o}return(0,i().__extends)(t,e),t}(Error),a=function(e){function t(t,n){return e.call(this,r.UNSUPPORTED_FORMATTER,t,n)||this}return(0,i().__extends)(t,e),t}(o),s=function(e){function t(t,n){return e.call(this,r.INVALID_CONFIG,t,n)||this}return(0,i().__extends)(t,e),t}(o),l=function(e){function t(t,n){return e.call(this,r.MISSING_DATA,t,n)||this}return(0,i().__extends)(t,e),t}(o),u=function(e){function t(t,n,i){var o=e.call(this,r.FORMAT_ERROR,"".concat(t,"\nLocale: ").concat(n,"\n"),i)||this;return o.locale=n,o}return(0,i().__extends)(t,e),t}(o),c=function(e){function t(t,n,r,i){var o=e.call(this,"".concat(t,"\nMessageID: ").concat(null==r?void 0:r.id,"\nDefault Message: ").concat(null==r?void 0:r.defaultMessage,"\nDescription: ").concat(null==r?void 0:r.description,"\n"),n,i)||this;return o.descriptor=r,o.locale=n,o}return(0,i().__extends)(t,e),t}(u),f=function(e){function t(t,n){var i=e.call(this,r.MISSING_TRANSLATION,'Missing message: "'.concat(t.id,'" for locale "').concat(n,'", using ').concat(t.defaultMessage?"default message (".concat("string"==typeof t.defaultMessage?t.defaultMessage:t.defaultMessage.map((function(e){var t;return null!==(t=e.value)&&void 0!==t?t:JSON.stringify(e)})).join(),")"):"id"," as fallback."))||this;return i.descriptor=t,i}return(0,i().__extends)(t,e),t}(o)},964979:(e,t,n)=>{"use strict";var r=n(746518),i=n(444576),o=n(497751),a=n(406980),s=n(824913).f,l=n(39297),u=n(190679),c=n(323167),f=n(332603),d=n(955002),h=n(516193),p=n(743724),m=n(996395),g="DOMException",v=o("Error"),y=o(g),b=function(){u(this,_);var e=arguments.length,t=f(e<1?void 0:arguments[0]),n=f(e<2?void 0:arguments[1],"Error"),r=new y(t,n),i=new v(t);return i.name=g,s(r,"stack",a(1,h(i.stack,1))),c(r,this,b),r},_=b.prototype=y.prototype,w="stack"in new v(g),S="stack"in new y(1,2),E=y&&p&&Object.getOwnPropertyDescriptor(i,g),k=!(!E||E.writable&&E.configurable),T=w&&!k&&!S;r({global:!0,constructor:!0,forced:m||T},{DOMException:T?b:y});var x=o(g),O=x.prototype;if(O.constructor!==x)for(var C in m||s(O,"constructor",a(1,x)),d)if(l(d,C)){var N=d[C],I=N.s;l(x,I)||s(x,I,a(6,N.c))}},967357:(e,t,n)=>{"use strict";var r=n(746518),i=n(179504),o=n(567750),a=n(991291),s=n(500655),l=n(779039),u=i("".charAt);r({target:"String",proto:!0,forced:l((function(){return"\ud842"!=="𠮷".at(-2)}))},{at:function(e){var t=s(o(this)),n=t.length,r=a(e),i=r>=0?r:n+r;return i<0||i>=n?void 0:u(t,i)}})},968474:(e,t)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0})},969086:e=>{"use strict";var t,n,r,i=/[\\"\x00-\x1f\x7f-\x9f\u00ad\u0600-\u0604\u070f\u17b4\u17b5\u200c-\u200f\u2028-\u202f\u2060-\u206f\ufeff\ufff0-\uffff]/g,o={"\b":"\\b","\t":"\\t","\n":"\\n","\f":"\\f","\r":"\\r",'"':'\\"',"\\":"\\\\"};function a(e){return i.lastIndex=0,i.test(e)?'"'+e.replace(i,(function(e){var t=o[e];return"string"==typeof t?t:"\\u"+("0000"+e.charCodeAt(0).toString(16)).slice(-4)}))+'"':'"'+e+'"'}function s(e,i){var o,l,u,c,f,d=t,h=i[e];switch(h&&"object"==typeof h&&"function"==typeof h.toJSON&&(h=h.toJSON(e)),"function"==typeof r&&(h=r.call(i,e,h)),typeof h){case"string":return a(h);case"number":return isFinite(h)?String(h):"null";case"boolean":case"null":return String(h);case"object":if(!h)return"null";if(t+=n,f=[],"[object Array]"===Object.prototype.toString.apply(h)){for(c=h.length,o=0;o<c;o+=1)f[o]=s(o,h)||"null";return u=0===f.length?"[]":t?"[\n"+t+f.join(",\n"+t)+"\n"+d+"]":"["+f.join(",")+"]",t=d,u}if(r&&"object"==typeof r)for(c=r.length,o=0;o<c;o+=1)"string"==typeof(l=r[o])&&(u=s(l,h))&&f.push(a(l)+(t?": ":":")+u);else for(l in h)Object.prototype.hasOwnProperty.call(h,l)&&(u=s(l,h))&&f.push(a(l)+(t?": ":":")+u);return u=0===f.length?"{}":t?"{\n"+t+f.join(",\n"+t)+"\n"+d+"}":"{"+f.join(",")+"}",t=d,u}}e.exports=function(e,i,o){var a;if(t="",n="","number"==typeof o)for(a=0;a<o;a+=1)n+=" ";else"string"==typeof o&&(n=o);if(r=i,i&&"function"!=typeof i&&("object"!=typeof i||"number"!=typeof i.length))throw new Error("JSON.stringify");return s("",{"":e})}},971180:(e,t)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),t.ErrorTag=void 0,t.ErrorTag={NetworkError:"NetworkError"}},976135:(e,t,n)=>{e.exports=n(539754)},977811:e=>{"use strict";e.exports="undefined"!=typeof ArrayBuffer&&"undefined"!=typeof DataView},979577:(e,t,n)=>{"use strict";var r=n(439928),i=n(794644),o=n(618727),a=n(991291),s=n(175854),l=i.aTypedArray,u=i.getTypedArrayConstructor,c=i.exportTypedArrayMethod,f=!!function(){try{new Int8Array(1).with(2,{valueOf:function(){throw 8}})}catch(e){return 8===e}}();c("with",{with:function(e,t){var n=l(this),i=a(e),c=o(n)?s(t):+t;return r(n,u(n),i,c)}}.with,!f)},982026:function(e,t,n){"use strict";var r=this&&this.__createBinding||(Object.create?function(e,t,n,r){void 0===r&&(r=n);var i=Object.getOwnPropertyDescriptor(t,n);i&&!("get"in i?!t.__esModule:i.writable||i.configurable)||(i={enumerable:!0,get:function(){return t[n]}}),Object.defineProperty(e,r,i)}:function(e,t,n,r){void 0===r&&(r=n),e[r]=t[n]}),i=this&&this.__exportStar||function(e,t){for(var n in e)"default"===n||Object.prototype.hasOwnProperty.call(t,n)||r(t,e,n)};Object.defineProperty(t,"__esModule",{value:!0}),i(n(512335),t),i(n(729094),t)}}]);