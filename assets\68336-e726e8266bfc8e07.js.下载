"use strict";(globalThis.webpackChunknotion_next=globalThis.webpackChunknotion_next||[]).push([[68336],{68336:(e,t,n)=>{n.d(t,{F4:()=>m});n(16280),n(581454),n(944114),n(517642),n(658004),n(733853),n(845876),n(432475),n(515024),n(731698),n(898992),n(803949);var r=()=>n(532659);let s;function i(e,t){const{sql:n,tagName:r,className:i}=e,a=(null==t?void 0:t.stylize)??(e=>e),o=function(e){try{return s?s.highlight(e):e}catch{return e}}(n),c=o.split("\n"),u=[i?a(`(${i}) `,"undefined"):"",a(r??"sql","special"),a("`","string")];return c.length>2?(n.startsWith("\n")||u.push("\n"),u.push(c.map((e=>`  ${e}`)).join("\n")),n.endsWith("\n")||u.push("\n")):u.push(o),u.push(a("`","string")),u.join("")}function a(e,t){for(const n of t)e.push(n)}const o=Symbol("QueryArg");class c{constructor(e){this.value=e}}const u=new Set(["IS NULL","IS NOT NULL","IS TRUE","IS FALSE","=","!=","<","<=",">",">=","IS","IN","NOT IN","LIKE","NOT LIKE","MATCH"]);function p(e){if(u.has(e))return e;throw new Error(`Not a SQL operator: "${e}"`)}const l={createForDialect:e=>function(t){for(var n=arguments.length,r=new Array(n>1?n-1:0),s=1;s<n;s++)r[s-1]=arguments[s];return(new e).appendTemplate(t,...r)}};class f{constructor(){this.chunks=[],this.args=[]}appendTemplate(e){if(!Array.isArray(e)||!Array.isArray(e.raw))throw new Error("sql`` can only be used as a template literal tag");for(var t=arguments.length,n=new Array(t>1?t-1:0),r=1;r<t;r++)n[r-1]=arguments[r];for(const s of e)if(this.appendRaw_DANGEROUS(s),n.length){const e=n.shift();if(e instanceof c){this.appendIdentifier(e);continue}if(e instanceof f){this.append(e);continue}this.appendArg(e)}return this}append(e){return a(this.chunks,e.chunks),a(this.args,e.args),this}appendArg(e){return this.chunks.push(o),this.args.push(e),this}appendRaw_DANGEROUS(e){return this.chunks.push(e),this}appendIdentifier(e){const t=e instanceof c?e.value:e;return this.appendRaw_DANGEROUS(this.escapeIdentifier(t))}escapeIdentifier(e){if(/^[\w]+$/.test(e))return`"${e}"`;throw new Error(`Unexpected SQL identifier format: ${e}`)}sql(){let e=0;return this.chunks.map((t=>t===o?"$"+ ++e:t)).join("")}toString(){const e=JSON.stringify(this.args),t=this.constructor;return`${t===f?"Sql":t.name||t.TagName}(\`${this.sql()}\`, ${e})`}DEBUG_ONLY_getInterpolatedQuery(){let e=0;return this.chunks.map((t=>{if(t===o){const t=this.args[e];return e++,function(e){if(Array.isArray(e)){let t=!1;const n=e.map((e=>"string"==typeof e?y(e):("number"==typeof e||(t=!0),e)));if(!t)return`ARRAY[${n.join(",")}]`}if(null==e)return"NULL";switch(typeof e){case"string":case"symbol":return y(String(e));case"number":case"bigint":case"boolean":return String(e);case"function":case"object":case"undefined":return y(JSON.stringify(e))}}(t)}return t})).join("")}}if(f.TagName="sql","undefined"==typeof window){f.prototype[Symbol.for("nodejs.util.inspect.custom")]=function(e,t,n){return i({sql:this.DEBUG_ONLY_getInterpolatedQuery(),tagName:this.constructor.TagName||"sql",className:this.constructor===f?void 0:this.constructor.name},t)}}function h(e,t){if(t instanceof f)return t;if("string"==typeof t){if(!/^[ \t]*$/.test(t))throw new Error(`Unexpected indent format ${t}`);return e.raw_DANGEROUS(t)}if(t<0)return;const n="  ".repeat(t);return e`\n`.appendRaw_DANGEROUS(`${n}`)}function d(e,t,n){const r=e``;return t.forEach(((e,s)=>{r.append(e),s!==t.length-1&&r.append(n)})),r}const g=(new f).appendRaw_DANGEROUS(" "),w=new f;function y(e){let t=!1,n="'";for(let r=0;r<e.length;r++){const s=e[r];"'"===s?n+=s+s:"\\"===s?(n+=s+s,t=!0):n+=s}return n+="'",!0===t&&(n=` E${n}`),n}var N=()=>n(430476);class S extends f{static fromColumnType(e){if(!(0,n(732524).tx)(e))throw new Error(`Not a valid Sqlite column type: "${e}"`);return m.raw_DANGEROUS(e)}sql(){return this.chunks.map((e=>e===o?"?":e)).join("")}asRead(){return{sql:this.sql(),args:this.args,getData:!0}}asWrite(){return{sql:this.sql(),args:this.args}}async all(e,t){return(0,N().qU)({connection:e,sql:this.sql(),args:this.args,queryName:t})}async first(e){return(await this.all(e))[0]}async run(e){return(0,N().kx)({connection:e,sql:this.sql(),args:this.args})}}S.TagName="sqlite";const m=function(e){const t=l.createForDialect(e),n=t;return n.raw_DANGEROUS=t=>(new e).appendRaw_DANGEROUS(t),n.ident=t=>(new e).appendIdentifier(t),n.col=(t,n)=>{const r=new e;return n?(r.appendIdentifier(t),r.appendRaw_DANGEROUS("."),r.appendIdentifier(n)):r.appendIdentifier(t),r},n.cols=t=>{let n;return null!=t&&t.prefix?n=t.prefix:null!=t&&t.table&&(n="string"==typeof t.table?(new e).appendIdentifier(t.table):(new e).append(t.table),n.appendRaw_DANGEROUS(".")),function(e){const t=Object.create(null),n=e.dialect,s=e.prefix,i=e.iterable?new Set(e.iterable):void 0,a=e=>{const t=new n;return s&&t.append(s),t.appendIdentifier(e)},o=e=>"string"==typeof e&&(!i||!!i.has(e));if(i){const e=()=>r().jY.map(i,a)[Symbol.iterator]();t[Symbol.iterator]=e}return new Proxy(t,{get:(e,t,n)=>o(t)?a(t):Reflect.get(e,t,n),has:(e,t)=>o(t)||Reflect.has(e,t),ownKeys(e){const t=Object.keys(e);return i?Array.from(i).concat(t):t}})}({dialect:e,prefix:n,iterable:null==t?void 0:t.allowed})},n.op=t=>(new e).appendRaw_DANGEROUS(p(t)),n.expr=(t,n,r)=>{const s=new e;if("string"==typeof t){const e=t.split(".");for(let t=0;t<e.length;t++)0!==t&&s.appendRaw_DANGEROUS("."),s.appendIdentifier(e[t])}else s.append(t);return s.appendRaw_DANGEROUS(` ${p(n)} `),void 0!==r&&s.append(r),s},n.join=(e,t)=>d(n,e,t),n.and=(e,t)=>function(e,t){let n=arguments.length>2&&void 0!==arguments[2]?arguments[2]:-1;if(0===t.length)return e`TRUE`;if(1===t.length)return e`(${t[0]})`;const r=h(e,n),s=d(e,t,e`${r??g}AND `);return e`(${r??w}${s})`}(n,e,t),n.or=(e,t)=>function(e,t){let n=arguments.length>2&&void 0!==arguments[2]?arguments[2]:-1;if(0===t.length)return e`FALSE`;if(1===t.length)return e`(${t[0]})`;const r=h(e,n),s=d(e,t,e`${r??g}OR `);return e`(${r??w}${s})`}(n,e,t),n.comma=(e,t)=>function(e,t){let n=arguments.length>2&&void 0!==arguments[2]?arguments[2]:-1;return d(e,t,e`,${h(e,n)??g}`)}(n,e,t),n.newline=e=>h(n,e??0)??t`\n`,n.comment=t=>(new e).appendRaw_DANGEROUS(`/* ${t.replace(/\/(?=\*)|\*(?=\/)/g,"$& ")} */`),n}(S)},732524:(e,t,n)=>{n.d(t,{Ll:()=>s,Xb:()=>i,tx:()=>r});function r(e){switch(e){case"TEXT":case"NUMERIC":case"INTEGER":case"REAL":case"BLOB":return!0;default:return!1}}function s(e){return"Error"===e.type||"ErrorBefore"===e.type||"PreconditionFailed"===e.type||"OutOfSpace"===e.type||"SharedWorkerFailedToDelegate"===e.type}function i(e){return e}}}]);