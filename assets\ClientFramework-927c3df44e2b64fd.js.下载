"use strict";(globalThis.webpackChunknotion_next=globalThis.webpackChunknotion_next||[]).push([[75134],{17022:(e,t,o)=>{o.d(t,{e:()=>i});let n;n=(0,o(624184).zB)(1,o(60053));const i=n.AutoListener},56366:(e,t,o)=>{o.d(t,{$y:()=>c,K8:()=>a,O$:()=>s,r6:()=>i,uB:()=>r});let n;n=(0,o(624184).zB)(1,o(485671));const i=n.useComputedStoreInstance,r=n.useStoreInstance,s=n.useStoreState,a=n.useComputedStore,c=(n.useComputedStoreWithValue,n.useStore_DEPRECATED)},60053:(e,t,o)=>{o.r(t),o.d(t,{AutoListener:()=>s,AutoListenerObserver:()=>c,AutoListenerObserverList:()=>d});o(16280),o(944114),o(898992),o(354520),o(803949),o(581454);var n=()=>o(558842),i=()=>o(992202);const r=o(427652).jY;class s{static hasCurrentListener(){return Boolean(s.currentListener)}static logStoreAccess(e,t){const r=this.currentListener;(0,i().logStoreAccess)(e,r??void 0),r?r.logStoreAccess(e):this.ignoreCurrentListener||function(e,t){if(!(0,n().xx)())return;const i=(0,n().lz)(),r=o(496603).o8(t);{const t=`A component read ${e.constructor.name}'s state without subscribing to updates`;(0,o(624919).NK)(t,[[i],["state =",r]]),o(449412).Fg("A component read store state without subscribing to updates",{level:"error",extra:{"notion-component-info":i,"notion-store-name":e.constructor.name}})}}(e,t)}static withListenerIgnored(e){const t=s.currentListener,o=s.ignoreCurrentListener;s.currentListener=null,s.ignoreCurrentListener=!0;try{return e()}finally{s.currentListener=t,s.ignoreCurrentListener=o}}static withLogging(e){s.debug=!0;try{return e()}finally{s.debug=!1}}constructor(e){this.debug=!1,this.listenCycle=0,this.listenerVersionMap=new Map,this.args=void 0,this.isListening=!1,this.lastListener=null,this.listenerInfo=void 0,this.observers=void 0,this.onChange=e=>{(s.debug||this.debug)&&console.warn("AutoListener",this.args.debugName||"unknown",`onChange ${this.listenCycle}:`,e),this.isListening||this.args.onChange(e)},this.args=e,this.listenerInfo=r&&e.debugName?{listenerName:e.debugName,listenerType:e.source??"unknown"}:void 0,this.args.debug&&(this.debug=this.args.debug);(this.args.observers??a.map((e=>e.deref())).filter(o(534177).O9)).forEach((e=>{e.isActive()&&(this.observers??=[],this.observers.push(new WeakRef(e)))}))}destroy(e){this.listenerVersionMap.forEach(((t,o)=>{this.removeStoreListener(o,e)}))}startListener(e){var t;this.lastListener=s.currentListener,s.currentListener=this,this.isListening=!0,null===(t=this.observers)||void 0===t||t.forEach((e=>{var t;return null===(t=e.deref())||void 0===t?void 0:t.activate()})),e.incrementCycle&&this.listenCycle++}stopListener(e){var t,o;const n=null===(t=s.currentListener)||void 0===t?void 0:t.args.debugName;s.currentListener=this.lastListener,this.lastListener=null,this.isListening=!1,null===(o=this.observers)||void 0===o||o.forEach((e=>{var t;return null===(t=e.deref())||void 0===t?void 0:t.deactivate()})),e.isEndOfCycle&&this.listenerVersionMap.forEach(((e,t)=>{e<this.listenCycle&&this.removeStoreListener(t,n)}))}logStoreAccess(e){var t;(s.debug||this.debug)&&s.withListenerIgnored((()=>{console.warn("Autolistener",this.args.debugName||"unknown",`logStoreAccess ${this.listenCycle}:`,e)})),this.addStoreListener(e,r?null===(t=s.currentListener)||void 0===t?void 0:t.listenerInfo:void 0),this.listenerVersionMap.set(e,this.listenCycle)}addStoreListener(e,t){if((0,i().logListenerAdded)(e,this,6),!this.listenerVersionMap.has(e)&&(e.addListener(this.onChange,t),this.args.onAddListener&&this.args.onAddListener(e),a.length))for(const n of a){var o;null===(o=n.deref())||void 0===o||o.onAddListener(this,e)}}removeStoreListener(e,t){if(this.listenerVersionMap.has(e)&&(e.removeListener(this.onChange,t),(0,i().logListenerRemoved)(e,this),this.listenerVersionMap.delete(e),this.args.onRemoveListener&&this.args.onRemoveListener(e),a.length))for(const n of a){var o;null===(o=n.deref())||void 0===o||o.onRemoveListener(this,e)}}}s.debug=!1,s.currentListener=null,s.ignoreCurrentListener=!1;const a=[];class c{constructor(e,t){this.version=1,this.activeStackDepth=0,this.debugName=e,this.handlers=t}onAddListener(e,t){this.isActive()&&this.handlers.onAutoListenerAdded(e,t)}onRemoveListener(e,t){this.isActive()&&this.handlers.onAutoListenerRemoved(e,t)}isActive(){return this.activeStackDepth>0}register(){a.push(new WeakRef(this))}unregister(){const e=a.findIndex((e=>e.deref()===this));-1!==e&&(a.splice(e,1),this.handlers.didUnregister())}activate(){this.activeStackDepth++}deactivate(){this.activeStackDepth--}}class d{constructor(e){this.version=1,this.observers=e}forEach(e){this.observers.forEach(e)}}},73526:(e,t,o)=>{o.r(t);o(944114);var n=()=>o(466103);(0,n().exposeDebugEnvironmentValue)("toggleKeyboardShortcutStackDebugging",(e=>()=>{const t=!e.KeyboardShortcutStackStore.debugPropagation;e.KeyboardShortcutStackStore.debugPropagation=t,console.log(`Keyboard shortcut stack debugging is now ${t?"enabled":"disabled"}.`)})),(0,n().exposeDebugEnvironmentValue)("logKeyboardShortcutStackState",(e=>()=>{e.KeyboardShortcutStackStore.logDOMNodes()})),(0,n().exposeDebugEnvironmentValue)("logKeyboardShortcutMap",(e=>()=>{const t=e.KeyboardShortcutStackStore.state.stack,n=e.KeyboardShortcutsStore.state.shortcuts,i=(0,o(588165).a)(n);console.log("These are the current shortcut keybindings.","All unset and no-op bindings are passed through to the browser,","triggering its default behavior. Other bindings may or","may not trigger default browser behavior.\n\n");const r=Array.from(Object.entries(i));r.sort(((e,t)=>e[0]>t[0]?1:-1));for(const[e,s]of r){const n=e,i=[],r=s[0],a=s.slice(1);let c=!0;for(let e=t.length-1;e>=0;e--){const r=t[e],s=r.shortcuts[n];if(r.enable&&s){0===i.length&&(c=s===o(496603).D_);const e=c?" (no-op)":"";i.push(`${r.debugName}${e}`)}}const d=c?"font-weight: normal; color: gray":"font-weight: normal",l=i.length>0?i[0]:"unset",u=r?[`%c${n} (%c${r}%c): ${l}`,d,c?d:"font-weight: normal; color: blue",d]:[`%c${n}: ${l}`,d];if(a.length||i.length>1){console.groupCollapsed(...u),a.length&&console.log(`  Also triggered by ${a.join(", ")}.`);for(const e of i.slice(1))console.log(`  • Overrides ${e}.`);console.groupEnd()}else console.log(...u)}}))},93583:(e,t,o)=>{o.d(t,{A:()=>i});var n=o(296540);function i(){for(var e=arguments.length,t=new Array(e),o=0;o<e;o++)t[o]=arguments[o];return(0,n.useCallback)((e=>{for(const o of t)"function"==typeof o?o(e):null!=o&&(o.current=e)}),[t])}},105751:(e,t,o)=>{o.d(t,{getHtmlStreamQueueEntry:()=>d});o(16280),o(944114),o(814628);var n=()=>o(149208),i=()=>o(534177);let r=!1;const s={},a={};function c(e,t){if(null!==e){if(s[e])throw new Error(`Duplicate HTML stream entry: ${e}`);s[e]=t,(a[e]??=Promise.withResolvers()).resolve(t)}else{for(const e of(0,i().uv)(a))a[e].reject(e);r=!0}}function d(e){u();const t=a[e]??=Promise.withResolvers();return r&&t.reject(e),t.promise}let l=!1;function u(){if(l)return;l=!0;const e=window[n().n];if(!e)throw new Error("HTML stream queue not found");for(let t=0;t<e.length;t+=2)c(e[t],e[t+1]);e.length=0,e.push=c}},110750:(e,t,o)=>{o.d(t,{AE:()=>u,Bd:()=>c,Ee:()=>l,Un:()=>m,WP:()=>d,XB:()=>p,Xz:()=>h});o(944114),o(898992),o(354520);var n=()=>o(857639),i=()=>o(427704),r=()=>o(496603),s=()=>o(939768);const a=["onboarding","root"];class c extends(()=>o(757695))().Store{constructor(){super(...arguments),this.previousState=void 0}getInitialState(){let e;try{if("/nativetab/updates"===window.location.pathname)e="updates";else e="home"}catch{e="home"}return{activeTab:e,modal:[],tabs:{home:{rootPage:{type:"native",id:"home"},pages:[{type:"web",route:{name:"root"},url:"/"}],nativeRootPageOverrideEnabled:!0},search:{rootPage:{type:"native",id:"search"},pages:[],nativeRootPageOverrideEnabled:!0},updates:{rootPage:{type:"web",route:{name:"nativeTab",tab:"updates",spaceId:void 0},url:`${i().GJ.nativeTab}/updates`},pages:[]},addPage:{rootPage:{type:"web",route:{name:"new"},url:i().GJ.newPage},pages:[]},ai:{rootPage:{type:"web",route:{name:"nativeTab",tab:"assistant",spaceId:void 0},url:`${i().GJ.nativeTab}/assistant`},pages:[]}}}}getPreviousState(){return this.previousState}setUp(e){const{nativeInboxEnabled:t,nativeAiTabEnabled:o}=e;let n;t&&(n=(0,r().mg)(this.state),n.tabs.updates.rootPage={type:"native",id:"inbox"},n.tabs.updates.nativeRootPageOverrideEnabled=!0),o&&(n=n??(0,r().mg)(this.state),n.tabs.ai.rootPage={type:"native",id:"ai"},n.tabs.ai.nativeRootPageOverrideEnabled=!0),n&&this.setState(n)}updateWithWebPage(e){var t;const{page:o,action:i,updateMobileTabbedRouterArgs:s}=e,{url:c,route:d}=o,l=(null==s?void 0:s.silenceErrors)||!1;this.debug&&console.info(`TabbedRouterStore.updateWithWebPage ${i} ${d.name} ${c} clearNativeRootPageOverride: ${null==s?void 0:s.clearNativeRootPageOverride}`);const u=(0,r().mg)(this.state),m=u.tabs[u.activeTab];switch(i){case"pop":const e=((null===(t=u.modal)||void 0===t?void 0:t.length)??0)>0;u.modal=[];const o=(0,r().Kl)(m.pages,(e=>this.isWebPageEqual({page:e,otherRoute:d,otherUrl:c})));if(-1!==o)o<m.pages.length-1?m.pages=m.pages.slice(0,o+1):e||n().log({level:"error",from:"TabbedRouterStore",type:"updateWithPage",error:{message:`failed to pop page ${c} because it's the top page in the active tab and we don't have a modal`}});else if("web"===m.rootPage.type&&this.isWebPageEqual({page:m.rootPage,otherRoute:d,otherUrl:c}))m.pages=[];else{if("web"===m.rootPage.type?m.pages=m.pages.slice(0,-1):"native"===m.rootPage.type&&(m.pages.length>=1?m.pages=m.pages.slice(0,-1):m.nativeRootPageOverrideEnabled=!0),l)break;const e=`Unhandled pop - ${c} ${JSON.stringify(d)} is not in pages nor the rootPage`;this.debug&&console.error(`TabbedRouterStore.updateWithWebPage - ${e}`),n().log({level:"error",from:"TabbedRouterStore",type:"updateWithPage",error:{message:e}})}break;case"replace":if(u.modal&&u.modal.length>0){const e=u.tabs[u.activeTab].pages;if(e.length>0&&this.isWebPageEqual({page:e[e.length-1],otherUrl:c,otherRoute:d}))break;u.modal=[],m.nativeRootPageOverrideEnabled?(u.tabs[u.activeTab].pages=[{type:"web",route:d,url:c}],u.tabs[u.activeTab].nativeRootPageOverrideEnabled=void 0):u.tabs[u.activeTab].pages.push({type:"web",route:d,url:c});break}const i=m.rootPage;switch(i.type){case"web":m.pages=[],i.redirectedTo={route:d,url:c};break;case"native":const e=m.pages.length>0?m.pages[0]:void 0;m.pages=e?[{...e,redirectedTo:{route:d,url:c}}]:[{type:"web",route:d,url:c}]}break;case"push":const s=u.tabs[u.activeTab].pages;if(u.modal&&u.modal.length>0){const e=u.modal[u.modal.length-1];if("web"===e.type&&this.isWebPageEqual({page:e,otherUrl:c,otherRoute:d}))break;u.modal=[],n().log({level:"error",from:"TabbedRouterStore",type:"updateWithWebPage",error:{message:`Currently TabbedRouterStore doesn't support multiple pages in the modal yet, so the behavior is to clear the modal and push the page to the active tab's pages. Pushing a new page while a modal is present is unexpected. Page pushed: ${c}, modal page: ${e}`}})}if(s.length>0&&this.isWebPageEqual({page:s[s.length-1],otherUrl:c,otherRoute:d}))break;const h=s.filter((e=>{var t,o;if(null!==(t=e.redirectedTo)&&void 0!==t&&t.route){if(a.includes(null===(o=e.redirectedTo)||void 0===o?void 0:o.route.name))return!1}else if(a.includes(e.route.name))return!1;return!0}));h.push({type:"web",route:d,url:c}),u.tabs[u.activeTab].pages=h}("onboarding"===o.route.name||Boolean(null==s?void 0:s.clearNativeRootPageOverride))&&(m.nativeRootPageOverrideEnabled=void 0);const p=h(this.state);this.setState(u);const f=h(this.state);if(!l&&"replace"!==i&&(0,r().n4)(p,f)){const e=`topPage is the same. Web page ${i} ${d.name} ${c}`;this.debug&&console.error(`TabbedRouterStore.updateWithWebPage - ${e}`),n().log({level:"error",from:"TabbedRouterStore",type:"updateWithWebPage",error:{message:e}})}}updateWithNativePage(e){const{page:t,clearPages:o,navigationAction:n,navigationSource:i}=e,s=(0,r().mg)(this.state);s.navigationAction=n,s.navigationSource=i,s.modal=[];const a=s.tabs[s.activeTab],c=a.rootPage;this.debug&&console.info(`TabbedRouterStore.updateWithNativePage ${t.id}`),"native"===c.type&&c.id===t.id&&(a.nativeRootPageOverrideEnabled=!0,o&&(a.pages=[]),this.setState(s))}canGoBack(){return Boolean(p(this.state))}canGoForward(){return!1}setState(e){(0,r().n4)(this.state,e)||(this.previousState=this.state,this.instanceState=e,this.emit())}isWebPageEqual(e){const{page:t,otherUrl:o,otherRoute:n}=e;if(this.isRouteEqual(t.route,n))return!0;if(l(t)===o)return!0;if(t.redirectedTo){if(this.isRouteEqual(t.redirectedTo.route,n))return!0;if(t.redirectedTo.url===o)return!0}}isRouteEqual(e,t){if(e.name!==t.name)return!1;let o,n;if("nativeTab"===e.name){const o=t;return e.tab===o.tab&&((!o.spaceId||!e.spaceId||o.spaceId===e.spaceId)&&e.tab===o.tab)}return o=e,n=t,(0,r().n4)(o,n)}}function d(e){const t=h(e);let o,n;if("web"===t.type){const{route:e}=u(t);o=e.name,n=e}else o=t.id;const i={tab:e.activeTab,type:t.type,route:{name:o},tabDepth:f(e)};return n&&"blockId"in n&&(i.route.block_id=n.blockId),i}function l(e){let t=arguments.length>2?arguments[2]:void 0;const o=u(e,arguments.length>1&&void 0!==arguments[1]&&arguments[1]),n=s().ZO(o.url),i=t||e.queryParams;return i?s().O$(n,i):n}function u(e){return!(arguments.length>1&&void 0!==arguments[1]&&arguments[1])&&e.redirectedTo?e.redirectedTo:{route:e.route,url:e.url}}function h(e){return m(e).page}function m(e){var t;const o=e.tabs[e.activeTab];return(null===(t=e.modal)||void 0===t?void 0:t.length)?{page:e.modal[e.modal.length-1],type:"page"}:o.pages.length>0?"native"===o.rootPage.type&&o.nativeRootPageOverrideEnabled?{page:o.rootPage,type:"rootPage"}:{page:o.pages[o.pages.length-1],type:"page"}:{page:o.rootPage,type:"rootPage"}}function p(e){const t=e.tabs[e.activeTab];if(!t.nativeRootPageOverrideEnabled||"native"!==t.rootPage.type||0!==(e.modal??[]).length){if(e.modal.length>0){if(e.modal.length>1)return n().log({level:"error",from:"TabbedRouterStore",type:"getPreviousPage",error:{message:"found >1 modals and right now only 1 modal page is supported"}}),e.modal[e.modal.length-2];const t=(0,r().mg)(e);return t.modal=[],h(t)}return t.pages.length>0?t.pages.length>1?t.pages[t.pages.length-2]:t.rootPage:void 0}}function f(e){const t=e.tabs[e.activeTab];return t.nativeRootPageOverrideEnabled?1:1+t.pages.length}},118884:(e,t,o)=>{o.d(t,{X:()=>i});var n=o(296540);function i(e){const t=(0,n.useRef)(e);t.current=e,(0,n.useEffect)((()=>{const e=t.current;return()=>{e()}}),[])}},134134:(e,t,o)=>{o.d(t,{I:()=>n});console.log.bind(console);const n={log:()=>{}}},136590:(e,t,o)=>{o.d(t,{Ag:()=>c,aw:()=>d,yY:()=>a});var n=o(296540),i=()=>o(56366),r=o(474848);const s=(0,n.createContext)(void 0);s.displayName="EnvironmentContext";const a=s,c=(0,n.createContext)(void 0);function d(e){var t;const o=(0,i().O$)(null===(t=e.value)||void 0===t?void 0:t.deviceStore);return(0,r.jsx)(s.Provider,{value:e.value,children:(0,r.jsx)(c.Provider,{value:o,children:e.children})})}c.displayName="DeviceContext"},138418:(e,t,o)=>{o.d(t,{A:()=>n});class n extends(()=>o(757695))().Store{getInitialState(){return{mode:"light"}}}},178624:(e,t,o)=>{o.d(t,{R:()=>i});let n;n=(0,o(624184).zB)(1,o(279106));const i=n.LocalStorageKeyStore},199834:(e,t,o)=>{o.d(t,{L:()=>r,m:()=>s});o(944114),o(517642),o(658004),o(733853),o(845876),o(432475),o(515024),o(731698),o(898992),o(354520),o(581454);var n=()=>o(624919);const i={subtree:!0,childList:!0,attributes:!0,attributeOldValue:!0,characterData:!0,characterDataOldValue:!0};class r{constructor(e){this.node=void 0,this.onMutations=void 0,this.enableLogging=!1,this.unlockTokens=new Set,this.isLocked=!1,this.longRenderWarningTimeout=void 0,this.observer=void 0,this.isObserving=!1,this.queue=[],this.setNode=e=>{this.mutate((()=>{this.node=e||void 0}))},this.unlockForRender=e=>{this.unlockTokens.add(e),this.isLocked&&this.isObserving&&this.enableLogging&&this.startLongRenderWarning(),this.stopObservingAndHandleMutations()},this.onMutations=e.onMutations,this.enableLogging=e.enableLogging,this.observer="undefined"==typeof MutationObserver?void 0:new MutationObserver((e=>{this.queue.push(...this.filterMutationRecords(e)),this.stopObservingAndHandleMutations(),this.startObserving()}))}filterMutationRecords(e){return e.filter((e=>"attributes"!==e.type||e.target!==this.node))}mutate(e){const t={displayName:"DOMLock.mutate()"};try{return this.unlockForRender(t),e()}finally{this.lockAfterRender(t)}}lockAfterRender(e){this.unlockTokens.delete(e),this.isLocked&&0===this.unlockTokens.size&&(this.stopLongRenderWarning(),this.startObserving())}lock(e){this.isLocked=!0,this.lockAfterRender(e)}unlock(e){this.stopLongRenderWarning(),this.isLocked=!1,this.unlockForRender(e)}startObserving(){if(this.isObserving)return;const e=this.node;var t;e&&(null===(t=this.observer)||void 0===t||t.observe(e,i),this.isObserving=!0)}stopObservingAndHandleMutations(){if(this.observer&&this.isObserving){const e=this.queue.concat(this.filterMutationRecords(this.observer.takeRecords()));this.queue.length=0,this.observer.disconnect(),this.isObserving=!1,e.length>0&&this.onMutations(e)}}startLongRenderWarning(){if(this.longRenderWarningTimeout)return;const e=Date.now();this.longRenderWarningTimeout=window.setInterval((()=>{const t=Date.now()-e,i=Array.from(this.unlockTokens).map((e=>e.displayName)).join(", ");(0,n().NK)([`DOMLock: still unlocked after ${t}ms for ${this.unlockTokens.size} components: ${i}`,this]),o(449412).Fg(`DOMLock: Unlocked for a long time! components: ${i}`,{level:"error",extra:{timeUnlocked:t}})}),1e3)}stopLongRenderWarning(){this.longRenderWarningTimeout&&(window.clearInterval(this.longRenderWarningTimeout),this.longRenderWarningTimeout=void 0)}}function s(e,t){const o="See documentation for more details: https://dev.notion.so/notion/About-DOMLock-ContentEditableVoid-and-MaybeContentEditable-184b35e6e67f8092a306e41a781782d6";switch(e.type){case"attributes":if(e.target instanceof Element&&e.attributeName)return t&&(0,n().NK)(["Reverting mutation of attribute",e.attributeName,`from "${e.oldValue}" -> "${e.target.getAttribute(e.attributeName)}"`,"in component",(0,n().Qq)(e.target),e,o]),null===e.oldValue?void e.target.removeAttribute(e.attributeName):void e.target.setAttribute(e.attributeName,e.oldValue);break;case"characterData":return t&&(0,n().NK)(["Reverting mutation of characterData",`"${e.oldValue}" -> "${e.target.textContent}"`,"in component",(0,n().Qq)(e.target),e,o]),void(e.target.textContent=e.oldValue);case"childList":{t&&(0,n().NK)(["Reverting mutation of childList","in component",(0,n().Qq)(e.target),e,o]);let i=0;for(i=e.removedNodes.length-1;i>=0;i--)e.target.insertBefore(e.removedNodes[i],e.nextSibling);for(i=e.addedNodes.length-1;i>=0;i--){const t=e.addedNodes[i];t.parentNode&&t.parentNode.removeChild(t)}return}}console.error("DOMLock: unable to revert mutation",e,o)}},221844:(e,t,o)=>{o.d(t,{A:()=>a,Z:()=>s});o(944114),o(517642),o(658004),o(733853),o(845876),o(432475),o(515024),o(731698),o(898992),o(354520);var n=()=>o(452540),i=()=>o(792071),r=()=>o(445120);function s(e){const{pointer:t,userId:o}=e;return`${(0,n().jV)(t)}:${o||""}`}const a=class{get size(){return this._size}constructor(e){this.data=void 0,this.cacheOverrides=void 0,this.cacheUsingThisInstanceAsAnOverride=void 0,this.cacheFallbacks=void 0,this.cachesUsingThisInstanceAsAFallback=void 0,this.appliedTransaction=void 0,this.recordEvents=new(o(752196).A),this.isExpired=!1,this.isFrozen=!1,this._size=0,this.snapshotData=void 0,this.isTemplatePreview=!1,this.isMockTextStore=!1,this.isTemporaryData=void 0,this.onOperationCallback=void 0,this.isSyntheticAssistantData=void 0,this.relatedAssistantSessionId=void 0,this.cacheLogicalTime=0,this.name=void 0;const{data:t,name:n,isTemporaryData:i,onOperationCallback:s,isSyntheticAssistantData:a,relatedAssistantSessionId:c}=e;this.data=t??new(r().b),this._size=this.data.size,this.cacheOverrides=[],this.cacheFallbacks=new Set,this.cachesUsingThisInstanceAsAFallback=new Set,this.appliedTransaction=!1,this.name=n,this.isTemporaryData=i??!1,this.onOperationCallback=s,this.isSyntheticAssistantData=a??!1,this.relatedAssistantSessionId=c}expire(){this.isExpired=!0}freeze(){this.isFrozen=!0}assertUnexpired(){if(this.isExpired)throw new(o(210138).yI)(`InMemoryRecordCache "${this.name}" is expired! \n If you are using useLocalDraft() this is likely because the localDraftCache Hook did not run its cleanup function.`)}getEntry(e,t){return this.getEntryInternal({key:e,backfillSpaceIdOnPointer:!1,...t})}getEntryWithBackfilledSpaceId(e,t){return this.getEntryInternal({key:e,...t,backfillSpaceIdOnPointer:!0})}getEntryInternal(e){const{key:t,ignoreCache:o,ignoreAllCaches:n,backfillSpaceIdOnPointer:i}=e;if(!n)for(const c of this.cacheOverrides)if(c!==o){const e=c.getEntryInternal({key:t,ignoreCache:this,ignoreAllCaches:void 0,backfillSpaceIdOnPointer:i});if(e)return e}const r=this.data.getValue(t),s=this.data.getRole(t);let a=t.pointer;if(i){const e=this.data.getModelWithBackfilledSpaceId(t);e&&(a=e.pointer)}if(s)return{pointer:a,userId:t.userId,value:{value:r,role:s}};if(!n&&this.cacheUsingThisInstanceAsAnOverride&&this.cacheUsingThisInstanceAsAnOverride!==o)return this.cacheUsingThisInstanceAsAnOverride.getEntryInternal({key:t,ignoreCache:this,ignoreAllCaches:void 0,backfillSpaceIdOnPointer:i});if(!n)for(const c of this.cacheFallbacks)if(c!==o){const e=c.getEntryInternal({key:t,ignoreCache:this,ignoreAllCaches:void 0,backfillSpaceIdOnPointer:i});if(e)return e}}getRecord(e,t){const o=this.getEntry(e,t);if(o)return o.value}getRecordModel(e,t){var o;const n=this.getEntry(e,t);if(null!=n&&null!==(o=n.value)&&void 0!==o&&o.value)return i().Bj6.fromValue(e.pointer.table,n.value.value)}getModelAndRole(e){const t=this.getEntry(e);if(null!=t&&t.value)return{model:i().Bj6.fromValue(e.pointer.table,t.value.value),role:t.value.role}}checkRecordForTemporaryData(e){if(this.isTemporaryData&&this.data.getRole(e))return this;for(const t of this.cacheOverrides){if(!t.isTemporaryData)continue;if(t.getEntry(e,{ignoreCache:this}))return t}for(const t of this.cacheFallbacks){if(!t.isTemporaryData)continue;if(t.getEntry(e,{ignoreCache:this}))return t}}recordHasOverride(e){for(const t of this.cacheOverrides){if(t.getEntry(e,{ignoreCache:this}))return!0}return!1}makeGetRecordValueFn(e){let t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{};return o(813690).xb.fromMonomorphicFunctionUnsafe((o=>{const n=this.getEntry({pointer:o,userId:e},t);if(null!=n&&n.value.value)return n.value.value}))}makeGetRecordModelFn(e){return i().b4_.fromMonomorphicFunctionUnsafe((t=>this.getRecordModel({pointer:t,userId:e})),this.makeGetRecordValueFn(e))}makeGetRecordRoleFn(e){return t=>{const o=this.getRecord({pointer:t,userId:e});if(o&&o.role)return o.role}}getRole(e,t){const o=this.getRecord(e,t);if(o&&o.role)return o.role}hasRecord(e){return void 0!==this.getRole(e)}hasRecordWithVersion(e,t){return this.hasRecord(e)&&this.getVersion(e)===t}getVersion(e,t){const o=this.getRecord(e,t);return o&&o.value&&o.value.version?o.value.version:0}setRecord(e,t){this.assertUnexpired(),this.isFrozen||(this.cacheLogicalTime++,t?(this.hasRecord(e)||this._size++,this.data.setValueAndRole(e,t.value,t.role)):this.deleteRecord(e))}setModelAndRole(e,t,o){this.assertUnexpired(),this.isFrozen||(this.cacheLogicalTime++,this.hasRecord(e)||this._size++,this.data.setModelAndRole(e,t,o))}deleteRecord(e){this.assertUnexpired(),this.isFrozen||(this.cacheLogicalTime++,this.hasRecord(e)&&this._size--,this.data.delete(e))}addCacheOverride(e){this.cacheLogicalTime++,this.cacheOverrides.push(e),e.cacheUsingThisInstanceAsAnOverride=this,e.emitAll()}hasCacheOverride(e){return this.cacheOverrides.includes(e)}removeCacheOverride(e){this.cacheLogicalTime++,this.cacheOverrides=this.cacheOverrides.filter((t=>t!==e)),e.emitAll(),e.cacheUsingThisInstanceAsAnOverride=void 0}addCacheFallback(e){this.cacheLogicalTime++,this.cacheFallbacks.add(e),e.cachesUsingThisInstanceAsAFallback.add(this)}hasCacheFallback(e){return this.cacheFallbacks.has(e)}removeCacheFallback(e){this.cacheLogicalTime++,this.cacheFallbacks.delete(e),e.cachesUsingThisInstanceAsAFallback.delete(this)}forEachRecord(e,t,o){for(const{model:n,role:i,userId:r}of this.data)if("none"!==i&&e===r&&n&&t({model:n,role:i}),null!=o&&o.aborted)break}emitAll(){for(const{pointer:e}of this.data)this.emitRecord(e,[])}clearCache(){this.cacheLogicalTime++;const e=[];for(const{pointer:t}of this.data)e.push(t);this.appliedTransaction=!1,this.data=new(r().b);for(const t of e)this.emitRecord(t,[])}addListenerToRecord(e,t){const o=(0,n().jV)(e);this.recordEvents.addListener(o,t)}removeListenerToRecord(e,t){const o=(0,n().jV)(e);this.recordEvents.removeListener(o,t)}emitRecord(e,t){this.cacheLogicalTime++;const o=(0,n().jV)(e);this.recordEvents.emit(o,t),this.cacheUsingThisInstanceAsAnOverride&&this.cacheUsingThisInstanceAsAnOverride.emitRecord(e,t);for(const n of this.cachesUsingThisInstanceAsAFallback)n.emitRecord(e,t)}hasListener(e){var t;const o=(0,n().jV)(e);if(this.recordEvents.listenerCount(o)>0)return!0;if(null!==(t=this.cacheUsingThisInstanceAsAnOverride)&&void 0!==t&&t.hasListener(e))return!0;for(const n of this.cachesUsingThisInstanceAsAFallback)if(n.hasListener(e))return!0;return!1}}},223116:(e,t,o)=>{o.d(t,{$H:()=>i,CX:()=>s,Rb:()=>r,a9:()=>n});const n="adminContentSearchSettings.useContentSearch",i="settingsConsole.singleLegalHoldContentTab.useLegalHoldContent",r="sudoModeActions.privatePageRecordCache",s=[n,r]},225661:(e,t,o)=>{o.d(t,{s:()=>m});o(944114),o(898992),o(354520);var n=o(296540),i=()=>o(949054),r=()=>o(355543),s=o(474848);let a=[];const c=new(o(592328).A);function d(){return a.length>0?a[a.length-1]:null}function l(e){a=a.filter((t=>t!==e)),c.emit(a)}function u(){return document.activeElement instanceof HTMLElement||document.activeElement instanceof SVGElement?document.activeElement:null}let h=0;function m(e){let{children:t,active:o,onActiveChange:m}=e;const p=(0,r().w)((()=>++h)),f=(0,n.useRef)(null),g=(0,n.useRef)(null),[v,b]=(0,n.useState)(!1);return(0,n.useEffect)((()=>{function e(){const e=d()===p;e!==v&&(b(e),null==m||m(e))}return c.addListener(e),()=>{c.removeListener(e)}}),[p,m,v]),(0,n.useEffect)((()=>{function e(){const e=f.current,t=u();e&&t&&!e.contains(t)&&(g.current=t)}function t(){const e=f.current,t=u(),o=e&&e.contains(t),n=t===document.body;g.current&&(o||n)&&g.current.focus({preventScroll:!0}),g.current=null}if(o){n=p,a.includes(n)||(a.push(n),c.emit(a));const o=f.current;let r=null;if(o&&!o.contains(u())){const t=(0,i().Kr)(o);t.length>0?(e(),t[0].focus()):r=function(e,t){const o=new MutationObserver((()=>{const n=(0,i().Kr)(e);n.length>0&&(o.disconnect(),t(n))}));return o.observe(e,{childList:!0,subtree:!0}),()=>{o.disconnect()}}(o,(t=>{e(),t[0].focus()}))}return()=>{var e;null===(e=r)||void 0===e||e(),d()===p&&(l(p),t())}}var n;d()===p&&(l(p),t())}),[o,p]),(0,n.useEffect)((()=>{if(v){function e(e){const t=f.current;if(!v||"Tab"!==e.key||!t)return;const o=document.activeElement;if(!(o instanceof HTMLElement||o instanceof SVGElement))return;const n=(0,i().Kr)(t);if(0===n.length)return;const r=n.indexOf(o);let s=null;-1===r?s=e.shiftKey?n.length-1:0:e.shiftKey&&0===r?s=n.length-1:e.shiftKey||r!==n.length-1||(s=0);const a=null!==s?n[s]:null;a&&(a.focus(),e.preventDefault())}return document.addEventListener("keydown",e),()=>{document.removeEventListener("keydown",e)}}}),[v]),(0,s.jsx)("div",{ref:f,style:{display:"contents"},children:t})}},250454:(e,t,o)=>{o.d(t,{I:()=>n});o(944114);const n=new class{constructor(){this.longEventMetrics=[],this.shouldCollect=!1}setShouldCollect(e){this.shouldCollect=e}getShouldCollect(){return this.shouldCollect}generateUniqueId(){return Math.random().toString(36).substring(2,15)}start(e){const t=e?`rqf.${e}`:"rqf";performance.mark(`${t}.start`)}stop(e){const t=e?`rqf.${e}`:"rqf",o=performance.measure(t,`${t}.start`);this.add(o.duration)}add(e){e>500&&this.longEventMetrics.push({eventName:"render_queue_flush_long",eventProperties:{time:e}})}resetMetrics(){this.longEventMetrics=[]}getLongEventMetrics(){return this.longEventMetrics}}},265515:(e,t,o)=>{o.d(t,{G:()=>r});o(16280);class n extends(()=>o(757695))().Store{constructor(e){super(),this.getValue=e}getState(){return this.instanceState=this.getValue(),super.getState()}setState(){throw new Error("MapKeyStore is read-only")}getInitialState(){}}class i{constructor(e,t){this._stores=void 0,this._map=void 0,this._stores=e(),this._map=e(t)}delete(e){const t=this._map.delete(e);return t&&this.emitKey(e),this.deleteKeyStore(e),t}get(e){return this.getKeyStore(e).getState()}has(e){return this.getKeyStore(e).getState(),this._map.has(e)}set(e,t){const o=!this._map.has(e)||this._map.get(e)!==t;return this._map.set(e,t),o&&this.emitKey(e),this}setUnlessEqual(e,t,o){return!(this._map.has(e)&&o(this._map.get(e),t))&&(this.set(e,t),!0)}getKeyStore(e){let t=this._stores.get(e);return t||(t=new n((()=>this._map.get(e))),this._stores.set(e,t)),t}emitKey(e){const t=this._stores.get(e);null!=t&&t.listenerCount()?t.emit():t&&this._stores.delete(e)}deleteKeyStore(e){const t=this._stores.get(e);return!(!t||0!==t.listenerCount())&&this._stores.delete(e)}}class r extends i{constructor(e){super((e=>new Map(e??[])),e),this._keysStore=new n((()=>this._map.keys())),this[Symbol.toStringTag]="MapStore"}forEach(e,t){for(const[o,n]of this)e.apply(t,[n,o,this])}clear(){if(0===this.size)return;const e=Array.from(this._map.keys());this._map.clear();for(const t of e)this.emitKey(t);for(const t of this._stores.keys())this.deleteKeyStore(t);this._keysStore.emit()}get size(){return this._keysStore.getState(),this._map.size}*entries(){this.keys();for(const e of this._map.entries())this.get(e[0]),yield e}keys(){return this._keysStore.getState()}*values(){this.keys();for(const[e,t]of this.entries())yield t}[Symbol.iterator](){return this.entries()}set(e,t){const o=this._map.size;super.set(e,t);return this._map.size!==o&&this.emitIterable(),this}delete(e){const t=super.delete(e);return t&&this.emitIterable(),t}emitIterable(){this._keysStore.emit()}}},266625:(e,t,o)=>{o.d(t,{y:()=>r});var n=o(296540),i=()=>o(496603);function r(e,t){const o=(0,n.useRef)(Math.random().toString(36).substr(2,9)),r=t,s=(0,n.useRef)();return(0,n.useEffect)((()=>{if(s&&s.current&&i().n4(s.current,r))return;const t=o.current;e&&(e({type:"mount",id:t,props:r}),s.current=r)}),[e,r]),e?o.current:void 0}},272061:(e,t,o)=>{o.d(t,{B:()=>a,J:()=>r});o(944114),o(517642),o(658004),o(733853),o(845876),o(432475),o(515024),o(731698);var n=()=>o(534177),i=()=>o(496506);function r(e,t){const o=new Map,r=new Set,a={nodes:[],edges:[]},c=new Map;let d=0;function l(e){let t=e;return r.has(t)&&(t=`${t}_${++d}`),r.add(t),t}function u(e){let t=o.get(e.value);return t||(t=function(e){var t;let r,a=null===(t=e.value.constructor)||void 0===t?void 0:t.name;a||(a="Unknown"),"component"===e.kind?("debugName"in e.value&&(a=e.value.debugName),r={type:"component",id:l(a),label:a}):"store"===e.kind?e.value instanceof i().ComputedStore?(e.value.debugName&&(a=e.value.debugName),r={type:"computedstore",id:l(a),label:a}):r={type:"store",id:l(a),label:a}:(0,n().HB)(e);for(const o of s)o(r,e.value,(e=>{c.set(r,e)}));return o.set(e.value,r),r}(e)),t}for(const[n,i]of e.entries())for(const e of i)a.edges.push({from:u({kind:"store",value:n}).id,to:u({kind:"component",value:e}).id});for(const[n,i]of t.entries())for(const e of i)a.edges.push({from:u({kind:"store",value:n}).id,to:u({kind:"store",value:e}).id});for(const[n,i]of c.entries()){const e=o.get(i);e&&(n.parentUIStoreId=e.id)}return a.nodes=Array.from(o.values()),a}const s=[];function a(e){s.push(e)}},279106:(e,t,o)=>{o.r(t),o.d(t,{LocalStorageKeyStore:()=>s});o(16280),o(517642),o(658004),o(733853),o(845876),o(432475),o(515024),o(731698);var n=()=>o(496603);const i="notion_test_local_storage_key",r="notion_123";class s{constructor(e){let{key:t,namespace:a,important:c,trackingType:d}=e;if(this.lruStore=void 0,this.key=void 0,this.emitter=new(o(592328).A),this._canPersistToLocalStorage=void 0,this.canPersistToLocalStorage=()=>{if(void 0===this._canPersistToLocalStorage){let e;try{this.lruStore.set(i,r),e=this.lruStore.get(i,{disableLRU:!0}),e&&this.lruStore.remove(i)}catch{}this._canPersistToLocalStorage=e===r}return this._canPersistToLocalStorage},this.handleStorage=e=>{if("string"!=typeof e.key)return;if(this.lruStore.parseRawKeyToOwnedKey(e.key)===this.key){if(null===e.newValue&&e.oldValue)return void this.emit();if(e.newValue&&null===e.oldValue)return void this.emit();if(null===e.oldValue&&null===e.newValue)return;if(e.newValue&&e.oldValue)try{const t=JSON.parse(e.oldValue).value,o=JSON.parse(e.newValue).value;n().n4(t,o)||this.emit()}catch{o(449412).O8(new Error("Malformed value(s) found in localStorage"),{from:"LocalStorageKeyStore",type:"ParseError",data:{key:e.key,oldValue:e.oldValue,newValue:e.newValue}})}}},s.keysWithStores.has(`${a}:${t}`))throw new Error("Please create only one LocalStorageKeyStore per key.");s.keysWithStores.add(`${a}:${t}`),this.key=t,this.lruStore=new(o(419494).Ay)({namespace:a,important:c,trackingType:d,onHasPermissionForTrackingTypeChange:()=>{this.emit()}})}getState(){return o(60053).AutoListener.logStoreAccess(this,this.getDebugInfo()),this.lruStore.get(this.key)}get state(){return this.getState()}setState(e){const t=this.lruStore.get(this.key,{disableLRU:!0});n().n4(t,e)||(void 0!==e?this.lruStore.set(this.key,e):this.lruStore.remove(this.key),this.emit())}update(e){this.setState(e(this.state))}emit(){this.emitter.emit(this)}addListener(e){const t=this.emitter.listenerCount();this.emitter.addListener(e),0===t&&1===this.emitter.listenerCount()&&window.addEventListener("storage",this.handleStorage)}removeListener(e){const t=this.emitter.listenerCount();this.emitter.removeListener(e),1===t&&0===this.emitter.listenerCount()&&window.removeEventListener("storage",this.handleStorage)}getDebugInfo(){return this.lruStore.get(this.key,{disableLRU:!0})}static reset_TEST_ONLY(){s.keysWithStores=new Set([])}}s.keysWithStores=new Set([])},285719:(e,t,o)=>{o.d(t,{q:()=>i});var n=o(296540);function i(e){const[t,o]=(0,n.useState)(Date.now());return(0,n.useEffect)((()=>{const t=setInterval((()=>{o(Date.now())}),e);return()=>clearInterval(t)}),[e]),t}},299972:(e,t,o)=>{o.d(t,{A:()=>n});class n extends(()=>o(757695))().Store{getInitialState(){return{}}canGoBack(){return void 0!==this.state.historyState&&this.state.historyState.index>0}canGoForward(){return!this.state.historyState||this.state.historyState.index<window.history.length-1}}},311029:(e,t,o)=>{o.d(t,{B:()=>i});let n=0;function i(){return`id_${(++n).toString(36)}`}},312788:(e,t,o)=>{o.d(t,{E:()=>i,a:()=>r});const n=o(296540).createContext(void 0);n.displayName="RouterContext";const i=n,r=n.Provider},338381:(e,t,o)=>{o.d(t,{EV:()=>c,KH:()=>s,Ll:()=>r,MI:()=>h,Xs:()=>g,a2:()=>f,gR:()=>b,nx:()=>p,vq:()=>a,vr:()=>v,z:()=>l,zn:()=>m});o(944114);var n=()=>o(634201),i=()=>o(496603);function r(e){try{return Boolean(e&&void 0!==e.nodeType)}catch(t){return!1}}function s(e){return r(e)&&e.nodeType===Node.TEXT_NODE}function a(e){return r(e)&&e.nodeType===Node.ELEMENT_NODE}function c(e,t){for(;r(e)&&!t(e);)e=e.parentNode;return e}function d(e,t){var o;return s(e)?t>=((null===(o=e.textContent)||void 0===o?void 0:o.length)??0):t>=e.childNodes.length}function l(e,t){let o=e,n=t;if(!d(e,t)&&s(e))return{node:o,offset:n+1};for(;o&&n>=0;){if(!d(o,n)&&s(o))return{node:o,offset:n};if(a(o)&&0===o.childNodes.length&&"IMG"===o.nodeName)return{node:o,offset:n};if(a(o)&&o.childNodes.length>0)o=o.childNodes[n],n=0;else if(Boolean(o.nextSibling))o=o.nextSibling,n=0;else{var i;o=null===(i=o.parentNode)||void 0===i?void 0:i.nextSibling,n=0}}return{node:e,offset:t}}function u(e){var t;return e?s(e)?(null===(t=e.textContent)||void 0===t?void 0:t.length)??0:e.childNodes&&e.childNodes.length>0?e.childNodes.length:1:-1}function h(e,t){let o=e,n=t;if(t>0&&s(e))return{node:o,offset:n-1};let i=!0;for(;o&&n>=0;){if(!i&&n>=0&&s(o))return{node:o,offset:n};if(a(o)&&0===o.childNodes.length&&"IMG"===o.nodeName)return{node:o,offset:n};if(i=!1,a(o)&&o.childNodes.length>0&&n>0)o=o.childNodes[n-1],n=u(o);else if(o.previousSibling)o=o.previousSibling,n=u(o);else{var r;o=null===(r=o.parentNode)||void 0===r?void 0:r.previousSibling,n=o?u(o):-1}}return{node:e,offset:t}}function m(e){let t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:[];if(r(e)){for(const o of n().PE(e.childNodes))s(o)?t.push(o):o.childNodes&&m(o,t);return t}return[]}function p(e,t){return r(e)&&r(t)?i().SL(e.childNodes,(e=>e===t)):-1}const f=function(){if("undefined"==typeof window)return!0;if(window.InputEvent&&"function"==typeof window.InputEvent.prototype.getTargetRanges)return!0;const e=window.document.createElement("div");return e.contentEditable="true","onbeforeinput"in e}();function g(e){!f&&e.preventDefault&&e.preventDefault()}function v(e){return"inputType"in e}function b(e,t){return e!==t&&e.contains(t)}},352246:(e,t,o)=>{o.d(t,{t:()=>n});o(944114),o(296540);"undefined"!=typeof window&&localStorage.getItem("__rerenderDefenderFender");const n=new class{setShouldCollect(e){}getShouldCollect(){return this.shouldCollect}constructor(){this.shouldCollect=!1,this.shouldCollect=!1}add(e,t,o){}log(e,t,o,n){}resetMetrics(){}getMetrics(){return[]}}},355543:(e,t,o)=>{o.d(t,{w:()=>s});var n=o(296540),i=()=>o(17022);const r=Symbol("EMPTY_VALUE");function s(e){const t=(0,n.useRef)(r);return t.current===r&&(t.current=i().e.withListenerIgnored((()=>e()))),t.current}},378879:(e,t,o)=>{o.d(t,{a:()=>n});const n=(0,o(296540).createContext)(void 0);n.displayName="ReactivityObserverContext"},395361:(e,t,o)=>{o.d(t,{U:()=>r,Y:()=>s});var n=o(296540),i=()=>o(496603);function r(e,t){return(0,n.useRef)(i().sg(e,t)).current}function s(e,t){const o=(0,n.useRef)(e);o.current=e;const r=(0,n.useMemo)((()=>i().sg((function(){return o.current(...arguments)}),t)),[t]);return r}},396182:(e,t,o)=>{o.d(t,{I:()=>s});o(944114);var n=o(296540),i=()=>o(992202);class r{constructor(e,t){this.isUnmounted=!1,this.afterRerenderThunks=[],this.dequeueRerender=void 0,this.dispatch=t;const o=()=>new Promise((e=>{this.isUnmounted?e():(this.afterRerenderThunks.push(e),this.dispatch((e=>e+1)))}));o.componentName=e,this.dequeueRerender=o}enqueueRerender(e,t,n){o(134134).I.log("enqueueRender",t,n),i().isRecording()&&i().logComponentForceUpdateScheduled({componentName:e,debugName:t},n);const r=this.dequeueRerender;r.componentName=e,o(908006).default.enqueueComponentRender(r)}resolveRenderQueuePromises(){if(!this.afterRerenderThunks.length)return;const e=this.afterRerenderThunks;try{for(let t=0;t<e.length;t++)e[t]()}finally{e.length=0}}onUnmount(){this.isUnmounted=!0,this.resolveRenderQueuePromises()}}function s(){const[e,t]=(0,n.useState)(0);(0,n.useDebugValue)(e);const o=(0,n.useRef)(void 0);(0,n.useLayoutEffect)((()=>{var e;return null===(e=o.current)||void 0===e?void 0:e.resolveRenderQueuePromises()})),(0,n.useEffect)((()=>()=>{var e;return null===(e=o.current)||void 0===e?void 0:e.onUnmount()}),[]);return(0,n.useCallback)(((e,n,i)=>{(o.current??=new r(e,t)).enqueueRerender(e,n,i)}),[])}},396487:(e,t,o)=>{o.d(t,{I:()=>i});var n=()=>o(711059);class i extends(()=>o(757695))().Store{getInitialState(){return{}}isPatchUpdate(){return Boolean(this.state.appUpdate&&"ready"===this.state.appUpdate.state&&this.state.appUpdate.type===n().UpdateType.Minor)}isSilentUpdate(){return Boolean(this.state.appUpdate&&"ready"===this.state.appUpdate.state&&this.state.appUpdate.type===n().UpdateType.Silent)}isMajorUpdate(){return this.state.appUpdate&&"ready"===this.state.appUpdate.state&&this.state.appUpdate.type===n().UpdateType.Major||this.state.electronUpdate&&"ready"===this.state.electronUpdate.state&&this.state.electronUpdate.type===n().UpdateType.Major}}},401497:(e,t,o)=>{o.d(t,{$4:()=>c,DP:()=>r,IS:()=>a,eP:()=>s});o(16280);var n=o(296540),i=()=>o(560198);function r(){const e=(0,n.useContext)(i().Ke);if(!e)throw new Error("useTheme: no theme context found");return(0,n.useDebugValue)(e),e}function s(){return r().mode}function a(e,t){const o=0!==e.length?r():void 0,i=(0,n.useMemo)((()=>e(o)),[o,...t]);return(0,n.useDebugValue)(i),i}function c(e){return e}},402014:(e,t,o)=>{o.d(t,{SQ:()=>r,s7:()=>s,y$:()=>n});let n=function(e){return e[e.ButtonMouseDown=0]="ButtonMouseDown",e[e.EditorMouseDown=1]="EditorMouseDown",e[e.Click=2]="Click",e[e.EditorContextMenu=3]="EditorContextMenu",e[e.EditorMouseOver=4]="EditorMouseOver",e[e.EditorDoubleClick=5]="EditorDoubleClick",e[e.SidebarMouseMove=6]="SidebarMouseMove",e[e.MobileTap=7]="MobileTap",e[e.PerformanceToolbarMouseMove=8]="PerformanceToolbarMouseMove",e[e.CopyToClipboard=9]="CopyToClipboard",e}({});const i={};function r(e){const t=i[e.context],o=e.event.nativeEvent||e.event;if((null==t?void 0:t.deref())!==o){var n;i[e.context]=new WeakRef(o);"unhandled"===(null===(n=e.callback)||void 0===n?void 0:n.call(e))&&(i[e.context]=t)}}async function s(e){const t=i[e.context],o=e.event.nativeEvent||e.event;if((null==t?void 0:t.deref())!==o){var n;i[e.context]=new WeakRef(o);"unhandled"===await(null===(n=e.callback)||void 0===n?void 0:n.call(e))&&(i[e.context]=t)}}},402673:(e,t,o)=>{o.d(t,{$:()=>n});const n="function"==typeof requestIdleCallback?(e,t)=>requestIdleCallback(e,{timeout:t}):setTimeout},427652:(e,t,o)=>{o.d(t,{EZ:()=>d,N7:()=>c,jY:()=>n,m:()=>l,nH:()=>a});const n="undefined"!=typeof window&&"true"===localStorage.getItem("__enableDebugStoreMap"),i=new FinalizationRegistry((e=>{c(e)})),r={};function s(){}(0,o(604341).exposeDebugValue)("getDebugStoreMap",(function(){return r}));const a=n?function(e,t){const o=e.debugName;if(o){if(!r[o]){const n=new WeakRef(e);r[o]={count:0,store:n,listeners:{},type:t},i.register(e,o)}r[o].count+=1}}:s,c=n?function(e){if(!e||!r[e])return;const t=r[e].store;delete r[e];const o=null==t?void 0:t.deref();o&&i.unregister(o)}:s,d=n?function(e,t){let{listenerName:o,listenerType:n}=t;const i=r[e];if(!i)return;const{listeners:s}=i;s[o]||(s[o]={listenerType:n})}:s,l=n?function(e,t){const o=r[e];if(!o)return;const{listeners:n}=o;delete n[t]}:s},445356:(e,t,o)=>{o.d(t,{t:()=>i});var n=o(296540);class i extends n.Component{constructor(e){super(e),this.state={hasError:!1}}componentDidCatch(e,t){this.props.onError&&this.props.onError(e,t),this.setState({hasError:!0,error:e,errorInfo:t})}clearErrorState(){this.setState({hasError:!1})}render(){return this.state.hasError&&this.props.fallback?this.props.fallback({error:this.state.error,errorInfo:this.state.errorInfo,clearErrorState:this.clearErrorState.bind(this)}):this.props.children}}},452446:(e,t,o)=>{o.d(t,{q:()=>r,r:()=>i});const n=(0,o(296540).createContext)(void 0);n.displayName="PageContext";const i=n,r=n.Provider},466103:(e,t,o)=>{o.r(t),o.d(t,{exposeDebugEnvironmentValue:()=>s,exposeWindowDebugValue:()=>c,setEnvironment:()=>a});o(944114);var n=()=>o(604341);let i;const r=[];function s(e,t){r.push({name:e,getter:t}),i&&(0,n().exposeDebugValue)(e,t(i))}function a(e){i=e;for(const{name:t,getter:o}of r)(0,n().exposeDebugValue)(t,o(i))}function c(e,t){Object.defineProperty(window,`$${e}`,{get:t,enumerable:!0,configurable:!0})}},484714:(e,t,o)=>{o.d(t,{WS:()=>a,Y0:()=>s,v3:()=>r});o(16280);var n=o(296540),i=()=>o(136590);function r(){const e=(0,n.useContext)(i().yY);if(!e)throw new Error("No ClientEnvironment provided.");return e}function s(){const e=(0,n.useContext)(i().Ag);if(!e)throw new Error("No DeviceContext provided.");return e}function a(){return s().isPhone}},485671:(e,t,o)=>{o.r(t),o.d(t,{useComputedStore:()=>w,useComputedStoreInstance:()=>v,useComputedStoreWithValue:()=>C,useStoreInstance:()=>b,useStoreState:()=>S,useStore_DEPRECATED:()=>k});o(16280),o(898992),o(803949);var n=o(296540),i=()=>o(591779),r=()=>o(17022),s=()=>o(134134),a=()=>o(992202);"undefined"!=typeof window&&localStorage.getItem("__useSlowComponentNameLookup");function c(){return"UnknownFunctionComponent"}const d=c;var l=()=>o(396182),u=()=>o(496506),h=()=>o(378879);const m="no debugName: ",p={useComputedStore:`${m}UnknownFunctionComponent.useComputedStore`,useComputedStoreInstance:`${m}UnknownFunctionComponent.useComputedStoreInstance`,useStoreState:`${m}UnknownFunctionComponent.useStoreState`},f=c===c;function g(e,t,o){return t||(f?p[o]:`${m}${e}.${o}`)}function v(e,t){let o=arguments.length>2&&void 0!==arguments[2]?arguments[2]:{},r=arguments.length>3?arguments[3]:void 0;const s=(0,n.useContext)(h().a),a=g(d(),o.debugName||r,"useComputedStoreInstance");(0,n.useDebugValue)(a);const c=(0,n.useCallback)(e,t),l=(0,n.useRef)();l.current||(null==s||s.forEach((e=>1===e.version&&e.activate())),l.current=new(u().ComputedStore)(c,{...o,debugName:a,source:"useComputedStore"}),null==s||s.forEach((e=>1===e.version&&e.deactivate())),l.current.addListener(u().ZD));const m=l.current;return(0,n.useEffect)((()=>(m.addListener(u().ZD),()=>m.removeListener(u().ZD))),[m]),(0,i()._$)(t,i().MR)&&m.updateStoreInstance(c,Boolean(o.useDeepEqual)),m}function b(e,t){const o=(0,n.useRef)();if(e)return o.current=void 0,e;if(!t)throw new Error("useStore: no store instance, and no store constructor");return o.current||(o.current=new t),o.current}const y={debugName:"UNKNOWN",componentName:"UNKNOWN"};function S(e,t){const o=(0,n.useContext)(h().a),i=d(),c=g(i,t,"useStoreState"),u=(0,l().I)(),m=(0,n.useRef)(!1);(0,n.useLayoutEffect)((()=>{if(!e)return;const t=(0,a().isRecording)()?{debugName:c,componentName:i}:y,n=function(e){s().I.log("useSubscription listener called",{debugName:c,disabled:m.current},e),m.current||u(i,c,e)};return e.addListener(n,{listenerName:i,listenerType:"component"}),s().I.log("useStoreState: addListener",c,e),(0,a().logListenerAdded)(e,t,3),null==o||o.forEach((t=>1===t.version&&t.onAddListener(n,e))),()=>{s().I.log("useStoreState: removeListener",c,e),e.removeListener(n,i),(0,a().logListenerRemoved)(e,t),null==o||o.forEach((t=>1===t.version&&t.onRemoveListener(n,e)))}}),[e,c,i,u,o]),m.current=!0;try{const t=r().e.withListenerIgnored((()=>null==e?void 0:e.getState()));return(0,n.useDebugValue)(t),t}finally{m.current=!1}}function w(e,t){let o=arguments.length>2&&void 0!==arguments[2]?arguments[2]:{},i=arguments.length>3?arguments[3]:void 0;const r=g(d(),i&&o.debugName?`${o.debugName} - ${i}`:o.debugName||i,"useComputedStore");(0,n.useDebugValue)(r);return S(v(e,t,o,r),r)}function C(e,t){let o=arguments.length>2&&void 0!==arguments[2]?arguments[2]:{},i=arguments.length>3?arguments[3]:void 0;const r=g(d(),i&&o.debugName?`${o.debugName} - ${i}`:o.debugName||i,"useComputedStore");(0,n.useDebugValue)(r);const s=v(e,t,o,r);return[S(s,r),s]}function k(e,t){const o=b(e,t);(0,n.useDebugValue)(o.constructor.name);return[S(o),(0,n.useCallback)((e=>"function"==typeof e?o.update(e):o.setState(e)),[o]),o]}},496506:(e,t,o)=>{o.d(t,{ComputedStore:()=>i,ZD:()=>r,g5:()=>s});let n;n=(0,o(624184).zB)(1,o(585556));const i=n.ComputedStore,r=n.NO_OP_SUBSCRIBER,s=(n.getUniqueStoreId,n.USE_COMPREHENSIVE_STORE_MAP_STORAGE_KEY,n.getComputedStoreStats)},507707:(e,t,o)=>{o.d(t,{A:()=>r});const n=2,i=4;function r(e,t){if(!e&&!t)return 0;if(!t)return-1;if(!e)return 1;const o=e.compareDocumentPosition(t);return(o&i)===i?-1:(o&n)===n?1:0}},543906:(e,t,o)=>{o.d(t,{A:()=>i});var n=o(296540);function i(e,t){const o=(0,n.useRef)(!1);(0,n.useEffect)((()=>{!o.current&&t&&(o.current=!0,e())}),[t,e])}},560198:(e,t,o)=>{o.d(t,{Ke:()=>d,VD:()=>l,_u:()=>u,wX:()=>h});var n=o(296540),i=()=>o(46369),r=()=>o(484714),s=()=>o(56366),a=o(474848);const c=(0,n.createContext)(void 0);c.displayName="ThemeContext";const d=c;function l(e){return e.device.prefersDarkInterface?"dark":"light"}const u=window.notionTheme;function h(e){const t=(0,r().v3)(),o=(0,s().K8)((()=>e.mode?e.mode:"system"===u?l(t):u||t.ThemeModeStore.state),[e.mode,t]),d=(0,n.useMemo)((()=>(0,i().O4)({theme:o})),[o]);return(0,a.jsx)(c.Provider,{value:d,children:e.children})}},585556:(e,t,o)=>{o.r(t),o.d(t,{ComputedStore:()=>w,NO_OP_SUBSCRIBER:()=>m,USE_COMPREHENSIVE_STORE_MAP_STORAGE_KEY:()=>p,getComputedStoreStats:()=>S,getUniqueStoreId:()=>h});o(581454);var n=()=>o(496603),i=()=>o(498212),r=()=>o(624919),s=()=>o(134134),a=()=>o(906510),c=()=>o(352246),d=()=>o(427652),l=()=>o(60053);const u=d().jY;function h(e){return(0,i().gB)(e.toString()).substring(0,8)}function m(){}const p="__useComprehensiveStoreMap",f="undefined"!=typeof window&&"true"===localStorage.getItem(p);let g=0,v=0,b=0,y=0;function S(){return{computedStoreCount:g,computedStoreSubscriptionCount:v,computedStoreRecomputesTotal:b,computedStoreRerendersTotal:y}}(0,o(604341).exposeDebugValue)("getComputedStoreStats",S);class w{constructor(e,t){this.debug=!1,this.debugName=void 0,this.computeFn=void 0,this.autoListener=void 0,this.emitter=void 0,this.shouldRecompute=!1,this.useDeepEqual=!1,this.recomputeSchedule=void 0,this.options=void 0,this.handleChange=()=>{if("lazy"===this.recomputeSchedule)this.setShouldRecompute(),o(908006).default.enqueueComputedStoreRecompute((0,o(720665).Zg)(this.performScheduledRecompute,"debugName",this.debugName));else if("eager"===this.recomputeSchedule)this.recomputeState();else if("debounce"===this.recomputeSchedule.type){var e;null===(e=this.recomputeStateDebounced)||void 0===e||e.call(this)}},this.performScheduledRecompute=()=>{this.shouldRecompute&&0!==this.listenerCount()&&this.recomputeState()},this.recomputeState=()=>{this.shouldRecompute=!1;const e=this.lastState;this.listenerCount()>0&&this.autoListener.startListener({incrementCycle:!0});const t={startMs:0,recomputeEndMs:0,comparisonEndMs:0};w.profiling&&(t.startMs=performance.now());const i=this.computeFn();w.profiling&&(t.recomputeEndMs=performance.now()),this.listenerCount()>0&&this.autoListener.stopListener({isEndOfCycle:!0});if(!(this.useDeepEqual?n().n4:o(821062).A)(e,i)){if(c().t.getShouldCollect()&&!this.useDeepEqual&&(0,n().n4)(e,i)){let e=`${this.computeFn.toString().replace(/\s+/g," ").replace(/__WEBPACK_IMPORTED_MODULE_\d+__/g,"").replace("_stores_","").replace(/'["default"]'/g,"").replace('[\\"default\\"]',"")}`;e.length>100&&(e=`${e.slice(0,100)}...`),c().t.add(this.options.debugName,e,i,this.options.silenceRerenderDefender)}(w.debug||this.debug)&&s().I.log("ComputedStore.recomputeState emit",this.debugName,this),b+=1,y+=1,this.lastState=i,this.emit()}else(w.debug||this.debug)&&s().I.log("ComputedStore.recomputeState unchanged",this.debugName,this),b+=1;if(this.debugName.startsWith("ExperimentStore.")||a().S3.increment("ComputedStore.recomputeState",this.debugName),w.profiling){t.comparisonEndMs=performance.now();const e=t.recomputeEndMs-t.startMs,o=t.comparisonEndMs-t.recomputeEndMs,n=w.profilingMap.get(this.debugName);n?w.profilingMap.set(this.debugName,{runs:n.runs+1,totalRecomputeTimeMs:n.totalRecomputeTimeMs+e,totalComparisonTimeMs:n.totalComparisonTimeMs+o}):w.profilingMap.set(this.debugName,{runs:1,totalRecomputeTimeMs:e,totalComparisonTimeMs:o})}},this.computeFn=e,this.options=t,this.debugName=f?`${t.debugName}${u?`.${e.toString().replace(/\s+/g,"").slice(0,100)}.${(0,i().Ay)()}`:""}`:`${t.debugName}${u?`.${h(e)}`:""}`,t.debug&&(this.debug=!0),this.autoListener=new(l().AutoListener)({onChange:this.handleChange,debugName:this.debugName,debug:this.debug,source:"computed-store",observers:t.observers}),this.emitter=new(o(592328).A),this.useDeepEqual=Boolean(t.useDeepEqual),t.recomputeSchedule&&"object"==typeof t.recomputeSchedule&&"debounce"===t.recomputeSchedule.type&&(this.recomputeStateDebounced=(0,n().sg)(this.recomputeState,t.recomputeSchedule.debounceMs,{maxWait:t.recomputeSchedule.maxWait}),this.lastState=t.recomputeSchedule.initialValue),"useComputedStore"!==t.source&&"Component"!==t.source||(this.debugName.startsWith("ExperimentStore.")||a().S3.increment("ComputedStore.constructor",this.debugName),g+=1),this.recomputeSchedule=(null==t?void 0:t.recomputeSchedule)??"lazy"}getState(){return l().AutoListener.logStoreAccess(this,this.getDebugInfo()),this.listenerCount()>0?(this.shouldRecompute&&(this.recomputeStateDebounced??this.recomputeState)(),this.lastState):this.computeFn()}get state(){return this.getState()}addListener(e,t){const o=this.emitter.listenerCount();this.emitter.addListener(e);const n=this.emitter.listenerCount();if(e!==m){const e=n-o;v+=e,u&&t&&e>0&&(0,d().EZ)(this.debugName,t)}0===o&&1===n&&((this.recomputeStateDebounced??this.recomputeState)(),(0,d().nH)(this,"computed-store"))}removeListener(e,t){const o=this.emitter.listenerCount();var n;(this.emitter.removeListener(e),e!==m&&(v-=1,t&&(0,d().m)(this.debugName,t)),1===o&&0===this.emitter.listenerCount())&&(null===(n=this.recomputeStateDebounced)||void 0===n||n.cancel(),"useComputedStore"!==this.options.source&&"Component"!==this.options.source||((0,d().N7)(this.debugName),g-=1),this.autoListener.destroy(this.debugName))}emit(){o(992202).logStoreEmit(this),this.emitter.emit(this)}listenerCount(){return this.emitter.listenerCount()}updateStoreInstance(e,t){const o=Boolean(t);this.useDeepEqual===o&&this.computeFn===e||((w.debug||this.debug)&&s().I.log("ComputedStore.updateStoreInstance changed",this.debugName),this.computeFn=e,this.useDeepEqual=o,this.setShouldRecompute())}setShouldRecompute(){this.shouldRecompute=!0}enqueueRecompute(){this.handleChange()}recompute(){this.recomputeState()}getDebugInfo(){return{hasListeners:this.listenerCount()>0,lastState:this.lastState}}static profileStart(){w.profilingMap.clear(),w.profiling=!0}static profileEnd(){w.profiling=!1;const e=Array.from(w.profilingMap.entries()).sort(((e,t)=>{const o=e[1].totalRecomputeTimeMs+e[1].totalComparisonTimeMs;return t[1].totalRecomputeTimeMs+t[1].totalComparisonTimeMs-o}));console.log("ComputedStore profile",e.map((e=>{let[t,o]=e;return{name:t,...o}})))}static profileFor(e){w.profileStart(),setTimeout((()=>{w.profileEnd()}),e)}}w.debug=!1,w.profiling=!1,w.profilingMap=new Map,(0,r().EX)({canFormat:e=>Boolean(e&&e instanceof w),header(e){const{span:t,object:o,objectSummary:n,CONTAINER_STYLE:i}=r().iY,s=e.lastState;return t(i,o(e,{useDefaultFormatter:!0}),"(",s&&"object"==typeof s?n(s):o(s),")")},hasBody:()=>!1,body:()=>null})},588165:(e,t,o)=>{function n(e){const t={};for(const o of e)t[o.id]=o.customizable&&o.customKeyCombination?o.customKeyCombination:o.defaultKeyCombination;return t}function i(e,t){return n(e)[t]}o.d(t,{a:()=>n,c:()=>i})},588316:(e,t,o)=>{o.d(t,{A:()=>r});var n=o(296540);function i(e){try{return e.matches(":focus-within")}catch(t){return!1}}function r(){const e=(0,n.useRef)(null),t=(0,n.useRef)(null),[o,r]=(0,n.useState)(!1),s=(0,n.useCallback)((()=>{const t=e.current;t&&r(i(t))}),[]),a=(0,n.useCallback)((o=>{if(e.current=o,o){var n;const e=()=>{r(i(o))},s=()=>{r(i(o))};o.addEventListener("focusin",e),o.addEventListener("focusout",s),null===(n=t.current)||void 0===n||n.call(t),t.current=()=>{o.removeEventListener("focusin",e),o.removeEventListener("focusout",s)}}else{var a;null===(a=t.current)||void 0===a||a.call(t),t.current=null}s()}),[s]);return(0,n.useEffect)((()=>()=>{var e;return null===(e=t.current)||void 0===e?void 0:e.call(t)}),[]),(0,n.useEffect)((()=>{if(o)return document.addEventListener("pointerdown",s),document.addEventListener("keydown",s),()=>{document.removeEventListener("pointerdown",s),document.removeEventListener("keydown",s)}}),[o,s]),[a,o]}},590966:(e,t,o)=>{o.r(t),o.d(t,{CurrentUser:()=>n});class n{constructor(e){this._id=void 0,this._loggedInUserIds=void 0,this._adminUserId=void 0,this.args=e,this._id=null==e?void 0:e.id,this._loggedInUserIds=(null==e?void 0:e.loggedInUserIds)||[],this._adminUserId=null==e?void 0:e.adminUserId}get id(){return this._id}get loggedInUserIds(){return this._loggedInUserIds}get adminUserId(){return this._adminUserId}isLoggedIn(){var e;return Boolean(null===(e=this.args)||void 0===e?void 0:e.id)}}},609990:(e,t,o)=>{o.d(t,{X:()=>r});o(581454);var n=o(440961),i=()=>o(496603);class r extends(()=>o(757695))().Store{constructor(){super(...arguments),this.debugPropagation=!1}getInitialState(){return{stack:[]}}getDebugInfoForStackItem(e){const t=this.state.stack[e],o=t.enable?"":" (disabled)";return[`#${e} ${t.debugName}${o}:`,n.findDOMNode(t.listener)]}logDOMNodes(){console.info("The following components are listening for keyboard shortcuts. Shortcuts propagate from the bottom up.");for(let e=0;e<this.state.stack.length;e++)console.info(...this.getDebugInfoForStackItem(e))}logDebugState(){console.info(this.state.stack.map((e=>({shortcuts:i().z7(i().mg(e.shortcuts),i().b0),node:n.findDOMNode(e.listener),debugName:e.debugName,enable:e.enable,listener:e.listener}))))}}},624184:(e,t,o)=>{o.d(t,{PJ:()=>n,zB:()=>i});o(16280);function n(){return!1}function i(e,t){if(1!==e)throw new Error(`Required NOTION_REACTIVITY_VERSION=${e}, but is 1`);return t}globalThis.window},624919:(e,t,o)=>{o.d(t,{EX:()=>p,Iz:()=>s,NK:()=>d,Qq:()=>a,iY:()=>m});o(944114),o(898992),o(354520),o(672577),o(803949),o(581454);var n=()=>o(604341),i=()=>o(763824),r=()=>o(534177);async function s(e){let t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:"",o=arguments.length>2&&void 0!==arguments[2]?arguments[2]:"magenta";const n=e=>e===1/0?3e3:e===-1/0?-3e3:e,r=e.width||(e.right??e.left)-e.left||50,s=e.height||(e.bottom??e.top)-e.top||50,a=window.document.createElement("DIV");a.style.position="absolute",a.style.zIndex="20000",a.style.top=`${n(e.top)}px`,a.style.left=`${n(e.left)}px`,a.style.minWidth=`${n(r)}px`,a.style.minHeight=`${n(s)}px`,a.style.border=`1px solid ${o}`,a.style.background=o,a.style.font="10px monaco",a.style.opacity="0.3",a.style.pointerEvents="none",a.style.userSelect="none",a.textContent=t,window.document.body.appendChild(a),await(0,i().wR)(1e3),a.remove()}function a(e){let t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:0;const o=e[Object.keys(e).find((e=>e.startsWith("__reactInternalInstance$")))];if(!o)return;if(o._currentElement){let e=o._currentElement._owner;for(let o=0;o<t;o++)e=e._currentElement._owner;return e._instance}const n=e=>{let t=e.return;for(;"string"==typeof t.type;)t=t.return;return t};let i=n(o);for(let r=0;r<t;r++)i=n(i);return i.stateNode}function c(e){return"string"==typeof e?[e]:e}function d(e,t){const o=["%c NOTION%c WARNING %c","background: black; color: white;","background: black; color: orange","font-weight: normal",...c(e)];if(t){console.groupCollapsed(...o);for(const e of t)console.log(...c(e));console.trace(),console.groupEnd()}else console.warn(...o)}function l(e){return function(t){for(var o=arguments.length,n=new Array(o>1?o-1:0),i=1;i<o;i++)n[i-1]=arguments[i];return g([e,t,...n.filter(r().O9)])}}(0,n().exposeDebugValue)("debugDrawRect",s),(0,n().exposeDebugValue)("debugReactComponentFromDOMNode",a);const u=Math.floor(20/6);function h(e){return function(t){for(var o=arguments.length,n=new Array(o>1?o-1:0),i=1;i<o;i++)n[i-1]=arguments[i];return m.div({...t,style:`font-weight: bold; font-size: ${12+u*e}px; line-height: 1.2em; `+(t.style??"")},...n)}}const m={CONTAINER_STYLE:{style:"background: hsla(0, 5%, 50%, 0.09); padding: 0px 3px; border-radius: 3px; margin: 2px;"},div:l("div"),span:l("span"),ol:l("ol"),li:l("li"),table:l("table"),tr:l("tr"),td:l("td"),h1:h(1),h2:h(2),h3:h(3),h4:h(4),h5:h(5),h6:h(6),object:(e,t)=>void 0!==e?g(["object",{object:e,config:t}]):"undefined",tiny:e=>e&&"object"==typeof e?Array.isArray(e)?`[…] (${e.length})`:"{…}":m.object(e),objectSummary:function(e){let{asJSON:t}=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{asJSON:!1};const{span:o,tiny:n,object:i}=m;if(!e||"object"!=typeof e)return i(e);if(t)try{const t=JSON.stringify(e),n=t.slice(0,50),r=n.length<t.length?"…":void 0;return o({},i(e)," ",n,r)}catch(c){}if(Array.isArray(e)){const t=e.slice(0,3);return o({},i(e)," [",...t.map(((e,i)=>o({},n(e),i===t.length-1?void 0:", "))),t.length<e.length?", …":void 0,"]")}const r=Object.entries(e),s=r.slice(0,4),a=s.map(((e,t)=>{let[i,r]=e;return o({},i,": ",n(r),t===s.length-1?void 0:", ")}));return o({},i(e)," {",...a,s.length<r.length?", …":void 0,"}")},countUniques(e){if(1===e.length)return m.object(e[0]);const t=new Map;e.forEach((e=>{const o=t.get(e)??0;t.set(e,o+1)}));const o=Array.from(t).map((e=>{let[t,o]=e;return{value:t,count:o}})).sort(((e,t)=>t.count-e.count));return m.autoTable({rows:o,header:!1})},autoTable(e){const{rows:t,header:o}=e,n={style:"vertical-align: top"},i={style:"padding: 2px 0.25em; font-weight: bold; border-bottom: 1px solid #000"},s={style:"white-space: pre; border-bottom: 1px solid #000"},a=m,c=Array.isArray(o)?o:"object"==typeof o?(0,r().uv)(o):(0,r().uv)(t[0]??{}),d=!1!==o?a.tr(n,...c.map((e=>"object"!=typeof o||Array.isArray(o)?a.td(i,String(e)):a.td(i,o[e])))):void 0,l=t.map((e=>a.tr(n,...c.map((t=>a.td(s,a.object(e[t])))))));return 0===l.length&&l.push(a.tr(n,a.td(s,"(no rows)"))),a.table(a.CONTAINER_STYLE,d,...l)},maxHeight(e){const t="number"==typeof e?`${e}px`:e;for(var o=arguments.length,n=new Array(o>1?o-1:0),i=1;i<o;i++)n[i-1]=arguments[i];return m.div({style:`max-height: ${t}; overflow: auto;`},...n)}};function p(e){let t=globalThis.devtoolsFormatters||[];e.id&&(t=t.filter((t=>t.id!==e.id))),t.unshift(function(e){const{canFormat:t,header:o,body:n,hasBody:i,id:r}=e;return{id:r,header(e,n){try{return null!=n&&n.useDefaultFormatter?null:t(e)?o(e,n):null}catch(i){throw console.error("DevTools Formatter.header() error",i),i}},hasBody(e,o){try{return(null==o||!o.useDefaultFormatter)&&!!t(e)&&i(e,o)}catch(n){throw console.error("DevTools Formatter.hasBody() error",n),n}},body(e,t){try{return n(e,t)}catch(o){throw console.error("DevTools Formatter.body() error",o),o}}}}(e)),globalThis.devtoolsFormatters=t}const f=Symbol("DevToolsHyperscriptPassthrough");function g(e){const t=e;return t&&(t[f]=!0),t}p({canFormat:e=>Boolean(e&&"object"==typeof e&&f in e),header:e=>e,hasBody:()=>!1,body:()=>null});p({canFormat:e=>Boolean(e&&e instanceof o(244641).c9),header(e){const{span:t,CONTAINER_STYLE:o}=m;return t(o,t({},e.toFormat("yyyy-MM-dd HH:mm:ss.SSS (ZZ)")))},hasBody:()=>!0,body(e){const{table:t,tr:o,td:n,CONTAINER_STYLE:i}=m;return t(i,o({},n({},"time zone:"),n({},e.zoneName)),o({},n({},"relative:"),n({},e.toRelative({round:!1})??"")),o({},n({},"ISO:"),n({},e.toISO())),o({},n({},"since epoch (ms):"),n({},e.toMillis().toString())))}})},628182:(e,t,o)=>{o.d(t,{U4:()=>d,nJ:()=>l,s1:()=>a,uF:()=>c});let n;function i(e){if(void 0!==n)return n;const t="undefined"!=typeof window&&void 0!==window.CSS&&"function"==typeof window.CSS.supports&&window.CSS.supports("padding-top","env(safe-area-inset-top)"),o=e.isIOS&&e.isMobileBrowser&&!e.isSafari;return n=t&&!o,n}function r(e,t){return i(e)?parseInt(window.getComputedStyle(document.documentElement).getPropertyValue(t).replace("px",""),10):0}var s=()=>o(757695);const a=44,c=52,d=s().Store.createValue({supportsNativeSafeAreaConfig:!1,top:0,bottom:0,left:0,right:0});class l extends s().Store{constructor(e,t){if(super(),this.environment=void 0,this.fullWindowEl=void 0,this.window=void 0,this.visibilityChangedResizeDeadline=0,this.updateWindowSize=()=>{this.setState(this.getCurrentState()),this.visibilityChangedResizeDeadline=0},this.updateWindowSizeDebounced=o(496603).sg(this.updateWindowSize,300),this.window=t,this.environment=e,this.environment.device.isMobileBrowser){const e=this.window.document.createElement("div");e.style.position="fixed",e.style.height="100vh",e.style.top="0px",e.style.pointerEvents="none",this.window.document.body.appendChild(e),this.fullWindowEl=e}this.window.document.addEventListener("visibilitychange",(()=>{this.visibilityChangedResizeDeadline=Date.now()+200})),this.window.addEventListener("resize",(()=>{if(0===this.instanceState.width||this.visibilityChangedResizeDeadline&&Date.now()<this.visibilityChangedResizeDeadline)return this.updateWindowSize(),void this.updateWindowSizeDebounced.cancel();this.updateWindowSizeDebounced()})),this.updateWindowSize()}getSafePaddingTopCSS(e){return`calc(${e}px + ${this.state.paddingTopCSS})`}getSafePaddingLeftCSS(e){return`calc(${e}px + ${this.state.paddingLeftCSS})`}getSafePaddingRightCSS(e){return`calc(${e}px + ${this.state.paddingRightCSS})`}getSafePaddingBottomCSS(e){return`calc(${e}px + ${this.state.paddingBottomCSS})`}getSafePaddingTopPx(e){return e+this.state.paddingTop}getSafePaddingLeftPx(e){return e+this.state.paddingLeft}getSafePaddingRightPx(e){return e+this.state.paddingRight}getSafePaddingBottomPx(e){return e+this.state.paddingBottom}isLandscape(){return this.state.width===this.state.largestDimension}isPortrait(){return!this.isLandscape()}getCurrentState(){const{device:e}=this.environment,t=i(e),o=e.isIOS?this.window.document.documentElement.clientWidth:this.window.innerWidth,n=e.isIOS?this.window.document.documentElement.clientHeight:this.window.innerHeight,s=this.state&&this.state.largestDimension?this.state.largestDimension:0,a=Math.max(this.window.innerWidth,this.window.innerHeight,s);let c,l,u,h,m,p,f,g;if(e.isAndroid&&e.isMobileNative&&d.state.supportsNativeSafeAreaConfig){const{top:e,bottom:t,left:o,right:n}=d.state;c=e,l=o,u=n,h=t,m=`${e}px`,p=`${o}px`,f=`${n}px`,g=`${t}px`}else c=function(e){return r(e,"--safe-area-inset-top")}(e),l=function(e){return r(e,"--safe-area-inset-left")}(e),u=function(e){return r(e,"--safe-area-inset-right")}(e),h=function(e){return r(e,"--safe-area-inset-bottom")}(e),m=t?"env(safe-area-inset-top)":"0px",p=t?"env(safe-area-inset-left)":"0px",f=t?"env(safe-area-inset-right)":"0px",g=t?"env(safe-area-inset-bottom)":"0px";if(e.isMobileBrowser){this.window.document.body.style.height=`${window.innerHeight}px`;const e=this.window.document.querySelector("html");e&&(e.style.height=`${window.innerHeight}px`);const t=this.window.document.querySelector("body");t&&(t.style.height=`${window.innerHeight}px`);const o=this.window.document.querySelector("#notion-app");o&&o instanceof HTMLElement&&(o.style.height=`${window.innerHeight}px`)}return document.documentElement.style.setProperty("--full-viewport-height",`${n}px`),{width:o,height:n,paddingLeft:l,paddingTop:c,paddingBottom:h,paddingRight:u,paddingTopCSS:m,paddingLeftCSS:p,paddingRightCSS:f,paddingBottomCSS:g,largestDimension:a}}}},634782:(e,t,o)=>{o.d(t,{Q:()=>r,z:()=>i});o(517642),o(658004),o(733853),o(845876),o(432475),o(515024),o(731698);var n=()=>o(757695);class i extends n().Store{constructor(){super(...arguments),this.containerDomNodeStore=n().Store.createValue(null,{name:"containerDomNodeStore"})}getInitialState(){return{overlays:new Set}}}const r=new i},662303:(e,t,o)=>{o.d(t,{D:()=>s,J:()=>a,locale:()=>c});const n={locale:o(402390).q,messages:{},routes:{}};function i(){const e=window.LOCALE_SETUP;return e?{value:e}:{error:!0}}let r=n;if("undefined"!=typeof window&&"undefined"!=typeof navigator){const e=Boolean(window.__isElectron),t=/ReactNative/.test(navigator.userAgent)||/MobileNative/.test(navigator.userAgent);if(t&&/WebKit/.test(navigator.userAgent)){const e=i();e.error||(r=e.value)}else if(t){const e=function(){const e=i();if(e.error)return e;const t="ko"===e.value.locale.split("-")[0];for(const o of window.navigator.languages){const i=o.split("-")[0];if(t){if("en"===i)return{value:n};if("ko"===i)return e}if(e.value.locale.toLowerCase()===o.toLowerCase())return e}for(const o of window.navigator.languages){const t=o.split("-")[0];if(e.value.locale.split("-")[0].toLowerCase()===t.toLowerCase())return e}return{error:!0}}();e.error||(r=e.value)}else if(e){const e=function(e,t){if(e.error)return{value:n};if(t===e.value.locale)return e;if("en-US"===t)return{value:n};for(const o of window.navigator.languages){const t=o.split("-")[0];if("en"===t)return{value:n};if(e.value.locale.split("-")[0]===t)return e}return{error:!0}}(i(),o(994310).A.get("preferredLocale"));e.error||(r=e.value)}else{const e=i();e.error||(r=e.value)}}const s=r.messages,a=r.routes,c=r.locale},672993:(e,t,o)=>{o.r(t),o.d(t,{Store:()=>a});o(16280),o(814628);var n=()=>o(624919),i=()=>o(992202),r=()=>o(427652);const s=r().jY;class a{constructor(e){this.debug=!1,this.instanceState=void 0,this.emitter=new(o(592328).A),this.debugName=void 0,this.instanceState=this.getInitialState(),i().logStoreCreated(this,this.instanceState),this.debugName=e??this.constructor.name,(0,r().nH)(this,"store")}getState(){return o(60053).AutoListener.logStoreAccess(this,this.instanceState),this.instanceState}get state(){return this.getState()}setState(e){(0,o(821062).A)(this.instanceState,e)||(this.instanceState=e,i().logStoreSet(this,e),this.emit())}reset(){this.setState(this.getInitialState())}update(e){this.setState(e(this.state))}emit(){i().logStoreEmit(this),this.debug&&(console.groupCollapsed("emit:",this),console.log("store state:",this.instanceState),console.trace(),console.groupEnd()),this.emitter.emit(this)}addListener(e,t){const o=s?this.emitter.listenerCount():0;if(this.emitter.addListener(e),s){const e=this.emitter.listenerCount();t&&e-o>0&&this.debugName&&(0,r().EZ)(this.debugName,t)}}removeListener(e,t){this.emitter.removeListener(e),s&&t&&this.debugName&&(0,r().m)(this.debugName,t)}listenerCount(){return this.emitter.listenerCount()}waitUntil(e,t){const{signal:o,timeout:n}=t??{};if(null!=o&&o.aborted)return Promise.reject(o.reason);if(e())return Promise.resolve(void 0);let i;const{promise:r,resolve:s,reject:a}=Promise.withResolvers(),c=()=>{clearTimeout(i),null==o||o.removeEventListener("abort",d),this.removeListener(u)},d=()=>{c(),a(null==o?void 0:o.reason)},l=()=>{c(),a(new Error("Timeout"))},u=()=>{e()&&(c(),s(void 0))};return n&&(i=window.setTimeout(l,n)),this.addListener(u),null==o||o.addEventListener("abort",d,{once:!0}),r}getInitialState(){return{}}static createValue(e,t){return new(a.createClass(e,t))}static createClass(e,t){const o=class extends a{constructor(){super((null==t?void 0:t.name)||"StoreWithInitialState"),this.debug=Boolean(null==t?void 0:t.debug)}getInitialState(){return e instanceof Function?e():e}};return null!=t&&t.name&&Object.defineProperty(o,"name",{value:t.name}),o}}a.debug=!0,(0,n().EX)({canFormat:e=>Boolean(e&&e instanceof a),header(e){const{span:t,object:o,objectSummary:i,CONTAINER_STYLE:r}=n().iY,s=e.instanceState;return t(r,o(e,{useDefaultFormatter:!0}),"(",s&&"object"==typeof s?i(s):o(s),")")},hasBody:()=>!1,body:()=>null})},711740:(e,t,o)=>{o.d(t,{L:()=>n});class n{constructor(e){this.inMemoryRecordCache=void 0,this._persistedRecordCache=void 0,this.inMemoryRecordCache=e.inMemoryRecordCache,this._persistedRecordCache=e.persistedRecordCache}get persistedRecordCache(){return this._persistedRecordCache}disablePersistedRecordCache(){this._persistedRecordCache=void 0}}},721338:(e,t,o)=>{o.d(t,{P:()=>n});class n extends(()=>o(757695))().Store{getInitialState(){return{isComposing:!1}}}},721908:(e,t,o)=>{o.d(t,{d:()=>s});o(16280);var n=o(296540),i=()=>o(496603),r=()=>o(355543);function s(e,t){const o=(0,n.useRef)(e),s=(0,r().w)((()=>({fn:i().nF((function(){return o.current(...arguments)}),t),wait:t})));if((0,n.useEffect)((()=>{o.current=e}),[e]),s.wait!==t)throw new Error(`You must never change debounce wait (initial=${s.wait}, attempted=${t})`);return s.fn}},724200:(e,t,o)=>{o.d(t,{D:()=>r,j:()=>i});o(16280),o(517642),o(658004),o(733853),o(845876),o(432475),o(515024),o(731698),o(898992),o(803949);let n;{const e=(0,o(624184).zB)(1,o(60053));n={version:1,ReactivityObserver:e.AutoListenerObserver,ReactivityObserverList:e.AutoListenerObserverList}}function i(e,t){return new n.ReactivityObserver(e,t)}function r(){const e=new Set;for(var t=arguments.length,o=new Array(t),i=0;i<t;i++)o[i]=arguments[i];for(const s of o)if(void 0!==s)if(s instanceof n.ReactivityObserver)e.add(s);else{if(!(s instanceof n.ReactivityObserverList))throw new Error(`Invalid ReactivityObserver list item: ${s.constructor.name}`);s.forEach((t=>e.add(t)))}const r=Array.from(e);return new n.ReactivityObserverList(r)}},725252:(e,t,o)=>{o.d(t,{L:()=>i});o(16280);var n=o(296540);function i(e){const{value:t,validateLoaded:o}=e,i=e.waitTimeInMs||500,[r,s]=(0,n.useState)(!0),a=(0,n.useRef)(),c=void 0!==t&&(!o||r&&o(t));return(0,n.useEffect)((()=>(a.current=window.setTimeout((()=>{s(!1),a.current=void 0}),i),()=>{a.current&&(clearTimeout(a.current),a.current=void 0)})),[i]),(0,n.useEffect)((()=>{c&&a.current&&(s(!1),clearTimeout(a.current),a.current=void 0)}),[c]),void 0!==t&&c?{status:"resolved",value:t}:r?{status:"pending",value:void 0}:{status:"rejected",value:void 0,error:new Error("Reached end of timeout before value became defined.")}}},726637:(e,t,o)=>{o.d(t,{A:()=>r});o(16280);const n=window.CONFIG_OVERRIDE??{env:"production",isAdminMode:!1,isDevelopingInAirplaneMode:!1,isLocalhost:!1,offline:!0,version:"23.13.0.4461",buildTarget:"client",domainBaseUrl:"https://www.notion.so",adminUrl:"https://admin.notion.so",publicDomainName:"notion.site",protocol:"notion",staticS3:{url:"https://prod-notion-assets.s3-us-west-2.amazonaws.com",bucket:"prod-notion-assets"},lastUpdatedTime:1754395455842,api:{http:"/api/v3"},googleOAuth:{clientId:"905154081809-858sm3f0qnalqd9d44d9gecjtrdji9tf.apps.googleusercontent.com"},messageStore:{url:"https://msgstore.www.notion.so",api:"/api/v1"},audioProcessor:{url:"https://audioprocessor.www.notion.so",api:"/api/v1"},stripe:{key:"pk_live_vuNO27XGTCbXjVwneiECILjT"},calendar:{domainBaseUrl:"https://calendar.notion.so",notionAuthUrl:"https://calendar.notion.so/notion-auth",openNotionDatabaseUrl:"https://calendar.notion.so/open-notion-database",createEventUrl:"https://calendar.notion.so/event/create",calendarSettingsUrl:"https://calendar.notion.so/settings/calendars",calendarMeetWithUrl:"https://calendar.notion.so/meet-with",desktopProtocol:"cron",downloadUrl:"https://www.notion.so/calendar/download"},zoom:{desktopProtocol:"zoommtg"},cron:{domainBaseUrl:"https://calendar.cron.com"},mail:{apiBaseUrl:"https://api.mail.notion.so",domainBaseUrl:"https://mail.notion.so",protocol:"notionmail"},identity:{domainBaseUrl:"https://identity.notion.so"},revenueCat:{apiResponseMaxAge:6048e5,entitlementIDs:{personal:"notion.id.personal_pro"},productIDs:{personal:{monthly:"notion.id.personal_pro_monthly",yearly:"notion.id.personal_pro_yearly"}}},mutiny:{personalKey:"1149e901f65fc47c"},partnerStack:{apiKey:"pk_6nwYfqCKEoPt2lTuU8Veswm2zArJ3Apq"},pricing:{invoiceDaysUntilDue:30,free:{spaceBlockLimit:1e3,fileUploadMaxBytes:5e6},team_free:{spaceBlockLimit:1e3,fileUploadMaxBytes:5e6},personal_free:{fileUploadMaxBytes:5e6},student:{productId:"prod_FhChFoDp7gS1Ba"},personal:{productId:"prod_CpavZFCbxF2YGx",monthlyPrice:500,yearlyPrice:4800},plus:{productId:"prod_CpawK4ih14xs4t",monthlyPricePerMember:1e3,yearlyPricePerMember:9600},business:{productId:"prod_LEnFERYcTgENz8",monthlyPricePerMember:1800,yearlyPricePerMember:18e3},enterprise:{productId:"prod_Cpb8M1AFEFhdy1",monthlyPricePerMember:2500,yearlyPricePerMember:24e3},ai:{productId:"prod_N6tyEr9FFSTXJo",monthlyPricePerMember:1e3,yearlyPricePerMember:9600},sites_custom_hostnames:{productId:"prod_Q71OevO5uJ7LaT"}},promotions:{ai:{writer:{baseGrant:{singlePlayerAmount:10,multiplayerAmount:10,unit:"responses"},userGrant:{singlePlayerAmount:10,multiplayerAmount:10,unit:"responses"},grant032023:{singlePlayerAmount:20,multiplayerAmount:20,unit:"responses",waitMs:6048e5},studentGrant:{singlePlayerAmount:50,multiplayerAmount:50,unit:"responses"},studentGitHubGrant:{singlePlayerAmount:50,multiplayerAmount:50,unit:"responses"},maxAllowance:{free:500,paid:500}},qna:{baseGrant:{singlePlayerAmount:10,multiplayerAmount:10,unit:"responses"},userGrant:{singlePlayerAmount:10,multiplayerAmount:10,unit:"responses"},grant032023:{singlePlayerAmount:20,multiplayerAmount:10,unit:"responses",waitMs:6048e5},studentGrant:{singlePlayerAmount:50,multiplayerAmount:50,unit:"responses"},studentGitHubGrant:{singlePlayerAmount:50,multiplayerAmount:50,unit:"responses"},maxAllowance:{free:100,paid:500}}}},desktopS3:{url:"https://s3-us-west-2.amazonaws.com/desktop-release.notion-static.com"},publicFileS3:{url:"https://s3-us-west-2.amazonaws.com/public.notion-static.com",bucket:"public.notion-static.com"},secureFileConfig:{rootPath:"/f",protocol:"https",hostname:"file.notion.so"},loggly:{token:"9b01b08e-c969-4e27-837c-805d1fc6ec7b"},splunk:{token:"EA76605A-F565-4B17-A496-34435622A1EB"},embedly:{key:"421626497c5d4fc2ae6b075189d602a2"},iframely:{key:"222a85036317ca50d3ba5f321bfda6f0"},iframely_prod:{key:"656ac74fac4fff346b811dca7919d483"},aif:{url:"https://aif.notion.so/aif-production.html"},contentful:{spaceId:"spoqsaf9291f"},iOSAppId:1232780281,facebook:{pixelId:"499229960464487"},statsig:{apiKey:"client-Tgza5wNFa8dVt9BdeUfG6Vkm29bHxX10MhoztTMzLBB",localEvalSdkKey:"client-NmJQdieE6QZZ0dN5Eq9MWBIUPexKaCd7pAkr5RezFpY"},googleReCaptcha:{siteKey:"6LcvqigfAAAAAPaPL3j2YLldFcZVGwKvG9TmjDgK"},turnstile:{sitekey:"0x4AAAAAAADLq8YYJOHc6qqw"},google:{clientId:"905154081809-858sm3f0qnalqd9d44d9gecjtrdji9tf.apps.googleusercontent.com",mapsApiKey:"AIzaSyB543mcD0Ehv18H5e0iD8L-J2lyN7AvKCo"},sprig:{environmentId:"2HKBN1wgCwHr"},front:{domainBaseUrl:"https://www.notion.com"},imageProxy:{baseUrl:"https://img.notionusercontent.com/"}};if(!n)throw new Error("CONFIG not found");window.CONFIG=n;const i=(0,o(140583).E)(window,window.navigator.userAgent);n.isMobile=i.isMobile();const r=n},757695:(e,t,o)=>{o.d(t,{Store:()=>i});let n;n=(0,o(624184).zB)(1,o(672993));const i=n.Store},790748:(e,t,o)=>{o.d(t,{b:()=>i});var n=o(296540);function i(e,t,o){const i=(0,n.useMemo)(t,o);(0,n.useLayoutEffect)((()=>null==e?void 0:e.register(i)),[i,e])}},792485:(e,t,o)=>{o.d(t,{S:()=>r});var n=o(296540),i=()=>o(452446);function r(){return(0,n.useContext)(i().r)}},804773:(e,t,o)=>{o.d(t,{Y:()=>a,e:()=>s});o(944114),o(517642),o(658004),o(733853),o(845876),o(432475),o(515024),o(731698),o(898992),o(803949);var n=o(296540),i=o(474848);const r=(0,n.createContext)(void 0);function s(){const e=(0,n.useContext)(r),t=(0,n.useRef)(null),o=(0,n.useRef)(null),i=(0,n.useCallback)((t=>{if(!e)return;const{itemRefs:o,activeRef:n,setActiveRef:i,direction:r}=e;if("vertical"===r&&"ArrowUp"!==t.key&&"ArrowDown"!==t.key&&"Home"!==t.key&&"End"!==t.key||"horizontal"===r&&"ArrowLeft"!==t.key&&"ArrowRight"!==t.key&&"Home"!==t.key&&"End"!==t.key)return;const s=o.current;if(null===s||void 0===n)return;const{sortedElements:a,nodeToRef:c}=function(e){const t=[],o=new Map;return e.forEach((e=>{e.current&&"true"!==e.current.getAttribute("aria-disabled")&&"true"!==e.current.getAttribute("disabled")&&(t.push(e.current),o.set(e.current,e))})),{sortedElements:t.sort(((e,t)=>e.compareDocumentPosition(t)&Node.DOCUMENT_POSITION_FOLLOWING?-1:1)),nodeToRef:o}}(s),d=a.findIndex((e=>e===n.current));let l=d;-1===d?"vertical"===r&&"ArrowDown"===t.key||"horizontal"===r&&"ArrowRight"===t.key?l=0:("vertical"===r&&"ArrowUp"===t.key||"horizontal"===r&&"ArrowLeft"===t.key)&&(l=a.length-1):"vertical"===r&&"ArrowDown"===t.key||"horizontal"===r&&"ArrowRight"===t.key?l=(d+1)%a.length:"vertical"===r&&"ArrowUp"===t.key||"horizontal"===r&&"ArrowLeft"===t.key?l=(d-1+a.length)%a.length:"Home"===t.key?l=0:"End"===t.key&&(l=a.length-1);const u=a[l],h=c.get(u);var m;h&&(i(h),null===(m=h.current)||void 0===m||m.focus(),t.preventDefault())}),[e]),s=(0,n.useCallback)((()=>{var o;if(!e)return;const{itemRefs:n,setActiveRef:i}=e;null!==(o=n.current)&&void 0!==o&&o.has(t)&&i(t)}),[e]),a=(0,n.useCallback)((n=>{if(t.current=n,o.current&&(window.cancelAnimationFrame(o.current),o.current=null),!e)return;const{itemRefs:i,activeRef:r,setActiveRef:s}=e;var a,c;n?null===(a=i.current)||void 0===a||a.add(t):(null===(c=i.current)||void 0===c||c.delete(t),void 0!==r&&r===t&&(o.current=window.requestAnimationFrame((()=>{o.current=null,void 0===r||r.current&&document.contains(r.current)||s(void 0)}))))}),[e]);return{isTabbable:!e||(e.activeRef===t||void 0===e.activeRef),itemRef:a,onKeyDown:i,onFocus:s}}function a(e){let{direction:t,children:o}=e;const[s,a]=(0,n.useState)(void 0),c=(0,n.useRef)(new Set),d=(0,n.useMemo)((()=>({itemRefs:c,activeRef:s,setActiveRef:a,direction:t})),[s,t]);return(0,i.jsx)(r.Provider,{value:d,children:o})}r.displayName="FocusNavigatorContext"},807005:(e,t,o)=>{o.d(t,{Z:()=>r,v:()=>i});const n=(0,o(296540).createContext)(void 0);n.displayName="RestrictedContentContext";const i=n.Provider,r=n},814865:(e,t,o)=>{o.d(t,{mc:()=>r});class n extends(()=>o(757695))().Store{getInitialState(){return{initialized:!1,environment:void 0,shortcuts:[]}}}let i;const r=(i||(i=new n,(0,o(604341).exposeDebugValue)("keyboardShortcutsStore",i)),i)},819121:(e,t,o)=>{o.d(t,{A:()=>s,p:()=>r});var n=o(296540);let i;function r(e){try{return function(){if(void 0===i)try{i=!(!CSS||!CSS.supports)&&CSS.supports("selector(:focus-visible)")}catch{i=!1}return i}()?e.matches(":focus-visible"):e.matches(":focus")}catch(t){return!1}}function s(){const e=(0,n.useRef)(null),t=(0,n.useRef)(null),[o,i]=(0,n.useState)(!1),[s,a]=(0,n.useState)(!1),c=(0,n.useCallback)((()=>{const t=e.current;t&&a(r(t))}),[]),d=(0,n.useCallback)((o=>{if(e.current=o,o){var n;const e=()=>{i(!0),a(r(o))},s=()=>{i(!1),a(r(o))};o.addEventListener("focus",e),o.addEventListener("blur",s),null===(n=t.current)||void 0===n||n.call(t),t.current=()=>{o.removeEventListener("focus",e),o.removeEventListener("blur",s)}}else{var s;null===(s=t.current)||void 0===s||s.call(t),t.current=null}c()}),[c]);return(0,n.useEffect)((()=>()=>{var e;return null===(e=t.current)||void 0===e?void 0:e.call(t)}),[]),(0,n.useEffect)((()=>{if(o)return document.addEventListener("pointerdown",c),document.addEventListener("keydown",c),()=>{document.removeEventListener("pointerdown",c),document.removeEventListener("keydown",c)}}),[o,c]),[d,s]}},833744:(e,t,o)=>{o.d(t,{L:()=>i});var n=o(296540);function i(e){let{closeHandler:t,ref:o,active:i,excludedSelectors:r,ignoreKeydown:s}=e;const a=(0,n.useRef)(void 0);(0,n.useEffect)((()=>{if(i){const e=e=>{0===e.button&&e.target instanceof Node?a.current=e.target:a.current=void 0},n=e=>{const n=o.current,i=e.target,s=a.current;if(a.current=void 0,n&&i){for(const e of r||[]){const t=document.querySelectorAll(e);for(let e=0;e<t.length;++e){if(t[e].contains(i))return}}s&&(n===s||n.contains(s))||n===i||n.contains(i)||t()}},i=e=>{s||"Esc"!==e.key&&"Escape"!==e.key||t()};return window.addEventListener("mousedown",e),window.addEventListener("click",n),window.addEventListener("keydown",i),()=>{window.removeEventListener("mousedown",e),window.removeEventListener("click",n),window.removeEventListener("keydown",i)}}}),[t,o,i,r,s])}},841666:(e,t,o)=>{o.d(t,{Y:()=>a});var n=()=>o(496603),i=()=>o(662303);const r="¥",s="\\";function a(e){return[{id:"enter",description:"Insert line of text",defaultKeyCombination:["enter"]},{id:"shiftEnter",description:"Line break within text block",defaultKeyCombination:["shift+enter"]},{id:"commandEnter",description:"Execute command or confirm action",defaultKeyCombination:["command+enter"],visibleToUsers:!1},{id:"commandShiftEnter",description:"Execute command with shift modifier",defaultKeyCombination:["command+shift+enter"],visibleToUsers:!1},{id:"commandSlash",description:"Edit/change selected blocks",defaultKeyCombination:["command+/"]},{id:"toggleKeyboardShortcutsModalVisibility",description:"Show keyboard shortcuts help",defaultKeyCombination:["command+alt+/"]},{id:"commandS",description:"Save or sync changes",defaultKeyCombination:["command+S"],visibleToUsers:!1},{id:"commandJ",description:"Open AI command search",defaultKeyCombination:["command+j"],visibleToUsers:!1},{id:"createAIChatThread",description:"Create new AI chat thread",defaultKeyCombination:["command+shift+;"],visibleToUsers:!1},{id:"delete",description:"Delete selected content",defaultKeyCombination:n().oE(["delete",e.isApple?"ctrl+d":void 0])},{id:"deleteToEndOfLine",description:"Delete from cursor to end of line",defaultKeyCombination:n().oE([e.isApple?"ctrl+k":void 0]),visibleToUsers:!1},{id:"deleteNextWord",description:"Delete next word",defaultKeyCombination:n().oE([e.isWindows?"ctrl+delete":void 0]),visibleToUsers:!1},{id:"space",description:"Insert space character",defaultKeyCombination:["space"]},{id:"backspace",description:"Delete selected blocks",defaultKeyCombination:n().oE(["backspace","shift+backspace",e.isWindows?void 0:"alt+backspace","command+backspace","ctrl+backspace"])},{id:"esc",description:"Select current block",defaultKeyCombination:["esc"]},{id:"left",description:"Move cursor left or select different block",defaultKeyCombination:n().oE(["left","shift+left","command+shift+left",e.isWindows||e.isLinux?"ctrl+left":void 0,"ctrl+shift+left","alt+left","alt+shift+left"])},{id:"right",description:"Move cursor right or select different block",defaultKeyCombination:n().oE(["right","shift+right","command+shift+right",e.isWindows||e.isLinux?"ctrl+right":void 0,"ctrl+shift+right","alt+right","alt+shift+right"])},{id:"up",description:"Move cursor up or select different block",defaultKeyCombination:n().oE(["up","shift+up","alt+up","alt+shift+up","command+up",e.isApple?"ctrl+p":void 0])},{id:"down",description:"Move cursor down or select different block",defaultKeyCombination:n().oE(["down","shift+down","alt+down","alt+shift+down","command+down",e.isApple?"ctrl+n":void 0])},{id:"moveUp",description:"Move selected block up",defaultKeyCombination:["command+shift+up"]},{id:"moveDown",description:"Move selected block down",defaultKeyCombination:["command+shift+down"]},{id:"peekUp",description:"Peek at content above",defaultKeyCombination:[e.isWindows||e.isLinux?"alt+k":"ctrl+shift+k"]},{id:"peekDown",description:"Peek at content below",defaultKeyCombination:[e.isWindows||e.isLinux?"alt+j":"ctrl+shift+j"]},{id:"untab",description:"Un-indent content",defaultKeyCombination:["shift+tab"]},{id:"tab",description:"Indent content",defaultKeyCombination:["tab"]},{id:"ungroup",description:"Ungroup selected blocks",defaultKeyCombination:["command+shift+g"],visibleToUsers:!1},{id:"group",description:"Group selected blocks",defaultKeyCombination:["command+g"],visibleToUsers:!1},{id:"home",description:"Go to beginning of line",defaultKeyCombination:["home","shift+home"],visibleToUsers:!1},{id:"end",description:"Go to end of line",defaultKeyCombination:["end","shift+end"],visibleToUsers:!1},{id:"pageUp",description:"Scroll up one page",defaultKeyCombination:["code:PageUp","shift+code:PageUp"],visibleToUsers:!1},{id:"pageDown",description:"Scroll down one page",defaultKeyCombination:["code:PageDown","shift+code:PageDown"],visibleToUsers:!1},{id:"selectAll",description:"Select block with cursor",defaultKeyCombination:["command+a"]},{id:"redo",description:"Redo last action",defaultKeyCombination:["command+shift+z","command+y"],visibleToUsers:!1},{id:"undo",description:"Undo last action",defaultKeyCombination:n().oE(["command+z",e.isWindows?"alt+backspace":void 0]),visibleToUsers:!1},{id:"toggleUnderline",description:"Underline selected text",defaultKeyCombination:["command+u"]},{id:"toggleHighlight",description:"Highlight selected text",defaultKeyCombination:["command+shift+h"]},{id:"toggleBold",description:"Bold selected text",defaultKeyCombination:["command+b"]},{id:"toggleItalics",description:"Italic selected text",defaultKeyCombination:["command+i"]},{id:"toggleCode",description:"Create inline code",defaultKeyCombination:["command+e"]},{id:"toggleStrike",description:"Strikethrough selected text",defaultKeyCombination:["command+shift+x","command+shift+s"]},{id:"duplicate",description:"Duplicate selected blocks",defaultKeyCombination:["command+d"]},{id:"duplicateSchema",description:"Duplicate database schema",defaultKeyCombination:["command+shift+d"],visibleToUsers:!1},{id:"fillRight",description:"Fill cells right in table",defaultKeyCombination:["command+r"],visibleToUsers:!1},{id:"cut",description:"Cut selected content",defaultKeyCombination:["command+x"],visibleToUsers:!1},{id:"copy",description:"Copy selected content",defaultKeyCombination:["command+c"],visibleToUsers:!1},{id:"paste",description:"Paste content",defaultKeyCombination:["command+v"],visibleToUsers:!1},{id:"openLinkMenuOrOpenSearch",description:"Add link to selected text",defaultKeyCombination:["command+k"]},{id:"toggleInPageFindReplace",description:"Find and replace in current page",defaultKeyCombination:["command+alt+f"],visibleToUsers:!1},{id:"openEquationMenu",description:"Add TeX equation",defaultKeyCombination:["command+shift+e"],visibleToUsers:!1},{id:"goBack",description:"Go back a page",defaultKeyCombination:["command+["]},{id:"goForward",description:"Go forward a page",defaultKeyCombination:["command+]"]},{id:"newTab",description:"Create a new Notion tab",defaultKeyCombination:["command+t"]},{id:"commandLeft",description:"Go to beginning of line",defaultKeyCombination:["command+left"],visibleToUsers:!1},{id:"commandRight",description:"Go to end of line",defaultKeyCombination:["command+right"],visibleToUsers:!1},{id:"goUp",description:"Go to parent page",defaultKeyCombination:["command+shift+u"]},{id:"quickFind",description:"Open search or jump to a recently viewed page",defaultKeyCombination:["command+p"]},{id:"search",description:"Find in current page",defaultKeyCombination:["command+f"]},{id:"goToBeginningOfLine",description:"Go to beginning of line",defaultKeyCombination:["ctrl+a","ctrl+shift+a"],visibleToUsers:!1},{id:"goToEndOfLine",description:"Go to end of line",defaultKeyCombination:["ctrl+e","ctrl+shift+e"],visibleToUsers:!1},{id:"goForwardOneChar",description:"Move forward one character",defaultKeyCombination:["ctrl+f","ctrl+shift+f"],visibleToUsers:!1},{id:"goBackwardOneChar",description:"Move backward one character",defaultKeyCombination:["ctrl+b","ctrl+shift+b"],visibleToUsers:!1},{id:"comment",description:"Create comment",defaultKeyCombination:["command+shift+m"]},{id:"suggest",description:"Suggest changes",defaultKeyCombination:["command+shift+alt+x"],visibleToUsers:!1},{id:"caption",description:"Add caption to media",defaultKeyCombination:["command+alt+m"],visibleToUsers:!1},{id:"react",description:"Add reaction",defaultKeyCombination:["command+alt+r"],visibleToUsers:!1},{id:"rename",description:"Rename current page or block",defaultKeyCombination:["command+shift+r"],visibleToUsers:!1},{id:"copyLinkToCurrentPage",description:"Copy page URL",defaultKeyCombination:["command+l"]},{id:"copyLinkToPageInCommandSearch",description:"Copy link to page from command search",defaultKeyCombination:["command+shift+c"],visibleToUsers:!1},{id:"copyLinkToBlock",description:"Copy link to specific block",defaultKeyCombination:[e.isApple?"command+ctrl+l":"alt+shift+l"],visibleToUsers:!1},{id:"copyCurrentPageLinkifiedBlockTitle",description:"Copy current page title as link",defaultKeyCombination:["command+alt+l"],visibleToUsers:!1},{id:"toggleSidebar",description:"Toggle sidebar",defaultKeyCombination:"ja-JP"===i().locale?[`command+${r}`,`command+${s}`,"command+code:IntlRo"]:["command+\\"],visibleToUsers:!1},{id:"toggleUpdateSidebar",description:"Toggle update sidebar",defaultKeyCombination:"ja-JP"===i().locale?[`command+shift+${r}`,`command+shift+${s}`,"command+code:IntlRo"]:["command+shift+\\"],visibleToUsers:!1},{id:"toggleBothSidebars",description:"Toggle both sidebars",defaultKeyCombination:["command+."],visibleToUsers:!1},{id:"toggleAISidebar",description:"Toggle AI sidebar",defaultKeyCombination:"ja-JP"===i().locale?["command+:"]:["command+;"],visibleToUsers:!1},{id:"openCommentsTabInUpdateSidebar",description:"Open comments tab in update sidebar",defaultKeyCombination:["ctrl+alt+9"],visibleToUsers:!1},{id:"openUpdatesTabInUpdateSidebar",description:"Open updates tab in update sidebar",defaultKeyCombination:["ctrl+alt+0"],visibleToUsers:!1},{id:"zoomIn",description:"Zoom in",defaultKeyCombination:["command+="]},{id:"zoomOut",description:"Zoom out",defaultKeyCombination:["command+-"]},{id:"zoomReset",description:"Reset zoom to 100%",defaultKeyCombination:["command+0"],visibleToUsers:!1},{id:"settings",description:"Open settings",defaultKeyCombination:["command+,"]},{id:"newPage",description:"Create a new page",defaultKeyCombination:["command+n"]},{id:"newPageAndDictate",description:"Create new page and start dictation",defaultKeyCombination:[e.isApple?"command+ctrl+n":"ctrl+alt+n"],visibleToUsers:!1},{id:"dictate",description:"Start voice dictation",defaultKeyCombination:["command+o"],visibleToUsers:!1},{id:"backbutton",description:"Browser back button",defaultKeyCombination:["backbutton"],visibleToUsers:!1},{id:"toggleDarkMode",description:"Toggle dark mode",defaultKeyCombination:["command+shift+l"]},{id:"openFile",description:"Open file picker",defaultKeyCombination:["command+alt+o"],visibleToUsers:!1},{id:"moveTo",description:"Move block to different page",defaultKeyCombination:["command+shift+p"],visibleToUsers:!1},{id:"toggleAllToggles",description:"Expand/close all toggles",defaultKeyCombination:["command+alt+t"]},{id:"switchSpacesPreTabs",description:"Switch between spaces (pre-tabs)",defaultKeyCombination:["command+1","command+2","command+3","command+4","command+5","command+6","command+7","command+8","command+9"],visibleToUsers:!1},{id:"switchSpacesPostTabs",description:"Switch between spaces (post-tabs)",defaultKeyCombination:e.isApple?["ctrl+shift+0","ctrl+shift+1","ctrl+shift+2","ctrl+shift+3","ctrl+shift+4","ctrl+shift+5","ctrl+shift+6","ctrl+shift+7","ctrl+shift+8","ctrl+shift+9"]:["alt+shift+0","alt+shift+1","alt+shift+2","alt+shift+3","alt+shift+4","alt+shift+5","alt+shift+6","alt+shift+7","alt+shift+8","alt+shift+9"],visibleToUsers:!1},{id:"turnIntoType",description:"Turn block into different type",defaultKeyCombination:e.isApple?["command+alt+0","command+alt+1","command+alt+2","command+alt+3","command+alt+4","command+alt+5","command+alt+6","command+alt+7","command+alt+8","command+alt+9"]:["command+shift+0","command+shift+1","command+shift+2","command+shift+3","command+shift+4","command+shift+5","command+shift+6","command+shift+7","command+shift+8","command+shift+9"]},{id:"toggleRecordingInputLatency",description:"Toggle input latency recording",defaultKeyCombination:n().oE([e.isApple?"command+alt+ctrl+m":void 0]),visibleToUsers:!1},{id:"keypress",description:"Handle all key presses",defaultKeyCombination:["keypress"],visibleToUsers:!1},{id:"togglePropertyVisibility",description:"Toggle property visibility",defaultKeyCombination:["command+alt+p"],visibleToUsers:!1},{id:"toggleFavorite",description:"Toggle favorite status",defaultKeyCombination:[e.isApple?"command+ctrl+shift+f":"ctrl+alt+shift+f"],visibleToUsers:!1},{id:"toggleAllUpdates",description:"Toggle all updates visibility",defaultKeyCombination:[e.isApple?"command+alt+u":"ctrl+alt+u"],visibleToUsers:!1},{id:"toggleAllTeams",description:"Toggle all teams visibility",defaultKeyCombination:[e.isApple?"command+alt+a":"ctrl+alt+a"],visibleToUsers:!1},{id:"openHome",description:"Open home page",defaultKeyCombination:[e.isApple?"command+ctrl+h":"ctrl+alt+h"],visibleToUsers:!1},{id:"openShareMenu",description:"Open share menu",defaultKeyCombination:[e.isApple?"command+shift+o":"ctrl+shift+o"],visibleToUsers:!1},{id:"openInSidePeek",description:"Open link in side peek",defaultKeyCombination:["alt+click"],visibleToUsers:!1},{id:"openExperimentSettings",description:"Open experiment settings",defaultKeyCombination:[e.isApple?"command+alt+shift+e":"ctrl+alt+shift+e"],visibleToUsers:!1},{id:"notionAiCommandSearchDefault",description:"Open AI command search",defaultKeyCombination:[e.isApple?"shift+command+j":"shift+ctrl+j"],visibleToUsers:!1}]}},852507:(e,t,o)=>{o.d(t,{l:()=>r,w:()=>s});var n=o(296540),i=()=>o(17022);function r(e){const t=(0,n.useRef)(!1);!1===t.current&&(t.current=!0,i().e.withListenerIgnored((()=>e())))}function s(e){const t=(0,n.useRef)(!1);(0,n.useEffect)((()=>{if(!1===t.current)return t.current=!0,e()}),[e])}},872994:(e,t,o)=>{o.d(t,{e:()=>s,y:()=>a});var n=o(296540),i=o(474848);const r=(0,n.createContext)(void 0);function s(e){const{children:t,...o}=e,s=(0,n.useMemo)((()=>"void"in o?void 0:o),Object.values(o));return(0,i.jsx)(r.Provider,{value:s,children:t})}function a(){return(0,n.useContext)(r)}r.displayName="ContentEditableContext"},892235:(e,t,o)=>{o.d(t,{S:()=>c});var n=o(296540),i=()=>o(558842),r=()=>o(378879),s=()=>o(724200),a=o(474848);function c(e){const{observer:t,children:o}=e,c=(0,n.useContext)(r().a),d=(0,n.useMemo)((()=>t?(0,s().D)(t,c):c),[t,c]);return(0,n.useEffect)((()=>(null==t||t.register(),()=>{null==t||t.unregister()})),[t]),t?(0,a.jsx)(r().a.Provider,{value:d,children:o}):(0,i().Du)(o)}},901167:(e,t,o)=>{o.d(t,{AD:()=>c,Hz:()=>g,Id:()=>w,b2:()=>S,cm:()=>h,dv:()=>r,hU:()=>v,ip:()=>l,ph:()=>a,qb:()=>f,sT:()=>y,sr:()=>C,tD:()=>u,t_:()=>p,xr:()=>k});var n=()=>o(338381);const i="data-content-editable-leaf",r={[i]:!0},s="data-content-editable-root",a={[s]:!0},c="data-content-editable-error",d="data-content-editable-root-tiny-selection-trap",l={[d]:!0},u="data-content-editable-selecting",h="data-content-editable-composing",m="data-content-editable-void",p={[m]:!0};function f(e){return n().vq(e)&&e.hasAttribute(i)}function g(e){return n().vq(e)&&e.hasAttribute(d)}function v(e){return Boolean(n().vq(e)&&e.querySelector(`[${i}]`))}function b(e){return n().vq(e)&&e.hasAttribute(s)}function y(e){return n().EV(e,f)}function S(e,t){return e.parentElement===t?e:n().EV(e,(e=>e.parentElement===t))||e}function w(e,t){if(!e||!t)return!1;const o=C(e),n=C(t);return Boolean(o&&o===n)}function C(e){const t=n().EV(e,(e=>b(e)||function(e){return n().vq(e)&&e.hasAttribute(m)}(e)));if(t&&b(t))return t}function k(e){return n().EV(e,(e=>function(e){return n().vq(e)&&e.hasAttribute(c)}(e)))}},905343:(e,t,o)=>{o.d(t,{A:()=>n});o(944114),o(898992),o(672577);const n=new class{constructor(){this.traces=void 0,this.traces=[]}addTrace(e){this.traces.find((t=>t.type===e.type&&t.name===e.name&&t.start===e.start&&t.end===e.end))||this.traces.push(e)}}},906510:(e,t,o)=>{o.d(t,{S3:()=>a});o(944114),o(898992),o(803949);class n{mark(e){}onMark(e){}increment(e,t,o){}add(e,t,o){}getMetrics(){return{metricTotals:{},metrics:{}}}}class i{constructor(){this.metricTotals={},this.metrics={},this.callbacks=[],this.subNameFilter=e=>e}mark(e){this.callbacks.forEach((t=>t(e))),this.flush(e,this.metricTotals,this.metrics),this.metricTotals={},this.metrics={}}onMark(e){this.callbacks.push(e)}increment(e,t){let o=arguments.length>2&&void 0!==arguments[2]?arguments[2]:1;this.add(e,t,{count:o,sum:o})}add(e,t,o){this.metricTotals[e]=this.metricTotals[e]||{count:0,sum:0},this.metricTotals[e].count+=o.count,this.metricTotals[e].sum+=o.sum;const n=this.subNameFilter(t);this.metrics[e]=this.metrics[e]||{},this.metrics[e][n]=this.metrics[e][n]||{count:0,sum:0},this.metrics[e][n].count+=o.count,this.metrics[e][n].sum+=o.sum}setSubNameFilter(e){this.subNameFilter=e}}class r extends i{constructor(){super(...arguments),this.accumulatedMetrics={metricTotals:{},metrics:{}}}flush(e,t,o){for(const[n,i]of Object.entries(t))this.accumulatedMetrics.metricTotals[n]=this.accumulatedMetrics.metricTotals[n]||[],this.accumulatedMetrics.metricTotals[n].push({mark:e,data:i});for(const[n,i]of Object.entries(o))this.accumulatedMetrics.metrics[n]=this.accumulatedMetrics.metrics[n]||[],this.accumulatedMetrics.metrics[n].push({mark:e,data:i})}getMetrics(){return this.accumulatedMetrics}}const s="undefined"!=typeof window&&"true"===localStorage.getItem("NotionPerformanceCounter.debug");const a=function(){if(s){const e=new r,t=/[0-9a-f]{8}-?[0-9a-f]{4}-?[0-9a-f]{4}-?[0-9a-f]{4}-?[0-9a-f]{12}/gi;return e.setSubNameFilter((e=>e.replace(t,"UUID"))),e}return new n}();(0,o(604341).exposeDebugValue)("NotionPerformanceCounter",a)},908006:(e,t,o)=>{o.r(t),o.d(t,{default:()=>c});o(517642),o(658004),o(733853),o(845876),o(432475),o(515024),o(731698),o(581454);var n=()=>o(496603),i=()=>o(502800),r=()=>o(250454);function s(e,t,o){e.set(t,(e.get(t)??0)+o)}function a(e,t){return t[1]-e[1]}const c=new class{constructor(){this.debug=!1,this.debugStatsPerFlush=void 0,this.debugLogMinComponentRerenders=void 0,this.pauseCount=0,this.renderIsQueued=!1,this.pendingAnimationFrame=void 0,this.computedStoreQueue=new Set,this.computedStoreQueueDebugNameCounters=new Map,this.componentRenderQueue=new Set,this.flushQueue=new Set,this.renderRemovedQueue=new Set,this.currentlyRendering=new Set,this.flushSync=void 0,this.maybeInstrumentStoreRecompute=(e,t)=>{if(!this.debug||!t)return void e();const o=performance.now();e();const n=performance.now()-o,{debugName:i}=e,r=t.get(i);void 0===r?t.set(i,n):t.set(i,r+n)},this.processRenderQueueCallback=async e=>{if(!this.renderRemovedQueue.has(e))try{this.currentlyRendering.add(e),await e()}catch(t){this.throttledLog({level:"error",from:"RenderQueue",type:"componentRender",error:(0,i().convertErrorToLog)(t)})}finally{this.currentlyRendering.delete(e)}},this.throttledLog=n().nF((e=>{console.info(e)}),5e3),this.flush=this.flush.bind(this)}isPaused(){return this.pauseCount>0}enqueueComputedStoreRecompute(e){this.debug&&!this.computedStoreQueue.has(e)&&s(this.computedStoreQueueDebugNameCounters,e.debugName,1),this.computedStoreQueue.add(e),this.enqueueFlush()}enqueueComponentRender(e){this.componentRenderQueue.add(e),this.enqueueFlush()}enqueueFlush(){this.pendingAnimationFrame&&document.hidden&&(window.cancelAnimationFrame(this.pendingAnimationFrame),this.renderIsQueued=!1,this.pendingAnimationFrame=void 0),this.renderIsQueued||0!==this.pauseCount||(this.renderIsQueued=!0,this.flushAfterAnimationFrame())}flushAfterAnimationFrame(){document.hidden?Promise.resolve().then(this.flush):this.pendingAnimationFrame=window.requestAnimationFrame(this.flush)}removeRenderFromQueue(e){this.renderRemovedQueue.add(e)}afterNextFlush(e){return new Promise((t=>{this.flushQueue.add((()=>{e&&e(),t()})),this.enqueueFlush()}))}pause(){this.afterNextFlush((()=>{this.pauseCount++}))}unpause(){this.pauseCount--,0!==this.pauseCount||this.renderIsQueued||(this.renderIsQueued=!0,this.flushAfterAnimationFrame())}async flush(){try{this.flushSync||(this.flushSync=(await Promise.resolve().then(o.t.bind(o,440961,19))).flushSync);const t=this.flushSync,c=this.debug?{computedStoreRecomputes:0,componentRerenders:0,computedStoreCounters:new Map,computedStoreDurationCounters:new Map,componentRenderCounters:new Map,totalStoreRecomputeTime:0,startTime:performance.now(),storeRecomputeStart:0}:void 0;let d;r().I.getShouldCollect()&&document.hasFocus()&&(d=r().I.generateUniqueId(),r().I.start(d));do{for(;this.computedStoreQueue.size>0;){const t=this.computedStoreQueue;if(this.computedStoreQueue=new Set,this.debug&&c){const{computedStoreQueueDebugNameCounters:e}=this;this.computedStoreQueueDebugNameCounters=new Map;for(const[t,o]of e.entries())s(c.computedStoreCounters,t,o);c.storeRecomputeStart=performance.now()}for(const o of t)try{this.maybeInstrumentStoreRecompute(o,null==c?void 0:c.computedStoreDurationCounters)}catch(e){this.throttledLog({level:"error",from:"RenderQueue",type:"computedStoreRecompute",error:(0,i().convertErrorToLog)(e)})}this.debug&&c&&(c.totalStoreRecomputeTime+=performance.now()-c.storeRecomputeStart,c.computedStoreRecomputes+=t.size)}const n=this.componentRenderQueue;let r;if(this.componentRenderQueue=new Set,this.renderRemovedQueue.clear(),t((()=>{r=Promise.all(Array.from(n.values()).map(this.processRenderQueueCallback))})),r){if((await(0,o(763824).nQ)(1e4,r)).timeout){const e=[...this.currentlyRendering].map((e=>e.componentName));this.currentlyRendering.clear(),this.throttledLog({level:"error",from:"RenderQueue",type:"rerenderTimeOut",data:{miscDataToConvertToString:{componentNames:e}}})}}if(this.debug&&c){c.componentRerenders+=n.size;for(const{componentName:e}of n.values())s(c.componentRenderCounters,e,1)}const a=this.flushQueue;this.flushQueue=new Set;for(const t of a)try{t()}catch(e){this.throttledLog({level:"error",from:"RenderQueue",type:"afterNextFlush",error:(0,i().convertErrorToLog)(e)})}}while(this.componentRenderQueue.size>0||this.flushQueue.size>0||this.computedStoreQueue.size>0);if(r().I.getShouldCollect()&&d&&r().I.stop(d),this.debug&&c&&c.componentRerenders>(this.debugLogMinComponentRerenders??5)){if(console.groupCollapsed("Flushed render queue",{recomputes:c.computedStoreRecomputes,rerenders:c.componentRerenders,totalMs:Math.floor(performance.now()-c.startTime),recomputeMs:Math.floor(c.totalStoreRecomputeTime)}),c.computedStoreRecomputes>0){console.log("Recomputed stores:");const e=Object.fromEntries([...c.computedStoreCounters.entries()].sort(a));if(c.totalStoreRecomputeTime>=1)for(const[t,o]of c.computedStoreCounters)e[t]={count:o,duration:n().LI(c.computedStoreDurationCounters.get(t)??0,3)};console.table(e)}if(c.componentRerenders>0){console.log("Rerenders:");const e=Object.fromEntries([...c.componentRenderCounters.entries()].sort(a));console.table(e)}console.groupEnd(),this.debugStatsPerFlush=[...this.debugStatsPerFlush??[],c]}}finally{this.renderIsQueued=!1,this.pendingAnimationFrame=void 0}}clearDebugStats(){this.debugStatsPerFlush=void 0}getDebugStatsPerFlush(){return this.debugStatsPerFlush??[]}setDebugLogMinComponentRerenders(e){this.debugLogMinComponentRerenders=e}serializeDebugStatsPerFlush(){return this.getDebugStatsPerFlush().map((e=>({computedStoreRecomputes:e.computedStoreRecomputes,componentRerenders:e.componentRerenders,computedStoreCounters:Object.fromEntries([...e.computedStoreCounters.entries()].sort(a)),computedStoreDurationCounters:Object.fromEntries([...e.computedStoreDurationCounters.entries()].sort(a)),componentRenderCounters:Object.fromEntries([...e.componentRenderCounters.entries()].sort(a)),totalStoreRecomputeTime:e.totalStoreRecomputeTime,startTime:e.startTime,storeRecomputeStart:e.storeRecomputeStart})))}}},959180:(e,t,o)=>{o.d(t,{A:()=>r,g:()=>i});o(898992),o(354520);class n extends(()=>o(965828))().O{getMembersByDOMOrder(e){let{filter:t}=e;return super.filter(t).sort(((e,t)=>(0,o(507707).A)(e.getNode(),t.getNode())))}}function i(e){if(e){if(e instanceof Element)return e;if("getNode"in e){const t=e.getNode();if(t instanceof Element)return t}}}const r=n},965828:(e,t,o)=>{o.d(t,{O:()=>n});o(944114),o(517642),o(658004),o(733853),o(845876),o(432475),o(515024),o(731698),o(898992),o(803949);class n{constructor(){this.members=new Set}register(e){return this.members.add(e),()=>this.unregister(e)}unregister(e){this.members.delete(e)}getSize(){return this.members.size}hasMembers(){return this.getSize()>0}find(e){for(const t of this.members)if(e(t))return t}filter(e){const t=[];for(const o of this.members)e(o)&&t.push(o);return t}forEach(e){this.members.forEach(e)}}},968526:(e,t,o)=>{o.r(t),o.d(t,{TimeSeries:()=>v,useTimeSeriesPalette:()=>b});o(898992),o(354520),o(430670),o(581454);var n=()=>o(622297),i=()=>o(774726),r=o(296540),s=()=>o(869190),a=()=>o(512062),c=()=>o(720665),d=()=>o(662303),l=()=>o(401497),u=o(474848);const h=7,m=0,p=25,f=16,g=5;const v=(0,o(810073).A)((function(e){const{width:t,height:a,layers:v,showTooltip:b,hideTooltip:y,tooltipData:S,tooltipTop:w=0,tooltipLeft:C=0,curveType:k="curveLinear",renderTooltipSubLabel:E}=e,T=(0,l().DP)(),R=(0,r.useMemo)((()=>{let t;try{t=(0,o(229907).lT)(e.startDate,e.endDate)}catch(r){return[]}const n=(0,c().$z)(e.data,(e=>e.ds)),i=Object.fromEntries(v.map((e=>[e.key,0])));return t.map((e=>{const t=n.get(e);return{ds:e,values:t?t[0].values:i}}))}),[v,e.data,e.endDate,e.startDate]),A=(0,r.useMemo)((()=>Math.max(...R.flatMap((e=>v.map((t=>{let{key:o}=t;return e.values[o]})))))),[R,v]),L=(0,r.useMemo)((()=>{const e=Math.max(0,Math.floor(Math.log10(A)));return f+10+8*e}),[A]),M=(0,r.useMemo)((()=>R.length<g?m+24:m),[R.length]),N={domain:[0,A],range:[a-p,h]},x=(0,n().A)(N),I=R.map((e=>new Date(e.ds).valueOf())),U={domain:[Math.min(...I),Math.max(...I)],range:[L,t-M]},P=(0,n().A)(U),[D,O]=function(e,t){const o=e.range[1]-e.range[0],n=t.range[0]-t.range[1],i=e.domain[0],s=e.domain[1],a=s-i,d=t.domain[1];return[(0,r.useMemo)((()=>{if(a<c().nD*(g+1))return Array.from({length:1+a/c().nD},((e,t)=>i+t*c().nD));const e=Math.floor(o/312*9),t=Math.max(1,Math.floor(a/e)),n=Math.ceil(t/c().nD)*c().nD;return Array.from({length:e},((e,t)=>i+(2*t+1)*n)).filter((e=>e<s))}),[o,a,i,s]),(0,r.useMemo)((()=>{const e=Math.floor(n/142*6),t=Math.max(1,Math.floor(d/e)+1);return Array.from({length:e},((e,o)=>(o+1)*t)).filter((e=>e<=d))}),[n,d])]}(U,N),K=(0,r.useMemo)((()=>(0,o(890479).A)((e=>new Date(e.ds).getTime())).center),[]),_=(0,r.useCallback)((e=>{const{x:t}=(0,o(834391).A)(e)||{x:0},n=P.invert(t),i=K(R,n),r=R[i],s=P(new Date(r.ds));b({tooltipData:r,tooltipLeft:s,tooltipTop:x(r.values[v[0].key])})}),[R,b,P,x,K,v]);return(0,u.jsxs)("div",{children:[(0,u.jsxs)("svg",{width:t,height:a,children:[v.map((e=>{let{key:t,stroke:n,fill:i}=e;return(0,u.jsx)(o(879060).A,{data:R,x:e=>P(new Date(e.ds).valueOf()),y:e=>x(e.values[t]),yScale:x,stroke:n,fill:i,curve:o(309852)[k]},t)})),(0,u.jsx)(o(437143).A,{width:t,height:a,fill:"transparent",onTouchStart:_,onTouchMove:_,onMouseMove:_,onMouseLeave:y}),S?(0,u.jsxs)("g",{children:[(0,u.jsx)(i().A,{from:{x:L,y:w},to:{x:C,y:w},stroke:T.icon.tertiary,strokeWidth:1,pointerEvents:"none",strokeDasharray:"5,2"}),(0,u.jsx)(i().A,{from:{x:C,y:w},to:{x:C,y:a-p},stroke:T.icon.tertiary,strokeWidth:1,pointerEvents:"none",strokeDasharray:"5,2"}),(0,u.jsx)("circle",{cx:C,cy:w,r:5,fill:v[0].stroke,pointerEvents:"none"})]}):void 0,(0,u.jsx)(o(889065).A,{left:L,scale:x,stroke:T.icon.secondary,tickStroke:T.icon.tertiary,tickLength:10,hideZero:!0,tickValues:O,tickFormat:e=>`${e}`,tickLabelProps:()=>({dx:"-0.25em",dy:"0.25em",fontSize:12,fill:T.text.primary,textAnchor:"end"})}),(0,u.jsx)(o(940767).A,{top:a-p,scale:P,stroke:T.icon.secondary,tickStroke:T.icon.tertiary,tickValues:D,tickLength:10,tickFormat:e=>(0,s().W_)(e.valueOf(),"month_day",d().locale,"UTC"),tickLabelProps:()=>({fontSize:12,fill:T.text.primary,textAnchor:"middle"})})]}),S?(0,u.jsx)("div",{children:(0,u.jsx)(o(966514).A,{top:w-5,left:C-4,style:{...o(534725).k,background:"#36352F",padding:6,borderRadius:3,color:"white",fontSize:12,boxShadow:"0px 4px 8px rgba(0, 0, 0, 0.04), 0px 0px 2px rgba(0, 0, 0, 0.06), 0px 0px 1px rgba(0, 0, 0, 0.04)",zIndex:109},children:(0,u.jsxs)("div",{style:{display:"flex",flexDirection:"column",gap:7},children:[(0,u.jsx)("div",{children:`On ${(0,s().W_)(o(604995).C6.isoToUnixMs(S.ds,"UTC")??Date.now(),"medium",d().locale,"UTC")}`}),v.map((e=>{let{key:t,stroke:o,renderLabel:n}=e;return(0,u.jsxs)("div",{children:[(0,u.jsxs)("div",{style:{display:"flex",gap:5,alignItems:"center"},children:[(0,u.jsx)("div",{style:{background:o,borderRadius:3,width:12,height:12}}),(0,u.jsx)("div",{children:n(S.values[t])})]}),E?E():void 0]},t)}))]})})}):void 0]})}));function b(){const e=(0,l().DP)();return{blueLayer:{stroke:e.blueColor,fill:"dark"===e.mode?e.palette.blue[300]:e.palette.blue[50]},yellowLayer:{stroke:a().M.light.yellow[300],fill:"dark"===e.mode?e.palette.yellow[300]:e.palette.yellow[50]}}}},992202:(e,t,o)=>{o.r(t),o.d(t,{INTERNAL_TESTING_USE_ONLY__getStateLog:()=>O,captureStack:()=>v,getForceUpdateCauses:()=>W,getSerializedDebugGraph:()=>V,isRecording:()=>D,logComponentForceUpdateScheduled:()=>A,logListenerAdded:()=>L,logListenerRemoved:()=>M,logStoreAccess:()=>R,logStoreCreated:()=>E,logStoreEmit:()=>k,logStoreSet:()=>T,logTypingLag:()=>N,renderLatestGraph:()=>G,resume:()=>U,runWithTimer:()=>w,startRecordingReactivityLog:()=>x,stop:()=>I,toggleRecordingInputLatency:()=>P,updateReactivityRecordingOptions:()=>K});o(16280),o(944114),o(517642),o(658004),o(733853),o(845876),o(432475),o(515024),o(731698),o(898992),o(672577),o(581454);var n=()=>o(697938),i=()=>o(496603),r=()=>o(534177),s=()=>o(720665),a=()=>o(496506),c=()=>o(624919),d=()=>o(272061),l=()=>o(908006);const u=30,h=10,m=25,p=/webpack-internal:\/+/g,f=/node_modules\//g,g=/\(<anonymous>\)/g;function v(e){if(!S.captureStacks)return;const t=Error.stackTraceLimit;try{var o;Error.stackTraceLimit=m;const t=new Error("capture stack frame");let i=((null===(o=t.stack)||void 0===o?void 0:o.replace(p,"").split("\n"))||[]).slice(e).map((e=>e.trim()));for(let e=i.length-1;e>=0;e--)if(n=i[e],!Boolean(n.match(f)||n.match(g))){const t=i.length-(e+1);i=i.slice(0,e+1),t>0&&i.push(`(Omitted ${t} framework frames in node_modules)`);break}return i.join("\n")}finally{Error.stackTraceLimit=t}var n}class b{constructor(){this.isActive=!1,this.captureStacks=!0,this.log=[],this.inputEvents=[],this.storeEmits=[],this.componentForceUpdates=[],this.componentRenders=[],this.storeListeners=new Map,this.latestPerformanceDebugGraph=void 0}}let y,S=new b;function w(e,t){let o=!(arguments.length>2&&void 0!==arguments[2])||arguments[2];function n(){console.info("Starting perf"),x({captureStacks:o}),setTimeout((()=>{l().default.afterNextFlush((()=>{console.info("Stopping perf"),I(),console.info("Store emits:"),function(){const e={};i().__(S.storeEmits,(t=>{let{storeName:o}=t;e[o]||(e[o]=0),e[o]++}));const t=Object.keys(e).map((t=>({storeName:t,emitCount:e[t]}))),o=i().Ul(t,(e=>{let{emitCount:t}=e;return-1*t})),n=i().di(o,0,u);console.table(n),t.length>u&&console.info(`+ ${t.length-u} more updates`)}(),console.info("Component force updates:"),function(){const e=c().iY,t=i().$z(S.componentForceUpdates,(e=>e.stack)),o=Object.entries(t),n=i().Ul(o,(e=>{let[t,o]=e;return-o.length})),r=n.map((t=>{let[o,n]=t;const i=n.length,r=n.map((e=>e.componentName)),s=n.map((e=>e.storeName));return{Count:i,Components:e.countUniques(r),Stores:e.countUniques(s),StoreUpdateStack:e.div({},o)}}));console.log(c().iY.autoTable({rows:r,header:!0})),n.length>u&&console.info(`+ ${n.length-u} more updates`);console.info("Note: an 'undefined' storeName means the component updated for some reason other than a store emit.")}(),console.info("Component renders:"),function(){const e={};i().__(S.componentRenders,(t=>{let{componentName:o}=t;e[o]||(e[o]=0),e[o]++}));const t=Object.keys(e).map((t=>({componentName:t,renderCount:e[t]}))),o=i().Ul(t,(e=>{let{renderCount:t}=e;return-1*t})),n=i().di(o,0,u);console.table(n),t.length>u&&console.info(`+ ${t.length-u} more updates`)}(),console.info("Why components rendered after stores changed:"),console.info("  To fix, remove either codepath"),function(){const e=W(),t=i().$z(S.componentForceUpdates,(t=>{var o;const n=e.get(t);return`${(null==n||null===(o=n.emitted)||void 0===o?void 0:o.stack)||t.stack}:${null==n?void 0:n.listenStack}`})),o=c().iY,n=(0,r().WP)(t).map((t=>{let[,n]=t;const i=e.get(n[0]),r=n.length,s=n.map((e=>e.componentName)),a=n.map((e=>(null==i?void 0:i.emitted.storeName)||e.storeName||"(unknown)")),c=(null==i?void 0:i.listenStack)||"(unknown)",d=(null==i?void 0:i.emitted.stack)||n[0].stack;return{Count:r,Components:o.countUniques(s),Stores:o.countUniques(a),ListenStack:o.div({},c),EmitStack:o.div({},d)}})).sort(((e,t)=>t.Count-e.Count)),s=n.slice(0,h),a={Count:"Count",Components:"Component",Stores:"Store",ListenStack:o.div({},o.div({},"ListenStack"),o.div({style:"white-space: auto"},"One (of possibly many) reason(s) why this component is subscribed to this store")),EmitStack:o.div({},o.div({},"EmitStack"),o.div({style:"white-space: auto"},"One (of possibly many) trigger(s) that caused the component's listener on this store to be woken up"))};console.info(o.autoTable({rows:s,header:a})),s.length<n.length&&(console.groupCollapsed(`+ ${n.length-h} more updates with causes`),console.info(o.autoTable({rows:n.slice(h),header:a})),console.groupEnd());const d=V();S.latestPerformanceDebugGraph=d,console.log('Performance debug graph (visualize this with "__console.performanceHelpers.renderLatestGraph()" or "notion ts-node src/tools/renderPerformanceDebugGraph.ts"):',d)}(),console.info("Input latency:"),q()}))}),t)}0===e?n():setTimeout(n,e)}function C(e){return S.isActive&&S.log.push(e),e}function k(e){S.isActive&&S.storeEmits.push(C({type:"store.emit",store:e,storeName:H(e),stack:v(3)}))}function E(e,t){S.isActive&&C({type:"store.created",store:e,state:t,stack:v(0)})}function T(e,t){S.isActive&&C({type:"store.set",store:e,state:t,stack:v(4)})}function R(e,t){S.isActive&&C({type:"store.access",store:e,listener:t,stack:v(0)})}function A(e,t){S.isActive&&S.componentForceUpdates.push(C({type:"component.forceUpdate",component:e,componentName:e.componentName,store:t,storeName:null==t?void 0:t.constructor.name,stack:v(2)}))}function L(e,t,o){if(S.isActive){const n=C({type:"store.listeners.added",store:e,listener:t,stack:v(o)});let i=S.storeListeners.get(e);i||(i=new Map,S.storeListeners.set(e,i)),i.has(t)||i.set(t,n.stack)}}function M(e,t){if(S.isActive){C({type:"store.listeners.removed",store:e,listener:t,stack:v(0)});const o=S.storeListeners.get(e);if(!o)return;o.delete(t),0===o.size&&S.storeListeners.delete(e)}}function N(e,t){var o;S.isActive&&(o={type:"input.latency",metricType:e,timeMs:t},S.isActive&&S.inputEvents.push(o))}function x(e){S=new b,S.captureStacks=e.captureStacks,S.isActive=!0}function I(){S.isActive=!1}function U(){S.isActive=!0}function P(){if(S.isActive)return clearTimeout(y),y=void 0,I(),void q();console.info(`Recording input latency for ${s().Xb}ms (press Ctrl+Alt+Command+M again to end)`),x({captureStacks:!1}),clearTimeout(y),y=window.setTimeout((()=>{I()}),s().Xb)}function D(){return S.isActive}function O(){if(!S.isActive)throw new Error("performanceHelpers.start() must be called before getStateLog()");return S.log.slice()}function K(e){if(!S.isActive)throw new Error("performanceHelpers.start() must be called before getStateLog()");S.captureStacks=e.captureStacks}function _(e,t){return`${t}\n\nComputedStore listener:\n${e}`}function F(e){let t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:new Set;return function*(){if(!("autoListener"in e))return;const o=e.autoListener;for(const[n,i]of S.storeListeners){if(t.has(n))continue;if(!i.has(o))continue;t.add(n);const r=S.storeEmits.find((e=>e.store===n));if(!r)continue;const s=i.get(o);yield{computedStore:e,store:n,storeEmit:r,listenStack:s,seen:t}}}()}function $(e,t){let o=arguments.length>2&&void 0!==arguments[2]?arguments[2]:new Set;for(const{store:n}of F(e,o)){let i=t.get(n);i||(i=new Set,t.set(n,i)),i.add(e),n instanceof a().ComputedStore&&$(n,t,o)}}function j(e){let t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:new Set;const o=new Map;for(const{store:n,listenStack:i,storeEmit:r}of F(e,t))if(n instanceof a().ComputedStore){const e=j(n,t);for(const[t,n]of e.entries()){let e=i;e&&n.stack&&(e=_(e,n.stack)),o.set(t,{stack:e,deepEmit:r})}}else o.set(n,{stack:i,deepEmit:r});return o}function*z(){for(const t of S.componentForceUpdates){var e;const{component:o,store:n}=t;if(!n)continue;const i=o,r=B(i,o),s=(null===(e=S.storeListeners.get(n))||void 0===e?void 0:e.get(i))||`No listen stack available for listener ${r}`,a=S.storeEmits.find((e=>e.store===n));a&&(yield{forceUpdate:t,component:o,store:n,listenStack:s,storeEmit:a})}}function V(){const e=new Map,t=new Map;for(const{component:o,store:n}of z()){let i=t.get(n);i||(i=new Set,t.set(n,i)),i.add(o),n instanceof a().ComputedStore&&$(n,e)}return(0,d().J)(t,e)}function W(){const e=new Map;for(const t of z()){const{store:o,forceUpdate:n}=t;let{listenStack:i,storeEmit:r}=t;if(r.store instanceof a().ComputedStore){const e=j(r.store);for(const[t,o]of e){r={type:"store.emit",store:o.deepEmit.store,stack:o.stack&&r.stack?`${o.stack}\n\nThen ComputedStore emitted:\n${r.stack}`:void 0,storeName:`${o.deepEmit.storeName} via ${r.storeName}`},o.stack&&(i=_(i,o.stack));break}}e.set(n,{listeningTo:o,listenStack:i,emitted:r})}return e}function q(){const e=i().$z(S.inputEvents,(e=>e.metricType)),t=Object.entries(e).map((e=>{let[t,o]=e;const n=o.map((e=>e.timeMs)),r=i().Ul(n);return{type:t,count:n.length,min:r[0],max:r[r.length-1],mean:parseFloat(i().i2(n).toFixed(2)),p50:r[Math.floor(.5*r.length)],p75:r[Math.floor(.75*r.length)],p95:r[Math.floor(.95*r.length)]}}));console.table(i().Ul(t,"type"))}function B(e,t){return"debugName"in e?e.debugName:`${e.constructor.name} of ${t.debugName}`}function G(){let e=arguments.length>0&&void 0!==arguments[0]&&arguments[0];if(!S.latestPerformanceDebugGraph)throw new Error("No latest performance debug graph exists! Please use runWithTimer() first.");{const t=(0,n().E)(S.latestPerformanceDebugGraph,e);window.open(`https://dreampuf.github.io/GraphvizOnline/#${encodeURIComponent(t)}`,"_blank")}}function H(e){return e.debugName?e.debugName:e.constructor.name}},993905:(e,t,o)=>{o.d(t,{s:()=>a});var n=o(296540),i=()=>o(872994),r=o(474848);const s={WebkitUserSelect:"none",userSelect:"none",pseudoSelection:{background:"transparent"}},a=n.forwardRef(((e,t)=>{const{allowSelectionWithin:n,style:a,className:c,...d}=e,l=(0,i().y)(),u=(0,r.jsx)("div",{ref:t,contentEditable:!1,...(0,o(955825).AH)(c,!n&&s,a),...o(901167).t_,...d});return l?(0,r.jsx)(i().e,{void:!0,children:u}):u}));a.displayName="ContentEditableVoid"}}]);