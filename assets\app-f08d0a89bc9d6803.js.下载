(()=>{"use strict";var e,t,n,o,r,a={2881:(e,t,n)=>{n.r(t)},3243:(e,t,n)=>{n.d(t,{tq:()=>r,v3:()=>s,vc:()=>i,zF:()=>a});var o=()=>n(645873);const r={TeamHome:new(o().O2)("teamHome",(()=>Promise.all([n.e(75134),n.e(99223),n.e(58795),n.e(9304),n.e(17230),n.e(23740),n.e(78405),n.e(48113),n.e(36639),n.e(48307),n.e(14310),n.e(28398),n.e(55850),n.e(34438),n.e(28464),n.e(33358),n.e(48986),n.e(89041)]).then(n.bind(n,533741)))),TeamHomeMoreMenu:new(o().O2)("TeamHomeMoreMenu",(()=>Promise.all([n.e(75134),n.e(99223),n.e(58795),n.e(17230),n.e(23740),n.e(36639),n.e(48307),n.e(14310),n.e(28398),n.e(55850),n.e(51363)]).then(n.bind(n,583273)))),TeamJoinLeaveButton:new(o().O2)("TeamJoinLeaveButton",(()=>Promise.all([n.e(75134),n.e(99223),n.e(58795),n.e(17230),n.e(23740),n.e(36639),n.e(48307),n.e(14310),n.e(28398),n.e(55850),n.e(209)]).then(n.bind(n,341974))))},a=(0,o()._h)(r.TeamHome,(e=>e.default)),i=(0,o()._h)(r.TeamHomeMoreMenu,(e=>e.default)),s=(0,o()._h)(r.TeamJoinLeaveButton,(e=>e.TeamJoinLeaveButton))},7615:(e,t,n)=>{n.d(t,{Ay:()=>i,Jm:()=>r,Yq:()=>a});var o=()=>n(292588);const r="space_entitlement_usage",a=n(638681).literals("responses","tokens","seconds"),i={table:r,columnTypes:{id:o().A.UUID,version:o().A.Number,last_version:o().A.Number,created_at:o().A.Number,updated_at:o().A.Number,space_id:o().A.UUID,type:o().A.String,usage:o().A.Number,unit:o().A.String},requiredColumns:{id:!0,version:!0,created_at:!0,updated_at:!0,space_id:!0,type:!0,usage:!0,unit:!0},model:(0,n(152853).P)({})}},11048:(e,t,n)=>{n.d(t,{A:()=>i});var o=n(296540),r=()=>n(872994),a=n(474848);function i(e){let{name:t,children:n}=e;const i=(0,r().y)(),c=(0,o.useRef)({displayName:`MCE(${t})`});return(0,o.useInsertionEffect)((()=>{i&&i.domLock.lockAfterRender(c.current)})),(0,a.jsxs)(a.Fragment,{children:[(0,a.jsx)(s,{token:c}),(0,a.jsx)(a.Fragment,{children:n})]})}function s(e){let{token:t}=e;const n=(0,r().y)();return(0,o.useInsertionEffect)((()=>{n&&n.domLock.unlockForRender(t.current)})),null}},11113:(e,t,n)=>{n.d(t,{Id:()=>r,oY:()=>a});n(944114);var o=()=>n(534177);function r(e){const t=[{type:"home",value:""}];e.crumbs&&t.push(...function(e){const t=e.split(","),n=[];for(const r of t){const[e,t]=r.split(":");if(i(e)&&t)switch(e){case"cat":n.push({type:"category",value:t});break;case"pro":"all"===t?n.push({type:"profiles",value:""}):n.push({type:"profile",value:t});break;case"ser":n.push({type:"search",value:t});break;case"col":"all"===t?n.push({type:"collections",value:""}):n.push({type:"collection",value:t});break;default:(0,o().HB)(e)}}return n}(e.crumbs));const n=function(e){const{pageType:t,slug:n,query:r}=e,a=t??"";if(function(e){return"profiles"===e||"search"===e||"categories"===e||"templates"===e||"collections"===e}(a))switch(a){case"profiles":return n?{type:"profile",value:n}:{type:"profiles",value:""};case"categories":return n?{type:"category",value:n}:void 0;case"search":return r?{type:"search",value:r}:void 0;case"templates":return n?{type:"template",value:n}:void 0;case"collections":return n?{type:"collection",value:n}:{type:"collections",value:""};default:(0,o().HB)(a)}return}(e);return n&&t.push(n),t}function a(e,t){const n=function(e){return"profiles"===e||"search"===e||"categories"===e||"collections"===e}(e);if(n)switch(e){case"categories":return t?`cat:${t}`:void 0;case"profiles":return t?`pro:${t}`:"pro:all";case"search":return t?`ser:${t}`:void 0;case"collections":return t?`col:${t}`:"col:all";default:(0,o().HB)(e)}}function i(e){return"pro"===e||"ser"===e||"cat"===e||"col"===e}},15623:(e,t,n)=>{n.d(t,{e6:()=>r,yc:()=>a});var o=()=>n(496603);function r(e){const t=localStorage.getItem(function(e){return`featureGateOverride-${e}`}(e));return"true"===t||"false"!==t&&void 0}function a(e){const t=localStorage.getItem(function(e){return`experimentOverrideKey-${e}`}(e))||void 0;if(!(0,o().Im)(t))return t}},20765:(e,t,n)=>{n.d(t,{loadCss:()=>o});async function o(){await Promise.all([Promise.resolve().then(n.bind(n,2881)),Promise.resolve().then(n.bind(n,49360)),Promise.resolve().then(n.bind(n,53334)),Promise.resolve().then(n.bind(n,669528)),Promise.resolve().then(n.bind(n,261642)),Promise.resolve().then(n.bind(n,841126)),Promise.resolve().then(n.bind(n,640254)),Promise.resolve().then(n.bind(n,692186)),Promise.resolve().then(n.bind(n,209881)),Promise.resolve().then(n.bind(n,787212)),Promise.resolve().then(n.bind(n,862114)),Promise.resolve().then(n.bind(n,328058)),Promise.resolve().then(n.bind(n,192188)),Promise.resolve().then(n.bind(n,468928))])}},29958:(e,t,n)=>{n.d(t,{C:()=>o});const o="Unrecognized"},45331:(e,t,n)=>{n.d(t,{A:()=>o,Y:()=>r});const o="1f8d872b-594c-80a4-b2f4-00370af2b13f";function r(e){return e===o}},49360:(e,t,n)=>{n.r(t)},52523:(e,t,n)=>{n.d(t,{default:()=>r});var o=()=>n(529543);class r{constructor(e){this.environment=void 0,this.getCookieWithoutPermissionCheck=e=>o().getCookieWithoutPermissionCheck(this.environment,e),this.removeCookie=(e,t)=>(o().removeCookie(e,t),Promise.resolve(void 0)),this.isMobileNative=()=>this.environment.device.isMobileNative,this.environment=e}}},53334:(e,t,n)=>{n.r(t)},56222:(e,t,n)=>{n.d(t,{A:()=>o});n(16280);const o=new class{constructor(){this.sdkInstance=void 0}get sdk(){if(void 0===this.sdkInstance)throw new Error("The Sentry SDK was accessed before `sentryInitializeLight` was called! This should never happen.");return this.sdkInstance}set sdk(e){this.sdkInstance=e}}},61844:(e,t,n)=>{n.d(t,{FN:()=>r,Wx:()=>o});function o(e){return"upwork"===e||"perfmark"===e}const r=["perfmark","reverse","reverse_mm_ent","business_reverse","stacked_business_trial","referral_biz_trial","samsung_tablet_preload_2025","business_reverse_14d","stacked_business_trial_14d","stacked_business_trial_14d_personal",...n(258037).Kl];n(902006).My},71509:(e,t,n)=>{let o,r;function a(e){return e===o?{wasLastPrefetchedPage:!0,dedupeSessionId:r}:{wasLastPrefetchedPage:!1}}function i(e,t){o=e,r=t}n.d(t,{I:()=>a,w:()=>i})},94442:(e,t,n)=>{async function o(e){const{loadCurrentUserId:t}=await Promise.resolve().then(n.bind(n,479954));return await t(e)}n.d(t,{loadCurrentUserId:()=>o})},96689:(e,t,n)=>{n.d(t,{Ay:()=>g,GQ:()=>l,NR:()=>c,NX:()=>p,Q4:()=>d,Zz:()=>b,n2:()=>i,zX:()=>s});var o=()=>n(638681),r=()=>n(313127),a=()=>n(292588);function i(e){return{id:e,table:p,spaceId:e}}const s=65,c="doc_notes",l="wiki",d="project_management",u=o().object({required:{enabled:o().boolean(),start_ts:o().number(),reason:o().literals("migration")},optional:{start_datetime:o().string(),start_timezone:o().string()}}),p="space",m={table:p,columnTypes:{id:a().A.UUID,version:a().A.Number,last_version:a().A.Number,name:a().A.String,email_domains:a().A.StringArray,pages:a().A.StringArray,icon:a().A.String,disable_public_access:a().A.Boolean,disable_public_access_requests:a().A.Boolean,disable_guests:a().A.Boolean,disable_move_to_space:a().A.Boolean,disable_export:a().A.Boolean,disable_space_page_edits:a().A.Boolean,disable_team_creation:a().A.Boolean,beta_enabled:a().A.Boolean,created_time:a().A.Number,last_edited_time:a().A.Number,deleted_by:a().A.UUID,permanently_deleted_time:a().A.Number,created_by_table:a().A.String,created_by_id:a().A.UUID,last_edited_by_table:a().A.String,last_edited_by_id:a().A.UUID,admin_disable_public_access:a().A.Boolean,space_pages:a().A.UUIDArray,plan_type:a().A.String,subscription_tier:a().A.String,invite_link_enabled:a().A.Boolean,initial_use_cases:a().A.String,public_home_page:a().A.String,bot_settings:a().A.JSON,settings:a().A.JSON,overdue_subscription:a().A.JSON,short_id:a().A.Number,short_id_str:a().A.String,referral_id:a().A.UUID},model:(0,n(152853).P)({RecordStore:!0,properties:{icon:{getMethod:"getRawIcon",getKeyStoreMethod:"getIconStore"},subscription_tier:{defaultOnRead:"free"},settings:{defaultOnRead:{}}}})},f=o().object({required:{feature:o().literals("ai"),id:o().literals(...n(928217).nD),received_at:o().number()},optional:{}}),g=(o().object({required:{},optional:{is_teams_enabled:o().boolean(),migrated_to_teams_at:o().number(),space_request:o().union([o().object({required:{type:o().literals("personal_external_transfer"),status:o().literals("requested","started","completed"),requested_time:o().number(),requested_from_space_id:o().string(),requested_by_user_id:o().string(),contact_email:o().string()},optional:{start_time:o().number(),target_user_id:o().string(),completed_time:o().number()}}),o().object({required:{type:o().literals("organization_personal_external_transfer"),status:o().literals("requested","started","completed"),requested_time:o().number(),requested_by_organization_id:o().string(),requested_by_user_id:o().string(),contact_email:o().string()},optional:{start_time:o().number(),target_user_id:o().string(),completed_time:o().number()}}),o().object({required:{type:o().literal("team_claim_upgrade"),status:o().literals("requested","started","added_and_downgraded_users","completed"),requested_time:o().number(),requested_by_space_id:o().string(),target_space_admin_ids:o().array(o().string()),requested_by_id:o().string(),requested_by_table:o().literals("bot","notion_user")},optional:{started_time:o().number(),completed_time:o().number()}}),o().object({required:{type:o().literal("organization_claiming_space"),status:o().literals("requested","added_space_to_organization","completed"),requested_time:o().number(),requested_by_organization_id:o().string(),requested_by_id:o().string(),requested_by_table:o().literal("notion_user")},optional:{started_time:o().number(),completed_time:o().number()}}),o().object({required:{type:o().literal("domain_claim_space_deletion"),status:o().literals("started","canceled"),requested_time:o().number(),started_time:o().number(),requested_by_space_id:o().string(),requested_by_id:o().string(),requested_by_table:o().literals("bot","notion_user")},optional:{contact_email:o().string(),last_delayed_time:o().number(),canceled_time:o().number(),canceled_by_id:o().string(),canceled_by_table:o().literals("bot","notion_user")}}),o().object({required:{type:o().literal("organization_domain_claim_space_deletion"),status:o().literals("started","canceled"),requested_time:o().number(),started_time:o().number(),requested_by_organization_id:o().string(),requested_by_id:o().string(),requested_by_table:o().literals("bot","notion_user")},optional:{contact_email:o().string(),last_delayed_time:o().number(),canceled_time:o().number(),canceled_by_id:o().string(),canceled_by_table:o().literals("bot","notion_user")}}),o().object({required:{type:o().literal("space_content_duplication"),status:o().literals("started","completed","failed","dry_run_completed","failed_with_closed_source","pending_deletion"),started_time:o().number(),target_space_id:o().string(),requested_by_id:o().string(),requested_by_table:o().literals("bot","notion_user"),contact_email:o().string(),custom_banner_message:o().undefinable(o().string())},optional:{completed_time:o().number(),failed_time:o().number(),deletion_time:o().number()}})]),in_ai_program:o().boolean(),is_assistant_experience_enabled:o().boolean(),all_users_ai_enabled:o().boolean(),database_sync_row_limit:o().number(),disable_membership_requests:o().boolean(),disable_member_upgrade_requests:o().boolean(),disable_external_membership_requests:o().boolean(),enable_guest_invite_requests:o().boolean(),seen_membership_requests:o().boolean(),disable_guest_membership_requests:o().boolean(),seen_guest_membership_requests:o().boolean(),first_invited_member_time:o().number(),hide_sites_banner:o().boolean(),disable_ai_feature:o().boolean(),enable_ai_feature:o().boolean(),disable_page_analytics:o().boolean(),disable_team_guests:o().boolean(),grant_awards:o().array(f),crdt_status:o().literals("off","prefer_off","prefer_on","migrating","on"),subscription_elapsed_date:o().number(),space_survey_data:o().object({required:{},optional:{intent:o().object({required:{value:o().string(),version:o().number(),collected_at:o().number(),collected_from:o().literals(...r().dH)},optional:{}}),use_cases:o().object({required:{value:o().array(o().string()),version:o().number(),collected_at:o().number(),collected_from:o().literals(...r().dH)},optional:{}}),company_size:o().object({required:{value:o().string(),version:o().number(),collected_at:o().number(),collected_from:o().literals(...r().dH)},optional:{}}),collaborative_intent:o().object({required:{value:o().string(),version:o().number(),collected_at:o().number(),collected_from:o().literals(...r().dH)},optional:{}})}}),seen_block_limit_reduction:o().boolean(),seen_block_limit_reduction_v2:o().boolean(),seen_block_limit_increase:o().boolean(),seen_block_limit_earlier:o().boolean(),exposed_to_timed_trials_in_onboarding:o().boolean(),exposed_to_timed_trials_in_onboarding_v2:o().boolean(),exposed_to_hide_block_limit_counter:o().boolean(),exposed_to_block_management:o().boolean(),reach_block_limit_time:o().number(),zero_retention_enabled:o().boolean(),embedding_index_is_live:o().boolean(),embedding_index_generation:o().number(),sharded_entitlement_usage_tables:o().boolean(),are_scim_invite_emails_suppressed:o().boolean(),disable_notion_calendar:o().boolean(),custom_emoji_creation_only_admin:o().boolean(),self_serve_enterprise_eligible:o().boolean(),was_in_personal_home_default_treatment:o().boolean(),is_personal_home_enabled:o().boolean(),show_home_virtual_views:o().boolean(),enable_ai_training:o().boolean(),manage_internal_domains:o().union([o().literal("auto"),o().literal("notion_admin_override")]),premium_feature_overrides:o().record(o().string(),o().union([o().literal("unlimited"),o().number(),o().boolean()])),delete_from_trash_delay_seconds:o().number(),purge_delay_seconds:o().number(),purge_transcripts_delay_seconds:o().number(),free_charts:o().array(o().string()),hipaa_compliancy_data:o().object({required:{last_edited_by_id:o().string(),last_edited_time:o().number()},optional:{}}),disallow_webhook_automation_action:o().boolean(),domain_link_sharing_enabled:o().boolean(),freeze:u,exposed_to_stackranking_experiment:o().object({required:{},optional:{adoption_hide_features_v1:o().boolean()}})}}),m),b="e12b42ac-4e54-476f-a4f5-7d6bdb1e61e2"},99895:(e,t,n)=>{n.d(t,{G:()=>o});class o extends Map{constructor(e,t){super(t),this.defaultFactory=void 0,this.defaultFactory=e}get(e){if(!super.has(e)){const t=this.defaultFactory(e);super.set(e,t)}return super.get(e)}update(e,t){const n=t(this.get(e));return this.set(e,n),n}toMap(){return new Map(this)}}},100875:(e,t,n)=>{n.d(t,{S9:()=>s,cq:()=>r,hr:()=>a,qm:()=>i});var o=()=>n(994310);function r(e){const{spaceView:t,preference:n}=e;if(!t)return;const r=s({userId:t.getUserId(),spaceId:t.getSpaceId()});r&&o().A.set(r,n)}function a(e){const{spaceView:t,pageId:n}=e;if(!t)return;const r=i({spaceId:t.getSpaceId(),userId:t.getUserId()});r&&o().A.set(r,n)}function i(e){const{userId:t,spaceId:n}=e;return t&&n?`firstPageInSidebar:${t}:${n}`:null}function s(e){const{userId:t,spaceId:n}=e;return t&&n?`onAppStartPreference:${t}:${n}`:null}},117296:(e,t,n)=>{n.d(t,{KM:()=>r,VJ:()=>a,Ww:()=>i,Y2:()=>c,Y8:()=>s});var o=()=>n(645873);const r={personalHome:new(o().O2)("personalHome",(()=>Promise.all([n.e(75134),n.e(99223),n.e(58795),n.e(9773),n.e(36556),n.e(40537),n.e(44711),n.e(16471),n.e(53321),n.e(96346),n.e(55373),n.e(36192),n.e(37353),n.e(29151),n.e(90825),n.e(99117),n.e(17230),n.e(23740),n.e(93747),n.e(33019),n.e(71621),n.e(92845),n.e(84468),n.e(75078),n.e(73801),n.e(48486),n.e(5605),n.e(95794),n.e(42660),n.e(34359),n.e(44934),n.e(21194),n.e(46414),n.e(66320),n.e(74562),n.e(28271),n.e(28866),n.e(34877),n.e(11676),n.e(11341),n.e(30858),n.e(17254),n.e(63479),n.e(4422),n.e(24587),n.e(27796),n.e(4330),n.e(8184),n.e(28908),n.e(82213),n.e(66626),n.e(39987),n.e(35094),n.e(15139),n.e(97513),n.e(27164),n.e(65881),n.e(3122),n.e(40532),n.e(9562),n.e(52651),n.e(78405),n.e(90978),n.e(1707),n.e(48113),n.e(36639),n.e(48307),n.e(14310),n.e(28398),n.e(55850),n.e(94995),n.e(32254),n.e(89362),n.e(25580),n.e(83246),n.e(44802)]).then(n.bind(n,518042)))),CustomDBPanelEmptyState:new(o().O2)("CustomDBPanelEmptyState",(async()=>await Promise.all([n.e(75134),n.e(99223),n.e(58795),n.e(17230),n.e(23740),n.e(36639),n.e(48307),n.e(14310),n.e(28398),n.e(55850),n.e(69224)]).then(n.bind(n,52372)))),TipsInAppModal:new(o().O2)("TipsInAppModal",(async()=>Promise.all([n.e(75134),n.e(99223),n.e(58795),n.e(17230),n.e(23740),n.e(36639),n.e(48307),n.e(14310),n.e(28398),n.e(55850),n.e(58204)]).then(n.bind(n,62863)))),personalHomeLearnHelpers:new(o().O2)("personalHomeLearnHelpers",(async()=>Promise.all([n.e(48307),n.e(41135)]).then(n.bind(n,539780)))),RecentsCachingListener:new(o().O2)("RecentsCachingListener",(async()=>Promise.all([n.e(75134),n.e(99223),n.e(58795),n.e(17230),n.e(23740),n.e(36639),n.e(48307),n.e(14310),n.e(28398),n.e(55850),n.e(62516)]).then(n.bind(n,437879))))},a=(0,o()._h)(r.personalHome,(e=>e.PersonalHomeContainer)),i=(0,o()._h)(r.personalHome,(e=>e.SetupPendingHomeTile)),s=(0,o().jQ)(r.CustomDBPanelEmptyState,(e=>e.default)),c=(0,o()._h)(r.TipsInAppModal,(e=>e.TipsInAppModal))},118165:(e,t,n)=>{n.d(t,{initializeReactivityVersion:()=>o});function o(){return 1}},140283:(e,t,n)=>{n.d(t,{A:()=>o,B:()=>r});const o=1024,r=1048576},140583:(e,t,n)=>{n.d(t,{E:()=>r});n(581454);var o=n.n(n(722653));function r(e,t){const n=e.navigator,r=new(o().UAParser)(t);function a(){return/Yandex/.test(t)}function i(){return/Windows.*Edge/i.test(t)}function s(){return!i()&&!a()&&(/Chrome/i.test(t)||/CriOS/i.test(t))}function c(){return/Android/i.test(t)||"Android"===r.getOS().name}function l(){return!c()&&(/iPad/i.test(t)||"MacIntel"===n.platform&&"ontouchend"in document)}function d(){return!c()&&(/iPhone|iPod/i.test(t)||l())}function u(){return d()||c()||/Windows Phone/i.test(t)}function p(){return/Mac/i.test(t)&&!u()}function m(){return/Windows/i.test(t)&&!u()}function f(){return/CrOS/i.test(t)}function g(){return(/Linux/i.test(t)||f())&&!u()}function b(){return`${r.getBrowser().name}`}return{isYandex:a,isEdgeHTML:i,isChrome:s,isSafari:function(){return!i()&&!a()&&!s()&&/Safari/i.test(t)},isFirefox:function(){return/Firefox/i.test(t)},isAndroid:c,isIpad:l,isIOS:d,isMobile:u,isMobileNative:function(){return/ReactNative/.test(t)||/MobileNative/.test(t)},isMobileNativeCalendar:function(){return/NotionCalendar/.test(t)},isDesktop:function(){return!u()},isMac:p,isWindows:m,isChromebook:f,isLinux:g,isRetina:function(){return!!e.matchMedia&&e.matchMedia("(-webkit-min-device-pixel-ratio: 1.5),\t\t\t\t\t\t(min--moz-device-pixel-ratio: 1.5),\t\t\t\t\t\t(-o-min-device-pixel-ratio: 3/2),\t\t\t\t\t\t(min-resolution: 1.5dppx)").matches},getDeviceOS:function(){return p()?"mac":m()?"windows":d()?"ios":c()?"android":s()?"chrome":g()?"linux":"unknown"},getBrowserName:b,getBrowserVersion:function(){return`${r.getBrowser().version}`},getDeviceOSVersion:function(){const e=r.getOS().version;return e?e.split(".").map((e=>parseInt(e,10))):[]},getDoNotTrackEnabled:function(){return"1"===e.doNotTrack||("yes"===n.doNotTrack||"1"===n.doNotTrack||("1"===n.msDoNotTrack||!!(e.external&&e.external.msTrackingProtectionEnabled&&e.external.msTrackingProtectionEnabled())))},getIsBannedGoogleSSOUserAgent:function(){return!!/AppleWebKit.*LinkedInApp/i.test(t)||(!!/AppleWebKit.*BytedanceWebview/i.test(t)||!("Facebook"!==b()||!/AppleWebKit/i.test(t)))}}}},141625:(e,t,n)=>{n.d(t,{createDevice:()=>l});n(581454);var o=()=>n(726637),r=()=>n(496506),a=()=>n(757695),i=()=>n(140583),s=()=>n(496603),c=()=>n(250182);function l(e,t){var n;const l=(0,i().E)(e,(null==t?void 0:t.userAgentOverride)??e.navigator.userAgent),d=Boolean(e.__isElectron),u=Boolean(e.__isElectronMail),p=Boolean(e.__isElectronCalendar),m=d||u||p,f=d&&"darwin"===e.__platform,g=d&&"win32"===e.__platform,b=d?function(){const e=/Electron\/(\w+\.\w+.\w+)/.exec(navigator.userAgent);if(e)return e[1].split(".").map((e=>parseInt(e,10)));return}():void 0;let h,v;var _,y;d&&(v=null===(_=e.__desktopConfig)||void 0===_?void 0:_.desktopAppId,h=null===(y=e.__desktopConfig)||void 0===y?void 0:y.targetPlatform);const w=l.isMobileNative(),S=l.isMobileNativeCalendar(),k=d||w,A=!k,C="https:"===e.location.protocol||"http:"===e.location.protocol,I=l.isYandex(),P=l.isEdgeHTML(),T=l.isChrome(),E=l.isSafari(),R=l.isFirefox(),D=l.isAndroid(),M=l.isIpad(),q=l.isIOS(),O=l.isMobile();null==t||null===(n=t.horizontalSizeClassStore)||void 0===n||n.setState(q?M?"unknown":"compact":"unknown");const B=a().Store.createValue(e.innerHeight,{name:"windowHeightStore"}),x=a().Store.createValue(e.innerWidth,{name:"windowWidthStore"});e.addEventListener("resize",s().sg((()=>{B.setState(e.innerHeight),x.setState(e.innerWidth)}),300));let N=Math.max(x.state,B.state);const L=new(r().ComputedStore)((()=>{if(N=Math.max(x.state,B.state),D){const e=600,t=969,n=x.state===N?t:e;return x.state>=n}if(!q)return!1;if(null==t||!t.horizontalSizeClassStore)return!1;switch(null==t?void 0:t.horizontalSizeClassStore.state){case"compact":return!1;case"regular":return!0;case"unknown":return!!M&&x.state>680}}),{debugName:"isTablet"}),U=l.isDesktop(),F=l.isMac(),V=l.isWindows(),j=l.isChromebook(),J=l.isLinux(),H=l.isRetina(),W=O&&A,$=U&&A,z="undefined"!=typeof chrome&&void 0!==chrome.tabs,Z=o().A.version,G=l.getDeviceOS(),K=(0,c().getDeviceOSVersion)().length>0?(0,c().getDeviceOSVersion)():l.getDeviceOSVersion(),Q=d?"electron":w?"react-native":"browser",X=(()=>{if(d||p||u)return f?"mac-desktop":"windows-desktop";if(w){if(D)return"android";if(q)return"ios"}return"web"})(),Y=(()=>{if(d)return f?"mac-desktop":"windows-desktop";if(w){if(D)return"android";if(q)return"ios"}return O?"web-mobile":"web-desktop"})(),ee=l.getBrowserName(),te=l.getBrowserVersion(),ne=l.getDoNotTrackEnabled(),oe=l.getIsBannedGoogleSSOUserAgent(),re=matchMedia("(prefers-reduced-motion: reduce)").matches;return{isElectron:d,isElectronMail:u,isElectronCalendar:p,isElectronAny:m,isElectronMac:f,isElectronWindows:g,desktopAppId:v,electronVersion:b,isMobileNative:w,isMobileBeta:!1,isMobileNativeCalendar:S,isNative:k,isBrowser:A,isHttpApp:C,isYandex:I,isEdgeHTML:P,isChrome:T,isSafari:E,isFirefox:R,isAndroid:D,isIOS:q,isIpad:M,isMobile:O,get isTablet(){return L.state},get isPhone(){return O&&(!L.state||q&&!M)},get isSmallPhone(){return O&&x.state<=320},isDesktop:U,isMac:F,get isApple(){return F||q},isWindows:V,isChromebook:j,isLinux:J,isRetina:H,isMobileBrowser:W,isDesktopBrowser:$,isChromeExtension:z,isIPhoneX:!1,version:Z,desktopAppVersion:undefined,mobileAppVersion:undefined,os:G,osVersion:K,platform:Q,auditLogPlatform:X,browserName:ee,browserVersion:te,doNotTrackEnabled:ne,isBannedGoogleSSOUserAgent:oe,get prefersDarkInterface(){return matchMedia("(prefers-color-scheme: dark)").matches},ramSizeInGB:undefined,deviceType:Y,prefersReducedMotion:re,desktopTargetPlatform:h}}},149208:(e,t,n)=>{n.d(t,{n:()=>o});const o="__notion_html_async"},150749:(e,t,n)=>{n.d(t,{getProfilingToolForSession:()=>a});var o=()=>n(165162);let r;function a(){var e;if(void 0!==r)return r;const t=(null===(e=(0,o().bo)("sentry"))||void 0===e?void 0:e.get("profilesSessionSampleRate",0))||0;return Math.random()<t?(r="sentry",r):(r="none",r)}},152853:(e,t,n)=>{n.d(t,{P:()=>o});Symbol("Model: default value disabled");function o(e){return e}},155959:(e,t,n)=>{n.d(t,{T:()=>r});const o="__mobileAppFeatures";function r(e){const t="undefined"==typeof window?{}:window[o];return void 0!==t&&!0===t[e]}},165162:(e,t,n)=>{n.d(t,{StatsigInitializer:()=>g,ZP:()=>S,bo:()=>k,checkLocalOnDeviceGate:()=>_,getOrCreateStableID:()=>C,statsigClientLoader:()=>v});n(16280),n(898992),n(803949),n(581454);var o=()=>n(636978),r=(o(),()=>n(105138)),a=(r(),()=>n(788238)),i=(a(),()=>n(563528)),s=(i(),()=>n(871386)),c=()=>n(726637),l=()=>n(15623),d=()=>n(29958),u=()=>n(386466),p=()=>n(468076);const m="STATSIG_LOCAL_STORAGE_INTERNAL_STORE_OVERRIDES_V3";let f=null;const g={initializePromise:void 0,isComplete:!1,error:void 0,stableID:void 0,currentUserDoNotUse:void 0,statsigStageTwoUser:void 0,environment:void 0,statsigConfigFetch:void 0,fetchConfigFilePromise:async function(){return f||(f=fetch((0,u().l4)(c().A.version),{cache:"force-cache"}).then((async e=>{var t;const n=null===(t=e.body)||void 0===t?void 0:t.pipeThrough(new TextDecoderStream),o=n?await async function(e){let t="";const n=e.getReader();let o=!1;for(;!o;){const{value:e,done:r}=await n.read();o=r,e&&(t+=e)}return t}(n):"";return o}))),f}};class b extends a().LocalOverrideAdapter{constructor(){super(...arguments),this.preSaveFunction=void 0}setPreSaveFunction(e){this.preSaveFunction||(this.preSaveFunction=e)}getAllOverrides(){const e=super.getAllOverrides();Object.keys((null==e?void 0:e.experiment)??{}).map(o()._DJB2).forEach((t=>{null==e||delete e.experiment[t]}));return Object.keys((null==e?void 0:e.gate)??{}).map(o()._DJB2).forEach((t=>{null==e||delete e.gate[t]})),e}overrideExperiment(e,t){super.overrideExperiment(e,t),this.saveConfig()}overrideGate(e,t){super.overrideGate(e,t),this.saveConfig()}removeExperimentOverride(e){super.removeExperimentOverride(e),this.saveConfig()}removeGateOverride(e){super.removeGateOverride(e),this.saveConfig()}removeAllOverrides(){super.removeAllOverrides(),this.saveConfig()}loadConfig(){const e=localStorage.getItem(m);try{let t;if(e&&(t=JSON.parse(e)),!t||"object"!=typeof t)return;void 0!==t.gates&&Object.entries(t.gates).forEach((e=>{let[t,n]=e;"boolean"==typeof n&&super.overrideGate(t,n)}));const n=e=>"object"==typeof e&&null!==e;void 0!==t.configs&&Object.entries(t.configs).forEach((e=>{let[t,o]=e;n(o)&&super.overrideExperiment(t,o)}))}catch(t){}}saveConfig(){var e;null===(e=this.preSaveFunction)||void 0===e||e.call(this);const t=super.getAllOverrides();t&&localStorage.setItem(m,JSON.stringify({gates:t.gate??{},configs:t.experiment??{},layers:t.layer??{}}))}}class h{constructor(e,t){this.localOverrideAdapter=void 0,this.onDeviceEvalAdapter=void 0,this.localOverrideAdapter=e,this.onDeviceEvalAdapter=t}getGateOverride(e,t,n){var o;const r=this.localOverrideAdapter.getGateOverride(e,t);if(null!==r)return r;e.details.lcut=void 0;const a=this.onDeviceEvalAdapter.getGateOverride(e,t,n);return null!=a&&null!==(o=a.details.reason)&&void 0!==o&&o.endsWith(d().C)?null:a}overrideGate(e,t){this.localOverrideAdapter.overrideGate(e,t)}removeGateOverride(e){this.localOverrideAdapter.removeGateOverride(e)}getDynamicConfigOverride(e,t,n){var o;const r=this.localOverrideAdapter.getDynamicConfigOverride(e,t);if(null!==r)return r;e.details.lcut=void 0;const a=this.onDeviceEvalAdapter.getDynamicConfigOverride(e,t,n);return null!=a&&null!==(o=a.details.reason)&&void 0!==o&&o.endsWith(d().C)?null:a}overrideDynamicConfig(e,t){this.localOverrideAdapter.overrideDynamicConfig(e,t)}removeDynamicConfigOverride(e){this.localOverrideAdapter.removeDynamicConfigOverride(e)}getExperimentOverride(e,t,n){var o;const r=this.localOverrideAdapter.getExperimentOverride(e,t);if(null!==r)return r;e.details.lcut=void 0;const a=this.onDeviceEvalAdapter.getExperimentOverride(e,t,n);return null!=a&&null!==(o=a.details.reason)&&void 0!==o&&o.endsWith(d().C)?null:a}overrideExperiment(e,t){this.localOverrideAdapter.overrideExperiment(e,t)}removeExperimentOverride(e){this.localOverrideAdapter.removeExperimentOverride(e)}getLayerOverride(e,t,n){var o;const r=this.localOverrideAdapter.getLayerOverride(e,t);if(null!==r)return r;e.details.lcut=void 0;const a=this.onDeviceEvalAdapter.getLayerOverride(e,t,n);return null!=a&&null!==(o=a.details.reason)&&void 0!==o&&o.endsWith(d().C)?null:a}overrideLayer(e,t){this.localOverrideAdapter.overrideLayer(e,t)}removeLayerOverride(e){this.localOverrideAdapter.removeLayerOverride(e)}getParamStoreOverride(e,t){return this.onDeviceEvalAdapter.getParamStoreOverride(e,t)}getAllOverrides(){return this.localOverrideAdapter.getAllOverrides()}removeAllOverrides(){this.localOverrideAdapter.removeAllOverrides()}setData(e){this.onDeviceEvalAdapter.setData(e)}loadFromStorage(){return this.localOverrideAdapter.loadFromStorage()}loadConfig(){this.localOverrideAdapter.loadConfig()}setPreSaveFunction(e){this.localOverrideAdapter.setPreSaveFunction(e)}}const v=new class{constructor(){this.completedStageOne=void 0,this.completedStageTwo=void 0,this.overrideAdapter=void 0,this.cachedOnDeviceEvalAdapter=void 0}async getUninitializedStatsigClient(e){const{statsigUser:t,initialValues:n,overrideStableID:o,configFilePromise:a}=e;const i=new b;this.cachedOnDeviceEvalAdapter||(this.cachedOnDeviceEvalAdapter=await this.getOnDeviceEvalAdapter(a)),this.overrideAdapter=new h(i,this.cachedOnDeviceEvalAdapter),o&&(t.customIDs=t.customIDs||{},t.customIDs.stableID=o),g.currentUserDoNotUse=t;const s=new(r().StatsigClient)(c().A.statsig.apiKey,t,{environment:{tier:"production"},networkConfig:{api:"https://exp.notion.so/v1/"},overrideAdapter:this.overrideAdapter,disableEvaluationMemoization:!1,logEventCompressionMode:r().LogEventCompressionMode.Forced});return"string"==typeof n&&s.dataAdapter.setData(n),s}async getOnDeviceEvalAdapter(e){return new(i().OnDeviceEvalAdapter)(await e??null)}async fetchConfigFile(e){const t=performance.now();if(!e)return;const o=await(0,n(763824).nQ)(1e3,e);return g.statsigConfigFetch={durationMs:Math.round(performance.now()-t),success:!o.timeout},o.timeout&&(g.error=new Error("Statsig config fetch timeout"),n(857639).log({level:"error",from:"StatsigClientLoader.ts",type:"statsig_on_device_eval_timeout",data:{miscDataToConvertToString:{timeout:o.timeout}}})),o.result}async loadStageOne(e){const{currentUserId:t,device:n,overrideStableID:o,browserId:r,deviceId:a,locale:i,configFilePromise:s}=e,c=(0,u().tX)({data:{userId:t,device:(0,u().getDeviceAttributesForStatsigUser)(n),stableId:o,browserId:r,deviceId:a,locale:i},config:{env:"production"}}),l=await this.getUninitializedStatsigClient({statsigUser:c,initialValues:void 0,overrideStableID:o,configFilePromise:s});return l.initializeSync(),this.completedStageOne=l,localStorage.setItem(A,o),l}async loadStageTwo(e,t){var n,o;const{statsigUser:r}=e,a="object"==typeof e.initialValues?JSON.stringify(e.initialValues):e.initialValues,i="stableID"in e?e.stableID:null===(n=e.initialValues)||void 0===n?void 0:n.evaluated_keys.customIDs.stableID;let s;return i&&(r.customIDs=r.customIDs||{},r.customIDs.stableID=i),this.completedStageOne?(s=this.completedStageOne,a&&s.dataAdapter.setData(a),g.currentUserDoNotUse=r,s.updateUserSync(r)):s=await this.getUninitializedStatsigClient({statsigUser:r,initialValues:a,overrideStableID:i,configFilePromise:t}),null===(o=this.overrideAdapter)||void 0===o||o.loadConfig(),g.statsigStageTwoUser=r,this.completedStageTwo=s,s}async getClient(){return await g.initializePromise,this.getClientSync()}getClientSync(){return this.completedStageTwo?this.completedStageTwo:this.completedStageOne}getLocalUserOnlyClient(){return this.completedStageOne}syncUserInternal(e){g.currentUserDoNotUse=e}};function _(e){const{gateName:t,disableExposureLogging:n,enableEventTrailLogging:o}=e,r=Boolean(g.initializePromise),a=(0,l().e6)(t);if(void 0!==a)return a;if(r&&!g.error)try{const e=v.getLocalUserOnlyClient();if(!e)return null;const r=e.checkGate(t,{disableExposureLog:n});return o&&(p().y[`statsig-${t}`]=r),r}catch{return null}return null}const y="group",w="_unassigned";function S(e){const{experimentId:t,param:n=y,disableExposureLogging:o}=e;if(Boolean(g.initializePromise)&&!g.error){let e;try{const r=v.getLocalUserOnlyClient();if(!r)return;e=o?r.getExperiment(t,{disableExposureLog:!0}):r.getExperiment(t);const a=e.get(n,w);if(a&&a!==w)return a}catch{}}}function k(e){if(Boolean(g.initializePromise)&&!g.error)try{const t=v.getLocalUserOnlyClient();return t?t.getDynamicConfig(e,{disableExposureLog:!0}):void 0}catch{return}}const A="STATSIG_LOCAL_STORAGE_STABLE_ID";function C(){if(g.stableID)return g.stableID;let e;try{e=localStorage.getItem(A)??void 0}catch(t){}return e||(e=(0,s().A)()),g.stableID=e,e}},179034:(e,t,n)=>{n.d(t,{Ay:()=>c,Ky:()=>i,NL:()=>a,ev:()=>s,nd:()=>r});var o=()=>n(292588);const r=[...["dmca","graphic_or_harmful","exploitation","harassment_abuse_or_threat","hateful","ip_infringement","deceptive_or_malicious","personal_or_confidential","other","verified"]],a={block:!0,collection:!0,space:!0,comment:!0,team:!0,automation_action:!0,skill:!0,collection_view:!0,layout:!0,channel:!0,assistant_chat_session:!0},i={to_do:!0,header:!0,sub_header:!0,sub_sub_header:!0,toggle:!0,quote:!0,bulleted_list:!0,divider:!0,numbered_list:!0,code:!0,text:!1,page:!1,personal_home_page:!1,factory:!1,button:!1,column_list:!1,column:!1,embed:!1,framer:!1,tweet:!1,gist:!1,drive:!1,audio:!1,maps:!1,invision:!1,mixpanel:!1,image:!1,video:!1,file:!1,bookmark:!1,equation:!1,collection_view:!1,collection_view_page:!1,form:!1,breadcrumb:!1,copy_indicator:!1,link_to_page:!1,link_to_collection:!1,figma:!1,loom:!1,typeform:!1,codepen:!1,pdf:!1,callout:!1,table_of_contents:!1,whimsical:!1,miro:!1,abstract:!1,sketch:!1,excalidraw:!1,replit:!1,hex:!1,deepnote:!1,ai_block:!1,drawing:!1,slide:!1,post:!1,transclusion_container:!1,transclusion_reference:!1,external_object_instance:!1,table:!1,table_row:!1,tab:!1,external_object_instance_page:!1,alias:!1,workflow:!1,transcription:!1,person_profile:!1},s="block",c={table:s,columnTypes:{id:o().A.UUID,space_id:o().A.UUID,version:o().A.Number,last_version:o().A.Number,type:o().A.String,properties:o().A.JSON,content:o().A.StringArray,non_content_children:o().A.UUIDArray,discussions:o().A.StringArray,view_ids:o().A.StringArray,collection_id:o().A.UUID,permissions:o().A.JSON,created_time:o().A.Number,last_edited_time:o().A.Number,copied_from:o().A.UUID,file_ids:o().A.StringArray,ignore_block_count:o().A.Boolean,is_template:o().A.Boolean,parent_id:o().A.UUID,parent_table:o().A.String,alive:o().A.Boolean,moved:o().A.JSON,format:o().A.JSON,created_by:o().A.UUID,last_edited_by:o().A.UUID,created_by_table:o().A.String,created_by_id:o().A.UUID,last_edited_by_table:o().A.String,last_edited_by_id:o().A.UUID,content_classification:o().A.String,moved_to_trash_table:o().A.String,moved_to_trash_id:o().A.UUID,moved_to_trash_time:o().A.Number,deleted_from_trash_time:o().A.Number,deleted_from_trash_table:o().A.String,deleted_from_trash_id:o().A.UUID,crdt_data:o().A.JSON,crdt_format_version:o().A.Number},requiredColumns:{parent_id:!0,parent_table:!0,alive:!0,type:!0,space_id:!0},defaultColumnValues:{alive:!1},model:(0,n(152853).P)({RecordStore:!0,properties:{content:{getMethod:"getContentIds",getKeyStoreMethod:"getContentStore"},view_ids:{getMethod:"getCollectionViewIds",getKeyStoreMethod:"getCollectionViewsStore"},properties:{defaultOnRead:{}},format:{defaultOnRead:{}},copied_from:{getMethod:!1},collection_id:{getMethod:!1}}})}},183558:(e,t,n)=>{n.d(t,{Ay:()=>l,Mb:()=>i,Ut:()=>s,_g:()=>d});n(16280),n(898992),n(823215);var o=()=>n(498212),r=()=>n(534177);const a=[":",";","<","=",">","?","@","A","B","C","D","E","F","G","H","I","J","K","L","M","N","O","P","Q","R","S","T","U","V","W","X","Y","Z","[","\\","]","^","_","`","a","b","c","d","e","f","g","h","i","j","k","l","m","n","o","p","q","r","s","t","u","v","w","x","y","z","{","|","}","~"],i=["!",'"',"#","$","%","&","'","(",")","*","+",",","-",".","/","0","1","2","3","4","5","6","7","8","9",...a];function s(e){return e.length>0&&[...e].every((e=>(0,r().Xk)(i,e)))}function c(){return a[Math.floor(Math.random()*a.length)]}function l(){let e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:4;if(e<1)throw new Error("ShortID length must be at least 1");let t=c();for(;t.length<e;)t+=c();return t}function d(e){let t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:4;if(t<1||t>16)throw new Error("ShortID length must be between 1 and 16");const n=(0,o().gB)(e).replace(/-/g,"");let r="";for(let o=0;o<t;o++){const e=2*o,t=n.substring(e,e+2),i=parseInt(t,16)%a.length;r+=a[i]}return r}},187174:(e,t,n)=>{n.d(t,{BC:()=>u,V$:()=>l,Yb:()=>d,fQ:()=>p});n(898992),n(823215),n(672577);var o=n(296540),r=()=>n(319625),a=()=>n(534177),i=()=>n(558842),s=()=>n(591779),c=()=>n(336811);function l(e,t){let n=arguments.length>2&&void 0!==arguments[2]?arguments[2]:{};const{debugName:a}=n;(0,o.useDebugValue)(a);const i=(0,o.useRef)(0),s=(0,c().T)(),[l,d]=(0,o.useState)({status:"idle"}),u=(0,o.useCallback)((()=>i.current++),[i]),p=(0,o.useMemo)((()=>async function(){i.current++;const t=i.current;d((e=>({...e,status:"pending"})));try{const n=await e(...arguments);return t===i.current&&s.current&&d((e=>({...e,status:"resolved",value:n,error:void 0}))),n}catch(n){return t===i.current&&s.current&&d((e=>({...e,status:"rejected",error:(0,r().A)(n),value:void 0}))),Promise.reject(n)}}),[s,...t]);return[l,p,u]}function d(e,t){let n=arguments.length>2&&void 0!==arguments[2]?arguments[2]:{};const{debounce:r,throttle:a,interval:i,debugName:c}=n;(0,o.useDebugValue)(c);const d=(0,s().lW)(t,r,s().MR),u=(0,s().wb)(d,a,s().MR),[p,m,f]=l(e,u,n);return(0,o.useEffect)((function(){function e(){m().catch((e=>function(e,t){console.error("useAsync: promise rejected",t,e)}(e,c)))}if(void 0!==i){let t;function n(){t&&(window.clearInterval(t),t=void 0)}function o(){!document.hidden?t=window.setInterval(e,i):n()}return document.addEventListener("visibilitychange",o),o(),e(),()=>{document.removeEventListener("visibilitychange",o),n(),f()}}return e(),f}),[m,f,i,c]),[p,m,f]}function u(e){let{state:t,render:n,forceRenderLoading:o,spinAfterMs:r=300}=e;const a="resolved"!==t.status||(o??!1),c=(0,s().b_)(a,!1,r,Object.is);return a?(0,i().Du)(n(c)):null}function p(e){const t=Object.values(e),n=(0,o.useMemo)((()=>{const t={};for(const[n,o]of(0,a().WP)(e)){if(!o.value&&"resolved"!==o.status)return;t[n]=o.value}return t}),t);return(0,o.useMemo)((()=>{if(t.every((e=>"resolved"===e.status)))return{status:"resolved",value:n};if(t.every((e=>"idle"===e.status)))return{status:"idle",value:n};const e=t.find((e=>"rejected"===e.status));return e||{status:"pending",value:n}}),t)}},192188:(e,t,n)=>{n.r(t)},203587:(e,t,n)=>{n.d(t,{G:()=>a});const o=80;var r=()=>n(469425);async function a(e){try{const t=await(0,r().CY)(e);return t.pragmas.user_version===o?{endSchema:t,migrations:[],fastForward:void 0}:(await n.e(59031).then(n.bind(n,459031))).AllMigrations}catch(t){return(await n.e(59031).then(n.bind(n,459031))).AllMigrations}}},206267:(e,t,n)=>{n.d(t,{Dt:()=>i,JW:()=>a,gB:()=>r});var o=()=>n(498212);const r=o().gB,a=o().lZ,i=o().lZ},209199:(e,t,n)=>{n.d(t,{A1:()=>k,Hj:()=>g,Tf:()=>d,Vf:()=>S,Yr:()=>a,Zv:()=>P,fy:()=>i,gI:()=>v,hL:()=>A,iK:()=>w,nG:()=>m,o_:()=>y,p5:()=>D,q:()=>I,ur:()=>f,v9:()=>T,vL:()=>C,vR:()=>R});n(898992),n(354520),n(672577),n(581454),n(908872);var o=()=>n(534177);const r={"en-US":{marketing:!0,preferred:!0,routing:!0,language:!1,development:!1,beta:!1},"ko-KR":{marketing:!0,preferred:!0,routing:!1,language:!1,development:!1,beta:!1},"ja-JP":{marketing:!0,preferred:!0,routing:!1,beta:!0,language:!1,development:!1},"fr-FR":{marketing:!0,preferred:!0,routing:!1,language:!1,development:!1,beta:!1},"de-DE":{marketing:!0,preferred:!0,routing:!1,language:!1,development:!1,beta:!1},"es-ES":{marketing:!0,preferred:!0,routing:!0,language:!1,development:!1,beta:!1},"es-LA":{marketing:!0,preferred:!0,routing:!1,language:!1,development:!1,beta:!1},"pt-BR":{marketing:!0,preferred:!0,routing:!1,language:!1,development:!1,beta:!1},"fi-FI":{marketing:!1,preferred:!0,routing:!0,beta:!0,language:!1,development:!1},"da-DK":{marketing:!1,preferred:!0,routing:!0,beta:!0,language:!1,development:!1},"nl-NL":{marketing:!1,preferred:!0,routing:!0,beta:!0,language:!1,development:!1},"nb-NO":{marketing:!1,preferred:!0,routing:!0,beta:!0,language:!1,development:!1},"sv-SE":{marketing:!1,preferred:!0,routing:!0,beta:!0,language:!1,development:!1},"zh-CN":{marketing:!0,preferred:!0,routing:!0,development:!1,beta:!0,language:!1},"zh-TW":{marketing:!0,preferred:!0,routing:!0,development:!1,beta:!0,language:!1},"en-GB":{marketing:!0,preferred:!0,routing:!0,development:!1,beta:!1,language:!1},"id-ID":{marketing:!0,preferred:!0,routing:!1,development:!1,beta:!1,language:!1},"vi-VN":{marketing:!0,preferred:!0,routing:!1,development:!1,beta:!1,language:!1},"th-TH":{marketing:!0,preferred:!0,routing:!1,development:!1,beta:!1,language:!1},es:{marketing:!0,preferred:!1,routing:!0,language:!0,development:!1,beta:!1},fr:{marketing:!0,preferred:!1,routing:!0,language:!0,development:!1,beta:!1},pt:{marketing:!0,preferred:!1,routing:!0,language:!0,development:!1,beta:!1},ko:{marketing:!0,preferred:!1,routing:!0,language:!0,development:!1,beta:!1},ja:{marketing:!0,preferred:!1,routing:!0,language:!0,development:!1,beta:!1},de:{marketing:!0,preferred:!1,routing:!0,language:!0,development:!1,beta:!1},vi:{marketing:!0,preferred:!1,routing:!0,language:!0,development:!1,beta:!1},id:{marketing:!0,preferred:!1,routing:!0,language:!0,development:!1,beta:!1},th:{marketing:!0,preferred:!1,routing:!0,language:!0,development:!1,beta:!1},"he-IL":{marketing:!1,preferred:!0,routing:!1,language:!1,development:!0,beta:!0},"ar-SA":{marketing:!1,preferred:!0,routing:!1,language:!1,development:!0,beta:!0}},a=(0,o().WP)(r).filter((e=>{let[t,{preferred:n,marketing:o}]=e;return n&&o})).map((e=>{let[t]=e;return t})),i=(0,o().WP)(r).filter((e=>{let[t,{preferred:n,development:o}]=e;return n&&!o})).map((e=>{let[t]=e;return t})),s=(0,o().WP)(r).filter((e=>{let[t,{preferred:n,beta:o}]=e;return n&&o})).map((e=>{let[t]=e;return t})),c=((0,o().WP)(r).filter((e=>{let[t,{routing:n,development:o}]=e;return n&&!o})).map((e=>{let[t]=e;return t})),(0,o().WP)(r).filter((e=>{let[t,{routing:n,marketing:o}]=e;return n&&o})).map((e=>{let[t]=e;return t}))),l=["pseudo"],d=[...l,...(0,o().WP)(r).filter((e=>{let[t,{preferred:n,development:o}]=e;return n&&o})).map((e=>{let[t]=e;return t}))],u=((0,o().WP)(r).filter((e=>{let[t,{routing:n,development:o}]=e;return n&&o})).map((e=>{let[t]=e;return t})),[...i,...s,...d]),p=(0,o().WP)(r).filter((e=>{let[t,{language:n}]=e;return n})).map((e=>{let[t]=e;return t})),m=d.map((e=>({[e.split("-").join("")]:`/${e.toLocaleLowerCase()}`}))).reduce(((e,t)=>Object.assign(e,t)),{}),f=u.map((e=>({[e.split("-").join("")]:`/${e.toLocaleLowerCase()}`}))).reduce(((e,t)=>Object.assign(e,t)),{}),g=p.map((e=>({[e.toLocaleLowerCase()]:`/${e.toLocaleLowerCase()}`}))).reduce(((e,t)=>Object.assign(e,t)),{}),b={"es-LA":"es-419","zh-TW":"zh-Hant-TW",pseudo:"yav"},h=["en","ko","ja","fr","de","es","pt","fi","da","nl","nb","sv","zh","id","vi","th"],v={de:"de-DE",ko:"ko-KR",en:"en-US",es:"es-LA",fr:"fr-FR",ja:"ja-JP",pt:"pt-BR",fi:"fi-FI",da:"da-DK",nl:"nl-NL",nb:"nb-NO",sv:"sv-SE",zh:"zh-CN",id:"id-ID",vi:"vi-VN",th:"th-TH"},_={"es-ES":"es","es-LA":"es","en-US":"en","en-GB":"en","pt-BR":"pt","fr-FR":"fr","de-DE":"de","ja-JP":"ja","da-DK":"da","sv-SE":"sv","zh-CN":"zh","zh-TW":"zh","ko-KR":"ko","nb-NO":"nb","nl-NL":"nl","fi-FI":"fi","id-ID":"id","vi-VN":"vi","th-TH":"th","he-IL":"he","ar-SA":"ar",pseudo:"en"};function y(e){return _[e]}function w(e){return(0,o().Xk)(i,e)}function S(e){return["he-IL","ar-SA"].includes(e)}function k(){return"rtl"===document.dir}function A(e){return(0,o().Xk)(h,e)}function C(e){return w(e)||function(e){return(0,o().Xk)(s,e)}(e)||function(e){return(0,o().Xk)(d,e)}(e)}const I="en-US";function P(e){var t;if(!e)return I;let n=e;const r=e.toLowerCase();return function(e){return(0,o().Xk)(c,e)}(r)&&(n=E[r]),null===(t=n)||void 0===t||null===(t=t.replace(/(\-[a-z])\w+/g,(e=>e.toUpperCase())))||void 0===t?void 0:t.replace(/([A-Z]*[A-Z]\-)+/gm,(e=>e.toLocaleLowerCase()))}u.map((e=>b[e]??e)).filter((function(e){return!(e in b)}));function T(e){const t=P(e);return[...i,...d,...s].includes(t)?b[t]||t:I}const E={"en-US":"en-US",ko:"ko-KR",ja:"ja-JP",fr:"fr-FR",de:"de-DE","es-ES":"es-ES",es:"es-LA",pt:"pt-BR","zh-CN":"zh-CN","zh-TW":"zh-TW","en-GB":"en-GB",vi:"vi-VN",th:"th-TH",id:"id-ID"};function R(e){return(0,o().Xk)(a,e)}function D(e){if(e)return R(e)?e:void 0}},209881:(e,t,n)=>{n.r(t)},210138:(e,t,n)=>{n.d(t,{Cd:()=>k,ic:()=>D,Qk:()=>h,VH:()=>v,AM:()=>_,mh:()=>y,Df:()=>O,s7:()=>M,qQ:()=>I,Um:()=>P,X5:()=>E,Dr:()=>T,m_:()=>x,WX:()=>i,Kh:()=>q,_D:()=>F,yP:()=>V,mv:()=>p,Fg:()=>U,lV:()=>R,Ul:()=>C,rT:()=>w,yI:()=>f,Xg:()=>B,Ow:()=>L,vP:()=>d,d5:()=>A,ub:()=>b,US:()=>s,iD:()=>c,op:()=>u,pA:()=>m,md:()=>N,IQ:()=>S});n(16280);var o=()=>n(513416);const r=["DatastoreInfraError","PostgresConnectionError","PostgresConnectionTerminatedError","PostgresDeadlockError","PostgresServerLoginFailingError","PostgresTimeout","PgPoolWaitConnectionTimeout","PgbouncerMaxClientConnExceeded","CrdtAssertionError","CrdtIdAlreadyExistsError","CrdtOperationAlreadyAppliedError","CrdtOperationLikelyAlreadyAppliedError","EnqueueSyncTaskTimeout","EnqueueSyncTaskFailure","OfflineSyncPageNotFoundDuringVersionCheckError","DictationOpenAIError","DictationForbiddenError","DictationUpgradeRequiredError","DictationRateLimitError","DictationAudioRecorderFailedError","DictationMissingUserOrSpaceError","DictationSystemAudioFailedError","DictationStartTimeoutError","DictationSystemAudioHardwareError","StreamingSocketUnavailableError","RedundantDuplicationRequestError","TemplateNotPurchasedError","AbortedError","AWSRequestHandlerError","AiSourcePickerQueryError","AiSourcePickerResponseError","AirtableError","AppleError","AsanaError","AudioProcessorError","BillingSequencingLockError","BlockPropertiesMaxSize","CantAccessWorkspaceFromNetworkError","CantAccessWorkspaceFromEKMError","CellMappingNotFoundError","ChartPaywallError","ChiliPiperError","ChromeError","CohereError","CollectionSchemaOptionSizeTooLarge","CollectionDeletedSchemaMaxSize","CollectionSchemaTooLargeError","CollectionSchemaMaxSize","ConfluenceImportError","ConnectionError","ContentfulError","ContentfulNotFoundError","ContentfulUnauthorizedError","DatabaseLookupError","DebeziumError","DecryptionError","DiffbotError","DiffbotExtractError","DirectRequestHandlerError","DiscordError","DomainNameUnchangedError","Dynamo5XXError","DynamoConditionalCheckError","ElasticsearchBulkIndexError","ElasticsearchError","ElasticsearchIndexingError","ElasticsearchLtrError","EvernoteError","ExportRendererError","FailedConsolidationError","FileImportError","FileUploadError","ForbiddenError","GetStatusError","GiphyError","GlobalOauthError","GoogleAdsError","GoogleError","GroupsV1NotSupportedError","HttpRequestError","IframelyError","ImportError","IndexActivityEditsError","InternalApiError","InternalFetchError","InternalServerError","InvalidNameErrorData","InvalidOrExpiredTokenError","JiraError","JiraPermissionError","JiraApiError","JiraCloudSubscriptionExpiredError","JsonParseError","LogicalError","MarketoError","MarketoRateLimitError","MarketoServerError","MemcachedError","MessageStoreError","MessageStoreRedisError","MfaBackupCodeExpiredError","MicrosoftError","MissingSecretError","MissingStaticFileError","MissingTokenError","MondayError","MultiCellLogicalError","NetworkError","NoAvailableSearchClustersError","NoSearchResponseError","NotFoundError","NotImplementedError","NotionCalendarError","NotionMailError","OpenAIError","OperationConflictError","OrganizationNotFoundError","PandocError","PaymentRequiredError","PdfTodocxError","PostProcessingTaskError","PostgresCardinalityViolation","PostgresCollisionError","PostgresConnectionError","PostgresConnectionTerminatedError","PostgresDeadlockError","PostgresInvalidTextRepresentation","PostgresInvalidUnicodeCharacter","PostgresNullConstraintError","PostgresProgramLimitExceeded","PostgresServerLoginFailingError","PostgresTimeout","PostgresUniqueViolation","ProjectManagementImporterError","PublicAPIError","QueueProxyError","QuipError","RedisError","RequestHandlerError","RequestProxyUpstreamError","RequestTimeoutCancellationError","ResourceExpiredError","RevenueCatError","S3UploadError","S3BatchOperationError","SalesforceError","InvalidExternalIntegrationTokenError","SamlConfigurationError","ServiceUnavailableError","SlackError","SpaceFrozenError","SpaceNotFoundError","StatsigBootstrapError","StripeError","SyncedCollectionUpdateError","TaskQueueDataTooLongError","TokenRateLimitError","TokenStoreError","TooManyRequestsError","TransactionTimeout","TrelloError","TwilioError","UnauthorizedError","UnfurlResponseError","UniversalRetrievalError","UnknownCollectionServerError","UnknownError","UnprocessableEntity","UnsavedTransactionsError","UnsplashError","UnsupportedMediaTypeError","UntriedTransactionError","UserRateLimitResponse","UserValidationError","UserValidationResponse","ValidationError","WebhookError","WorkspaceConsolidationRetrySmallerBatchError","WorkspaceCreationError","ZendeskError","ConfigValidationError","RequestRecordLoadLimitError"],a=Error;class i extends a{constructor(e){var t;super(e.message),this.level=void 0,this.statusCode=void 0,this.headers=void 0,this.name=void 0,this.data=void 0,this.error=void 0,this.body=void 0,this.retryable=void 0,this.level=e.level,this.statusCode=e.status,this.name=e.name,this.data=e.data,this.error=e.error,this.body={errorId:(0,n(498212).lZ)(),name:e.name,clientData:e.clientData,debugMessage:e.message},void 0!==e.stack&&(this.stack=e.stack),this.retryable=e.retryable||(null===(t=e.error)||void 0===t?void 0:t.retryable)||!1}}function s(e){return"object"==typeof e&&null!==e&&r.includes(e.name)}function c(e){if(!s(e))return!1;for(var t=arguments.length,n=new Array(t>1?t-1:0),o=1;o<t;o++)n[o-1]=arguments[o];for(const r of n)if(e.name===r.type||e instanceof r)return!0;return!1}class l extends i{constructor(e){super(e),this.body=void 0;const t=this.body;this.body={...t,clientData:e.clientData}}}function d(e){return void 0!==e.clientData}function u(e){return L(e)&&d(e)}class p extends Error{constructor(e,t,n){super(e),this.data=void 0,this.clientData=void 0,this.name="QueueApiError",this.data=t,this.clientData=n}}function m(e){return Boolean(e&&"QueueApiError"===e.name&&void 0!==e.clientData)}(class extends i{constructor(e){super({level:"info",status:404,name:"MissingTokenError",message:e,error:(arguments.length>1&&void 0!==arguments[1]?arguments[1]:{}).error})}}).type="MissingTokenError";(class extends i{constructor(e){super({level:"info",status:422,name:"UnprocessableEntity",message:e})}}).type="UnprocessableEntity";class f extends i{constructor(e){let t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{};super({level:"warning",status:400,name:"ValidationError",message:e,data:t.data,error:t.error,clientData:t.clientData})}}f.type="ValidationError";class g extends i{constructor(e,t){let n=arguments.length>2&&void 0!==arguments[2]?arguments[2]:{};super({level:"error",status:500,name:e,message:t,data:n.data,error:n.error,clientData:n.clientData})}}function b(e){return e instanceof g}class h extends g{constructor(e){super("CrdtAssertionError",e,arguments.length>1&&void 0!==arguments[1]?arguments[1]:{})}}class v extends g{constructor(e){super("CrdtIdAlreadyExistsError",e,arguments.length>1&&void 0!==arguments[1]?arguments[1]:{})}}class _ extends g{constructor(e){super("CrdtOperationAlreadyAppliedError",e,arguments.length>1&&void 0!==arguments[1]?arguments[1]:{})}}class y extends g{constructor(e){super("CrdtOperationLikelyAlreadyAppliedError",e,arguments.length>1&&void 0!==arguments[1]?arguments[1]:{})}}(class extends i{constructor(e){let t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{};super({level:"warning",status:400,name:"ExportRendererError",message:e,data:t.data,error:t.error,clientData:t.clientData})}}).type="ExportRendererError";class w extends l{constructor(e,t){super({level:"info",status:400,name:"UserValidationError",message:e,clientData:t,...arguments.length>2&&void 0!==arguments[2]?arguments[2]:{}})}}w.type="UserValidationError";function S(e){return Boolean(e&&"UserValidationResponse"===e.name&&void 0!==e.clientData)}class k extends l{constructor(e){super({level:"info",status:403,name:"CantAccessWorkspaceFromNetworkError",message:"Unfortunately, we can't access this workspace from your network. Please try again from a different network.",clientData:e})}}function A(e){return void 0!==e&&("cant_access_workspace_from_network"===e.type||"cant_access_workspace_from_client"===e.type)}class C extends l{constructor(e,t){super({level:"warning",status:429,name:"UserRateLimitResponse",message:"Please try again later.",clientData:e,data:t})}}C.type="UserRateLimitResponse";class I extends i{constructor(e,t){super({level:"info",status:403,name:"ForbiddenError",message:e,data:t})}}I.type="ForbiddenError";class P extends i{constructor(e){let t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{};super({level:"error",status:400,name:"LogicalError",message:e,data:t.data,error:t.error})}}P.type="LogicalError";(class extends i{constructor(e,t){super({...t,level:"error",status:503,name:"DatabaseLookupError",message:e})}}).type="DatabaseLookupError";(class extends i{constructor(e,t){super({level:"error",status:503,name:"ElasticsearchBulkIndexError",message:"ElasticsearchBulkIndexError",data:e,error:t.error}),this.response=void 0,this.response=e}}).type="ElasticsearchBulkIndexError";(class extends i{constructor(e){super({level:"error",status:503,name:"ElasticsearchIndexingError",message:"ElasticsearchIndexingError",data:e,error:e.error})}}).type="ElasticsearchIndexingError";class T extends i{constructor(e){let t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{};super({level:"error",status:502,name:"NetworkError",message:e,data:t.data,error:t.error})}}T.type="NetworkError";(class extends i{constructor(e){let t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{};super({level:"error",status:400,name:"SalesforceError",message:e,data:t.data,error:t.error,clientData:t.clientData})}}).type="SalesforceError";(class extends i{constructor(e,t){let n=arguments.length>2&&void 0!==arguments[2]?arguments[2]:{};super({level:"error",status:401,name:"InvalidExternalIntegrationTokenError",message:`${e}: ${t}`,data:n.data,error:n.error})}}).type="InvalidExternalIntegrationTokenError";class E extends i{constructor(e){let t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{};super({level:"error",status:400,name:"MondayError",message:e,data:t.data,error:t.error,clientData:t.clientData})}}class R extends i{constructor(e){let t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{};super({level:"error",status:400,name:"TrelloError",message:e,data:t.data,error:t.error})}}class D extends l{constructor(e,t){super({level:"info",status:400,name:"ConfluenceImportError",message:e,...t})}}class M extends l{constructor(e,t){super({level:"info",status:400,name:"FileImportError",message:e,...t})}}(class extends i{constructor(e){super({level:"error",status:(0,o().Mh)(e.status)?e.status:400,name:"UnfurlResponseError",message:e.message??"Error parsing unfurl response",data:e.data})}}).type="UnfurlResponseError";(class extends i{constructor(){let e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{};e.error&&e.error.client&&e.error.client&&delete e.error.client,super({level:"warning",status:400,name:"PostgresInvalidUnicodeCharacter",message:"Unsupported unicode escape sequence.",data:e.data,error:e.error})}}).type="PostgresInvalidUnicodeCharacter";(class extends i{constructor(){let e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{};e.error&&e.error.client&&e.error.client&&delete e.error.client,super({level:"warning",status:400,name:"PostgresInvalidTextRepresentation",message:"The given text cannot be converted to the desired data type (eg, invalid unicode or UUID)",data:e.data,error:e.error})}}).type="PostgresInvalidTextRepresentation";class q extends i{constructor(e){let t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{};super({level:"warning",status:400,name:"OperationConflictError",message:e,data:t.data,error:t.error,clientData:t.clientData})}}class O extends i{constructor(e){let t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{};super({level:"error",status:400,name:"EvernoteError",message:e,data:t.data,error:t.error})}}class B extends i{constructor(e,t,n){super({level:"error",status:400,name:"WorkspaceCreationError",message:e,data:t,error:n})}}(class extends i{constructor(e){super({level:"error",status:500,name:"UnknownError",message:"Unrecognized error.",error:e,retryable:(arguments.length>1&&void 0!==arguments[1]?arguments[1]:{}).retryable})}}).type="UnknownError";class x extends i{constructor(e){super({level:"error",status:404,name:"NotFoundError",message:(null==e?void 0:e.message)||"Resource not found.",error:null==e?void 0:e.error})}}x.type="NotFoundError";function N(e){return Boolean("unsaved_transactions"===(null==e?void 0:e.type)&&e.errors.length>0)}function L(e){return"object"==typeof e&&null!==e&&e.errorId&&e.name}(class extends i{constructor(e){const{message:t,originalError:n}=e;super({level:"error",status:400,name:"DynamoConditionalCheckError",message:t,error:n})}}).type="DynamoConditionalCheckError";(class extends i{constructor(e){super({level:"error",status:404,name:"SpaceNotFoundError",message:e})}}).type="SpaceNotFoundError";(class extends i{constructor(e){let t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{};super({level:"error",status:500,name:"InternalServerError",message:e,data:t.data,error:t.error})}}).type="InternalServerError";(class extends i{constructor(e){super({level:"error",status:400,name:"WorkspaceConsolidationRetrySmallerBatchError",message:e})}}).type="WorkspaceConsolidationRetrySmallerBatchError";(class extends i{constructor(e,t){super({level:"error",status:422,name:"SpaceFrozenError",message:`Space is frozen. Please try once the freeze is lifted. Reason: ${t.reason}`,data:{spaceId:e,reason:t.reason},retryable:!0})}}).type="SpaceFrozenError";class U extends i{constructor(){let e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{};super({level:"error",status:400,name:"TaskQueueDataTooLongError",message:"Task data too long",data:e.data,error:e.error,clientData:e.clientData})}}U.type="TaskQueueDataTooLongError";const F=402;class V extends i{constructor(e){super({level:"error",name:"PaymentRequiredError",status:F,message:e??"Payment is required."})}}V.type="PaymentRequiredError"},219187:(e,t,n)=>{n.r(t),n.d(t,{electronApi:()=>r().electronApi,electronCalendarApi:()=>a,electronMailApi:()=>i,getAndCacheDesktopVersionAsync:()=>l,getDesktopVersion:()=>c});var o=()=>n(711059),r=()=>n(675742);const a=window.__electronCalendarApi,i=window.__electronMailApi;let s;function c(){if(s)return(0,o().parseVersion)(s)}async function l(){return!s&&r().electronApi&&(s=await r().electronApi.getAppVersion()),c()}},227440:(e,t,n)=>{n.d(t,{A:()=>a,e:()=>i});class o extends(()=>n(757695))().Store{getInitialState(){return{online:!0,syncing:!1,lastOfflineTimestamp:void 0,mobileConnectivityType:void 0}}}const r=new o,a=r;(0,n(604341).exposeDebugValue)("ConnectionStore",r);const i=new(n(496506).ComputedStore)((()=>r.state.online),{debugName:"connectionStoreIsOnlineStore"})},236005:(e,t,n)=>{n.d(t,{H2:()=>m,Kr:()=>l,Mq:()=>f});n(898992),n(737550);var o=()=>n(427704);const r=["de-de","es-la","fr-fr","ja-jp","ko-kr","pt-br","zh-cn","zh-tw","pseudo","de","en-us","es","es-es","fr","ja","ko","pt","en-gb","id","vi","th"];function a(e){const{path:t,route:n}=e;if(t.toLowerCase()===n.toLowerCase())return!0;const o=n.endsWith("/")?n:`${n}/`;return!!t.startsWith(o)}function i(e){const{path:t,routes:n,locales:o}=e;for(const r of n){if(a({path:t,route:r}))return{route:r};for(const e of o){if(a({path:t,route:`/${e}${r}`}))return{route:r,locale:e}}}}const s=[o().JZ.downloadWindows,o().JZ.downloadWindowsArm,o().JZ.downloadMacAppleSilicon,o().JZ.downloadMacIntel,o().JZ.downloadMacUniversal,o().JZ.downloadMac,o().JZ.downloadCalendarMacAppleSilicon,o().JZ.downloadCalendarMacIntel,o().JZ.downloadCalendarMacUniversal,o().JZ.downloadCalendarMac,o().JZ.downloadCalendarWindows,o().JZ.downloadMailMac],c=[/^\/desktop\/Notion.*\.(dmg|exe)$/],l="fredir";Object.assign(o().Bf,n(209199).nG);const d=Object.values(o().zK),u=Object.values(o().eR),p=Object.values(o().Bf);function m(e,t){var n;return null===(n=f(e,t))||void 0===n?void 0:n[0]}function f(e,t){if(void 0!==e&&!i({path:e,routes:s,locales:[]})&&!c.some((t=>Boolean(e.match(t)))))return i({path:e,routes:d,locales:[]})?["internal",e]:i({path:e,routes:p,locales:r})?["devOnly",e]:i({path:e,routes:u,locales:r})?["normal",e]:"/"!==e||t?r.some((t=>e.toLowerCase()===`/${t.toLowerCase()}`))?["root",e]:e.startsWith(o().eR.templateCreator)?["normal","/@:templateCreator"]:void 0:["root",e]}},239391:(e,t,n)=>{n.d(t,{J:()=>r,q:()=>a});var o=()=>n(645873);const r=new(o().O2)("BlockPropertyRouter",(()=>Promise.all([n.e(75134),n.e(99223),n.e(58795),n.e(17230),n.e(23740),n.e(34359),n.e(48113),n.e(36639),n.e(48307),n.e(14310),n.e(28398),n.e(55850),n.e(46580),n.e(22542),n.e(28464),n.e(23993),n.e(40241),n.e(46990)]).then(n.bind(n,740241)))),a=(0,o()._h)(r,(e=>e.BlockPropertyRouter))},249229:(e,t,n)=>{async function o(e){let{environment:t,currentUserId:o}=e;const{performPrefetchRequests:r}=await Promise.resolve().then(n.bind(n,669484));return r({environment:t,currentUserId:o})}n.d(t,{prefetchRequests:()=>o})},249386:(e,t,n)=>{n.d(t,{BM:()=>o,HM:()=>r});const o="n",r="null"},250182:(e,t,n)=>{n.r(t),n.d(t,{getDesktopDeviceInfo:()=>r,getDeviceOSVersion:()=>o});n(581454);function o(){const e=a();return e?e.os.split(".").map((e=>parseInt(e,10))):[]}async function r(){var e;const t={};if(!window.__isElectron)return{};const n=a();if(n)t.desktopOsVersion=n.os,t.desktopKernelVersion=n.kernel,t.desktopElectronVersion=n.electron,t.desktopChromiumVersion=n.chromium,t.desktopArch=n.arch,t.desktopPlatform=n.platform,t.desktopMachineModelIdentifier=n.machineModelIdentifier;else{const e=["architecture","fullVersionList","platformVersion"],n=await navigator.userAgentData.getHighEntropyValues(e);t.desktopArch="arm"===(o=n.architecture)?"arm64":o,t.desktopOsVersion=n.platformVersion,t.desktopPlatform=window.__platform;const r=navigator.userAgent.match(/Chrome\/([\d.]+)/);t.desktopChromiumVersion=r?r[1]:"Unknown";const a=navigator.userAgent.match(/Electron\/([\d.]+)/);t.desktopElectronVersion=a?a[1]:"Unknown"}var o;return t.desktopTargetPlatform=null===(e=window.__desktopConfig)||void 0===e?void 0:e.targetPlatform,t}function a(){return window.__desktopDeviceInfo}},258037:(e,t,n)=>{n.d(t,{Kl:()=>a,bC:()=>i,wJ:()=>r});n(581454);const o={"12m - Lenny Business":{type:"trial",campaign:"admin_12m_lenny_business",duration:{days:365},subscriptionTier:"business",products:["business"]},"1m - Business":{type:"trial",campaign:"admin_1m_startups_business",duration:{days:30},subscriptionTier:"business",products:["business"]},"3m - Business":{type:"trial",campaign:"admin_3m_startups_business",duration:{days:90},subscriptionTier:"business",products:["business"]},"6m - Business":{type:"trial",campaign:"admin_6m_startups_business",duration:{days:180},subscriptionTier:"business",products:["business"]},"12m - Business":{type:"trial",campaign:"admin_12m_startups_business",duration:{days:365},subscriptionTier:"business",products:["business"]},"1m - smb":{type:"trial",campaign:"admin_1m_smb_business",duration:{days:30},subscriptionTier:"business",products:["business"]},"3m - smb":{type:"trial",campaign:"admin_3m_smb_business",duration:{days:90},subscriptionTier:"business",products:["business"]},creator_6m:{type:"trial",campaign:"creator_6m",duration:{days:180},subscriptionTier:"plus",products:["plus"]},creator_12m:{type:"trial",campaign:"creator_12m",duration:{days:365},subscriptionTier:"plus",products:["plus"]},creator_12m_no_ai:{type:"trial",campaign:"creator_12m_no_ai",duration:{days:365},subscriptionTier:"plus",products:["plus"]},admin_6m_business:{type:"trial",campaign:"admin_6m_business",duration:{days:180},subscriptionTier:"business",products:["business"]},plus_bundle_1m:{type:"trial",campaign:"plus_bundle_1m",duration:{days:30},subscriptionTier:"plus",products:["plus"]},business_1m:{type:"trial",campaign:"business_1m",duration:{days:30},subscriptionTier:"business",products:["business"]}},r=(0,n(534177).uv)(o),a=Object.values(o).map((e=>{let{campaign:t}=e;return t})),i=[{type:"coupon",campaign:"team_three_months",discount:{type:"percent_off",percentOff:50},duration:{months:3}},{type:"coupon",campaign:"team_annual",discount:{type:"percent_off",percentOff:10},duration:{months:12}},{type:"coupon",campaign:"business_three_months",discount:{type:"percent_off",percentOff:10},duration:{months:3}},{type:"coupon",campaign:"business_annual",discount:{type:"percent_off",percentOff:10},duration:{months:12}},{type:"coupon",campaign:"enterprise_three_months",discount:{type:"percent_off",percentOff:10},duration:{months:3}},{type:"coupon",campaign:"enterprise_annual",discount:{type:"percent_off",percentOff:10},duration:{months:12}},{type:"coupon",campaign:"business_three_months",discount:{type:"percent_off",percentOff:10},duration:{months:3}},{type:"coupon",campaign:"ai_fifty_percent_upgrade",discount:{type:"percent_off",percentOff:50},duration:{months:12}},{type:"coupon",campaign:"ai_fifty_percent_downgrade",discount:{type:"percent_off",percentOff:50},duration:{months:3}},{type:"coupon",campaign:"resurrection_offer",discount:{type:"percent_off",percentOff:50},duration:{months:3}},{type:"coupon",campaign:"lic_25",discount:{type:"percent_off",percentOff:25},duration:{months:12}},{type:"coupon",campaign:"lic_50",discount:{type:"percent_off",percentOff:50},duration:{months:12}},{type:"coupon",campaign:"new_year_2025",discount:{type:"percent_off",percentOff:50},duration:{months:3}},{type:"coupon",campaign:"biz_upgrade_2025_8",discount:{type:"percent_off",percentOff:8},duration:{months:12}},{type:"coupon",campaign:"biz_upgrade_2025_10",discount:{type:"percent_off",percentOff:10},duration:{months:12}},{type:"coupon",campaign:"linkedin_perk_2025_july",discount:{type:"seat_count",seatCount:1},duration:{months:6}},{type:"coupon",campaign:"linkedin_perk_2025_fall",discount:{type:"seat_count",seatCount:1},duration:{months:3}},{type:"coupon",campaign:"referral_50",discount:{type:"percent_off",percentOff:50},duration:{months:12}},{type:"coupon",campaign:"expansion_offer",discount:{type:"percent_off",percentOff:50},duration:{months:1}},{type:"trial",campaign:"default",duration:{days:14},subscriptionTier:"plus",products:["plus"]},{type:"trial",campaign:"enterprise",duration:{days:30},subscriptionTier:"plus",products:["plus"]},{type:"trial",campaign:"mm_ent",duration:{days:30},subscriptionTier:"plus",products:["plus"]},{type:"trial",campaign:"upwork",duration:{days:30},subscriptionTier:"plus",products:["plus"]},{type:"trial",campaign:"perfmark",duration:{days:30},subscriptionTier:"plus",products:["plus"]},{type:"trial",campaign:"reverse",duration:{days:14},subscriptionTier:"plus",products:["plus"]},{type:"trial",campaign:"reverse_mm_ent",duration:{days:30},subscriptionTier:"plus",products:["plus"]},{type:"trial",campaign:"business_reverse",duration:{days:30},subscriptionTier:"business",products:["business"]},{type:"trial",campaign:"business_cc",duration:{days:30},subscriptionTier:"business",products:["business"]},{type:"trial",campaign:"stacked_business_trial",duration:{days:30},subscriptionTier:"business",products:["business"]},{type:"trial",campaign:"referral_biz_trial",duration:{days:90},subscriptionTier:"business",products:["business"]},{type:"trial",campaign:"samsung_tablet_preload_2025",duration:{days:30},subscriptionTier:"plus",products:["plus","ai"]},{type:"trial",campaign:"business_reverse_14d",duration:{days:14},subscriptionTier:"business",products:["business"]},{type:"trial",campaign:"business_cc_14d",duration:{days:14},subscriptionTier:"business",products:["business"]},{type:"trial",campaign:"stacked_business_trial_14d",duration:{days:14},subscriptionTier:"business",products:["business"]},{type:"trial",campaign:"business_cc_14d_personal",duration:{days:14},subscriptionTier:"business",products:["business"]},{type:"trial",campaign:"stacked_business_trial_14d_personal",duration:{days:14},subscriptionTier:"business",products:["business"]},...Object.values(o)]},258595:(e,t,n)=>{n.d(t,{Ay:()=>r,Ko:()=>a,WS:()=>u});n(16280),n(944114);var o=()=>n(56222);function r(e){o().A.sdk=function(e){return{onInjectSDK:e.onInjectSDK,isFullSDK:!1,addBreadcrumb:function(){s({f:"addBreadcrumb",a:arguments})},captureMessage:function(){return s({f:"captureMessage",a:arguments}),""},captureException:function(){return s({f:"captureException",a:arguments}),""},captureEvent:function(){return s({f:"captureEvent",a:arguments}),""},configureScope:function(){s({f:"configureScope",a:arguments})},withScope:function(){s({f:"withScope",a:arguments})},showReportDialog:function(){s({f:"showReportDialog",a:arguments})}}}(e),window.addEventListener("error",c),window.addEventListener("unhandledrejection",l)}function a(){window.removeEventListener("error",c),window.removeEventListener("unhandledrejection",l)}const i=[];function s(e){("e"in e||"p"in e||e.f&&e.f.indexOf("capture")>-1||e.f&&e.f.indexOf("showReportDialog")>-1)&&async function(){if(d)return;d=!0;const e=o().A.sdk;if(e.isFullSDK)throw new Error("Failed to report error to Sentry");await e.onInjectSDK()}(),i.push(e)}function c(){s({e:[].slice.call(arguments)})}function l(e){s({p:"reason"in e?e.reason:"detail"in e&&"reason"in e.detail?e.detail.reason:e})}let d=!1;function u(e){try{for(const o of i)if("f"in o){const{f:t,a:n}=o;e[t].apply(e,n)}const t=window.onerror,n=window.onunhandledrejection;for(const e of i)"e"in e&&t?t.apply(window,e.e):"p"in e&&n&&n.apply(window,[e.p])}catch(t){console.error(t)}}},261642:(e,t,n)=>{n.r(t)},277942:(e,t,n)=>{n.d(t,{$m:()=>l,He:()=>_,J1:()=>m,M4:()=>b,NV:()=>h,_:()=>f,n_:()=>v,ps:()=>y,rm:()=>g,s7:()=>s});var o=()=>n(645873);const r=new(o().O2)("FullPageAI",(()=>Promise.all([n.e(75134),n.e(99223),n.e(58795),n.e(9773),n.e(36556),n.e(40537),n.e(44711),n.e(16471),n.e(53321),n.e(96346),n.e(55373),n.e(36192),n.e(37353),n.e(29151),n.e(90825),n.e(99117),n.e(17230),n.e(23740),n.e(33019),n.e(71621),n.e(92845),n.e(84468),n.e(75078),n.e(73801),n.e(48486),n.e(5605),n.e(95794),n.e(42660),n.e(34359),n.e(44934),n.e(21194),n.e(46414),n.e(66320),n.e(74562),n.e(28271),n.e(28866),n.e(34877),n.e(11676),n.e(11341),n.e(30858),n.e(17254),n.e(63479),n.e(4422),n.e(24587),n.e(27796),n.e(4330),n.e(8184),n.e(28908),n.e(82213),n.e(66626),n.e(39987),n.e(35094),n.e(15139),n.e(97513),n.e(27164),n.e(65881),n.e(3122),n.e(40532),n.e(9562),n.e(52651),n.e(78405),n.e(90978),n.e(1707),n.e(48113),n.e(36639),n.e(48307),n.e(14310),n.e(28398),n.e(55850),n.e(95699)]).then(n.bind(n,130145)))),a=new(o().O2)("AIModePicker",(()=>Promise.all([n.e(75134),n.e(99223),n.e(58795),n.e(9773),n.e(36556),n.e(40537),n.e(44711),n.e(16471),n.e(53321),n.e(96346),n.e(55373),n.e(36192),n.e(37353),n.e(29151),n.e(90825),n.e(99117),n.e(17230),n.e(23740),n.e(33019),n.e(71621),n.e(92845),n.e(84468),n.e(75078),n.e(73801),n.e(48486),n.e(5605),n.e(95794),n.e(42660),n.e(34359),n.e(44934),n.e(21194),n.e(46414),n.e(66320),n.e(74562),n.e(28271),n.e(28866),n.e(34877),n.e(11676),n.e(11341),n.e(30858),n.e(17254),n.e(63479),n.e(4422),n.e(24587),n.e(27796),n.e(4330),n.e(8184),n.e(28908),n.e(82213),n.e(66626),n.e(39987),n.e(35094),n.e(15139),n.e(97513),n.e(27164),n.e(65881),n.e(3122),n.e(40532),n.e(9562),n.e(52651),n.e(78405),n.e(90978),n.e(1707),n.e(48113),n.e(36639),n.e(48307),n.e(14310),n.e(28398),n.e(55850),n.e(82926)]).then(n.bind(n,912034)))),i=new(o().O2)("AgentModelPicker",(()=>Promise.all([n.e(75134),n.e(99223),n.e(58795),n.e(17230),n.e(23740),n.e(36639),n.e(48307),n.e(14310),n.e(28398),n.e(55850),n.e(8243)]).then(n.bind(n,521395)))),s=new(o().O2)("AIChatView",(()=>Promise.all([n.e(75134),n.e(99223),n.e(58795),n.e(9773),n.e(36556),n.e(40537),n.e(44711),n.e(16471),n.e(53321),n.e(96346),n.e(55373),n.e(36192),n.e(37353),n.e(29151),n.e(90825),n.e(99117),n.e(17230),n.e(23740),n.e(16128),n.e(33019),n.e(71621),n.e(92845),n.e(84468),n.e(75078),n.e(73801),n.e(48486),n.e(5605),n.e(95794),n.e(42660),n.e(34359),n.e(44934),n.e(21194),n.e(46414),n.e(66320),n.e(74562),n.e(28271),n.e(28866),n.e(34877),n.e(11676),n.e(11341),n.e(30858),n.e(17254),n.e(63479),n.e(4422),n.e(24587),n.e(27796),n.e(4330),n.e(8184),n.e(28908),n.e(82213),n.e(66626),n.e(39987),n.e(35094),n.e(15139),n.e(97513),n.e(27164),n.e(65881),n.e(3122),n.e(40532),n.e(9562),n.e(52651),n.e(78405),n.e(90978),n.e(1707),n.e(48113),n.e(36639),n.e(48307),n.e(14310),n.e(28398),n.e(55850),n.e(32254),n.e(64271),n.e(77676),n.e(93373),n.e(76260),n.e(9586),n.e(47108)]).then(n.bind(n,241262)))),c=new(o().O2)("AgentChatView",(()=>Promise.all([n.e(75134),n.e(99223),n.e(58795),n.e(9773),n.e(36556),n.e(40537),n.e(44711),n.e(16471),n.e(53321),n.e(96346),n.e(55373),n.e(36192),n.e(37353),n.e(29151),n.e(90825),n.e(99117),n.e(17230),n.e(23740),n.e(481),n.e(33019),n.e(71621),n.e(92845),n.e(84468),n.e(75078),n.e(73801),n.e(48486),n.e(5605),n.e(95794),n.e(42660),n.e(34359),n.e(44934),n.e(21194),n.e(46414),n.e(66320),n.e(74562),n.e(28271),n.e(28866),n.e(34877),n.e(11676),n.e(11341),n.e(30858),n.e(17254),n.e(63479),n.e(4422),n.e(24587),n.e(27796),n.e(4330),n.e(8184),n.e(28908),n.e(82213),n.e(66626),n.e(39987),n.e(35094),n.e(15139),n.e(97513),n.e(27164),n.e(65881),n.e(3122),n.e(40532),n.e(9562),n.e(52651),n.e(78405),n.e(90978),n.e(1707),n.e(48113),n.e(36639),n.e(48307),n.e(14310),n.e(28398),n.e(55850),n.e(31276),n.e(48717),n.e(78741),n.e(93373),n.e(34442),n.e(60796),n.e(48823),n.e(46283)]).then(n.bind(n,71204)))),l=new(o().O2)("AIChatStore",(()=>Promise.all([n.e(36639),n.e(28398),n.e(37342)]).then(n.bind(n,166442)))),d=new(o().O2)("UnifiedChatInput",(()=>Promise.all([n.e(75134),n.e(99223),n.e(58795),n.e(9773),n.e(36556),n.e(40537),n.e(44711),n.e(16471),n.e(53321),n.e(96346),n.e(55373),n.e(36192),n.e(37353),n.e(29151),n.e(90825),n.e(99117),n.e(17230),n.e(23740),n.e(16128),n.e(33019),n.e(71621),n.e(92845),n.e(84468),n.e(75078),n.e(73801),n.e(48486),n.e(5605),n.e(95794),n.e(42660),n.e(34359),n.e(44934),n.e(21194),n.e(46414),n.e(66320),n.e(74562),n.e(28271),n.e(28866),n.e(34877),n.e(11676),n.e(11341),n.e(30858),n.e(17254),n.e(63479),n.e(4422),n.e(24587),n.e(27796),n.e(4330),n.e(8184),n.e(28908),n.e(82213),n.e(66626),n.e(39987),n.e(35094),n.e(15139),n.e(97513),n.e(27164),n.e(65881),n.e(3122),n.e(40532),n.e(9562),n.e(52651),n.e(78405),n.e(90978),n.e(1707),n.e(48113),n.e(36639),n.e(48307),n.e(14310),n.e(28398),n.e(55850),n.e(32254),n.e(77676),n.e(60796),n.e(76260),n.e(9586),n.e(63075)]).then(n.bind(n,459991)))),u=new(o().O2)("AgentThreadHistoryMenu",(()=>Promise.all([n.e(75134),n.e(99223),n.e(58795),n.e(17230),n.e(23740),n.e(36639),n.e(48307),n.e(14310),n.e(28398),n.e(55850),n.e(58703)]).then(n.bind(n,755543)))),p=new(o().O2)("AgentChatActions",(()=>Promise.all([n.e(75134),n.e(99223),n.e(58795),n.e(9773),n.e(36556),n.e(40537),n.e(44711),n.e(16471),n.e(53321),n.e(96346),n.e(55373),n.e(36192),n.e(37353),n.e(29151),n.e(90825),n.e(99117),n.e(17230),n.e(23740),n.e(481),n.e(33019),n.e(71621),n.e(92845),n.e(84468),n.e(75078),n.e(73801),n.e(48486),n.e(5605),n.e(95794),n.e(42660),n.e(34359),n.e(44934),n.e(21194),n.e(46414),n.e(66320),n.e(74562),n.e(28271),n.e(28866),n.e(34877),n.e(11676),n.e(11341),n.e(30858),n.e(17254),n.e(63479),n.e(4422),n.e(24587),n.e(27796),n.e(4330),n.e(8184),n.e(28908),n.e(82213),n.e(66626),n.e(39987),n.e(35094),n.e(15139),n.e(97513),n.e(27164),n.e(65881),n.e(3122),n.e(40532),n.e(9562),n.e(52651),n.e(78405),n.e(90978),n.e(1707),n.e(48113),n.e(36639),n.e(48307),n.e(14310),n.e(28398),n.e(55850),n.e(31276),n.e(48717),n.e(78741),n.e(93373),n.e(34442),n.e(60796),n.e(48823),n.e(46283)]).then(n.bind(n,809729)))),m=(0,o()._h)(r,(e=>e.FullPageAI)),f=(0,o()._h)(a,(e=>e.AIModePicker)),g=(0,o()._h)(i,(e=>e.AgentModelPicker)),b=(0,o()._h)(s,(e=>e.AIChatView)),h=(0,o()._h)(c,(e=>e.AgentChatView)),v=(0,o()._h)(d,(e=>e.UnifiedChatInputProvider)),_=(0,o()._h)(u,(e=>e.AgentThreadHistoryMenu)),y=(0,o()._h)(p,(e=>e.AgentChatActions))},283699:(e,t,n)=>{n.d(t,{Eo:()=>p,Hn:()=>u,R2:()=>h,T0:()=>d,TS:()=>b,bD:()=>_,hl:()=>m,nQ:()=>f,og:()=>l,q1:()=>v,v:()=>g});n(944114),n(517642),n(658004),n(733853),n(845876),n(432475),n(515024),n(731698),n(898992),n(354520),n(803949),n(581454);var o=()=>n(534177),r=()=>n(839816);const a=!0,i=864e5,s=365*i,c={notion_cookie_consent:90*i,growSumoPartnerKey:90*i,notion_s2s_tracking_params:180*i,notion_check_cookie_consent:Number(i),csrf:Number(i),notion_calendar_csrf:Number(i),notion_cookie_sync_completed:90*i,notion_test_cookie_sync_completed:7*i,notion_sync_user_id:90*i};function l(){return r().Rv.filter((e=>!c[e]))}function d(e,t){const n=(0,o().O)(c,e)?c[e]:void 0,r=t??(n||s);return Date.now()+r}async function u(e){const{name:t,cookieService:n}=e;return p({trackingType:r().X6[t],cookieService:n})}async function p(e){const{trackingType:t,cookieService:n}=e;if("necessary"===t)return!0;if(n.isMobileNative())return!1;return function(e){const{trackingType:t,...n}=e;if("necessary"===t)return!0;if("testOnly"===t)return!0;if(n.isMobileNative)return!1;const{hasError:o}=g(n);if(o)return a;const{notion_cookie_consent:r,notion_check_cookie_consent:i}=n;return r?function(e){let{notion_cookie_consent:t,trackingType:n}=e;const o=function(e){try{return JSON.parse(e)}catch(t){return}}(t);if(!o)return a;if(!o.permission)return a;const r=o.permission[n];if("boolean"!=typeof r)return a;return r}({notion_cookie_consent:r,trackingType:t}):"true"!==i}({trackingType:t,...await m({cookieService:n})})}async function m(e){const{cookieService:t}=e,{getCookieWithoutPermissionCheck:n}=t,o=t.isMobileNative();if(o)return{isMobileNative:o,notion_check_cookie_consent:void 0,notion_cookie_consent:void 0};return{isMobileNative:o,notion_check_cookie_consent:await n("notion_check_cookie_consent"),notion_cookie_consent:await n("notion_cookie_consent")}}async function f(e){const{service:t,cookieService:n}=e;return p({trackingType:r().fr[t],cookieService:n})}function g(e){let{notion_check_cookie_consent:t,notion_cookie_consent:n}=e;if(!t)return{hasError:!0,errorType:"emptyCheckCookieConsent"};if("true"!==t&&"false"!==t)return{hasError:!0,errorType:"parseCheckCookieConsentIsNotBoolean"};if(n)try{const e=JSON.parse(n),{permission:t}=e;if(!t)return{hasError:!0,errorType:"emptyPermissionInCookieConsent"};if("boolean"!=typeof t.necessary||"boolean"!=typeof t.performance||"boolean"!=typeof t.preference||"boolean"!=typeof t.targeting)return{hasError:!0,errorType:"malformedPermissionInCookieContent"}}catch(o){return{hasError:!0,errorType:"parseCookieConsentError"}}return{hasError:!1}}function b(e){const{preference:t,performance:n,targeting:o}=e;return t&&n&&o?"accept_all":t||n||o?"partial":"deny_all"}function h(e){let{publicDomainName:t,env:n}=e;const o=window.location.host.split(":")[0];return"local"===n?o:`.${o}`}async function v(e){const{cookieService:t}=e,{removeCookie:n}=t,o=function(){const e={necessary:[],preference:[],performance:[],targeting:[]};for(const t in r().X6){const n=r().X6[t];"testOnly"!==n&&e[n].push(t)}return e}();for(const r in o){if(!(await p({trackingType:r,cookieService:t}))){const e=o[r];await Promise.all(e.map((e=>n(e))))}}}async function _(e){const{cookieService:t}=e,{removeCookie:n}=t;for(const o of r().dc){if(await p({trackingType:o,cookieService:t}))continue;const e=S(o);for(const t of e){const e=y(t);await Promise.all(e.map((e=>n(e))));w(t).forEach((e=>localStorage.removeItem(e)))}}}function y(e){return document.cookie.split(";").map((e=>e.trim())).filter((t=>t.startsWith(e))).map((e=>e.split("=")[0]))}function w(e){return Object.keys(localStorage).filter((t=>t.startsWith(e)))}function S(e){const t=[];for(const n in r().fr)r().fr[n]===e&&t.push(...r().II[n]);return t}new Set(["BE","GR","LT","PT","BG","ES","LU","RO","CZ","FR","HU","SI","DK","HR","MT","SK","DE","IT","NL","FI","EE","CY","AT","SE","IE","LV","PL","IS","NO","LI","CH","GB"])},292588:(e,t,n)=>{n.d(t,{A:()=>o});const o={UUID:"UUID",String:"String",Number:"Number",Boolean:"Boolean",StringArray:"StringArray",JSON:"JSON",XML:"XML",UUIDArray:"UUIDArray",CIDR:"CIDR",NumberArray:"NumberArray",CIDRArray:"CIDRArray",Blob:"Blob"}},302777:(e,t,n)=>{n.d(t,{JZ:()=>o,OU:()=>a});n(898992),n(672577);Symbol("ID path");function o(e){return"object"==typeof e}function r(e,t){if(Array.isArray(e))return e.find((e=>(null==e?void 0:e.id)===t.id))}function a(e,t){let n=e;for(let i=0;i<t.length;i++){const e=t[i];if(null==n)return;var a;if(o(e))n=r(n,e);else n=null===(a=n)||void 0===a?void 0:a[e]}return n}},313127:(e,t,n)=>{n.d(t,{BQ:()=>T,De:()=>a,JE:()=>O,Tj:()=>M,_Z:()=>i,ay:()=>R,dH:()=>r,h0:()=>q,j9:()=>B,t_:()=>D});n(944114),n(898992),n(823215),n(354520),n(581454);var o=()=>n(857639);const r=["onboarding","inapp_modal"];function a(e){const{maybeFunction:t,guessedFunctionVersion:n,maybeRole:r,guessedRoleVersion:a,maybeCategories:i,guessedCategoriesVersion:p,maybeEducationRole:m,guessedEducationRoleVersion:g,maybeEducationLevel:_,guessedEducationLevelVersion:y,maybeNotionUseFrequency:w,guessedNotionUseFrequencyVersion:S,collectedFrom:I,fromMigration:P,logData:T}=e,E=e.collectedAt||(new Date).getTime(),R=void 0!==t&&function(e){if(!(4!==e.guessedFunctionVersion&&4.1!==e.guessedFunctionVersion||"unfilled"!==e.maybeFunction&&"personal"!==e.maybeFunction))return;if(4.1===e.guessedFunctionVersion&&"student"===e.maybeFunction)return;const t=(e=>{const{maybeFunction:t,collectedAt:n,collectedFrom:o,guessedFunctionVersion:r}=e,a={4:{founderCeo:"founder_or_ceo",internalCommunication:"internal_communication",itAdmin:"it_admin",knowledgeManagement:"knowledge_management",productDesign:"product_design",projectProgramMgmt:"project_or_program_management"},4.1:{internalCommunication:"internal_communication",itAdmin:"it_admin",knowledgeManagement:"knowledge_management",productDesign:"product_design",projectProgramMgmt:"project_or_program_management"}},i=r?[r]:[],s=Object.keys(b).filter((e=>b[e].created_at<=n)).reverse().map((e=>parseFloat(e)));i.push(...s);for(const c of i){if("string"==typeof t&&b[c].options.includes(t))return{value:t,version:c,collected_at:n,collected_from:o};if(a[c]&&void 0!==a[c][t])return{value:a[c][t],version:c,collected_at:n,collected_from:o}}})(e);if(t)return void 0!==e.guessedFunctionVersion&&e.guessedFunctionVersion!==t.version&&o().log({level:"warning",from:"surveyDataHelpers.attemptCreateUserSurveyData",type:"ValidationError",data:{miscDataToConvertToString:{field:"function",unexpected_version:e.guessedFunctionVersion,field_version:t.version,field_value:t.value,from_migration:Boolean(e.fromMigration),...e.logData}}}),t;o().log({level:"warning",from:"surveyDataHelpers.attemptCreateUserSurveyData",type:"ValidationError",data:{miscDataToConvertToString:{field:"function",unexpected_value:e.maybeFunction,guessed_version:e.guessedFunctionVersion,latest_version:s,from_migration:Boolean(e.fromMigration),...e.logData}}})}({maybeFunction:t,collectedFrom:I,collectedAt:E,guessedFunctionVersion:n,fromMigration:P,logData:T}),D=void 0!==r&&function(e){const t=(e=>{const{maybeRole:t,collectedAt:n,collectedFrom:o,guessedRoleVersion:r}=e,a={3:{founder_ceo:"founder_or_ceo"}},i=r?[r]:[],s=Object.keys(h).filter((e=>h[e].created_at<=n)).reverse().map((e=>parseFloat(e)));i.push(...s);for(const c of i){if("string"==typeof t&&h[c].options.includes(t))return{value:t,version:c,collected_at:n,collected_from:o};if(a[c]&&void 0!==a[c][t])return{value:a[c][t],version:c,collected_at:n,collected_from:o}}})(e);if(t)return void 0!==e.guessedRoleVersion&&e.guessedRoleVersion!==t.version&&o().log({level:"warning",from:"surveyDataHelpers.attemptCreateUserSurveyData",type:"ValidationError",data:{miscDataToConvertToString:{field:"role",unexpected_version:e.guessedRoleVersion,field_version:t.version,field_value:t.value,from_migration:Boolean(e.fromMigration),...e.logData}}}),t;o().log({level:"warning",from:"surveyDataHelpers.attemptCreateUserSurveyData",type:"ValidationError",data:{miscDataToConvertToString:{field:"role",unexpected_value:e.maybeRole,guessed_version:e.guessedRoleVersion,latest_version:c,from_migration:Boolean(e.fromMigration),...e.logData}}})}({maybeRole:r,collectedFrom:I,collectedAt:E,guessedRoleVersion:a,fromMigration:P,logData:T}),M=Array.isArray(i)&&i.length>0&&function(e){const t=(e=>{const{maybeCategories:t,collectedAt:n,collectedFrom:o,guessedCategoriesVersion:r}=e,a=r?[r]:[];for(const i of a)if(t.every((e=>v[i].options.includes(e))))return{value:t,version:i,collected_at:n,collected_from:o}})(e);if(t)return void 0!==e.guessedCategoriesVersion&&e.guessedCategoriesVersion!==t.version&&o().log({level:"warning",from:"surveyDataHelpers.attemptCreateSpaceSurveyData",type:"ValidationError",data:{miscDataToConvertToString:{field:"use_cases",unexpected_version:e.guessedCategoriesVersion,field_version:t.version,field_value:t.value,from_migration:Boolean(e.fromMigration),...e.logData}}}),t;o().log({level:"warning",from:"surveyDataHelpers.attemptCreateUserSurveyData",type:"ValidationError",data:{miscDataToConvertToString:{field:"use_cases",unexpected_value:e.maybeCategories,guessed_version:e.guessedCategoriesVersion,latest_version:f,from_migration:Boolean(e.fromMigration),...e.logData}}})}({maybeCategories:i,collectedFrom:I,collectedAt:E,guessedCategoriesVersion:p,fromMigration:P,logData:T}),q=void 0!==m&&function(e){const t=(e=>{const{maybeEducationRole:t,collectedAt:n,collectedFrom:o,guessedEducationRoleVersion:r}=e,a=r?[r]:[],i=Object.keys(k).filter((e=>k[e].created_at<=n)).reverse().map((e=>parseFloat(e)));a.push(...i);for(const s of a)if("string"==typeof t&&k[s].options.includes(t))return{value:t,version:s,collected_at:n,collected_from:o}})(e);if(t)return void 0!==e.guessedEducationRoleVersion&&e.guessedEducationRoleVersion!==t.version&&o().log({level:"warning",from:"surveyDataHelpers.attemptCreateUserSurveyData",type:"ValidationError",data:{miscDataToConvertToString:{field:"education_role",unexpected_version:e.guessedEducationRoleVersion,field_version:t.version,field_value:t.value,from_migration:Boolean(e.fromMigration),...e.logData}}}),t;o().log({level:"warning",from:"surveyDataHelpers.attemptCreateUserSurveyData",type:"ValidationError",data:{miscDataToConvertToString:{field:"education_role",unexpected_value:e.maybeEducationRole,guessed_version:e.guessedEducationRoleVersion,latest_version:l,from_migration:Boolean(e.fromMigration),...e.logData}}})}({maybeEducationRole:m,collectedFrom:I,collectedAt:E,guessedEducationRoleVersion:g,fromMigration:P,logData:T}),O=void 0!==_&&function(e){const t=(e=>{const{maybeEducationLevel:t,collectedAt:n,collectedFrom:o,guessedEducationLevelVersion:r}=e,a=r?[r]:[],i=Object.keys(A).filter((e=>A[e].created_at<=n)).reverse().map((e=>parseFloat(e)));a.push(...i);for(const s of a)if("string"==typeof t&&A[s].options.includes(t))return{value:t,version:s,collected_at:n,collected_from:o}})(e);if(t)return void 0!==e.guessedEducationLevelVersion&&e.guessedEducationLevelVersion!==t.version&&o().log({level:"warning",from:"surveyDataHelpers.attemptCreateUserSurveyData",type:"ValidationError",data:{miscDataToConvertToString:{field:"education_level",unexpected_version:e.guessedEducationLevelVersion,field_version:t.version,field_value:t.value,from_migration:Boolean(e.fromMigration),...e.logData}}}),t;o().log({level:"warning",from:"surveyDataHelpers.attemptCreateUserSurveyData",type:"ValidationError",data:{miscDataToConvertToString:{field:"education_level",unexpected_value:e.maybeEducationLevel,guessed_version:e.guessedEducationLevelVersion,latest_version:d,from_migration:Boolean(e.fromMigration),...e.logData}}})}({maybeEducationLevel:_,collectedFrom:I,collectedAt:E,guessedEducationLevelVersion:y,fromMigration:P,logData:T}),B=void 0!==w&&function(e){const t=(e=>{const{maybeNotionUseFrequency:t,collectedAt:n,collectedFrom:o,guessedNotionUseFrequencyVersion:r}=e,a=r?[r]:[],i=Object.keys(C).filter((e=>C[e].created_at<=n)).reverse().map((e=>parseFloat(e)));a.push(...i);for(const s of a)if("string"==typeof t&&C[s].options.includes(t))return{value:t,version:s,collected_at:n,collected_from:o}})(e);if(t)return void 0!==e.guessedNotionUseFrequencyVersion&&e.guessedNotionUseFrequencyVersion!==t.version&&o().log({level:"warning",from:"surveyDataHelpers.attemptCreateUserSurveyData",type:"ValidationError",data:{miscDataToConvertToString:{field:"notion_use_frequency",unexpected_version:e.guessedNotionUseFrequencyVersion,field_version:t.version,field_value:t.value,from_migration:Boolean(e.fromMigration),...e.logData}}}),t;o().log({level:"warning",from:"surveyDataHelpers.attemptCreateUserSurveyData",type:"ValidationError",data:{miscDataToConvertToString:{field:"notion_use_frequency",unexpected_value:e.maybeNotionUseFrequency,guessed_version:e.guessedNotionUseFrequencyVersion,latest_version:u,from_migration:Boolean(e.fromMigration),...e.logData}}})}({maybeNotionUseFrequency:w,collectedFrom:I,collectedAt:E,guessedNotionUseFrequencyVersion:S,fromMigration:P,logData:T}),x={...R&&{function:R},...D&&{role:D},...M&&{categories:M},...q&&{education_role:q},...O&&{education_level:O},...B&&{notion_use_frequency:B}};if(Object.keys(x).length>0)return x}function i(e){const{maybeIntent:t,guessedIntentVersion:n,maybeCollaborativeIntent:r,guessedCollaborativeIntentVersion:a,maybeUseCases:i,guessedUseCasesVersion:s,maybeCompanySize:c,guessedCompanySizeVersion:l,collectedFrom:d,fromMigration:u,logData:b}=e,h=e.collectedAt||(new Date).getTime(),v="string"==typeof t&&function(e){const t=(e=>{const{maybeIntent:t,collectedAt:n,collectedFrom:o,guessedIntentVersion:r}=e,a={2:{personal:"life",team:"work"}},i=r?[r]:[],s=Object.keys(_).filter((e=>_[e].created_at<=n)).reverse().map((e=>parseFloat(e)));i.push(...s);for(const c of i){if("string"==typeof t&&_[c].options.includes(t))return{value:t,version:c,collected_at:n,collected_from:o};if(2===e.guessedIntentVersion&&"string"==typeof t&&a[c]&&void 0!==a[c][t])return{value:a[c][t],version:c,collected_at:n,collected_from:o}}})(e);if(t)return void 0!==e.guessedIntentVersion&&e.guessedIntentVersion!==t.version&&o().log({level:"warning",from:"surveyDataHelpers.attemptCreateSpaceSurveyData",type:"ValidationError",data:{miscDataToConvertToString:{field:"intent",unexpected_version:e.guessedIntentVersion,field_version:t.version,field_value:t.value,fromMigration:e.fromMigration||!1,...e.logData}}}),t;o().log({level:"warning",from:"surveyDataHelpers.attemptCreateSpaceSurveyData",type:"ValidationError",data:{miscDataToConvertToString:{field:"intent",unexpected_value:e.maybeIntent,guessed_version:e.guessedIntentVersion,latest_version:p,from_migration:Boolean(e.fromMigration),...e.logData}}})}({maybeIntent:t,collectedFrom:d,collectedAt:h,guessedIntentVersion:n,fromMigration:u}),k="string"==typeof r&&function(e){const t=(e=>{const{maybeCollaborativeIntent:t,collectedAt:n,collectedFrom:o,guessedCollaborativeIntentVersion:r}=e,a=r?[r]:[],i=Object.keys(y).filter((e=>y[e].created_at<=n)).reverse().map((e=>parseFloat(e)));a.push(...i);for(const s of a)if("string"==typeof t&&y[s].options.includes(t))return{value:t,version:s,collected_at:n,collected_from:o}})(e);if(t)return void 0!==e.guessedCollaborativeIntentVersion&&e.guessedCollaborativeIntentVersion!==t.version&&o().log({level:"warning",from:"surveyDataHelpers.attemptCreateSpaceSurveyData",type:"ValidationError",data:{miscDataToConvertToString:{field:"collaborative_intent",unexpected_version:e.guessedCollaborativeIntentVersion,field_version:t.version,field_value:t.value,fromMigration:e.fromMigration||!1,...e.logData}}}),t;o().log({level:"warning",from:"surveyDataHelpers.attemptCreateSpaceSurveyData",type:"ValidationError",data:{miscDataToConvertToString:{field:"collaborative_intent",unexpected_value:e.maybeCollaborativeIntent,guessed_version:e.guessedCollaborativeIntentVersion,latest_version:m,from_migration:Boolean(e.fromMigration),...e.logData}}})}({maybeCollaborativeIntent:r,collectedFrom:d,collectedAt:h,guessedCollaborativeIntentVersion:a,fromMigration:u}),A=Array.isArray(i)&&i.length>0&&function(e){const t=(e=>{const{maybeUseCases:t,collectedAt:n,collectedFrom:o,guessedUseCasesVersion:r}=e,a=r?[r]:[],i=Object.keys(w).filter((e=>parseFloat(e)!==r)).reverse().map((e=>parseFloat(e)));a.push(...i);for(const s of a)if(t.every((e=>w[s].options.includes(e))))return{value:t,version:s,collected_at:n,collected_from:o}})(e);if(t)return void 0!==e.guessedUseCasesVersion&&e.guessedUseCasesVersion!==t.version&&o().log({level:"warning",from:"surveyDataHelpers.attemptCreateSpaceSurveyData",type:"ValidationError",data:{miscDataToConvertToString:{field:"use_cases",unexpected_version:e.guessedUseCasesVersion,field_version:t.version,field_value:t.value,from_migration:Boolean(e.fromMigration),...e.logData}}}),t;o().log({level:"warning",from:"surveyDataHelpers.attemptCreateSpaceSurveyData",type:"ValidationError",data:{miscDataToConvertToString:{field:"use_cases",unexpected_value:e.maybeUseCases,guessed_version:e.guessedUseCasesVersion,latest_version:f,from_migration:Boolean(e.fromMigration),...e.logData}}})}({maybeUseCases:i,collectedFrom:d,collectedAt:h,guessedUseCasesVersion:s,fromMigration:u,logData:b}),C="string"==typeof c&&function(e){const t=(e=>{const{maybeCompanySize:t,collectedAt:n,collectedFrom:o,guessedCompanySizeVersion:r}=e,a=r?[r]:[],i=Object.keys(S).filter((e=>S[e].created_at<=n)).reverse().map((e=>parseFloat(e)));a.push(...i);for(const s of a)if("string"==typeof t&&S[s].options.includes(t))return{value:t,version:s,collected_at:n,collected_from:o}})(e);if(t)return void 0!==e.guessedCompanySizeVersion&&e.guessedCompanySizeVersion!==t.version&&o().log({level:"warning",from:"surveyDataHelpers.attemptCreateSpaceSurveyData",type:"ValidationError",data:{miscDataToConvertToString:{field:"company_size",unexpected_version:e.guessedCompanySizeVersion,field_version:t.version,field_value:t.value,from_migration:Boolean(e.fromMigration),...e.logData}}}),t;o().log({level:"warning",from:"surveyDataHelpers.attemptCreateSpaceSurveyData",type:"ValidationError",data:{miscDataToConvertToString:{field:"company_size",unexpected_value:e.maybeCompanySize,guessed_version:e.guessedCompanySizeVersion,latest_version:g,from_migration:Boolean(e.fromMigration),...e.logData}}})}({maybeCompanySize:c,collectedFrom:d,collectedAt:h,guessedCompanySizeVersion:l,fromMigration:u,logData:b}),I={...v&&{intent:v},...k&&{collaborative_intent:k},...A&&{use_cases:A},...C&&{company_size:C}};if(Object.keys(I).length>0)return I}const s=4.1,c=3,l=1,d=1,u=1,p=2,m=1,f=3,g=3,b={1:{options:["personal","programmer","product_manager","designer","marketing","sales","customer_support","hr_recruiting","student","unfilled","entrepreneur","marketing_sales","operations_hr","media","freelancer","it","educator","other"],created_at:0},3:{options:["eng","design","product","marketing","sales","finance","support","hr","it","operations","student","educator","other"],created_at:164747328e4},4:{options:["creative","educator","eng","finance","founder_or_ceo","hr","internal_communication","it_admin","knowledge_management","marketing","operations","product","product_design","project_or_program_management","sales","student","support","other"],created_at:16432596e5},4.1:{options:["creative","educator","eng","finance","hr","internal_communication","it_admin","knowledge_management","marketing","operations","product","product_design","project_or_program_management","sales","support","other"],created_at:167761092e4},4.2:{options:["creative","internal_communication","eng","product_design","project_or_program_management","product","marketing","sales_or_success","support","finance","hr","it_admin","knowledge_management","operations","educator","other","student"],created_at:1682636494e3}},h={1:{options:["team_lead","org_lead","company_lead","not_lead","unfilled","personal","exec","team_manager","member"],created_at:0},2:{options:["exec","dept_lead","team_manager","member","solo","personal","other"],created_at:164747328e4},3:{options:["founder_or_ceo","exec","dept_lead","team_manager","member","solo","business_owner","other"],created_at:167761092e4},4:{options:["founder_or_ceo","exec","director","manager","individual_contributor","business_owner","freelancer","other"],created_at:1682636494e3}},v={1:{options:["planner","notes","research","clubs","teachingLessonPlans","teachingClassroomManagement","websiteBuildingForSchool","internshipApplications","groupProjects","todos","projectsTasks","habitWellness","budgetPersonalFinance","travel","hobbies","careerBuilding","foodNutrition","websiteBuildingForLife","entertainment","budget","habitTracker","journal","jobApplications","lessonPlans","classroomManagement","projectManagement","websiteBuilding"],created_at:1710198791e3}},_={1:{options:["personal","team","school"],created_at:0},2:{options:["life","work","school"],created_at:167761092e4}},y={1:{options:["singleplayer","multiplayer"],created_at:1730494772e3}},w={1:{options:["personal_notes_to_dos","team_docs_knowledge_base","team_project_management"],created_at:0},2:{options:["docs","wikis","notes","project","goals","other"],created_at:164747328e4},3:{options:["docs","wikis","notes","project","goals","ai","other"],created_at:167702934e4}},S={1:{options:["1_100","101_1000","1001+"],created_at:0},2:{options:["1_49","50_99","100_299","300_999","1000_5000","5000+"],created_at:167113674e4},3:{options:["1_10","10_49","50_99","100_299","300_999","1000_5000","5000+"],created_at:1730494772e3}},k={1:{options:["student","faculty member","staff member"],created_at:17240256e5}},A={1:{options:["k-12","undergraduate","graduate"],created_at:17240256e5}},C={1:{options:["never","a few times"],created_at:1727806707}},I=[...b[4].options,"unfilled"],P=[...h[2].options.filter((e=>"other"!==e)),"unfilled"],T=w[3].options,E=S[3].options,R=(k[1].options,A[1].options,_[1].options);y[1].options;function D(e){if(P.includes(e))return e}function M(e){if(I.includes(e))return e}function q(e){return e.map((e=>{if(T.includes(e))return e})).filter((e=>void 0!==e))}function O(e){if(E.includes(e))return e}function B(e){if(R.includes(e))return e}},319625:(e,t,n)=>{n.d(t,{A:()=>o});n(16280);function o(e){if(e instanceof Error)return e;if("string"==typeof e)try{e=JSON.parse(e)}catch{}return"object"==typeof e&&null!==e?Object.assign(new Error("Expected error, but caught non-error object"),e):"string"==typeof e?Object.assign(new Error(e),{cause:e}):Object.assign(new Error(`Expected error, but caught \`${String(e)}\` (${typeof e})`),{cause:e})}},321098:(e,t,n)=>{n.d(t,{g:()=>o});const o=new(n(178624).R)({key:"lcpcDeduplicationFeatureGate",namespace:n(419494).Bq,important:!1,trackingType:"necessary"})},328058:(e,t,n)=>{n.r(t)},332393:(e,t,n)=>{n.d(t,{Ay:()=>u,C7:()=>c,Li:()=>s,SF:()=>d,Vf:()=>i,YN:()=>r,kC:()=>a,rr:()=>l});n(898992),n(354520),n(737550);var o=()=>n(292588);const r={notion_user:!0,space:!0};function a(e){return"external"===e.getType()||"guest"===e.getType()}const i=["published","unpublished"],s="integration";function c(e,t){if(e){const{developer_space_id:n,extra_testing_space_ids:o}=t.getInfo();return n===e||(o??[]).some((t=>t===e))}return!1}function l(e){return e.info.original_url_patterns&&e.info.original_url_patterns.filter((e=>{var t;return null===(t=e.additional_types)||void 0===t?void 0:t.collection})).length>0}function d(e){return!!e&&"internal"===e.type}const u={table:s,columnTypes:{id:o().A.UUID,version:o().A.Number,last_version:o().A.Number,name:o().A.String,parent_table:o().A.String,parent_id:o().A.UUID,created_at:o().A.Number,created_by_id:o().A.UUID,created_by_table:o().A.String,updated_at:o().A.Number,updated_by_id:o().A.UUID,updated_by_table:o().A.String,redirect_uris:o().A.StringArray,status:o().A.String,info:o().A.JSON,alive:o().A.Boolean,capabilities:o().A.JSON,type:o().A.String,listing_status:o().A.String},model:(0,n(152853).P)({RecordStore:!0})}},336811:(e,t,n)=>{n.d(t,{O:()=>a,T:()=>r});var o=n(296540);function r(){const e=(0,o.useRef)(!1);return(0,o.useEffect)((()=>(e.current=!0,()=>{e.current=!1})),[]),e}function a(){const e=(0,o.useRef)(!1);return(0,o.useEffect)((()=>()=>{e.current=!0}),[]),e}},347320:(e,t,n)=>{n.d(t,{A:()=>a,O:()=>r});const o="collection-prefetch-cache:",r="inline-db-lookup";function a(e){return e?`${o}${e}`:"queryCollection"}},361242:(e,t,n)=>{n.d(t,{Sn:()=>o,g4:()=>r});const o="loadOrigin",r="tabCount"},362749:(e,t,n)=>{n.d(t,{Cj:()=>b,HK:()=>c,Hf:()=>S,LL:()=>h,Nd:()=>g,Pl:()=>y,Sd:()=>p,UL:()=>i,V2:()=>d,YZ:()=>m,ck:()=>l,fu:()=>w,hO:()=>a,jj:()=>u,lA:()=>r,ln:()=>_,m5:()=>s,op:()=>v,or:()=>f,rU:()=>k});var o=()=>n(645873);const r={sidebar:new(o().O2)("sidebar",(()=>Promise.all([n.e(75134),n.e(99223),n.e(58795),n.e(9773),n.e(36556),n.e(40537),n.e(44711),n.e(16471),n.e(53321),n.e(96346),n.e(55373),n.e(36192),n.e(37353),n.e(29151),n.e(90825),n.e(99117),n.e(17230),n.e(23740),n.e(24119),n.e(49889),n.e(20108),n.e(22900),n.e(85128),n.e(22862),n.e(33019),n.e(71621),n.e(92845),n.e(84468),n.e(75078),n.e(42660),n.e(34359),n.e(44934),n.e(21194),n.e(46414),n.e(66320),n.e(74562),n.e(28271),n.e(28866),n.e(34877),n.e(11676),n.e(11341),n.e(30858),n.e(17254),n.e(63479),n.e(4422),n.e(24587),n.e(27796),n.e(4330),n.e(8184),n.e(28908),n.e(82213),n.e(66626),n.e(39987),n.e(35094),n.e(15139),n.e(97513),n.e(27164),n.e(65881),n.e(3122),n.e(40532),n.e(9562),n.e(52651),n.e(78405),n.e(90978),n.e(1707),n.e(48113),n.e(36639),n.e(48307),n.e(14310),n.e(28398),n.e(55850),n.e(30291),n.e(46580),n.e(86280),n.e(50569),n.e(32254),n.e(89910),n.e(58786),n.e(77690),n.e(16195),n.e(44763),n.e(42525),n.e(51843),n.e(76690),n.e(33882),n.e(75104),n.e(82720),n.e(1440),n.e(9426),n.e(29875),n.e(26942),n.e(5268),n.e(38536),n.e(89379),n.e(36479),n.e(28480),n.e(21007),n.e(63248),n.e(31061)]).then(n.bind(n,721414)))),SidebarComponent:new(o().O2)("SidebarComponent",(async()=>await Promise.all([n.e(75134),n.e(99223),n.e(58795),n.e(17230),n.e(23740),n.e(28908),n.e(36639),n.e(48307),n.e(14310),n.e(28398),n.e(55850),n.e(75104),n.e(29875),n.e(26942),n.e(72982)]).then(n.bind(n,361956)))),SidebarLibraryButton:new(o().O2)("SidebarLibraryButton",(async()=>await Promise.all([n.e(75134),n.e(99223),n.e(58795),n.e(17230),n.e(23740),n.e(36639),n.e(48307),n.e(14310),n.e(28398),n.e(55850),n.e(40902)]).then(n.bind(n,624959)))),SidebarAgentsButton:new(o().O2)("SidebarAgentsButton",(async()=>await Promise.all([n.e(75134),n.e(99223),n.e(58795),n.e(17230),n.e(23740),n.e(36639),n.e(48307),n.e(14310),n.e(28398),n.e(55850),n.e(38507)]).then(n.bind(n,350076)))),SidebarTrash:new(o().O2)("SidebarTrash",(async()=>await Promise.all([n.e(75134),n.e(99223),n.e(58795),n.e(9773),n.e(36556),n.e(40537),n.e(44711),n.e(16471),n.e(53321),n.e(96346),n.e(55373),n.e(36192),n.e(37353),n.e(29151),n.e(90825),n.e(99117),n.e(17230),n.e(23740),n.e(33019),n.e(71621),n.e(92845),n.e(84468),n.e(75078),n.e(73801),n.e(48486),n.e(5605),n.e(95794),n.e(42660),n.e(34359),n.e(44934),n.e(21194),n.e(46414),n.e(66320),n.e(74562),n.e(28271),n.e(28866),n.e(34877),n.e(11676),n.e(11341),n.e(30858),n.e(17254),n.e(63479),n.e(4422),n.e(24587),n.e(27796),n.e(4330),n.e(8184),n.e(28908),n.e(82213),n.e(66626),n.e(39987),n.e(35094),n.e(15139),n.e(97513),n.e(27164),n.e(65881),n.e(3122),n.e(40532),n.e(9562),n.e(52651),n.e(78405),n.e(90978),n.e(1707),n.e(48113),n.e(36639),n.e(48307),n.e(14310),n.e(28398),n.e(55850),n.e(8953)]).then(n.bind(n,539135)))),TabletSidebarButton:new(o().O2)("TabletSidebarButton",(async()=>await Promise.all([n.e(75134),n.e(99223),n.e(58795),n.e(17230),n.e(23740),n.e(36639),n.e(48307),n.e(14310),n.e(28398),n.e(55850),n.e(88873)]).then(n.bind(n,871466)))),AuthSyncListener:new(o().O2)("AuthSyncListener",(async()=>await Promise.all([n.e(75134),n.e(99223),n.e(58795),n.e(17230),n.e(23740),n.e(36639),n.e(48307),n.e(14310),n.e(28398),n.e(55850),n.e(21088)]).then(n.bind(n,171510)))),PublicPageSidebarContent:new(o().O2)("PublicPageSidebarContent",(async()=>Promise.all([n.e(75134),n.e(99223),n.e(58795),n.e(17230),n.e(23740),n.e(36639),n.e(48307),n.e(14310),n.e(28398),n.e(55850),n.e(29875),n.e(26942),n.e(33590)]).then(n.bind(n,565660)))),SidebarMobile:new(o().O2)("SidebarMobile",(async()=>await Promise.all([n.e(75134),n.e(99223),n.e(58795),n.e(90825),n.e(17230),n.e(23740),n.e(24119),n.e(22900),n.e(85128),n.e(95794),n.e(30858),n.e(4422),n.e(27796),n.e(4330),n.e(28908),n.e(39987),n.e(35094),n.e(15139),n.e(36639),n.e(48307),n.e(14310),n.e(28398),n.e(55850),n.e(86280),n.e(50569),n.e(58786),n.e(16195),n.e(42525),n.e(51843),n.e(75104),n.e(9426),n.e(29875),n.e(26942),n.e(45351),n.e(35115)]).then(n.bind(n,997556)))),LockedSidebarSection:new(o().O2)("LockedSidebarSection",(async()=>await Promise.all([n.e(75134),n.e(99223),n.e(58795),n.e(17230),n.e(23740),n.e(36639),n.e(48307),n.e(14310),n.e(28398),n.e(55850),n.e(29875),n.e(94412)]).then(n.bind(n,518299)))),OutlinerToggleOpenSetupModalButton:new(o().O2)("OutlinerToggleOpenSetupModalButton",(async()=>await Promise.all([n.e(75134),n.e(99223),n.e(58795),n.e(9773),n.e(36556),n.e(40537),n.e(44711),n.e(16471),n.e(53321),n.e(96346),n.e(55373),n.e(36192),n.e(37353),n.e(29151),n.e(90825),n.e(99117),n.e(17230),n.e(23740),n.e(33019),n.e(71621),n.e(92845),n.e(84468),n.e(75078),n.e(73801),n.e(48486),n.e(5605),n.e(95794),n.e(42660),n.e(34359),n.e(44934),n.e(21194),n.e(46414),n.e(66320),n.e(74562),n.e(28271),n.e(28866),n.e(34877),n.e(11676),n.e(11341),n.e(30858),n.e(17254),n.e(63479),n.e(4422),n.e(24587),n.e(27796),n.e(4330),n.e(8184),n.e(28908),n.e(82213),n.e(66626),n.e(39987),n.e(35094),n.e(15139),n.e(97513),n.e(27164),n.e(65881),n.e(3122),n.e(40532),n.e(9562),n.e(52651),n.e(78405),n.e(90978),n.e(1707),n.e(48113),n.e(36639),n.e(48307),n.e(14310),n.e(28398),n.e(55850),n.e(53977)]).then(n.bind(n,794946)))),SidebarInboxButton:new(o().O2)("SidebarInboxButton",(async()=>await Promise.all([n.e(75134),n.e(99223),n.e(58795),n.e(44711),n.e(53321),n.e(17230),n.e(23740),n.e(36639),n.e(48307),n.e(14310),n.e(28398),n.e(55850),n.e(28307)]).then(n.bind(n,84987)))),SidebarFeedButton:new(o().O2)("SidebarFeedButton",(async()=>await Promise.all([n.e(75134),n.e(99223),n.e(58795),n.e(17230),n.e(23740),n.e(28908),n.e(36639),n.e(48307),n.e(14310),n.e(28398),n.e(55850),n.e(13073)]).then(n.bind(n,820022)))),SidebarMeetingsButton:new(o().O2)("SidebarMeetingsButton",(async()=>await Promise.all([n.e(75134),n.e(99223),n.e(58795),n.e(17230),n.e(23740),n.e(36639),n.e(48307),n.e(14310),n.e(28398),n.e(55850),n.e(29623)]).then(n.bind(n,290488))))},a=(0,o()._h)(r.sidebar,(e=>e.SidebarTeamBrowserContent)),i=(0,o()._h)(r.sidebar,(e=>e.SidebarSwitcherMultiAccountPopup)),s=(0,o()._h)(r.sidebar,(e=>e.SidebarFooter)),c=(0,o()._h)(r.sidebar,(e=>e.SidebarCard)),l=(0,o()._h)(r.sidebar,(e=>e.SidebarSectionWithPopup)),d=(0,o()._h)(r.SidebarTrash,(e=>e.SidebarTrash)),u=(0,o()._h)(r.TabletSidebarButton,(e=>e.TabletSidebarButton)),p=(0,o()._h)(n(117296).KM.RecentsCachingListener,(e=>e.default)),m=(0,o()._h)(r.SidebarComponent,(e=>e.Sidebar)),f=(0,o()._h)(r.PublicPageSidebarContent,(e=>e.PublicPageSidebarContent)),g=(0,o()._h)(r.SidebarMobile,(e=>e.default)),b=(0,o()._h)(r.LockedSidebarSection,(e=>e.LockedSidebarSection)),h=(0,o()._h)(r.OutlinerToggleOpenSetupModalButton,(e=>e.OutlinerToggleOpenSetupModalButton)),v=(0,o()._h)(r.SidebarLibraryButton,(e=>e.SidebarLibraryButton)),_=(0,o()._h)(r.SidebarAgentsButton,(e=>e.SidebarAgentsButton));async function y(){return(await Promise.all([n.e(75134),n.e(99223),n.e(58795),n.e(9773),n.e(36556),n.e(40537),n.e(17230),n.e(23740),n.e(33019),n.e(71621),n.e(92845),n.e(84468),n.e(75078),n.e(36639),n.e(48307),n.e(14310),n.e(28398),n.e(55850),n.e(63137)]).then(n.bind(n,375215))).createPageInTeamSyncImpl}const w=(0,o()._h)(r.SidebarInboxButton,(e=>e.SidebarInboxButton)),S=(0,o()._h)(r.SidebarFeedButton,(e=>e.SidebarFeedButton)),k=(0,o()._h)(r.SidebarMeetingsButton,(e=>e.SidebarMeetingsButton))},365902:(e,t,n)=>{async function o(){const{default:e}=await Promise.resolve().then(n.bind(n,373407));e();(await Promise.resolve().then(n.bind(n,644425))).initializeEarlyLogging()}n.d(t,{loadErrorReporting:()=>o})},373407:(e,t,n)=>{n.d(t,{default:()=>s});var o=()=>n(726637),r=()=>n(258595),a=()=>n(445155),i=()=>n(165162);function s(){(0,r().Ay)({onInjectSDK:c})}async function c(){const{getProfilingToolForSession:e}=await Promise.resolve().then(n.bind(n,150749)),{Sentry:t,sentryInitializeFull:r}=await(0,a().q)(),s=(0,i().bo)("sentry");r({Sentry:{...t,isFullSDK:!0},config:o().A,getErrorsSampleRate:()=>(null==s?void 0:s.get("errorsSampleRate",1))||1,getTracesSampleRate:()=>(null==s?void 0:s.get("tracesSampleRate-v0",0))||0,getReplaysSessionSampleRate:()=>(null==s?void 0:s.get("replaysSessionSampleRate-v0",0))||0,getReplaysOnErrorSampleRate:()=>(null==s?void 0:s.get("replaysOnErrorSampleRate-v0",0))||0,getIsProfilingEnabled:()=>"sentry"===e(),getProfilesSampleRate:()=>(null==s?void 0:s.get("profilesSampleRate",0))||0})}},386164:(e,t,n)=>{n.d(t,{E5:()=>d,NV:()=>c,WS:()=>l});n(581454);var o=()=>n(496603),r=()=>n(498212),a=()=>n(851941),i=()=>n(870723),s=()=>n(436604);function c(e,t){if(t&&(0,i().$t)(e.table))if("space"===e.table){if(e.id!==t)return}else{if(!e.spaceId)return{...e,spaceId:t};if(e.spaceId!==t)return}return e}function l(e){const{cellId:t,recordId:n,spaceId:o,verifyShortSpaceIdVersion:i}=e,c={};let l=n?(0,a().dR)(n):void 0;if(n&&l&&i){"notion-v0"===(0,r().Iq)(n)&&(l=void 0)}return t?c[s().tx]=t:l?c[s().eG]=l.toString():o&&(c[s().B3]=o),c}function d(e){const t=new Map,n=o().$z(e,(e=>o().Ul(Object.entries(e.headers),(e=>{let[t]=e;return t})).map((e=>{let[t,n]=e;return[t,n].join(":")})).join("|")));for(const o of Object.values(n)){const[e]=o;e&&t.set(e.headers,o.map((e=>{let{headers:t,...n}=e;return n})))}return t}},386466:(e,t,n)=>{n.d(t,{Ym:()=>a,getDeviceAttributesForStatsigUser:()=>i,l4:()=>c,tX:()=>s});n(898992),n(803949);var o=()=>n(496603),r=()=>n(534177);function a(e){const t={};return e?((0,r().WP)(e).forEach((e=>{let[n,r]=e;null!=n&&null!=r&&(t[String(n)]=o().Kg(r)?r:JSON.stringify(r))})),t):t}function i(e){var t;return{clientVersion:e.version,mobileVersion:e.mobileAppVersion,desktopVersion:null===(t=e.electronVersion)||void 0===t?void 0:t.join("."),isElectron:e.isElectron,isMobileNative:e.isMobileNative,isMobileBeta:e.isMobileBeta,isMobileBrowser:e.isMobileBrowser,isBrowser:e.isBrowser,isMobile:e.isMobile,isDesktopBrowser:e.isDesktopBrowser,isTablet:e.isTablet}}function s(e){var t;const{data:n,config:o}=e,{userId:r,device:a}=n,i={locale:"locale"in n?n.locale:void 0,...a,spaceCreatedTime:"spaceCreatedTime"in n?n.spaceCreatedTime:void 0,spaceCreatedDate:"spaceCreatedDate"in n?n.spaceCreatedDate:void 0,spaceSubscriptionTier:"subscriptionTier"in n?n.subscriptionTier:void 0,isSalesAssistedPlan:"isSalesAssistedPlan"in n?n.isSalesAssistedPlan:void 0,userSignupTime:"userSignupTime"in n?n.userSignupTime:void 0,domainType:"domainType"in n?n.domainType:void 0,planType:"planType"in n?n.planType:void 0,browserId:"browserId"in n?n.browserId:void 0,stableID:"stableId"in n?n.stableId:void 0,hasBrowserId:"browserId"in n&&Boolean(null===(t=n.browserId)||void 0===t?void 0:t.length)};"spaceId"in n&&n.spaceId&&(i.spaceId=n.spaceId),"deviceId"in n&&n.deviceId&&(i.deviceId=n.deviceId);return{userID:r,custom:i,customIDs:{..."deviceId"in n&&n.deviceId&&{deviceId:n.deviceId},..."stableId"in n&&n.stableId&&{stableID:n.stableId},..."spaceId"in n&&n.spaceId&&{spaceId:n.spaceId}},privateAttributes:{..."production"!==o.env&&"userEmail"in n&&n.userEmail&&{email:n.userEmail}},statsigEnvironment:{tier:o.env}}}function c(e){return`/statsig/local_eval_config_spec-v${e}.json`}},402390:(e,t,n)=>{n.d(t,{CR:()=>s,H:()=>p,Nk:()=>m,_z:()=>u,dk:()=>h,g5:()=>_,q:()=>i,wW:()=>d});n(898992),n(672577);var o=()=>n(959013),r=()=>n(209199),a=()=>n(485993);const i="en-US";function s(e){return function(e){return e.normalize("NFD").replace(/[\u0300-\u036f]/g,"")}(e).toLowerCase()}const c=(0,o().MT)();let l=(0,o().EY)({locale:"en-US",defaultLocale:"en-US"},c);function d(){return l}function u(){return l}function p(e){return(0,r().vL)(e.locale)?e.locale:i}function m(e){return(0,r().vR)(e.locale)?e.locale:i}const f={decimal:".",integerSeparator:","};let g=l,b=v(l);function h(e){return g!==e&&(g=e,b=v(e)),b}function v(e){var t,n;const o=e.formatNumberToParts(10000.1),r=null===(t=o.find((e=>"decimal"===e.type)))||void 0===t?void 0:t.value,a=null===(n=o.find((e=>"group"===e.type)))||void 0===n?void 0:n.value;return r&&a?{decimal:r,integerSeparator:a}:f}function _(e){let{locale:t,env:n}=e;document.documentElement.lang=t.substring(0,2),(("local"===n||"development"===n)&&(0,a().f)()||(0,r().Vf)(t))&&(document.documentElement.dir="rtl")}},410555:(e,t,n)=>{n.d(t,{A:()=>o});const o=new(n(178624).R)({key:"openExternalLinkSettingStorageKey",namespace:n(419494).Bq,important:!0,trackingType:"necessary"})},410690:(e,t,n)=>{n.r(t),n.d(t,{appStartTimeKey:()=>r,getAppStartTime:()=>a,getPageOriginTime:()=>i});n(16280),n(898992),n(672577);var o=()=>n(449412);const r="__webStartTime";function a(){const e=window[r];if("number"==typeof e)return e;{const e=new Error("App Start Time not found. It should be in index.html.");o().Fg(e)}}function i(){const e=performance.getEntriesByType("navigation").find((e=>e instanceof PerformanceNavigationTiming));let t=(null==e?void 0:e.redirectStart)||(null==e?void 0:e.fetchStart);if("number"!=typeof t){const{redirectStart:e,fetchStart:n,navigationStart:o}=performance.timing;e?t=e-o:n&&(t=n-o)}if("number"==typeof t)return t;{const e=new Error("Page Origin Time not found");o().Fg(e)}}},419494:(e,t,n)=>{n.d(t,{Bq:()=>l,cd:()=>c,lD:()=>d,Ay:()=>v,HW:()=>m});n(16280),n(944114),n(581454);function o(e,t,o){n(863024).add(e,t,o)}const r=[100,1e3,1e4];function a(e){return e&&e.timestamp?e.timestamp:0}function i(e,t){const n=a(e.value),o=a(t.value);return n===o?0:n>o?1:-1}class s{constructor(e){this.store=e}get(e){let{disableLRU:t=!1}=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{};const n=this.store.get(e);if(n)return t||this.set(e,n.value,n.important),n.value}set(e,t,n){let o;for(const a of r){if(o=this.attemptSet(e,t,n),!o)return;this.purge(a)}throw o}remove(e){this.store.remove(e)}scan(e){this.store.scan(e)}squeeze(e){const t=this.store.getSize();if(t>e){const n=t-e+Math.round(e/2);this.purge(n)}}attemptSet(e,t,n){try{this.store.set(e,{id:e,value:t,timestamp:Date.now(),important:n})}catch(o){return o}}purge(e){const t=[];this.store.scan(((n,r)=>{r.important||(o(t,{key:n,value:r},i),t.splice(e))})),t.map((e=>this.store.remove(e.key)))}}const c="LocalPreferenceStore3",l="KeyValueStore2",d="LRU:",u=["BlockCache","LocalPreferenceStore","LocalPreferenceStore2","KeyValueStore","LocalTransactionStore","LocalRecordStore","LocalRecordStore2","LRU:KeyValueStore2:offline_page_state"],p=["amplitude","intercom","STATSIG","statsig.cached.evaluations"];function m(e,t){return`${e||"guest"}:${t}`}let f=!1;function g(){let e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{};return new s({get:e=>{const t=localStorage.getItem(d+e);if(t)return JSON.parse(t)},set:(t,o)=>{const r=JSON.stringify(o);if(e.maxValueLength&&r.length>e.maxValueLength)throw new Error(`Value exceeds max length (${e.maxValueLength})`);try{localStorage.setItem(d+t,r)}catch(a){if(!f){f=!0;const e=Object.keys(localStorage),o=n(496603).Ul(e.map((e=>[e,JSON.stringify(localStorage[e]).length])),(e=>-e[1]));n(857639).log({level:"error",from:"LocalStorageStore",type:"set",error:(0,n(502800).convertErrorToLog)(a),data:{miscDataToConvertToString:{keysWithSizes:JSON.stringify(o),sessionHeartbeat:localStorage["LRU:KeyValueStore2:NotionSessionHeartbeat3"],setKey:d+t,setValueLength:r.length}}})}const e=[];for(let t=0;t<localStorage.length;t++)e.push(localStorage.key(t));for(const t of e){const e=p.some((e=>t.startsWith(e)));e&&localStorage.removeItem(t)}localStorage.setItem(d+t,r)}},remove:e=>{localStorage.removeItem(d+e)},scan:e=>{const t=[];for(let n=0;n<localStorage.length;n++)t.push(localStorage.key(n));for(const n of t)if(u.some((e=>n.startsWith(e))))localStorage.removeItem(n);else if(n.startsWith(d)){const t=localStorage.getItem(n);if(t){const o=JSON.parse(t);e(n.slice(d.length),o)}}},getSize:()=>localStorage.length})}function b(){return n(412215)}const h={getCookieWithoutPermissionCheck:e=>Promise.resolve(b().get(e)),removeCookie:e=>Promise.resolve(b().remove(e)),isMobileNative:()=>"undefined"!=typeof window&&(0,n(140583).E)(window,window.navigator.userAgent).isMobileNative()};const v=class{constructor(e){let{namespace:t,important:o,trackingType:r,maxValueLength:a,onHasPermissionForTrackingTypeChange:i}=e;this.namespace=void 0,this._hasPermissionForTrackingType=void 0,this._initPromise=void 0,this.lru=void 0,this.important=void 0,this.lru=g({maxValueLength:a}),this.important=o,this.namespace=t,this._hasPermissionForTrackingType="necessary"===r,this._initPromise=n(283699).Eo({trackingType:r,cookieService:h}).then((e=>{const t=this.hasPermissionForTrackingType;this.hasPermissionForTrackingType=e,t!==e&&(null==i||i())}))}async waitUntilReady(){try{await this._initPromise}catch{}}get(e){let{disableLRU:t=!1}=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{};if(this.hasPermissionForTrackingType)return this.lru.get(this.makeKey(e),{disableLRU:t})}set(e,t){if(this.hasPermissionForTrackingType)return this.lru.set(this.makeKey(e),t,this.important)}remove(e){return this.lru.remove(this.makeKey(e))}squeeze(e){return this.lru.squeeze(e)}scan(e){this.lru.scan(((t,n)=>{t.startsWith(this.namespace)&&e(t.substring(this.namespace.length+1),n.value)}))}parseRawKeyToOwnedKey(e){const[t,n,o]=e.split(":");if(`${t}:`===d&&n===this.namespace&&o)return o}get hasPermissionForTrackingType(){return this._hasPermissionForTrackingType}set hasPermissionForTrackingType(e){this._hasPermissionForTrackingType=e}makeKey(e){return[this.namespace,e].join(":")}}},427704:(e,t,n)=>{n.d(t,{Bf:()=>s,GJ:()=>r,H9:()=>d,JZ:()=>u,eR:()=>i,zK:()=>a});n(581454);var o=()=>n(209199);const r={root:"/",onboarding:"/onboarding",make:"/make",googleOneTapRedirect:"/google-onetap-redirect",privacyCenterRedirect:"/privacy-center-redirect",login:"/login",loginCalendar:"/login/calendar",loginMail:"/login/mail",loginWithEmail:"/loginwithemail",loginSuccess:"/loginsuccess",logout:"/logout",signup:"/signup",signupCalendar:"/signup/calendar",signupMail:"/signup/mail",nativeOauthCallback:"/native/oauth2callback",nativeMailOauthCallback:"/nativemail/oauth2callback",nativeCalendarOauthCallback:"/nativecalendar/oauth2callback",desktopEmailConfirm:"/email-confirm",addAnotherAccountModal:"/?target=add_another_account",addAnotherAccount:"/add-another-account",unsubscribe:"/unsubscribe",invoice:"/invoice",admin:"/__admin",adminListData:"/__admin/data",adminPermissions:"/__admin/permissions",adminObject:"/__admin/object",newPage:"/new",aiFavorite:"/ai-favorite",oauthAuthorization:"/install-integration",notionCalendarAuthorization:"/install-calendar-integration",myIntegrations:"/my-integrations",creatorProfileIntegrations:"/profile/integrations",templatePreview:"/template-preview",nativeTab:"/nativetab",modal:"/modal",templateSubmission:"/submit-a-template",templateCreatorSubmission:"/submit-a-template/profile",studentGroupSignup:"/notion-for-student-orgs-apply",startupsApplication:"/startups-apply",smbsApplication:"/smbs-apply",lennyApplication:"/lenny-apply",creatorProgramApplication:"/creators",quickSearch:"/quick-search",meetingNotification:"/meeting-notification",blank:"/blank",chat:"/chat",agent:"/agent",agents:"/agents",meet:"/meet",creatorProfile:"/profile",creatorProfileTemplates:"/profile/templates",creatorProfileAnalytics:"/profile/analytics",creatorProfileCoupons:"/profile/coupons",creatorProfileConsultantApplication:"/profile/certification-application",localizedTemplates:"/localized-templates",marketplace:"/marketplace",gallery:"/gallery",home:"/home",posts:"/posts",formResponse:"/formResponse",partnerProgram:"/partnerProgram",community:"/community",consultantsRedirect:"/consultants",deprecatedGuideRedirect:"/guide",terms:"/terms",contentPolicy:"/content-policy",setupSessions:"/setup-sessions",setupSessionsConfirm:"/setup-sessions/confirm",skilljarLoginRedirect:"/skilljar-login-redirect",downloadMac:"/desktop/mac/download",downloadMacUniversal:"/desktop/mac-universal/download",downloadMacIntel:"/desktop/mac-intel/download",downloadMacAppleSilicon:"/desktop/mac-apple-silicon/download",downloadWindows:"/desktop/windows/download",downloadWindowsArm:"/desktop/windows-arm/download",downloadMacInstaller:"/desktop/mac-installer/download",downloadCalendarMac:"/calendar/desktop/mac/download",downloadCalendarMacUniversal:"/calendar/desktop/mac-universal/download",downloadCalendarMacAppleSilicon:"/calendar/desktop/mac-apple-silicon/download",downloadCalendarMacIntel:"/calendar/desktop/mac-intel/download",downloadCalendarWindows:"/calendar/desktop/windows/download",downloadMailMac:"/mail/desktop/mac/download",workflowTemplates:"/build-database",notFound:"/not-found",scimApiBasePath:"/scim/v2",settingsConsoleDefault:"/settings",settingsConsoleWorkspace:"/settings/workspace",settingsConsoleOrganization:"/settings/organization",loginPasswordReset:"/loginpasswordreset",passwordResetCallback:"/passwordresetcallback",passwordChangeRedirect:"/passwordchangeredirect",applePopupRedirect:"/applepopupredirect",appleAuthCallback:"/appleauthcallback",applePopupCallback:"/applepopupcallback",nativeMailAppleAuthCallback:"/nativemail/appleauthcallback",nativeCalendarAppleAuthCallback:"/nativecalendar/appleauthcallback",googlePopupRedirect:"/googlepopupredirect",googleAuthCallback:"/oauth2callback",googlePopupCallback:"/googlepopupcallback",microsoftPopupRedirect:"/microsoftpopupredirect",microsoftAuthCallback:"/microsoftauthcallback",microsoftPopupCallback:"/microsoftpopupcallback",nativeMailMicrosoftAuthCallback:"/nativemail/microsoftauthcallback",nativeCalendarMicrosoftAuthCallback:"/nativecalendar/microsoftauthcallback",passkeyAuthVerify:"/passkeyauthverify",passkeyAuthCallback:"/passkeyauthcallback",nativeMailPasskeyAuthCallback:"/nativemail/passkeyauthcallback",nativeCalendarPasskeyAuthCallback:"/nativecalendar/passkeyauthcallback",samlAuth:"/sso/saml",samlAuthCallback:"/samlauthcallback",nativeMailSamlAuthCallback:"/nativemail/samlauthcallback",nativeCalendarSamlAuthCallback:"/nativecalendar/samlauthcallback",passkeyRegistrationRedirect:"/passkeyregistrationredirect",passkeyRegistrationVerification:"/passkeyregistrationverification",passkeyRegistrationCallback:"/passkeyregistrationcallback",globalOauthAuthorization:"/oauth2/authorize",globalOauthPostLogin:"/oauth2/postlogin",globalOauthToken:"/oauth2/token",slackPopupRedirect:"/slackpopupredirect",slackAuthCallback:"/slackoauthcallback",trelloPopupRedirect:"/trellopopupredirect",trelloAuthCallback:"/trelloauthcallback",trelloElectronAuthCallback:"/trelloelectronauthcallback",asanaPopupRedirect:"/asanapopupredirect",asanaAuthCallback:"/asanaauthcallback",evernotePopupRedirect:"/evernotepopupredirect",evernoteAuthCallback:"/evernoteoauthcallback",evernoteElectronAuthCallback:"/evernoteelectronoauthcallback",googleDrivePopupRedirect:"/googledrivepopupredirect",googleDrivePickerPopup:"/googledrivepickerpopup",verifyNoPopupBlockerHtmlAndRedirect:"/verifyNoPopupBlockerHtmlAndRedirect",externalAuthCallback:"/externalauthcallback",externalAuthProxy:"/eap",serverIntegrationsAuthProxy:"/sip",userSessionAuth:"/userSessionAuth",syncUserSessionAuthCookies:"/syncCookies",productAuth:"/productAuth",calendarAuth:"/calendarAuth",audioProcessorAuth:"/audioProcessorAuth",authSync:"/authSync",externalIntegrationPopupRedirect:"/externalintegrationpopupredirect",externalIntegrationAuthCallback:"/externalintegrationauthcallback",datadogAuthCallback:"/datadogauthcallback",initiateExternalAuthentication:"/initiateExternalAuthentication",initiateExternalAuthenticationFromDesktop:"/initiateExternalAuthenticationFromDesktop",externalAuthNativeCallback:"/externalauthnativecallback",githubStudentPackHome:"/githubstudentpack",githubStudentPackAuthCallback:"/githubStudentPackAuthCallback",personProfileRedirect:"/profiles/:spaceId/:userId",navigateToBlock:"/navigate-to-block",plansSettings:"/?target=plans",membersSettings:"/?target=members",embedPublicPages:"/ebd",authValidate:"/api/v3/authValidate",ekmError:"/ekm-error"},a={nextJsInternal:"/_next",vercelInsightApi:"/_vercel/insights",frontApi:"/front-api",frontStatic:"/front-static",wellKnownVercel:"/.well-known/vercel",sitemap:"/sitemap.xml",robots:"/robots.txt",llms:"/llms.txt",sitemapRoot:"/sitemap-root.xml",sitemaps:"/sitemaps"},i={about:"/about",affiliates:"/affiliates",ai:"/product/ai",aiMeetingNotes:"/product/ai-meeting-notes",aiUseCases:"/product/ai/use-cases",blog:"/blog",careers:"/careers",calendarRoot:"/calendar",calendar:"/product/calendar",calendarSignup:"/product/calendar/signup",calendarDownloadDesktop:"/product/calendar/download/desktop",calendarDownload:"/product/calendar/download",calendarDownloadiOS:"/product/calendar/download/ios",calendarDownloadWindows:"/product/calendar/download/windows",calendarDownloadMac:"/product/calendar/download/mac",compareAgainst:"/compare-against",contactSales:"/contact-sales",contactSalesConfirmation:"/contact-sales/thank-you",customers:"/customers",download:"/download",desktop:"/desktop",docs:"/product/docs",embed:"/embed",engAndProduct:"/product/notion-for-product-development",enterprise:"/enterprise",enterpriseSearch:"/product/enterprise-search",events:"/events",evernote:"/evernote",faces:"/faces",help:"/help",helpReference:"/help/reference",helpResults:"/help/results",integrations:"/integrations",guides:"/help/guides",mail:"/product/mail",mailGA:"/product/mail/ga",mailDownload:"/product/mail/download",mobile:"/mobile",nonprofits:"/nonprofits",notes:"/notes",notionAcademy:"/help/notion-academy",offer:"/offer",pages:"/pages",partners:"/partners",personal:"/personal",pricing:"/pricing",product:"/product",projects:"/product/projects",redeem:"/redeem",releases:"/releases",releasesRSS:"/releases/rss.xml",remote:"/remote",resources:"/resources",security:"/security",startups:"/startups",sitesRoot:"/sites",sites:"/product/sites",sprigSurveyEmbed:"/embed/help/sprig-survey",charts:"/product/charts",upgradedAccount:"/upgraded-account",teams:"/teams",templates:"/templates",useCase:"/use-case",webClipper:"/web-clipper",webinars:"/webinars",wikis:"/product/wikis",forms:"/product/forms",becomeACreator:"/become-a-creator",becomeAPartner:"/become-a-partner",channelPartnerProgram:"/channel-partner-program",consultingPartnerProgram:"/consulting-partner-program",servicePartnerProgram:"/service-partner-program",servicesPartnerProgram:"/services-partner-program",solutionPartnerProgram:"/solution-partner-program",solutionsPartners:"/solutions-partners",solutionsPartnerProgram:"/solutions-partner-program",securityCompliancePartnerProgram:"/security-compliance-partner-program",technologyPartnerProgram:"/technology-partner-program",reportInappropriateContent:"/help/report-inappropriate-content",reportInappropriateForm:"/help/report-inappropriate-content#how-to-report-a-form",desktopMac:"/desktop/mac",desktopWindows:"/desktop/windows",desktopWhatsNew:"/desktop/whats-new",mobileIOS:"/mobile/ios",mobileAndroid:"/mobile/android",androidRedirect:"/android",apiBetaRedirect:"/api-beta",educatorsRedirect:"/educators",jobsRedirect:"/jobs",joinUsRedirect:"/join-us",studentsRedirect:"/students",toolsAndCraftRedirect:"/tools-and-craft",wikiRedirect:"/wiki",whyRedirect:"/why",workRedirect:"/work",guidesRedirect:"/guides",config24Redirect:"/config24",aiRedirect:"/ai",docsRedirect:"/docs",mailRedirect:"/mail",projectsRedirect:"/projects",wikisRedirect:"/wikis",...o().Hj,...o().ur,templateCreator:"/@",lp:"/lp",md:"/md",firstBlock:"/first-block",notionVsConfluence:"/compare-against/notion-vs-confluence",notionVsGranola:"/compare-against/notion-vs-granola",makeWithNotion:"/mwn",makeWithNotionAgenda:"/mwn-agenda",makeWithNotionLive:"/makewithnotionlive",aiConnectors:"/ai-connectors",builders:"/builders",severance:"/severance",explore:"/explore",workquiz:"/workquiz"},s={frontDev:"/__frontDev",sandbox:"/sandbox",storybook:"/storybook",components:"/components",frontAdmin:"/__front-admin",mdpb:"/__mdpb"},c={consultingPartners:"/consulting-partners"};r.logout,r.login,r.loginCalendar,r.loginMail,r.calendarAuth,r.signup,r.signupCalendar,r.signupMail,r.desktopEmailConfirm,r.loginWithEmail,r.unsubscribe,r.make,r.onboarding,r.invoice,r.admin,r.loginPasswordReset,r.addAnotherAccountModal,r.addAnotherAccount,r.passwordResetCallback,r.passwordChangeRedirect,r.googlePopupRedirect,r.googleAuthCallback,r.googlePopupCallback,r.passkeyAuthVerify,r.passkeyAuthCallback,r.applePopupRedirect,r.appleAuthCallback,r.applePopupCallback,r.microsoftPopupRedirect,r.microsoftAuthCallback,r.microsoftPopupCallback,r.samlAuth,l([r.samlAuth]),r.samlAuthCallback,r.slackPopupRedirect,r.slackAuthCallback,r.trelloPopupRedirect,r.trelloAuthCallback,r.externalAuthCallback,r.externalAuthNativeCallback,r.asanaPopupRedirect,r.asanaAuthCallback,r.evernotePopupRedirect,r.evernoteAuthCallback,r.googleDrivePopupRedirect,r.googleDrivePickerPopup,r.externalIntegrationAuthCallback,r.externalIntegrationPopupRedirect,r.deprecatedGuideRedirect,r.community,r.consultantsRedirect,r.setupSessions,r.skilljarLoginRedirect,r.myIntegrations,r.creatorProfileIntegrations,r.templatePreview,r.oauthAuthorization,r.notionCalendarAuthorization,r.nativeOauthCallback,r.nativeMailOauthCallback,r.nativeMailSamlAuthCallback,r.nativeMailAppleAuthCallback,r.nativeMailMicrosoftAuthCallback,r.nativeMailPasskeyAuthCallback,r.nativeCalendarOauthCallback,r.nativeCalendarSamlAuthCallback,r.nativeCalendarAppleAuthCallback,r.nativeCalendarMicrosoftAuthCallback,r.nativeCalendarPasskeyAuthCallback,r.templateSubmission,r.creatorProfile,r.creatorProfileAnalytics,r.creatorProfileTemplates,r.creatorProfileCoupons,r.templateCreatorSubmission,r.localizedTemplates,r.studentGroupSignup,r.marketplace,r.gallery,l([r.marketplace,r.gallery]),r.gallery,Object.values(a),Object.values(i),l(Object.values(i)),Object.values(s),i.templateCreator,Object.values(c),r.plansSettings,r.membersSettings,r.settingsConsoleDefault,r.settingsConsoleOrganization;function l(e){return e.map((e=>`${e}/*`))}const d={notionTwitter:"https://twitter.com/NotionHQ",appStore:"https://itunes.apple.com/app/notion-notes-tasks-wikis/id1232780281",playStore:"https://play.google.com/store/apps/details?id=notion.id",stagingChromeExtension:"https://chrome.google.com/webstore/detail/plicnlhlnddfonieaidfmagnjmkiiojd",devChromeExtension:"https://chrome.google.com/webstore/detail/ndbjfpdjbfcljfnfhoopkljapcaomhha",prodChromeExtension:"https://chrome.google.com/webstore/detail/knheggckgoiihginacbkhaalnibhilkk",devFirefoxExtension:"https://dev.notion.so/notion/Web-Clipper-fb7489a8ebe64aec987b9b136fa0fafc",prodFirefoxExtension:"https://addons.mozilla.org/en-US/firefox/addon/notion-web-clipper/",devSafariExtension:"https://dev.notion.so/65ce293fa99a4fb0a3302da0e8b09d73#2305488a752c497f80ee810d27454aea",prodSafariExtension:"https://apps.apple.com/us/app/notion-web-clipper/id1559269364?mt=12",notionCalendarAppStore:"https://apps.apple.com/app/notion-calendar/id1607562761",notionCalendarPlayStore:"https://play.google.com/store/apps/details?id=com.cron.calendar",notionMailBetaTermsAndConditions:"https://notion.notion.site/Notion-Mail-Beta-Terms-090d634456024038a86f8f0fedf55741",statusPage:"https://www.notion-status.com",mailHome:"https://mail.notion.so/",startWithATemplate:"/help/start-with-a-template",termsAndPrivacy:"https://www.notion.so/28ffdd083dc3473e9c2da6ec011b58ac",termsAndConditions:"https://www.notion.so/notion/Terms-and-Privacy-28ffdd083dc3473e9c2da6ec011b58ac",privacyPolicy:"https://www.notion.so/3468d120cf614d4c9014c09f6adc9091",californiaPrivacyNotice:"https://www.notion.so/notion/Privacy-Policy-3468d120cf614d4c9014c09f6adc9091?source=copy_link#18e15495ac2643659c303cbe96358620",mediaKit:"https://www.notion.so/205535b1d9c4440497a3d7a2ac096286",webClipperGuide:"https://www.notion.so/ba54b19ecaeb466b8070b9e683c5fce1",publicAPISpec:"https://www.notion.so/notion/Notion-API-specification-c29dd39d851543b49a24e1571f63c488",publicApiGetStarted:"https://www.notion.so/notion/Getting-started-98aa2aeeaf0b4836b089cd6fce0b920a",consultants:"https://www.notion.so/consulting-partners",cookiePolicy:"https://www.notion.so/notion/GDPR-c8eac6ea83a64fb1a3ea3bcd5c3d4951",developerTerms:"https://www.notion.so/notion/Developer-Terms-ba4131408d0844e08330da2cbb225c20",developerPortal:"https://developers.notion.com",developerOAuthDocs:"https://developers.notion.com/docs/authorization",youTube:"https://www.youtube.com/channel/UCoSvlWS5XcwaSzIcbuJ-Ysg",facebook:"https://www.facebook.com/NotionHQ/",twitter:"https://twitter.com/NotionHQ",linkedIn:"https://www.linkedin.com/company/notionhq/",instagram:"https://www.instagram.com/notionhq/",notionPerks:"https://startupshub.notion.site/Perks-Deal-Book-0671c595db8848eab159618fe9229c04",championsCommunity:"https://www.notion.so/Notion-Champions-20f977eb5fdd40d4a7a396f1742c3ea5",communityConduct:"https://www.notion.so/notion/Code-of-Conduct-9399b74373b94181bb1026d8afb11800",careersInternships:"https://app.ripplematch.com/t/1edfe69a",desktopWhatsNewProd:"https://notion.notion.site/What-s-New-Mac-Windows-5936dabc8dd6497895786c91b9d6f12a",desktopWhatsNewDev:"https://dev.notion.so/notion/What-s-new-in-Notion-for-Mac-Windows-Dev-81bb72067550431ea65cd6dab12a9ff1",notionForStartupsTerms:"https://www.notion.so/notion/Notion-for-Startup-Terms-936b74c0323745a186b1497747074020",notionLennyTerms:"https://www.notion.so/notion/Notion-s-Lenny-Greatest-Ever-Bundle-Program-18aefdeead05803782a7e59f633b60fd?pvs=4",notionCreatorProgramTerms:"https://notion.notion.site/Creator-Program-T-C-s-1bcefdeead0580059194dcc1701c12f3?pvs=4",notionPartnerProgramTerms:"https://notion.notion.site/notions-promotional-offer-for-linkedin-premium",notionPlusReferralTerms:"https://www.notion.so/notion/Notion-s-Promotional-Offer-for-Plus-Plan-Referral-Discount-210efdeead0580a594a8f5581b56ac41",startupInABox:"https://www.notion.so/templates/startup-in-a-box",notionExpansionOfferTerms:"https://www.notion.so/notion/Notion-s-Promotional-Offer-for-Expansion-Discount-210efdeead0580698e32eac713effd42",notionForSMBsTerms:"https://notion.notion.site/Notion-for-SMB-Terms-6e5f7f5ee9a94c63a7872877db9a6bb8",companyInABox:"https://www.notion.so/templates/company-in-a-box",sellerOnboardingLearnMore:"https://www.notion.so/help/selling-on-marketplace",marketplaceWebhookLearnMore:"https://www.notion.com/help/selling-on-marketplace#webhooks",customerOpenAi:"https://www.notion.com/customers/openai",aiLeapProgramFaqs:"https://www.notion.so/help/notion-ai-leap-program-faqs"},u={...d,...c,...s,...i,...r}},430476:(e,t,n)=>{n.d(t,{Fm:()=>b,G2:()=>p,Gd:()=>h,OJ:()=>f,Zj:()=>c,fb:()=>g,fi:()=>m,kx:()=>d,qU:()=>l});n(16280),n(898992),n(354520),n(581454),n(737550);var o=()=>n(532313),r=()=>n(534177),a=()=>n(720665),i=()=>n(651170),s=()=>n(732524);const c=999;async function l(e){let{connection:t,sql:n,args:o,queryName:r}=e;const a={sql:n,args:o,getData:!0},[i]=await p({connection:t,statements:[a],queryName:r});return i.data}async function d(e){let{connection:t,sql:n,args:o,queryName:r}=e;const a={sql:n,args:o};await p({connection:t,statements:[a],queryName:r})}let u;async function p(e){let{connection:t,statements:n,webLockRequest:c,queryName:l}=e;const d=async()=>{const e={sql:"BEGIN",getData:!1},o={sql:"COMMIT",getData:!1},d={sql:"ROLLBACK",getData:!1},p=n.map((e=>({...e,sql:(0,a().hr)(e.sql)}))),m=[e,...p,o],f=(0,s().Xb)({queryName:l,body:m,onError:d}),g=f,b=c?await v(c,(()=>t.execSqliteBatch(g))):await t.execSqliteBatch(g),h=b.body.slice(1,-1),_=function(e){const{batch:t,result:n,lastSuccessfulSqlBatch:o}=e,a=n.body.findIndex(s().Ll);if(a<0)return;const c=n.body[a],l={batch:t,result:n,errorSql:t.body[a].sql,errorArgs:t.body[a].args,errorIndex:a,sqliteCode:"sqliteCode"in c?c.sqliteCode:void 0};switch(c.type){case"Error":return c.message.includes("database disk image is malformed")?o?new(i().lh)({message:c.message,debugInfo:{...l,lastSuccessfulSqlBatch:o}}):new(i().PW)({message:c.message,debugInfo:l}):new(i().yY)({result:c,debugInfo:l});case"ErrorBefore":return new(i().pu)({message:"ErrorBefore before first Error",debugInfo:l});case"PreconditionFailed":return new(i().JV)({message:"The precondition SQL query did not pass, the batch execution was not attempted.",debugInfo:l});case"OutOfSpace":return new(i().w8)({message:"Sqlite has run out of space",debugInfo:l});case"SharedWorkerFailedToDelegate":return new(i().F)({message:"SharedWorker failed to delegate to a Worker",debugInfo:l});default:(0,r().HB)(c)}}({batch:f,result:b,lastSuccessfulSqlBatch:u});if(_||h.some(s().Ll))throw _;return u=p.map((e=>e.sql)),h};return await o().L5({initialInput:void 0,fn:()=>d(),handleError:(e,t)=>"SqlitePreconditionFail"!==e.name||t?{status:"throw",error:e}:{status:"retry"},retryAttemptsMS:[10,100,1e3],retryAttemptRandomOffsetMS:50})}async function m(e){let{connection:t}=e;const n=(0,s().Xb)({queryName:"vacuum",body:[{sql:"VACUUM"}],onError:void 0});await async function(e){return v({isWrite:!0,tables:"offlineStorage"},(()=>v({isWrite:!0,tables:"sqliteRecordCache"},e)))}((()=>t.execSqliteBatch(n)))}function f(e){return e&&e.data?e.data:[]}function g(e){const t=e.data[0];if(0===e.data.length||!t)throw new Error("Expected >1 result rows, instead had none.");return t}function b(e){return JSON.stringify(e).replace(/\u2028/g,"").replace(/\u2029/g,"")}function h(e){return e?1:0}async function v(e,t){var n;return"undefined"==typeof navigator||void 0===(null===(n=navigator.locks)||void 0===n?void 0:n.request)?t():await navigator.locks.request(e.tables,{mode:e.isWrite?"exclusive":"shared"},(()=>t()))}},436604:(e,t,n)=>{n.d(t,{B3:()=>a,eG:()=>r,tx:()=>o});const o="x-notion-cell",r="x-notion-space-short-id",a="x-notion-space-id"},445155:(e,t,n)=>{async function o(){const[e,{default:t}]=await Promise.all([Promise.all([n.e(51706),n.e(13326)]).then(n.bind(n,451706)),Promise.all([n.e(51706),n.e(13326)]).then(n.bind(n,747347))]);return{Sentry:e,sentryInitializeFull:t}}n.d(t,{q:()=>o})},449412:(e,t,n)=>{n.d(t,{Cr:()=>d,Fg:()=>i,O8:()=>l,Om:()=>s,lE:()=>p,nb:()=>c});n(16280);var o=()=>n(502800),r=()=>n(857639),a=()=>n(56222);function i(){return a().A.sdk.captureException(...arguments)}function s(){for(var e=arguments.length,t=new Array(e),n=0;n<e;n++)t[n]=arguments[n];a().A.sdk.withScope((e=>{e.setTag("disable_sampling",!0),a().A.sdk.captureException(...t)}))}function c(){if("startTransaction"in a().A.sdk)return a().A.sdk.startTransaction(...arguments)}function l(e,t){const{from:n,type:a,data:s,team:c}=t;let l,d="error is not an Error, bypassing Sentry";if(e instanceof Error){const t=u;u=void 0,l=d=i(e,{tags:{from:n,type:a,productArea:t}})||void 0}return r().log({level:"error",from:n,type:a,error:(0,o().convertErrorToLog)(e),data:s,sentryEventId:d,team:c}),l}function d(e){a().A.sdk.configureScope((t=>t.setTransactionName(e)))}let u;function p(e){u=e}},459225:(e,t,n)=>{n.d(t,{getPerformanceEventListeners:()=>D,startFlushing:()=>R});n(944114),n(898992),n(672577),n(814603),n(147566),n(198721);var o=()=>n(726637),r=()=>n(402673);class a{constructor(e){let{waitMs:t,maxWaitMs:o,maxSize:r,batchSize:a,prepareBatchFn:i}=e;this.queue=[],this.dequeueDebounced=void 0,this.maxSize=void 0,this.batchSize=void 0,this.measureFn=void 0,this.prepareBatchFn=void 0,this.dequeueDebounced=n(496603).sg(this.dequeue.bind(this),t,{maxWait:o}),this.maxSize=r,this.batchSize=a,this.measureFn=void 0,this.prepareBatchFn=i}enqueue(e){var t;!Boolean(null===(t=performance)||void 0===t?void 0:t.now)||this.queue.length>=this.maxSize||(this.queue.push(e),(0,r().$)(this.dequeueDebounced))}startFlushing(e){this.measureFn=e,this.dequeue()}dequeue(){var e;if(0===this.queue.length)return;if(!this.measureFn)return;const t=this.queue.splice(0,this.batchSize);this.queue.length>0&&(0,r().$)(this.dequeueDebounced);const n=(null===(e=this.prepareBatchFn)||void 0===e?void 0:e.call(this,t))??t;for(const{metric:o,data:r}of n)this.measureFn(o,r)}}const i=new a({waitMs:2e3,maxWaitMs:1e4,maxSize:1e4,batchSize:50});function s(e){var t,n;if(!Boolean(null===(t=performance)||void 0===t?void 0:t.now)||!Boolean(null===(n=performance)||void 0===n?void 0:n.getEntriesByType))return;const{startTime:o,metricData:r,endTime:a,entry:s,parseResponseEndTime:c,parseResponseStartTime:l}=e,d={js_duration:a-o,js_duration_with_long_tasks:p(o,a),js_request_start:o,js_after_fetch:l,js_after_response_body:c};l&&(d.js_network_duration=l-o),l&&c&&(d.js_parse_response_duration=c-l),s&&(r.response_transfer_size=s.transferSize,d.perf_duration=s.duration,d.perf_before_request_start_duration=s.requestStart-s.startTime,d.perf_request_start_to_response_start_duration=s.responseStart-s.requestStart,d.perf_receive_response_duration=s.responseEnd-s.responseStart,d.perf_redirect_duration=s.redirectEnd-s.redirectStart,d.perf_connect_duration=s.connectEnd-s.connectStart,d.perf_dns_duration=s.domainLookupEnd-s.domainLookupStart,d.perf_start=s.startTime,d.perf_redirect_start=s.redirectStart,d.perf_redirect_end=s.redirectEnd,d.perf_fetch_start=s.fetchStart,d.perf_request_start=s.requestStart,d.perf_response_start=s.responseStart,d.perf_response_end=s.responseEnd,d.http_protocol=s.nextHopProtocol,d.encoded_body_size=s.encodedBodySize,d.decoded_body_size=s.decodedBodySize);const u={metric:{metricName:"request.client_latency",startTime:o,endTime:a},data:{...r,...d}};i.enqueue(u)}function c(e){if(!e)return;const t=e.headers.get("X-Notion-Request-Id");return t?[t]:void 0}const l=6e5;let d;const u=[];function p(e,t){let n=0;for(const o of u){const r=Math.max(o.startTime,e),a=Math.min(o.startTime+o.duration,t);r<a&&(n+=a-r)}return n}const m=6e4,f=new(n(99895).G)((()=>[]));function g(e){e.parseResponseStartTime&&e.parseResponseEndTime&&s({startTime:e.startTime,metricData:e.metricData,endTime:e.endTime??performance.now(),entry:e.entry,parseResponseStartTime:e.parseResponseStartTime,parseResponseEndTime:e.parseResponseEndTime})}const b="r",h=`${b};dur=`;function v(e,t){var n,o;const r=null===(n=e.headers.get("Server-Timing"))||void 0===n||null===(n=n.split(","))||void 0===n||null===(n=n.find((e=>e.trimStart().startsWith(h))))||void 0===n?void 0:n.substring(h.length),a=null===(o=t.serverTiming)||void 0===o?void 0:o.find((e=>e.name===b));return void 0!==r&&void 0!==a&&r===a.duration.toString()}function _(e){0===f.get(e).length&&f.delete(e)}function y(e){if(!e.name.startsWith(S))return;const t=f.get(e.name);let n=!1;for(const[o,r]of t.entries())if("fetched"===r.type&&v(r.response,e)){r.entry=e,t.splice(o,1),_(e.name),g(r),n=!0;break}n||t.push({type:"observed",entry:e})}function w(e){const t=f.get(e.response.url);if(!e.entry)for(const[n,o]of t.entries())if("observed"===o.type&&v(e.response,o.entry)){e.entry=o.entry,t.splice(n,1),_(e.response.url);break}if(e.entry){const n=t.indexOf(e);-1!==n&&(t.splice(n,1),_(e.response.url)),g(e)}}const S=`${o().A.domainBaseUrl}${o().A.api.http}`,k=20,A=new a({waitMs:5e3,maxWaitMs:1e4,maxSize:1e4,batchSize:50,prepareBatchFn:e=>{const t=[];for(const{metric:n,data:o}of e)"request.client_latency.asset"===n.metricName&&t.push({metric:n,data:o});return[{metric:{metricName:"request.client_latency.assets",startTime:performance.now(),endTime:performance.now()},data:{assets:t.flatMap((e=>e.data))}}]}});function C(e){if("fetch"===e.initiatorType)return;let t;try{t=new URL(e.name).pathname}catch(o){t=e.name}const n={shortened_url:t.slice(-k),start_time:e.startTime,time:e.duration,response_transfer_size:e.transferSize,perf_duration:e.duration,http_protocol:e.nextHopProtocol,encoded_body_size:e.encodedBodySize,decoded_body_size:e.decodedBodySize};e.transferSize>0&&(n.perf_before_request_start_duration=e.requestStart-e.startTime,n.perf_request_start_to_response_start_duration=e.responseStart-e.requestStart,n.perf_receive_response_duration=e.responseEnd-e.responseStart,n.perf_redirect_duration=e.redirectEnd-e.redirectStart,n.perf_connect_duration=e.connectEnd-e.connectStart,n.perf_dns_duration=e.domainLookupEnd-e.domainLookupStart),A.enqueue({metric:{metricName:"request.client_latency.asset",startTime:e.startTime,endTime:e.startTime+e.duration},data:n})}let I;const P=1e3;let T=!1;function E(){var e,t,n;T||(T=!0,Boolean(null===(e=performance)||void 0===e?void 0:e.setResourceTimingBufferSize)&&Boolean(null===(t=performance)||void 0===t?void 0:t.addEventListener)&&Boolean(null===(n=performance)||void 0===n?void 0:n.clearResourceTimings)&&(performance.setResourceTimingBufferSize(P),performance.addEventListener("resourcetimingbufferfull",(()=>{performance.clearResourceTimings()})),I??=new PerformanceObserver((e=>{for(const t of e.getEntries())"resource"===t.entryType&&(t.name.startsWith(S)?y(t):C(t))})),I.observe({type:"resource",buffered:!0}),PerformanceObserver.supportedEntryTypes.includes("longtask")&&(d??=new PerformanceObserver((e=>{!function(e){const t=u.findIndex((e=>e.startTime>=performance.now()-l));u.splice(0,t);for(const n of e)u.push(n)}(e.getEntries())})),d.observe({type:"longtask",buffered:!0}))))}function R(e){i.startFlushing(e),A.startFlushing(e)}function D(e){const{eventName:t,isPrefetchRequest:n}=e;let o;E();return{onRequestStart(e){o={type:"initiated",startTime:performance.now(),metricData:{event_name:t,is_prefetch_request:n,request_body_size:e}}},onRequestFetched(e){var t;if("initiated"!==(null===(t=o)||void 0===t?void 0:t.type))return;const n={...o,type:"fetched",response:e,parseResponseStartTime:void 0,parseResponseEndTime:void 0,entry:void 0,endTime:void 0,metricData:{...o.metricData,status:"success",status_code:e.status,request_ids:c(e)}};!function(e,t){const n=f.get(t.url),o=n.findIndex((e=>("fetched"===e.type?e.startTime:e.entry.startTime)>=performance.now()-m));n.splice(0,o),n.push(e)}(n,e),o=n},onRequestFailed(e){o&&s({startTime:o.startTime,metricData:{...o.metricData,status:"failed",status_code:null==e?void 0:e.status,request_ids:c(e)},endTime:performance.now(),entry:void 0,parseResponseStartTime:void 0,parseResponseEndTime:void 0})},onParseResponseStart(){var e;"fetched"===(null===(e=o)||void 0===e?void 0:e.type)&&(o.parseResponseStartTime=performance.now())},onParseResponseDone(e){var t;"fetched"===(null===(t=o)||void 0===t?void 0:t.type)&&(o.parseResponseEndTime=performance.now(),o.endTime=performance.now(),w(o))},onParseResponseFailed(e){var t;"fetched"===(null===(t=o)||void 0===t?void 0:t.type)&&(o.parseResponseEndTime=performance.now(),o.endTime=performance.now(),o.metricData.status="failed",o.metricData.status_code=e.status,w(o))}}}},468076:(e,t,n)=>{n.d(t,{y:()=>o});const o={}},468928:(e,t,n)=>{n.r(t)},469425:(e,t,n)=>{n.d(t,{CY:()=>y,XI:()=>w,p6:()=>g});n(16280),n(944114),n(898992),n(354520),n(581454);var o=()=>n(319625),r=()=>n(502800),a=()=>n(763824),i=()=>n(534177),s=()=>n(720665),c=()=>n(671593),l=()=>n(638681),d=()=>n(430476),u=()=>n(68336);const p={db_true:(0,d().Gd)(!0),arg_true:(0,d().Gd)(!0),db_false:(0,d().Gd)(!1),arg_false:(0,d().Gd)(!1),db_float:10.5,arg_float:10.5,db_string:"hello",arg_string:"hello",db_null:null,arg_null:null},m=l().object({required:{db_true:l().literal(p.db_true),arg_true:l().literal(p.arg_true),db_false:l().literal(p.db_false),arg_false:l().literal(p.arg_false),db_float:l().literal(p.db_float),arg_float:l().literal(p.arg_float),db_string:l().literal(p.db_string),arg_string:l().literal(p.arg_string),db_null:l().isNull(),arg_null:l().isNull()},optional:{},exact:!0});async function f(e,t,n){const r=[...t.statements,{sql:`PRAGMA user_version = ${t.id}`,getData:!1}];if("execSqliteBatchV2"in e){const t=e,c=(0,s().hr)(`\n\t\t  SELECT\n\t\t    CASE user_version\n\t\t    WHEN ${n} THEN 1\n\t\t    ELSE 0 END AS precondition_result\n\t\t    FROM pragma_user_version() LIMIT 1\n\t\t`),l=new C(c,t);try{await(0,d().G2)({connection:l,statements:r})}catch(i){if("SqlitePreconditionFail"!==(0,o().A)(i).name)throw i;await(0,a().wR)(50)}}else{const[o]=await(0,d().G2)({connection:e,statements:[{sql:"SELECT * from pragma_user_version() LIMIT 1",getData:!0}]}),{user_version:a}=(0,d().fb)(o);if(a!==n)throw new Error(`Cannot apply migration ${t.id}: DB user_version=${a}, expected ${n}`);await(0,d().G2)({connection:e,statements:r})}}async function g(e){const{connection:t,target:n,log:o}=e,a=e.dumpSchemaFn??y,{migrations:i,endSchema:s}=n,l=s.pragmas.user_version,d=await a(t),u=d.pragmas.user_version,p=i.filter((e=>e.id>u&&e.id<=l));if(0===p.length)return void A(d,s);for(let r=u+1;r<=l;r++){const e=p[r-u-1];if(!e)throw new Error(`Migrating ${u} -> ${l}: missing migration from ${r-1} to ${r}`);if(e.id!==r)throw new Error(`Migrating ${u} -> ${l}: migration order mismatch: expected id ${r}, had id ${e.id}`)}if(n.fastForward&&0===u&&l===n.fastForward.id&&function(e){const{actual:t,expected:n}=e;return!(0,c().tf)(S(n),t)}({actual:d,expected:{pragmas:{user_version:0},tables:{},indexes:{}}})){o({level:"info",from:"sqliteSchemaHelpers",type:"attemptFastForwardMigration",data:{message:`Attempting fast-forward migration to version ${l}`}});try{await f(t,n.fastForward,0)}catch(m){throw o({level:"error",from:"sqliteSchemaHelpers",type:"fastForwardMigrationError",error:(0,r().convertErrorToLog)(m)}),m}o({level:"info",from:"sqliteSchemaHelpers",type:"successfulFastForwardMigration",data:{message:`Successfully fast-forward migrated to version ${l}`}})}else{o({level:"info",from:"sqliteSchemaHelpers",type:"attemptMigration",data:{message:`Attempting migration from ${u} to ${l}`}});for(const e of p)try{await f(t,e,e.id-1)}catch(m){throw o({level:"error",from:"sqliteSchemaHelpers",type:"migrationError",error:(0,r().convertErrorToLog)(m)}),m}o({level:"info",from:"sqliteSchemaHelpers",type:"successfulMigration",data:{message:`Successfully migrated to ${l}`}})}A(await a(t),s)}function b(){return u().F4`SELECT * FROM pragma_user_version()`}function h(e){const t=e.whereTable?u().F4`schema_type = 'table' AND (${e.whereTable})`:u().F4`schema_type = 'table'`,n=e.whereIndex?u().F4`schema_type = 'index' AND (${e.whereIndex})`:u().F4`schema_type = 'index'`,o=e.whereView?u().F4`schema_type = 'view' AND (${e.whereView})`:u().F4`schema_type = 'view'`,r=e.whereTrigger?u().F4`schema_type = 'trigger' AND (${e.whereTrigger})`:u().F4`schema_type = 'trigger'`,a=[t,n,e.views?o:void 0,e.triggers?r:void 0].filter(i().O9),s=u().F4.or(a,1);return{tableSchemaDumpQuery:u().F4`
			WITH schema_rows AS (
				SELECT
					s.name as schema_name,
					s.type as schema_type,
					s.tbl_name as schema_tbl_name,
					s.sql as schema_sql
				FROM sqlite_master s
			)
			SELECT * FROM schema_rows
			WHERE ${s}
			ORDER BY schema_type, schema_name ASC
		`,columnSchemaDumpQuery:u().F4`
			WITH schema_rows AS (
				SELECT
					s.name as schema_name,
					s.type as schema_type,
					s.tbl_name as schema_tbl_name
				FROM sqlite_master s
			),
			filtered_schema_rows AS (
				SELECT * FROM schema_rows
				WHERE ${s}
			)
			SELECT
				s.schema_name as schema_name,
				s.schema_type as schema_type,

				COALESCE(c.cid, i.cid) as col_cid,
				COALESCE(c.name, i.name) as col_name,
				c.type as col_type,
				c."notnull" as col_notnull,
				c.dflt_value as col_dflt_value,
				c.pk as col_pk,
				i.seqno as col_seqno
			FROM filtered_schema_rows s
			LEFT JOIN pragma_table_info(s.schema_name) AS c ON s.schema_type = 'table' OR s.schema_type = 'view'
			LEFT JOIN pragma_index_info(s.schema_name) AS i ON s.schema_type = 'index'
			ORDER BY s.schema_type, s.schema_name, col_seqno, col_cid ASC
		`}}function v(e,t,n){const o={pragmas:{user_version:e.user_version},tables:{},indexes:{}};for(const s of t)"table"===s.schema_type&&(o.tables[s.schema_name]={info:{name:s.schema_name,sql:s.schema_sql,tbl_name:s.schema_tbl_name,type:s.schema_type},columns:[]}),"view"===s.schema_type&&(o.views??={},o.views[s.schema_name]={info:{name:s.schema_name,sql:s.schema_sql,tbl_name:s.schema_tbl_name,type:s.schema_type},columns:[]}),"index"===s.schema_type&&(o.indexes[s.schema_name]={info:{name:s.schema_name,sql:s.schema_sql,tbl_name:s.schema_tbl_name,type:s.schema_type},columns:[]}),"trigger"===s.schema_type&&(o.triggers??={},o.triggers[s.schema_name]={info:{name:s.schema_name,sql:s.schema_sql,tbl_name:s.schema_tbl_name,type:s.schema_type}});for(const s of n){var r,a,i;if("table"===s.schema_type)null===(r=o.tables[s.schema_name])||void 0===r||r.columns.push({cid:s.col_cid??-100,name:s.col_name??"",type:s.col_type,notnull:s.col_notnull??0,dflt_value:s.col_dflt_value,pk:s.col_pk??0});if("view"===s.schema_type)null===(a=o.views)||void 0===a||null===(a=a[s.schema_name])||void 0===a||a.columns.push({cid:s.col_cid??-100,name:s.col_name??"",type:s.col_type,notnull:s.col_notnull??0,dflt_value:s.col_dflt_value,pk:s.col_pk??0});if("index"===s.schema_type)null===(i=o.indexes[s.schema_name])||void 0===i||i.columns.push({seqno:s.col_seqno??-100,cid:s.col_cid??-100,name:s.col_name??""})}return o}class _ extends Error{constructor(e){super(e.message),this.name="SqliteDriverCheckError",this.actual=void 0,this.expected=void 0,this.actual=e.actual,this.expected=e.expected}}async function y(e,t){const{tableSchemaDumpQuery:n,columnSchemaDumpQuery:o}=h({triggers:null==t?void 0:t.triggers,views:null==t?void 0:t.views,whereTable:null==t?void 0:t.whereTable,whereIndex:null==t?void 0:t.whereIndex,whereTrigger:null==t?void 0:t.whereTrigger,whereView:null==t?void 0:t.whereView}),r=[n.asRead(),o.asRead(),b().asRead()];null!=t&&t.skipDriverCheck||r.push(u().F4`SELECT
				1 as db_true,
				${p.arg_true} as arg_true,

				0 as db_false,
				${p.arg_false} as arg_false,

				10.5 as db_float,
				${p.arg_float} as arg_float,

				'hello' as db_string,
				${p.arg_string} as arg_string,

				NULL as db_null,
				${p.arg_null} as arg_null`.asRead());const[a,i,s,l]=await(0,d().G2)({connection:e,statements:r});if((null==t||!t.skipDriverCheck)&&l){const e=(0,d().fb)(l),t=(0,c().tf)(m,e,{rootVariableName:"driverCheckRow"});if(t)throw new _({message:t.message,actual:e,expected:p})}return v((0,d().fb)(s),(0,d().OJ)(a),(0,d().OJ)(i))}function w(e,t){return function(e,t,n){const o=[];if(n.resetTables)for(const r of Object.values(t.tables))o.push(e`DROP TABLE IF EXISTS ${e.ident(r.info.name)}`);if(n.resetIndexes)for(const r of Object.values(t.indexes))o.push(e`DROP INDEX IF EXISTS ${e.ident(r.info.name)}`);if(n.resetViews&&t.views)for(const r of Object.values(t.views))o.push(e`DROP VIEW IF EXISTS ${e.ident(r.info.name)}`);if(n.resetTriggers&&t.triggers)for(const r of Object.values(t.triggers))o.push(e`DROP TRIGGER IF EXISTS ${e.ident(r.info.name)}`);return n.resetUserVersion&&o.push(e`PRAGMA user_version = 0`),o}(e,t,{resetUserVersion:!0,resetTables:!0,resetIndexes:!0,resetViews:!0,resetTriggers:!0}).map((e=>e.asWrite()))}function S(e){const t=l().object({required:{user_version:l().literal(e.pragmas.user_version)},optional:{},exact:!0}),n=e=>l().object({required:{name:l().literal(e.name),type:l().literal(e.type),tbl_name:l().literal(e.tbl_name),sql:l().union([l().string(),l().isNull()])},optional:{}}),o=e=>l().object({required:{info:n(e.info),columns:l().tuple(e.columns.map((e=>l().object({required:{cid:l().literal(e.cid),name:l().literal(e.name),type:null===e.type?l().isNull():l().caseInsensitiveLiteral(e.type),notnull:l().literal(e.notnull),dflt_value:null===e.dflt_value?l().isNull():l().literal(e.dflt_value),pk:l().literal(e.pk)},optional:{}}))))},optional:{}}),r={};for(const[l,m]of Object.entries(e.tables))r[l]=o(m);const a={};for(const[m,f]of Object.entries(e.indexes))a[m]=l().object({required:{info:n(f.info),columns:l().tuple(f.columns.map((e=>l().object({required:{seqno:l().literal(e.seqno),cid:l().literal(e.cid),name:null===e.name?l().isNull():l().literal(e.name)},optional:{}}))))},optional:{}});let i,s;if(e.views){i={};for(const[t,n]of Object.entries(e.views))i[t]=o(n)}if(e.triggers){s={};for(const[t,o]of Object.entries(e.triggers))s[t]=l().object({required:{info:n(o.info)},optional:{}})}const c=l().object({required:r,optional:{},exact:!1}),d=l().object({required:a,optional:{},exact:!1}),u=i&&l().object({required:i,optional:{},exact:!1}),p=s&&l().object({required:s,optional:{},exact:!1});return l().object({required:{pragmas:t,tables:c,indexes:d},optional:{views:u??l().isUndefined(),triggers:p??l().isUndefined()},exact:!0})}class k extends Error{constructor(e){super(e.message),this.name="SqliteSchemaMismatch",this.actual=void 0,this.expected=void 0,this.actual=e.actual,this.expected=e.expected}}function A(e,t){const n=(0,c().tf)(S(t),e,{rootVariableName:"schema"});if(n)throw new k({message:n.message,actual:e,expected:t});if(t.views&&!e.views)throw new k({message:"Expected schema with view info, but no views dumped",actual:e,expected:t});if(t.triggers&&!e.triggers)throw new k({message:"Expected schema with trigger info, but no triggers dumped",actual:e,expected:t})}class C{constructor(e,t){this.precondition=e,this.connection=t}async execSqliteBatch(e){return await this.connection.execSqliteBatchV2({batch:e,precondition:{sql:this.precondition,getData:!0}})}completelyRebuildSqliteDb(){throw new Error("Not implemented.")}}},474618:(e,t,n)=>{(async function(){const e=performance.now(),{loadErrorReporting:t}=await Promise.resolve().then(n.bind(n,365902));let o;await t(),Promise.resolve().then(n.bind(n,865085));const r=localStorage.getItem("statsig:fetchLocalEvalConfigSpec");if("on"===r){const{StatsigInitializer:e}=await Promise.resolve().then(n.bind(n,165162));o=e.fetchConfigFilePromise()}const{loadCss:a}=await Promise.resolve().then(n.bind(n,20765));await a();const{getHtmlStreamQueueEntry:i}=await Promise.resolve().then(n.bind(n,105751));await window.LOCALE_SETUP_P,await Promise.race([i("ready"),i("bootReady")]),Promise.all([n.e(75134,"high"),n.e(99223,"high"),n.e(58795,"high"),n.e(9304,"high"),n.e(36556,"high"),n.e(40537,"high"),n.e(17230,"high"),n.e(23740,"high"),n.e(70039,"high"),n.e(78405,"high"),n.e(90978,"high"),n.e(1707,"high"),n.e(48113,"high"),n.e(36639,"high"),n.e(48307,"high"),n.e(14310,"high"),n.e(28398,"high"),n.e(55850,"high"),n.e(34438,"high"),n.e(28464,"high"),n.e(33358,"high"),n.e(64271,"high"),n.e(55269,"high"),n.e(85898,"high"),n.e(1694,"high"),n.e(36479,"high"),n.e(78217,"high")]).then(n.bind(n,276324));const{initializeReactivityVersion:s}=await Promise.resolve().then(n.bind(n,118165)),c=s(),{createMinimalEnvironment:l}=await Promise.resolve().then(n.bind(n,999822)),d=await l(),{loadCurrentUserId:u}=await Promise.resolve().then(n.bind(n,94442)),p=await u(d),{initializeStatsig:m}=await Promise.resolve().then(n.bind(n,904819)),f=m({environment:d,currentUserId:p,fetchConfigFilePromise:o});let g;"on"!==r&&(g=await f);const{deliverAndPersistReactivityVersion:b}=await Promise.resolve().then(n.bind(n,715668));await b(d,c);const{prefetchRequests:h}=await Promise.resolve().then(n.bind(n,249229)),v=await h({environment:d,currentUserId:p}),_=performance.now();Promise.all([n.e(58795),n.e(17230),n.e(36639),n.e(48307),n.e(14310)]).then(n.bind(n,388821));const{loadAppPreboot:y}=await Promise.resolve().then(n.bind(n,543190));await y();const{mainApp:w}=await Promise.all([n.e(75134,"high"),n.e(99223,"high"),n.e(58795,"high"),n.e(9304,"high"),n.e(36556,"high"),n.e(40537,"high"),n.e(17230,"high"),n.e(23740,"high"),n.e(70039,"high"),n.e(78405,"high"),n.e(90978,"high"),n.e(1707,"high"),n.e(48113,"high"),n.e(36639,"high"),n.e(48307,"high"),n.e(14310,"high"),n.e(28398,"high"),n.e(55850,"high"),n.e(34438,"high"),n.e(28464,"high"),n.e(33358,"high"),n.e(64271,"high"),n.e(55269,"high"),n.e(85898,"high"),n.e(1694,"high"),n.e(36479,"high"),n.e(78217,"high")]).then(n.bind(n,276324)),{transactionQueue:S,environment:k}=await w({minimalEnvironment:d,mainStartTime:e,prefetchInitiatedTime:_,prefetchCache:v,initializeStatsigResult:g,initializeStatsigPromise:f,currentUserId:p}),[A,{loadConsoleHelpers:C}]=await Promise.all([Promise.all([n.e(75134),n.e(99223),n.e(58795),n.e(17230),n.e(23740),n.e(36639),n.e(48307),n.e(14310),n.e(28398),n.e(55850),n.e(1797)]).then(n.bind(n,604341)),Promise.all([n.e(75134),n.e(99223),n.e(58795),n.e(17230),n.e(23740),n.e(36639),n.e(48307),n.e(14310),n.e(28398),n.e(55850),n.e(1797)]).then(n.bind(n,802935))]);A.onConsoleFirstEnabled("loadConsoleHelpers",(async()=>{await C({transactionQueue:S,environment:k})}))})().catch((async e=>{let t=e;try{const{convertErrorToLog:o}=await Promise.resolve().then(n.bind(n,502800));t=o(e)}catch(e){}try{var o;const{electronApi:e}=await Promise.resolve().then(n.bind(n,675742));null==e||null===(o=e.logErrorForOffline)||void 0===o||o.call(e,{level:"error",from:"main",type:"error",error:t})}catch(e){}throw e}))},475133:(e,t,n)=>{n.r(t),n.d(t,{getMobileNativeDeviceInfo:()=>s,getMobileVersion:()=>d,isIPhoneX:()=>c,mobileNativeDeviceInfoKey:()=>i,supportsDarkMode:()=>l});var o=()=>n(711059),r=()=>n(496603),a=()=>n(155959);const i="__reactNativeDeviceInfo";function s(){const e=window[i];return e?{mobileNativeAppVersion:e.appVersion,mobileNativeCellularConnection:e.cellularConnection,mobileNativeDeviceModel:e.deviceModel,mobileNativeDeviceId:e.deviceId,mobileNativeAndroidId:e.uniqueId,mobileNativeSystemVersion:e.systemVersion,mobileNativeAndroidApiLevel:e.androidApiLevel,mobileNativeDeviceBrand:e.deviceBrand,mobileNativeDeviceManufacturer:e.deviceManufacturer,mobileNativeDeviceCarrier:e.deviceCarrier,mobileNativeDeviceCountry:e.deviceCountry,mobileNativeFreeDiskStorageBytes:e.freeDiskStorageBytes,ramSizeInGB:e.ramSizeInGB,androidKeyboardProviderPackage:e.androidKeyboardProviderPackage,is_mobile_beta:e.isMobileBeta,is_automated_qa_build:(0,a().T)("isAutomatedQABuild")}:{}}function c(){const e=window[i];return!!e&&e.deviceModel.startsWith("iPhone X")}function l(e){const t=window[i];if(!t)return!1;if(e.device.isIOS){const e=t.systemVersion.split(".");if(e.length>0)return parseInt(e[0])>=13}else{const e=t.androidApiLevel;if("number"==typeof e&&!r().yr(e))return e>=29}return!1}function d(e){const t=window[i];if(t)return(0,o().parseMobileAppVersion)(t.appVersion,e.device.isAndroid)}},479954:(e,t,n)=>{n.d(t,{G:()=>p,l:()=>l,loadCurrentUserId:()=>d});var o=()=>n(604341),r=()=>n(857639),a=()=>n(677338),i=()=>n(994310),s=()=>n(502800),c=()=>n(529543);const l="current-user-id";async function d(e){const[t,n,o]=await Promise.all([u(e),p(e),(0,a().j4)()]);return o||t||(Array.isArray(n)?n[0]:n)}async function u(e){let t;const n=i().A.get(l);n&&(t=n);const o=await p(e);if(!o||!t||o.includes(t))return t}async function p(e){const t=await c().getCookie(e,"notion_users");if(t){const e=decodeURIComponent(t);let o;try{o=JSON.parse(e)}catch(n){r().log({level:"error",from:"loginActions",type:"getUserIdsFromCookieJsonError",error:(0,s().convertErrorToLog)(n),data:{message:`decoded cookie was not valid JSON: \`${e}\``,decoded:e,cookieValue:t}})}return Array.isArray(o)?o:void 0}}(0,o().exposeDebugValue)("loadCurrentUserId",d),(0,o().exposeDebugValue)("getUserIdsFromCookie",p)},485993:(e,t,n)=>{n.d(t,{Z:()=>s,f:()=>i});var o=()=>n(419494);const r="rtlDevToolbarSetting",a=new(o().Ay)({namespace:o().Bq,important:!1,trackingType:"necessary"});function i(){return Boolean(a.get(r))}function s(e){a.set(r,e)}},492553:(e,t,n)=>{n.d(t,{H:()=>r});class o extends(()=>n(757695))().Store{getInitialState(){return{entries:[]}}canGoBack(){return void 0!==this.state.activeIndex&&this.state.activeIndex>0}canGoForward(){return void 0!==this.state.activeIndex&&this.state.activeIndex<this.state.entries.length-1}popEntry(e){if(void 0===this.state.activeIndex)return;const t=this.state.activeIndex+e,n=this.state.entries[t];return this.update((e=>({...e,activeIndex:t}))),n}peekEntry(e){if(void 0===this.state.activeIndex)return;const t=this.state.activeIndex+e;return this.state.entries[t]}pushEntry(e){if(e.url.endsWith("/blank"))return;const t=this.state.activeIndex===this.state.entries.length-1;this.update((n=>{const o=[...t?n.entries:n.entries.slice(0,this.state.activeIndex+1),e];if(o.length>20){const e=o.length-20,t=o.slice(e);return{...n,entries:t,activeIndex:t.length-1}}return{...n,entries:o,activeIndex:o.length-1}}))}replaceEntry(e){this.update((t=>({...t,entries:[...t.entries.slice(0,this.state.activeIndex),e,...t.entries.slice(this.state.activeIndex+1)]})))}getHistoryEntries(e){if(void 0===this.state.activeIndex)return[];return("back"===e?this.state.entries.slice(0,this.state.activeIndex).reverse():this.state.entries.slice(this.state.activeIndex+1)).slice(0,10)}}const r=new o},493720:(e,t,n)=>{n.d(t,{R:()=>a,X:()=>r});var o=()=>n(558842);class r{constructor(){this.state=void 0}memo(e,t){if(this.state&&function(e,t){if(e.length!==t.length)return!1;for(let o=0;o<e.length;o++)if(!(0,n(821062).A)(e[o],t[o]))return!1;return!0}(t,this.state.dependencies))return this.state.memoized;const o=e();return this.state={memoized:o,dependencies:t},o}}function a(){const e=new r;return function(){for(var t=arguments.length,n=new Array(t),r=0;r<t;r++)n[r]=arguments[r];return e.memo((()=>(0,o().Px)(...n)),n)}}},496603:(e,t,n)=>{n.d(t,{$1:()=>T.a,$n:()=>it.a,$r:()=>$e.a,$z:()=>N.a,B8:()=>J.a,Bj:()=>fe.a,Bq:()=>D.a,Cr:()=>B.a,D_:()=>L.a,Dw:()=>Ze.a,E$:()=>V.a,Et:()=>Y.a,FF:()=>ot.a,Gv:()=>ee.a,HP:()=>se.a,HV:()=>ce.a,Hd:()=>st.a,Hn:()=>He.a,I6:()=>k.a,Im:()=>W.a,Iy:()=>rt.a,Jt:()=>x.a,KC:()=>Ke.a,Kg:()=>oe.a,Kl:()=>P.a,LG:()=>de.a,LI:()=>Te.a,LW:()=>Oe.a,Lc:()=>h.a,Lm:()=>H.a,MI:()=>Z.a,Mp:()=>et.a,My:()=>we.a,NF:()=>tt.a,NZ:()=>Ne.a,Nt:()=>y.a,O6:()=>je.a,Oo:()=>ye.a,Qd:()=>ne.a,R9:()=>b.a,RK:()=>Qe.a,RT:()=>Fe.a,SL:()=>A.a,Si:()=>w.a,Sk:()=>pe.a,T9:()=>ue.a,TF:()=>Pe.a,Tj:()=>C.a,Tn:()=>G.a,Tr:()=>m.a,Uk:()=>I.a,Ul:()=>Be.a,Up:()=>ke.a,VP:()=>ze.a,XM:()=>Ee.a,Z4:()=>te.a,ZH:()=>a.a,Zk:()=>xe.a,__:()=>v.a,aD:()=>R.a,b0:()=>re.a,bJ:()=>z.a,cJ:()=>ve.a,cb:()=>g.a,cz:()=>Le.a,d4:()=>le.a,dY:()=>p.a,di:()=>qe.a,fN:()=>Ae.a,gD:()=>Q.a,h1:()=>ge.a,hS:()=>Ye.a,hZ:()=>De.a,i2:()=>me.a,ih:()=>_.a,iv:()=>i.a,jB:()=>Se.a,jJ:()=>q.a,k4:()=>Me.a,kP:()=>j.a,kW:()=>ae.a,kZ:()=>X.a,lQ:()=>he.a,mK:()=>F.a,mO:()=>o.a,mg:()=>l.a,n4:()=>$.a,nF:()=>Je.a,o8:()=>c.a,oE:()=>d.a,pY:()=>ie.a,pb:()=>S.a,qE:()=>s.a,qI:()=>E.a,r4:()=>U.a,rG:()=>M.a,s:()=>Ve.a,s8:()=>We.a,sb:()=>Xe.a,se:()=>Re.a,sg:()=>f.a,sn:()=>nt.a,uk:()=>O.a,vF:()=>ct,wq:()=>be.a,x4:()=>Ge.a,xQ:()=>r.a,xW:()=>u.a,xu:()=>Ue.a,y1:()=>Ie.a,yT:()=>Ce.a,yU:()=>at.a,yr:()=>K.a,z7:()=>_e.a});n(254664);var o=n.n(n(413139)),r=n.n(n(984058)),a=n.n(n(314792)),i=(n(253551),n.n(n(821013))),s=n.n(n(978659)),c=n.n(n(932629)),l=n.n(n(688055)),d=(n(789647),n.n(n(183673))),u=n.n(n(492078)),p=n.n(n(137334)),m=n.n(n(574154)),f=n.n(n(738221)),g=(n(884684),n.n(n(360895))),b=n.n(n(666245)),h=n.n(n(897648)),v=(n(927537),n.n(n(976135))),_=(n(275288),n.n(n(414425))),y=n.n(n(660680)),w=n.n(n(619747)),S=n.n(n(587612)),k=n.n(n(907309)),A=n.n(n(324713)),C=n.n(n(820826)),I=n.n(n(620681)),P=n.n(n(94469)),T=n.n(n(756170)),E=n.n(n(547307)),R=n.n(n(78325)),D=n.n(n(835970)),M=n.n(n(503176)),q=n.n(n(539754)),O=n.n(n(333215)),B=n.n(n(44377)),x=n.n(n(858156)),N=n.n(n(494394)),L=(n(761448),n(400912),n.n(n(383488))),U=n.n(n(959104)),F=n.n(n(679859)),V=(n(763424),n.n(n(305287))),j=n.n(n(780191)),J=n.n(n(140866)),H=n.n(n(553812)),W=(n(3656),n(183602),n.n(n(962193))),$=n.n(n(302404)),z=n.n(n(623546)),Z=n.n(n(892297)),G=n.n(n(501882)),K=n.n(n(611741)),Q=n.n(n(269843)),X=n.n(n(305187)),Y=n.n(n(198023)),ee=n.n(n(223805)),te=n.n(n(540346)),ne=n.n(n(411331)),oe=(n(338440),n.n(n(185015))),re=n.n(n(762216)),ae=n.n(n(620249)),ie=n.n(n(338970)),se=n.n(n(395950)),ce=n.n(n(468090)),le=n.n(n(179674)),de=n.n(n(473916)),ue=n.n(n(394506)),pe=n.n(n(597551)),me=n.n(n(355083)),fe=n.n(n(150104)),ge=n.n(n(355364)),be=(n(406924),n(631684),n.n(n(136533))),he=n.n(n(263950)),ve=n.n(n(590179)),_e=n.n(n(142194)),ye=n.n(n(858059)),we=n.n(n(142877)),Se=(n(982485),n(852037),n.n(n(306498))),ke=n.n(n(244383)),Ae=n.n(n(971086)),Ce=(n(550583),n.n(n(258253))),Ie=n.n(n(623181)),Pe=n.n(n(14174)),Te=(n(912493),n.n(n(36944))),Ee=n.n(n(745620)),Re=n.n(n(33441)),De=n.n(n(63560)),Me=n.n(n(736049)),qe=(n(47091),n.n(n(737530))),Oe=n.n(n(204124)),Be=n.n(n(333031)),xe=(n(941298),n(473054),n.n(n(859043))),Ne=n.n(n(190128)),Le=n.n(n(336119)),Ue=n.n(n(531126)),Fe=n.n(n(44384)),Ve=n.n(n(834921)),je=n.n(n(287779)),Je=(n(730872),n.n(n(407350))),He=n.n(n(6638)),We=n.n(n(231521)),$e=n.n(n(582306)),ze=(n(399374),n.n(n(269884))),Ze=(n(213222),n.n(n(619488))),Ge=n.n(n(375494)),Ke=n.n(n(280299)),Qe=n.n(n(999786)),Xe=n.n(n(763375)),Ye=n.n(n(950014)),et=n.n(n(509063)),tt=n.n(n(97200)),nt=(n(273357),n.n(n(618330))),ot=(n(555808),n.n(n(891648))),rt=(n(466645),n(656625),n.n(n(715004))),at=n.n(n(265611)),it=(n(747248),n.n(n(307082))),st=n.n(n(180962));function ct(){return!0}},498212:(e,t,n)=>{n.d(t,{$4:()=>U,$h:()=>L,Ay:()=>j,Ct:()=>l,G2:()=>T,Iq:()=>k,OB:()=>F,S2:()=>E,TC:()=>N,TK:()=>D,Xw:()=>C,_q:()=>y,bC:()=>x,c_:()=>I,dY:()=>R,gB:()=>c,iv:()=>P,lZ:()=>s,np:()=>u,ot:()=>M,parseUUIDWithoutDashes:()=>p,rL:()=>B,rN:()=>q,sO:()=>A,uj:()=>h,w7:()=>V,zj:()=>O});n(16280),n(816573),n(878100),n(177936),n(748140),n(821903),n(491134),n(128845),n(237467),n(444732),n(979577),n(898992),n(354520),n(581454),n(964979);var o=()=>n(717344),r=n.n(n(883503)),a=()=>n(871386),i=()=>n(806080);function s(){var e;return null!==(e=globalThis.crypto)&&void 0!==e&&e.randomUUID?globalThis.crypto.randomUUID():(0,a().A)()}function c(e){const t=r()(e),n=(0,i().i)(t.split(""),2).map((e=>parseInt(e.join(""),16)));return(0,a().A)({random:n})}function l(e){const t=(0,o().sc)(e).slice(0,16);return btoa(String.fromCharCode(...t)).replace(/=+$/,"").replace(/\//g,"-").replace(/\+/g,"_")}const d=36;function u(e){return[e.substring(0,8),e.substring(8,12),e.substring(12,16),e.substring(16,20),e.substring(20,32)].join("-")}function p(e){if(e&&I(e))return u(e)}const m=new RegExp(/^[0-9A-F]{8}-[0-9A-F]{4}-4[0-9A-F]{3}-[89AB][0-9A-F]{3}-[0-9A-F]{12}$/i),f=new RegExp(/^[0-9A-F]{12}4[0-9A-F]{3}[89AB][0-9A-F]{15}$/i);const g=new RegExp(/^[0-9A-F]{8}-[0-9A-F]{4}-8[0-3][0-9A-F]{2}-[89AB][0-9A-F]{3}-[0-9A-F]{12}$/i),b=new RegExp(/^[0-9A-F]{12}8[0-3][0-9A-F]{2}[89AB][0-9A-F]{15}$/i);function h(e){const t=e;return Boolean(t&&t.length===d&&(V.includes(t)||t.match(m)||t.match(g)))}function v(e){return Boolean(e&&e.length===d&&g.test(e))}function _(e){return Boolean(e&&e.length===A&&b.test(e))}function y(e){return v(e)||_(e)}const w=464;function S(e){const t=parseInt(e.substring(0,3),16);return!Number.isNaN(t)&&4095!==t&&t>w}function k(e){return function(e){const t=e;return Boolean(t&&t.length===d&&(V.includes(t)||m.test(t)))}(e)||function(e){return Boolean(e&&e.length===A&&f.test(e))}(e)?"uuid-v4":v(e)?"2"===e[15]||"3"===e[15]?"notion-v1d":S(e)?"notion-v1":"notion-v0":_(e)?"2"===e[13]||"3"===e[13]?"notion-v1d":S(e)?"notion-v1":"notion-v0":"unknown"}const A=32;function C(e){return e.replace(/-/g,"")}function I(e){return Boolean(e&&e.length===A&&/^[a-f0-9]*$/g.test(e))}function P(e){return h(e)||I(e)}function T(e){return h(e)?e:u(e)}function E(e){return h(e)?C(e):e}function R(e){return function(e){return(0,i().s)(e.split(",").map((e=>{const t=e.replace(/\s/g,""),n=p(t);return n||t})))}(e).filter((e=>h(e)))}function D(e){return parseInt(e.slice(-5),16)}Symbol("Without dashes");function M(e){return C(e)}"0".charCodeAt(0),"a".charCodeAt(0),"-".charCodeAt(0);const q="00000000-0000-0000-0000-000000000000",O="00000000-0000-0000-0000-000000000001",B="00000000-0000-0000-0000-000000000003",x="00000000-0000-0000-0000-000000000004",N="00000000-0000-0000-0000-000000000005",L="00000000-0000-0000-0000-000000000006",U="00000000-0000-0000-0000-000000000009",F="00000000-0000-0000-0000-00000000000a",V=[q,O,"00000000-0000-0000-0000-000000000002",B,x,N,L,"00000000-0000-0000-0000-000000000007","00000000-0000-0000-0000-000000000008",U,F,"FFFFFFFF-FFFF-FFFF-FFFF-FFFFFFFFFFFF"],j=s},502492:(e,t,n)=>{n.d(t,{$J:()=>a,PW:()=>s,Q1:()=>c,it:()=>l,kb:()=>i});var o=()=>n(188835);function r(e){return e.src.endsWith("/")?e.dest.endsWith("/")?e.dest:`${e.dest}/`:e.dest.endsWith("/")?e.dest.slice(0,e.dest.length-1):e.dest}function a(e){const t=o().parse(e.httpUrl,!0);t.protocol=`${e.protocol}:`,t.port=e.includePort?t.port:null,t.host=null;const n=o().format(t);return r({src:e.httpUrl,dest:n})}function i(e){const t=o().parse(e.baseUrl),n=o().parse(e.schemeUrl,!0);n.protocol=t.protocol,n.port=t.port,n.host=t.host;const a=o().format(n);return r({src:e.schemeUrl,dest:a})}function s(e){const{url:t,isLocalHost:n}=e;return`${n?"http":"https"}://${t}`}function c(e){if(!e.url.startsWith(`${e.protocol}:`))return e.url;const t=o().parse(e.baseUrl,!0);return o().parse(e.url,!0).hostname===t.hostname?e.url:e.url.replace(`${e.protocol}://*`,`${e.protocol}:/`)}function l(e){return e.url.startsWith(e.protocol)?i({schemeUrl:e.url,baseUrl:e.domainBaseUrl}):e.url}},502800:(e,t,n)=>{n.d(t,{convertErrorToLog:()=>s,WS:()=>i,iX:()=>c});n(517642),n(658004),n(733853),n(845876),n(432475),n(515024),n(731698),n(581454);var o=()=>n(496603);const r=["name","message","stack","reason","data","info"];function a(e){let t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:15,n=arguments.length>2&&void 0!==arguments[2]?arguments[2]:new Set;if(o().Kg(e)||o().Et(e)||o().Lm(e))return e;if(Array.isArray(e))return n.has(e)||t<=0?[{"@":"…"}]:(n.add(e),e.map((e=>a(e,t-1,n))));if(e instanceof Set)return n.has(e)||t<=0?[{"@":"…"}]:(n.add(e),Array.from(e).map((e=>a(e,t-1,n))));if(o().bJ(e)||o().Z4(e)){if(n.has(e)||t<=0)return{"@":"…"};if(n.add(e),function(e){return Boolean("object"==typeof e&&e&&"toJSON"in e&&"function"==typeof e.toJSON)}(e))return a(e.toJSON());const o={};for(const r of Object.keys(e)){if(o[r])continue;if(r.length>0&&"_"===r[0]){o[r]="[omitted]";continue}const i=e[r];o[r]=a(i,t-1,n)}for(const i of r){if(o[i])continue;const r=e[i];r&&(o[i]=a(r,t-1,n))}return o}}function i(e,t){const{stringify:n=JSON.stringify,shouldCleanObjectsForLogging:o=!0}=t??{};try{if("object"==typeof e&&null!==e){return n(o?a(e,10):e)}return String(e)}catch(r){const e=r;return`Unable to safely convert to string: "${e.stack?e.stack:""}"`}}function s(e){let t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:JSON.stringify,n=arguments.length>2&&void 0!==arguments[2]&&arguments[2];const o={stringify:t,shouldCleanObjectsForLogging:!0};try{if("object"==typeof e&&null!==e){const{statusCode:t,name:r,message:a,data:s,error:c,stack:l,body:d,reason:u,queryName:p}=e,m={};if(void 0!==t&&(m.statusCode=Number(t)),r&&(m.name=String(r)),a&&(m.message=i(a,o)),u&&!m.message&&n&&(m.message=i(u,o)),p&&(m.queryName=String(p)),s&&(m.miscDataString=i(s,o)),c){m.miscErrorString=i(c,o);const e=c.code;void 0!==e&&(m.code=String(e))}if(l&&(m.stack=String(l)),d)if(m.body={},"object"==typeof d&&null!==d){const{errorId:e,name:t,message:n,clientData:r}=d;e&&(m.body.errorId=String(e)),t&&(m.body.name=String(t)),n&&(m.body.message=i(n,o)),r&&(m.body.clientDataString=i(r,o))}else m.body.message=i(d,o);return m}return{miscErrorString:i(e,o)}}catch(r){const e=r;return{miscErrorString:`Unable to safely convert error to log: "${e.stack?e.stack:""}"`}}}function c(e,t){const{miscDataToConvertToString:n,...o}=e,r=o;return void 0!==n&&(r.miscDataString=i(n,t)),r}},513416:(e,t,n)=>{n.d(t,{Mh:()=>c,PX:()=>u,f1:()=>l,kX:()=>d,nG:()=>i,xB:()=>a});n(16280),n(898992),n(672577),n(581454);const o={INTERNAL_SERVER_ERROR:500,NOT_IMPLEMENTED:501,BAD_GATEWAY:502,SERVICE_UNAVAILABLE:503,GATEWAY_TIMEOUT:504,HTTP_VERSION_NOT_SUPPORTED:505,VARIANT_ALSO_NEGOTIATES:506,INSUFFICIENT_STORAGE:507,LOOP_DETECTED:508,NOT_EXTENDED:510,NETWORK_AUTHENTICATION_REQUIRED:511,FORBIDDEN:403},r={BAD_REQUEST:400,UNAUTHORIZED:401,PAYMENT_REQUIRED:402,FORBIDDEN:403,NOT_FOUND:404,METHOD_NOT_ALLOWED:405,NOT_ACCEPTABLE:406,PROXY_AUTHENTICATION_REQUIRED:407,REQUEST_TIMEOUT:408,CONFLICT:409,GONE:410,LENGTH_REQUIRED:411,PRECONDITION_FAILED:412,PAYLOAD_TOO_LARGE:413,URI_TOO_LONG:414,UNSUPPORTED_MEDIA_TYPE:415,RANGE_NOT_SATISFIABLE:416,EXPECTATION_FAILED:417,IM_A_TEAPOT:418,MISDIRECTED_REQUEST:421,UNPROCESSABLE_ENTITY:422,LOCKED:423,FAILED_DEPENDENCY:424,TOO_EARLY:425,UPGRADE_REQUIRED:426,PRECONDITION_REQUIRED:428,TOO_MANY_REQUESTS:429,UNAVAILABLE_FOR_LEGAL_REASONS:451,RESOURCE_EXPIRED:419,FETCHING:439},a={...r,...o},i={OK:200,CREATED:201,ACCEPTED:202,NO_CONTENT:204,FOUND:302,TEMPORARY_REDIRECT:307,...r,...o};a.REQUEST_TIMEOUT,a.TOO_EARLY,a.TOO_MANY_REQUESTS,a.INTERNAL_SERVER_ERROR,a.BAD_GATEWAY,a.SERVICE_UNAVAILABLE,a.GATEWAY_TIMEOUT;class s{constructor(e,t,n){this.statusCode=e,this.body=t,this.headers=n}}Object.assign({},...(0,n(534177).WP)(i).map((e=>{let[t,n]=e;return{[t]:(e,t)=>new s(n,e,t)}})));new class{constructor(e){this.processedResponse=e}get statusCode(){return this.processedResponse.statusCode}}(new Error("missing-status-code"));function c(e){return Object.values(a).includes(e)}function l(e){return Object.values(o).includes(e)}function d(e){return"statusCode"in e&&c(e.statusCode)}function u(e){return!d(e)}},520348:(e,t,n)=>{n.d(t,{Jn:()=>d,L8:()=>c,P4:()=>l,Qs:()=>u});n(16280),n(944114);var o=()=>n(851941),r=()=>n(96689),a=()=>n(870723),i=()=>n(165162),s=()=>n(765957);function c(e){let t;try{t=function(e){var t,n;const o=null===(t=s().default.state.currentSpaceStore)||void 0===t?void 0:t.getModel();if(o)return o;const r=null===(n=s().default.state.mainEditorCurrentBlockStore)||void 0===n?void 0:n.pointer.spaceId;if(r){const t=d({defaultRecordCache:e.defaultRecordCache,userId:e.currentUser.id,spaceId:r});if(t)return t}throw new Error("getCurrentSpaceModel called before page was rendered.")}(e).id}catch(n){}return t?{"x-notion-space-id":t}:{}}function l(){var e;const t=null===(e=(0,i().bo)("mc_config"))||void 0===e?void 0:e.get("cell","disabled");return t&&"disabled"!==t?{"x-notion-cell":t}:{}}function d(e){const{defaultRecordCache:t,userId:n,spaceId:o}=e;return t.inMemoryRecordCache.getRecordModel({pointer:{table:r().NX,id:o},userId:n})}function u(e,t){const n=[],i=[],s={},c={};for(const l of e){const e=t(l);if(!e)throw new Error("Expected a record pointer");const d=(0,o().dR)(e.id);if((0,a().$t)(e.table)||(0,a().kW)(e.table)){let t;if(t=e.table===r().NX?e.id:e.spaceId,t)c[t]??=[],c[t].push(l);else if("main"!==d&&void 0!==d){const e=d.toString();s[e]??=[],s[e].push(l)}else i.push(l)}else n.push(l)}return{spaceCellObjects:c,spaceCellObjectsMissingSpaceId:i,spaceCellObjectsWithShortSpaceId:s,mainDbObjects:n}}},526147:(e,t,n)=>{n.d(t,{Ay:()=>s,F3:()=>c,Fu:()=>i,uB:()=>a});var o=()=>n(498212),r=()=>n(292588);const a={collection:!0},i="record_key",s={table:i,columnTypes:{id:r().A.UUID,version:r().A.Number,last_version:r().A.Number,space_id:r().A.UUID,scope_table:r().A.String,scope_id:r().A.UUID,parent_table:r().A.String,parent_id:r().A.UUID,record_key:r().A.String,alive:r().A.Boolean},requiredColumns:{space_id:!0,scope_table:!0,scope_id:!0,parent_table:!0,parent_id:!0,alive:!0},model:(0,n(152853).P)({RecordStore:!0})};function c(e){const{spaceId:t,parentTable:n,parentId:r}=e,[a,i]=["space",t];return(0,o().gB)(`${t}-${a}-${i}-${n}-${r}`)}},529543:(e,t,n)=>{n.r(t),n.d(t,{canSetCookie:()=>R,canShowCookieUi:()=>D,clearNotionCookiesWithNoPermission:()=>F,clearThirdPartyCookiesAndLocalStorageWithNoPermission:()=>V,getBrowserId:()=>k,getCookie:()=>S,getCookieConsentCookie:()=>w,getCookieWithoutPermissionCheck:()=>I,getExperimentDeviceId:()=>A,getGhostAdminUserId:()=>P,getHasPermissionArgs:()=>U,getPartnerStackCookie:()=>h,getPublicDomainUserId:()=>T,getS2STrackingCookie:()=>_,hasPermissionForCookie:()=>x,hasPermissionForThirdPartyService:()=>L,hasPermissionForTrackingType:()=>N,refreshNotionCookies:()=>q,removeCookie:()=>M,removeCookieConsentCookie:()=>y,setCookie:()=>f,setCookieConsentCookie:()=>g,setPartnerStackCookie:()=>b,setS2STrackingCookie:()=>v,validateHasPermissionArgs:()=>O});n(814603),n(147566),n(198721);var o=()=>n(726637),r=()=>n(283699),a=()=>n(857639),i=()=>n(206267),s=()=>n(839816),c=()=>n(427704),l=()=>n(711059),d=()=>n(502800),u=()=>n(155959),p=()=>n(675742),m=()=>n(836251);async function f(e,t){if(!R(e))return;if(!(await x(t.name,e)))return;const{mobileNative:n,device:a}=e,i={path:c().JZ.root,expires:r().T0(t.name),secure:!1,domain:r().R2(o().A),...t};if(a.isElectron&&p().electronApi&&p().electronApi.setCookie)p().electronApi.setCookie(i);else if(n&&n.setCookie)await n.setCookie(i);else{const{name:e,value:t,...n}=i;B().remove(e),B().set(e,t,{...n,expires:n.expires?new Date(n.expires):void 0})}}async function g(e,t){await f(e,{name:"notion_cookie_consent",value:JSON.stringify(t)})}async function b(e,t){await f(e,{name:"growSumoPartnerKey",value:t})}async function h(e){return S(e,"growSumoPartnerKey")}async function v(e,t){await f(e,{name:"notion_s2s_tracking_params",value:`partnerKey=${t.partnerKey}&clickId=${t.clickId}`})}async function _(e){const t=await S(e,"notion_s2s_tracking_params");if(t){const e=new URLSearchParams(t),n=e.get("partnerKey"),o=e.get("clickId");if(n&&o)return{partnerKey:n,clickId:o}}return{}}function y(){M("notion_cookie_consent")}async function w(e){return await I(e,"notion_cookie_consent")}async function S(e,t){if(await x(t,e))return I(e,t)}async function k(e){if(!(await x("notion_browser_id",e)))return;const t=await S(e,"notion_browser_id");if(t)return t;const n=i().JW();return await f(e,{name:"notion_browser_id",value:n}),n}async function A(e){const t=await k(e);if(t)return t;const n=await S(e,"notion_experiment_device_id");if(n)return n;const o=i().JW();return await f(e,{name:"notion_experiment_device_id",value:o}),o}const C=new(n(493720).X);async function I(e,t){if(void 0===t)return void a().log({level:"error",from:"clientCookieHelpers",type:"safeCookieRead--name-undefined",data:{message:"Trying to read cookie with undefined name"}});if(!E(e))return;const{mobileNative:n}=e;if(n&&n.api.getCookie){const e=await n.api.getCookie(t);if(!("error"in e))return e.value;a().log({level:"error",from:"clientCookieHelpers",type:`safeCookieRead--mobileNative--${t}`,error:(0,d().convertErrorToLog)(e.error)})}return C.memo((()=>B().get()),[document.cookie])[t]}async function P(e){return await S(e,"notion_ghost_admin_user_id")}async function T(e){if(e.device.isBrowser&&!(0,m().ok)())return await S(e,"notion_public_domain_user_id")}function E(e){return!(!e.device.isBrowser&&!e.device.isElectron)||!!e.mobileNative&&((0,u().T)("supportsWebViewCookies")||Boolean(e.mobileNative.api.getCookie))}function R(e){const{device:t,mobileNative:n}=e;return!!t.isBrowser||(t.isElectron?Boolean(null===p().electronApi||void 0===p().electronApi?void 0:p().electronApi.setCookie):!!n&&((0,u().T)("supportsWebViewCookies")||Boolean(n.api.setCookie)))}function D(e){const{isMobileNative:t,isIOS:n,isAndroid:o,mobileAppVersion:r}=e.device;if(!t)return!0;if(!r)return!0;const a=l().parseMobileAppVersion(r,o);let i;if(o)i=l().parseVersion("0.6.302");else{if(!n)return!0;i=l().parseVersion("0.4.403")}return!a||!i||l().isLessThanVersion(a,i)}function M(e,t){const n=t||r().R2(o().A);B().remove(e,{domain:n})}async function q(e){const{isMobileNative:t,isAndroid:n}=e.device;if(!t||!n)for(const o of r().og()){if(s().Pb.includes(o))continue;const t=await S(e,o);t&&await f(e,{name:o,value:t})}}async function O(e){const t=await U(e),n=r().v(t);n.hasError&&a().log({level:"error",type:n.errorType,from:"clientCookieHelpers",data:{miscDataToConvertToString:t}})}function B(){return n(412215)}async function x(e,t){return r().Hn({name:e,cookieService:t.cookieService})}async function N(e,t){return r().Eo({trackingType:e,cookieService:t.cookieService})}async function L(e,t){return r().nQ({service:e,cookieService:t.cookieService})}async function U(e){return r().hl({cookieService:e.cookieService})}async function F(e){return r().q1({cookieService:e.cookieService})}async function V(e){return r().bD({cookieService:e.cookieService})}(0,n(604341).exposeDebugValue)("canReadCookie",E)},530199:(e,t,n)=>{n.d(t,{Gh:()=>b,IG:()=>c,P5:()=>d,V3:()=>l,ZI:()=>a,ah:()=>p,bd:()=>u,dG:()=>m,fT:()=>i,h4:()=>s,q8:()=>f,uu:()=>r,w_:()=>g,xu:()=>o});const o="deepLinkOpenNewTab",r="configureOpenInDesktopApp",a="p",i="pm",s="v",c="d",l="n",d="t",u="at",p="aq",m="defaultUserMessage",f="targetConfig",g="asc",b="transcribe"},532313:(e,t,n)=>{n.d(t,{L5:()=>r,v8:()=>a});var o=()=>n(763824);async function r(e){const{fn:t,handleError:n,retryAttemptsMS:r,retryAttemptRandomOffsetMS:a}=e;let i,s=e.initialInput;for(let l=0;l<=r.length;l+=1)try{return await t(s,l)}catch(c){const e=l>=r.length,t=n(c,e,l,s);if("return"===t.status)return t.result;if("throw"===t.status){i=t.error;break}if(e){i=c;break}const d=r[l]+Math.random()*a;await o().wR(d),"retry"===t.status&&void 0!==t.input&&(s=t.input)}throw i}function a(e){return r({fn:e,handleError:()=>({status:"retry"}),retryAttemptsMS:arguments.length>1&&void 0!==arguments[1]?arguments[1]:[1e3,2e3,5e3,1e4],retryAttemptRandomOffsetMS:arguments.length>2&&void 0!==arguments[2]?arguments[2]:200,initialInput:void 0})}},532659:(e,t,n)=>{n.d(t,{hS:()=>r,jY:()=>o});n(944114),n(898992),n(354520),n(581454),n(727413);const o={*fromValues(){for(var e=arguments.length,t=new Array(e),n=0;n<e;n++)t[n]=arguments[n];yield*t},collect(e){const t=[];for(const n of e)t.push(n);return t},*withIndex(e){let t=0;for(const n of e)yield[t,n],t++},*chunk(e,t){let n=[];for(const o of e)n.push(o),n.length>=t&&(yield n,n=[]);n.length>0&&(yield n)},*map(e,t){for(const n of e)yield t(n)},*flatten(e){for(const t of e)yield*t},*concat(){for(var e=arguments.length,t=new Array(e),n=0;n<e;n++)t[n]=arguments[n];for(const o of t)yield*o},*filter(e,t){for(const n of e){t(n)&&(yield n)}},*take(e,t){let n=0;for(const o of e)if(yield o,n+=1,n>=t)break},*until(e,t){for(const n of e)if(yield n,t(n))break},*cleanup(e,t){try{for(const t of e)yield t}finally{t()}},withSideEffect:(e,t)=>Object.assign({},e,{*[Symbol.iterator](){for(const n of e)t(n),yield n}}),*ensureReturned(e){try{for(const t of e)yield t}finally{var t;if(i(e))null===(t=e.return)||void 0===t||t.call(e)}},*withStats(e,t){let n=0,o=0;const r=e[Symbol.iterator]();let a=Date.now();try{for(;;){const e=r.next(),i=Date.now()-a;if(e.done){null==t||t({type:"done",length:n,totalTimeMs:o,result:e.value});break}const{value:s}=e;0===n&&(null==t||t({type:"initial",initialTimeMs:i})),n+=1,o+=i,yield{value:s,elapsedTimeMs:i},a=Date.now()}}finally{var i;null===(i=r.return)||void 0===i||i.call(r)}}},r={is:e=>a(e),async*fromValues(){for(var e=arguments.length,t=new Array(e),n=0;n<e;n++)t[n]=arguments[n];yield*t},async collect(e){const t=[];for await(const n of e)t.push(n);return t},async*withIndex(e){let t=0;for await(const n of e)yield[t,n],t++},async*chunk(e,t){let n=[];for await(const o of e)n.push(o),n.length>=t&&(yield n,n=[]);n.length>0&&(yield n)},async*map(e,t){for await(const n of e)yield t(n)},async*flatten(e){for await(const t of e)yield*t},async*concat(){for(var e=arguments.length,t=new Array(e),n=0;n<e;n++)t[n]=arguments[n];for(const o of t)yield*o},async*filter(e,t){for await(const n of e){t(n)&&(yield n)}},async*take(e,t){let n=0;for await(const o of e)if(yield o,n+=1,n>=t)break},async*until(e,t){for await(const n of e)if(yield n,t(n))break},async*cleanup(e,t){try{for await(const t of e)yield t}finally{await t()}},withCleanup:(e,t)=>Object.assign({},e,{async*[Symbol.asyncIterator](){try{for await(const t of e)yield t}finally{await t()}}}),withSideEffect:(e,t)=>Object.assign({},e,{async*[Symbol.asyncIterator](){for await(const n of e)await t(n),yield n}}),async*ensureReturned(e){try{for await(const t of e)yield t}finally{var t;if(i(e))await(null===(t=e.return)||void 0===t?void 0:t.call(e))}},async*withStats(e,t){let n=0,o=0;const r=e[Symbol.asyncIterator]();let a=Date.now();try{for(;;){const e=await r.next(),i=Date.now()-a;if(e.done){null==t||t({type:"done",length:n,totalTimeMs:o,result:e.value});break}const{value:s}=e;0===n&&(null==t||t({type:"initial",initialTimeMs:i})),n+=1,o+=i,yield{value:s,elapsedTimeMs:i},a=Date.now()}}finally{var i;await(null===(i=r.return)||void 0===i?void 0:i.call(r))}}};function a(e){return Symbol.asyncIterator in e}function i(e){return"next"in e}},534177:(e,t,n)=>{n.d(t,{$r:()=>b,EI:()=>o,GV:()=>h,Gv:()=>g,HB:()=>f,O:()=>i,O9:()=>d,Pe:()=>l,Vq:()=>u,WP:()=>a,Xk:()=>s,s8:()=>_,uv:()=>r,uy:()=>p,y$:()=>c,zR:()=>v});n(16280),n(898992),n(737550);function o(e){return e.length>0}const r=Object.keys,a=Object.entries;function i(e,t){return t in e}Object.assign;function s(e,t){return e.includes(t)}function c(e,t){return e.has(t)}function l(e){return null!==e}function d(e){return void 0!==e}function u(e){return null!=e}function p(e){return!u(e)}class m extends Error{}function f(e,t){if(t)throw new m(t());let n="(unknown)";try{try{n=JSON.stringify(e)??"undefined"}catch(o){n=String(e);const t=o instanceof Error?o.message:void 0;t&&(n+=` (Not serializable: ${t})`)}}catch{}throw new m(`Expected value to never occur: ${n}`)}function g(e){return"object"==typeof e&&null!==e}function b(e){return e.toString()}function h(e,t){return e}Symbol("deprecated api name"),Symbol("abstracted api name"),Symbol("info message"),Symbol("warning message");function v(e,t){return e.startsWith(t)}function _(e,t){const n={};for(const[o,r]of a(e))n[o]=t(r,o);return n}},543190:(e,t,n)=>{n.d(t,{loadAppPreboot:()=>o});async function o(){await async function(){const{default:e}=await Promise.resolve().then(n.t.bind(n,440961,19)),t=e.findDOMNode;e.findDOMNode=function(e){try{return t(e)}catch(n){return null}}}()}},547568:(e,t,n)=>{n.d(t,{$:()=>i});n(517642),n(658004),n(733853),n(845876),n(432475),n(515024),n(731698),n(898992),n(354520),n(581454);var o=()=>n(427704);const r={bot:!0,api:!0,login:!0,logincallback:!0,[o().JZ.appleAuthCallback.substring(1)]:!0,[o().JZ.googleAuthCallback.substring(1)]:!0,[o().JZ.microsoftAuthCallback.substring(1)]:!0,[o().JZ.samlAuthCallback.substring(1)]:!0,[o().JZ.passkeyAuthVerify.substring(1)]:!0,[o().JZ.passkeyAuthCallback.substring(1)]:!0,[o().JZ.passkeyRegistrationCallback.substring(1)]:!0,[o().JZ.passkeyRegistrationVerification.substring(1)]:!0,[o().JZ.trelloAuthCallback.substring(1)]:!0,[o().JZ.externalAuthCallback.substring(1)]:!0,[o().JZ.asanaAuthCallback.substring(1)]:!0,[o().JZ.slackAuthCallback.substring(1)]:!0,[o().JZ.externalIntegrationAuthCallback.substring(1)]:!0,logoutcallback:!0,desktop:!0,mobile:!0,android:!0,signup:!0,product:!0,logout:!0,"join-us":!0,pricing:!0,about:!0,hiring:!0,why:!0,investors:!0,server:!0,invoice:!0,invite:!0,native:!0,make:!0,onboarding:!0,unsubscribe:!0,space:!0,"tools-and-craft":!0,getStatus:!0,status:!0,jobs:!0,security:!0,join:!0,students:!0,educators:!0,work:!0,startups:!0,wiki:!0,wikis:!0,projects:!0,remote:!0,notes:!0,press:!0,blog:!0,"about-us":!0,guide:!0,help:!0,faq:!0,faqs:!0,media:!0,"media-kit":!0,install:!0,download:!0,template:!0,templates:!0,mac:!0,windows:!0,linux:!0,ios:!0,sso:!0,saml:!0,auth:!0,docs:!0,developers:!0,signed:!0,image:!0,images:!0,front:!0,tutorial:!0,customers:!0,setlocale:!0,new:!0,nativetab:!0,"notion-assets":!0},a=Object.values(o().JZ).map((e=>{if(!e.startsWith("/"))return;const t=e.split("/")[1];return t&&0!==t.length?t:void 0})).filter(n(534177).O9),i=new Set([...Object.keys(r),...a])},547726:(e,t,n)=>{n.d(t,{A:()=>r});class o extends(()=>n(757695))().Store{getInitialState(){return{pageId:void 0,lastViewTime:void 0,lastExitTime:void 0}}}const r=o},554493:(e,t,n)=>{n.d(t,{H:()=>r});const o="TempFeatureGateValueCache";function r(e){return`${o}-${e}`}},558842:(e,t,n)=>{n.d(t,{Du:()=>i,Gn:()=>m,Px:()=>s,lz:()=>p,xx:()=>l});n(898992),n(354520),n(803949);var o=n(296540),r=()=>n(496603),a=()=>n(534177);function i(e){return e||null}function s(){for(var e=arguments.length,t=new Array(e),n=0;n<e;n++)t[n]=arguments[n];const o=t.filter(a().O9);return 1===o.length?o[0]:e=>{o.forEach((t=>{!function(e,t){"function"==typeof e?e(t):e&&(e.current=t)}(t,e)}))}}function c(){return o.__SECRET_INTERNALS_DO_NOT_USE_OR_YOU_WILL_BE_FIRED}function l(){var e;return Boolean(null===(e=c())||void 0===e||null===(e=e.ReactCurrentOwner)||void 0===e?void 0:e.current)}function d(e){if(e)return"string"==typeof e?e:e.displayName||e.name}function u(e){if(e.name||e.fileName)return e.fileName?`<${e.name??"unknown component"}> (at ${e.fileName}:${e.lineNumber})`:`<${e.name??"unknown component"}>`}function p(){const e=function(){var e,t,n,o,r;const a=null===(e=c())||void 0===e||null===(e=e.ReactCurrentOwner)||void 0===e?void 0:e.current;if(!a)return;const i=a._debugOwner;return{name:d(a.type),fileName:null===(t=a._debugSource)||void 0===t?void 0:t.fileName,lineNumber:null===(n=a._debugSource)||void 0===n?void 0:n.lineNumber,owner:i&&{name:d(i.type),fileName:null===(o=i._debugSource)||void 0===o?void 0:o.fileName,lineNumber:null===(r=i._debugSource)||void 0===r?void 0:r.lineNumber}}}();if(!e)return;const t=u(e),n=e.owner&&u(e.owner);return n?`${t||"<unknown component>"} in ${n}`:t||void 0}function m(e){return(0,r().aD)([e],(e=>e||[]))}},567417:(e,t,n)=>{n.r(t),n.d(t,{default:()=>r});class o extends(()=>n(757695))().Store{getInitialState(){}}const r=new o},584262:(e,t,n)=>{n.d(t,{B5:()=>S,CW:()=>p,DU:()=>v,G5:()=>_,GD:()=>C,Q5:()=>y,b_:()=>P,ec:()=>f,hJ:()=>b,k3:()=>l,p3:()=>w,qK:()=>k,rh:()=>m,y8:()=>g,z2:()=>h});n(944114);var o=()=>n(496603),r=()=>n(763824),a=()=>n(498212),i=()=>n(939768),s=()=>n(206267),c=()=>n(249386);const l="pvs",d="source",u="copy_link",p="qid",m="nid",f="openPageUpdatesTab";let g=function(e){return e[e.Email=0]="Email",e[e.Search=1]="Search",e[e.Sidebar=2]="Sidebar",e[e.LastVisitedPage=3]="LastVisitedPage",e[e.CopyLink=4]="CopyLink",e[e.Notification=5]="Notification",e[e.Slack=6]="Slack",e[e.ButtonAutomation=7]="ButtonAutomation",e[e.GithubLinkback=8]="GithubLinkback",e[e.SidebarBookmark=9]="SidebarBookmark",e[e.SidebarWorkspace=10]="SidebarWorkspace",e[e.SidebarShared=11]="SidebarShared",e[e.SidebarPrivate=12]="SidebarPrivate",e[e.SidebarTeam=13]="SidebarTeam",e[e.SidebarTeamBrowser=14]="SidebarTeamBrowser",e[e.SidebarQuickAdd=15]="SidebarQuickAdd",e[e.SidebarHome=16]="SidebarHome",e[e.SidebarTrash=17]="SidebarTrash",e[e.Breadcrumb=18]="Breadcrumb",e[e.PushNotification=19]="PushNotification",e[e.GoogleEvent=20]="GoogleEvent",e[e.Export=21]="Export",e[e.Import=22]="Import",e[e.Expand=23]="Expand",e[e.MentionInPage=24]="MentionInPage",e[e.LinkInPage=25]="LinkInPage",e[e.SearchQuery=26]="SearchQuery",e[e.SearchRecents=27]="SearchRecents",e[e.SidebarRecents=28]="SidebarRecents",e[e.Widget=29]="Widget",e[e.BackForward=30]="BackForward",e[e.PeekOpen=31]="PeekOpen",e[e.PeekClose=32]="PeekClose",e[e.PeekScroll=33]="PeekScroll",e[e.Direct=34]="Direct",e[e.LeaveOrRemove=35]="LeaveOrRemove",e[e.Duplicate=36]="Duplicate",e[e.Onboarding=37]="Onboarding",e[e.AppRedirect=38]="AppRedirect",e[e.NativeShareLink=39]="NativeShareLink",e[e.EditPublicPage=40]="EditPublicPage",e[e.Activity=41]="Activity",e[e.AIChat=42]="AIChat",e[e.Create=43]="Create",e[e.ChangeCollectionView=44]="ChangeCollectionView",e[e.Move=45]="Move",e[e.SwitchSpace=46]="SwitchSpace",e[e.Login=47]="Login",e[e.Restore=48]="Restore",e[e.JoinTeam=49]="JoinTeam",e[e.PersonalHomeViewAll=50]="PersonalHomeViewAll",e[e.AISlackQna=51]="AISlackQna",e[e.AISlackAssistant=52]="AISlackAssistant",e[e.AIQna=53]="AIQna",e[e.PersonalHomePage=54]="PersonalHomePage",e[e.PersonalHomeTileRecents=55]="PersonalHomeTileRecents",e[e.PersonalHomeTileTrending=56]="PersonalHomeTileTrending",e[e.PersonalHomeTileShared=57]="PersonalHomeTileShared",e[e.PersonalHomeTileTasks=58]="PersonalHomeTileTasks",e[e.PersonalHomeTileTips=59]="PersonalHomeTileTips",e[e.PersonalHomePageTasks=60]="PersonalHomePageTasks",e[e.PersonalHomeUnknown=61]="PersonalHomeUnknown",e[e.PersonalHomeNotes=62]="PersonalHomeNotes",e[e.PersonalHomeTileSimilarUsers=63]="PersonalHomeTileSimilarUsers",e[e.PageError=64]="PageError",e[e.SidebarPublicPageTemplateIncludes=65]="SidebarPublicPageTemplateIncludes",e[e.DefaultHome=66]="DefaultHome",e[e.PersonalHomeTasksCreate=67]="PersonalHomeTasksCreate",e[e.LocationProperty=68]="LocationProperty",e[e.TypedDBConversionToast=69]="TypedDBConversionToast",e[e.PersonalHomeTileTasksCreate=70]="PersonalHomeTileTasksCreate",e[e.DuplicateTemplateSwitchSpace=71]="DuplicateTemplateSwitchSpace",e[e.PersonalHomeTileTemplates=72]="PersonalHomeTileTemplates",e[e.SiteSettings=73]="SiteSettings",e[e.SiteBanner=74]="SiteBanner",e[e.CommentPublicPage=75]="CommentPublicPage",e[e.MobileInbox=76]="MobileInbox",e[e.TurnOnSprints=77]="TurnOnSprints",e[e.PersonalHomeLink=78]="PersonalHomeLink",e[e.PersonalHomeEmailLink=79]="PersonalHomeEmailLink",e[e.PersonalHomeErrorRedirect=80]="PersonalHomeErrorRedirect",e[e.PersonalHomeTileMostVisited=81]="PersonalHomeTileMostVisited",e[e.PersonalHomeTileLastEdited=82]="PersonalHomeTileLastEdited",e[e.PersonalHomeTileFavorites=83]="PersonalHomeTileFavorites",e[e.PageLayoutEditor=84]="PageLayoutEditor",e[e.AIEphemeralView=85]="AIEphemeralView",e[e.PersonalHomeCalendarAttachment=86]="PersonalHomeCalendarAttachment",e[e.PersonalHomeTileRecentsQuickAdd=87]="PersonalHomeTileRecentsQuickAdd",e[e.AiWriterDeeplink=88]="AiWriterDeeplink",e[e.PersonalHomeTileCustomDb=89]="PersonalHomeTileCustomDb",e[e.PersonalHomePageCustomDb=90]="PersonalHomePageCustomDb",e[e.PersonalHomeCustomDbCreate=91]="PersonalHomeCustomDbCreate",e[e.PersonalHomeTileCustomDbCreate=92]="PersonalHomeTileCustomDbCreate",e[e.SearchTabs=93]="SearchTabs",e[e.SidebarPrivatePane=94]="SidebarPrivatePane",e[e.SidebarSharedPane=95]="SidebarSharedPane",e[e.SiteCustomHeader=96]="SiteCustomHeader",e[e.PageTableOfContents=97]="PageTableOfContents",e[e.SearchCustomAction=98]="SearchCustomAction",e[e.DeletedFromTrashPageActions=99]="DeletedFromTrashPageActions",e[e.ChartsLaunchModal=100]="ChartsLaunchModal",e[e.SitesTooltipsTourDeeplink=101]="SitesTooltipsTourDeeplink",e[e.SitesLaunchModal=102]="SitesLaunchModal",e[e.HomeDigestEmail=103]="HomeDigestEmail",e[e.FormViewResponses=104]="FormViewResponses",e[e.FormPublicPage=105]="FormPublicPage",e[e.FormInternalPage=106]="FormInternalPage",e[e.AIGoogleDriveQna=107]="AIGoogleDriveQna",e[e.FormsLaunchModal=108]="FormsLaunchModal",e[e.WorkflowTemplateOnboarding=109]="WorkflowTemplateOnboarding",e[e.ShareMenu=110]="ShareMenu",e[e.NewWorkspaceWorkflowTemplateOnboarding=111]="NewWorkspaceWorkflowTemplateOnboarding",e[e.FormEmbed=112]="FormEmbed",e[e.TeamHomePage=113]="TeamHomePage",e[e.DemoTour=114]="DemoTour",e[e.FormResponseSnapshot=115]="FormResponseSnapshot",e[e.Workflows=116]="Workflows",e[e.CreateFormDeepLink=117]="CreateFormDeepLink",e[e.AuditLog=118]="AuditLog",e[e.MiniQuickFind=119]="MiniQuickFind",e[e.ActivityDigestEmail=120]="ActivityDigestEmail",e[e.SavePageTranslationCopy=121]="SavePageTranslationCopy",e[e.VerifiedPagesSettings=122]="VerifiedPagesSettings",e[e.EmbedPublicPageViewOriginal=123]="EmbedPublicPageViewOriginal",e[e.BrowsePage=124]="BrowsePage",e[e.WorkflowTemplatesDeeplink=125]="WorkflowTemplatesDeeplink",e[e.PublicShareLink=126]="PublicShareLink",e[e.UnifiedFeed=127]="UnifiedFeed",e[e.TeamFeed=128]="TeamFeed",e[e.UnifiedFeedScroll=129]="UnifiedFeedScroll",e[e.TeamFeedScroll=130]="TeamFeedScroll",e[e.ChartsDrilldown=131]="ChartsDrilldown",e[e.UnifiedDigestEmail=132]="UnifiedDigestEmail",e[e.MarketingMagicBox=133]="MarketingMagicBox",e[e.SharingEmptySidebarSection=134]="SharingEmptySidebarSection",e[e.AIMicrosoftTeamsQna=135]="AIMicrosoftTeamsQna",e[e.Backlinks=136]="Backlinks",e[e.TranscriptionBlockPopup=137]="TranscriptionBlockPopup",e[e.WorkspaceSettingsPeopleSectionHeader=138]="WorkspaceSettingsPeopleSectionHeader",e[e.CollectionViewBlockWorkflowControl=139]="CollectionViewBlockWorkflowControl",e[e.LibraryPage=140]="LibraryPage",e[e.PublicSiteShareViaSocialButton=141]="PublicSiteShareViaSocialButton",e[e.PublicPageSettings=142]="PublicPageSettings",e[e.PublicSiteViewerShareViaSocialButton=143]="PublicSiteViewerShareViaSocialButton",e[e.ManageDataSourcesMenu=144]="ManageDataSourcesMenu",e[e.GenericFeed=145]="GenericFeed",e[e.BrowserNavigation=146]="BrowserNavigation",e[e.Initialization=147]="Initialization",e[e.TranscriptionRecordingStoppedNotification=148]="TranscriptionRecordingStoppedNotification",e[e.PublicSiteMobileShareViaSocialButton=149]="PublicSiteMobileShareViaSocialButton",e[e.WorkspaceDiscovery=150]="WorkspaceDiscovery",e[e.SearchOfflinePages=151]="SearchOfflinePages",e[e.ExternalPagesSidebar=152]="ExternalPagesSidebar",e[e.AiBlock=153]="AiBlock",e[e.MeetingsPage=154]="MeetingsPage",e[e.WorkspaceDiscoverySharedPage=155]="WorkspaceDiscoverySharedPage",e[e.TranscriptionBlockNotification=156]="TranscriptionBlockNotification",e[e.CommentAuthor=157]="CommentAuthor",e[e.PersonHoverCard=158]="PersonHoverCard",e[e.PersonProfileLink=159]="PersonProfileLink",e[e.PersonProfileRecentActivity=160]="PersonProfileRecentActivity",e[e.PersonProfileTopCollaborators=161]="PersonProfileTopCollaborators",e[e.NewAiChatInput=162]="NewAiChatInput",e[e.Calendar=163]="Calendar",e[e.BrowserExtensionNewTab=164]="BrowserExtensionNewTab",e[e.BrowserExtensionNewTabSettings=165]="BrowserExtensionNewTabSettings",e}({});function b(e){return{[g.Activity]:"activity",[g.AIChat]:"ai_chat",[g.AIMicrosoftTeamsQna]:"ai_microsoft_teams_qna",[g.AISlackAssistant]:"ai_slack_assistant",[g.AISlackQna]:"ai_slack_qna",[g.AIQna]:"ai_qna",[g.AIEphemeralView]:"ai_chat_ephemeral_view",[g.AuditLog]:"audit_log",[g.AppRedirect]:"app_redirect",[g.BackForward]:"back_forward",[g.Breadcrumb]:"breadcrumb",[g.BrowserNavigation]:"browser_navigation",[g.ButtonAutomation]:"button_automation",[g.ChangeCollectionView]:"change_collection_view",[g.CopyLink]:u,[g.Create]:"create",[g.DefaultHome]:"default_home",[g.Direct]:"direct",[g.Duplicate]:"duplicate",[g.EditPublicPage]:"edit_public_page",[g.Email]:"email",[g.Expand]:"expand",[g.Export]:"export",[g.GithubLinkback]:"github_linkback",[g.GoogleEvent]:"google_event",[g.PersonalHomeViewAll]:"personal_home_view_all",[g.Import]:"import",[g.Initialization]:"initialization",[g.JoinTeam]:"join_team",[g.LastVisitedPage]:"last_visited_page",[g.LeaveOrRemove]:"leave_or_remove",[g.LinkInPage]:"link_in_page",[g.Login]:"login",[g.MentionInPage]:"mention_in_page",[g.Move]:"move",[g.NativeShareLink]:"native_share_link",[g.Notification]:"notification",[g.Onboarding]:"onboarding",[g.PeekClose]:"peek_close",[g.PeekOpen]:"peek_open",[g.PeekScroll]:"peek_scroll",[g.PushNotification]:"push_notification",[g.Restore]:"restore",[g.Search]:"search",[g.SearchQuery]:"search_query",[g.SearchRecents]:"search_recents",[g.SearchCustomAction]:"search_custom_action",[g.Sidebar]:"sidebar",[g.SidebarBookmark]:"sidebar_bookmark",[g.SidebarHome]:"sidebar_home",[g.SidebarPrivate]:"sidebar_private",[g.SidebarQuickAdd]:"sidebar_quick_add",[g.SidebarRecents]:"sidebar_recents",[g.SidebarShared]:"sidebar_shared",[g.SidebarTeam]:"sidebar_team",[g.SidebarTeamBrowser]:"sidebar_team_browser",[g.SidebarTrash]:"sidebar_trash",[g.SidebarWorkspace]:"sidebar_workspace",[g.Slack]:"slack",[g.SidebarPublicPageTemplateIncludes]:"sidebar_public_page_template_includes",[g.SwitchSpace]:"switch_space",[g.Widget]:"widget",[g.PersonalHomeTileRecents]:"home_tile_recents",[g.PersonalHomeTileTrending]:"home_tile_trending",[g.PersonalHomeTileShared]:"home_tile_shared",[g.PersonalHomeTileTasks]:"home_tile_tasks",[g.PersonalHomeTileTips]:"home_tile_tips",[g.PersonalHomePage]:"home",[g.PersonalHomePageTasks]:"home_tasks",[g.PersonalHomeUnknown]:"home_unknown",[g.PersonalHomeTileSimilarUsers]:"home_tile_similar_users",[g.PersonalHomeNotes]:"home_tile_notes",[g.PersonalHomeTasksCreate]:"home_tasks_create",[g.PersonalHomeTileTasksCreate]:"home_tile_tasks_create",[g.PersonalHomeCustomDbCreate]:"home_custom_db_create",[g.PersonalHomeTileCustomDbCreate]:"home_tile_custom_db_create",[g.PageError]:"page_error",[g.LocationProperty]:"location_property",[g.TypedDBConversionToast]:"typed_db_conversion_toast",[g.DuplicateTemplateSwitchSpace]:"duplicate_template_switch_space",[g.PersonalHomeTileTemplates]:"home_templates",[g.SiteSettings]:"published_site_settings",[g.SiteBanner]:"published_site_banner",[g.CommentPublicPage]:"comment_public_page",[g.MobileInbox]:"mobile_inbox",[g.TurnOnSprints]:"turn_on_sprints",[g.PersonalHomeLink]:"home_link",[g.PersonalHomeEmailLink]:"home_email_link",[g.PersonalHomeErrorRedirect]:"home_error_redirect",[g.PersonalHomeTileMostVisited]:"home_tile_most_visited",[g.PersonalHomeTileLastEdited]:"home_tile_last_edited",[g.PersonalHomeTileFavorites]:"home_tile_favorites",[g.PageLayoutEditor]:"page_layout_editor",[g.PersonalHomeCalendarAttachment]:"home_calendar_attachment",[g.PersonalHomeTileRecentsQuickAdd]:"home_tile_recents_quick_add",[g.PersonalHomeTileCustomDb]:"home_tile_custom_db",[g.PersonalHomePageCustomDb]:"home_custom_db",[g.AiWriterDeeplink]:"ai_writer_deeplink",[g.SearchTabs]:"search_tabs",[g.SidebarPrivatePane]:"sidebar_private_pane",[g.SidebarSharedPane]:"sidebar_shared_pane",[g.SiteCustomHeader]:"site_custom_header",[g.PageTableOfContents]:"page_table_of_contents",[g.DeletedFromTrashPageActions]:"deleted_from_trash_page_actions",[g.ChartsLaunchModal]:"charts_launch_modal",[g.FormsLaunchModal]:"forms_launch_modal",[g.SitesTooltipsTourDeeplink]:"sites_tooltips_tour_deeplink",[g.SitesLaunchModal]:"sites_launch_modal",[g.HomeDigestEmail]:"home_digest_email",[g.FormViewResponses]:"form_view_responses",[g.FormPublicPage]:"form_public_page",[g.FormInternalPage]:"form_internal_page",[g.AIGoogleDriveQna]:"ai_qna_google_drive",[g.WorkflowTemplateOnboarding]:"workflow_template_onboarding",[g.ShareMenu]:"share_menu",[g.NewWorkspaceWorkflowTemplateOnboarding]:"new_workspace_workflow_template_onboarding",[g.FormEmbed]:"form_embed",[g.DemoTour]:"demo_tour",[g.TeamHomePage]:"team_home_page",[g.FormResponseSnapshot]:"form_response_snapshot",[g.Workflows]:"workflows",[g.CreateFormDeepLink]:"create_form_deep_link",[g.MiniQuickFind]:"mini_quick_find",[g.ActivityDigestEmail]:"activity_digest_email",[g.SavePageTranslationCopy]:"save_page_translation_copy",[g.VerifiedPagesSettings]:"verified_pages_settings",[g.EmbedPublicPageViewOriginal]:"embed_public_page_view_original",[g.BrowsePage]:"browse_page",[g.WorkflowTemplatesDeeplink]:"workflow_templates_deeplink",[g.MarketingMagicBox]:"marketing_magic_box",[g.PublicShareLink]:"public_share_link",[g.UnifiedFeed]:"unified_feed",[g.TeamFeed]:"team_feed",[g.UnifiedFeedScroll]:"unified_feed_scroll",[g.TeamFeedScroll]:"team_feed_scroll",[g.ChartsDrilldown]:"charts_drilldown",[g.UnifiedDigestEmail]:"unified_digest_email",[g.SharingEmptySidebarSection]:"sharing_empty_sidebar_section",[g.Backlinks]:"backlinks",[g.TranscriptionBlockPopup]:"transcription_block_popup",[g.WorkspaceSettingsPeopleSectionHeader]:"workspace_settings_people_section_header",[g.PublicSiteShareViaSocialButton]:"public_site_share_via_social_button",[g.PublicSiteViewerShareViaSocialButton]:"public_site_viewed_share_via_social_button",[g.CollectionViewBlockWorkflowControl]:"collection_view_block_workflow_control",[g.LibraryPage]:"library_page",[g.PublicPageSettings]:"public_page_settings",[g.ManageDataSourcesMenu]:"manage_data_sources_menu",[g.GenericFeed]:"generic_feed",[g.TranscriptionRecordingStoppedNotification]:"transcription_recording_stopped_notification",[g.PublicSiteMobileShareViaSocialButton]:"public_site_mobile_share_via_social_button",[g.WorkspaceDiscovery]:"workspace_discovery",[g.SearchOfflinePages]:"search_offline_pages",[g.ExternalPagesSidebar]:"external_pages_sidebar",[g.AiBlock]:"ai_block",[g.MeetingsPage]:"meetings_page",[g.WorkspaceDiscoverySharedPage]:"workspace_discovery_shared_page",[g.TranscriptionBlockNotification]:"transcription_block_notification",[g.CommentAuthor]:"comment_author",[g.PersonHoverCard]:"person_hover_card",[g.PersonProfileLink]:"person_profile_link",[g.PersonProfileRecentActivity]:"person_profile_recent_activity",[g.PersonProfileTopCollaborators]:"person_profile_top_collaborators",[g.NewAiChatInput]:"new_ai_chat_input",[g.Calendar]:"calendar",[g.BrowserExtensionNewTab]:"browser_extension_new_tab",[g.BrowserExtensionNewTabSettings]:"browser_extension_new_tab_settings"}[e]}function h(e){return{[g.Activity]:!1,[g.AIChat]:!1,[g.AIMicrosoftTeamsQna]:!1,[g.AISlackAssistant]:!0,[g.AISlackQna]:!0,[g.AIQna]:!1,[g.AIEphemeralView]:!1,[g.AppRedirect]:!1,[g.BackForward]:!1,[g.Breadcrumb]:!1,[g.BrowserNavigation]:!1,[g.ButtonAutomation]:!1,[g.ChangeCollectionView]:!1,[g.CopyLink]:!0,[g.Create]:!1,[g.DefaultHome]:!1,[g.Direct]:!0,[g.Duplicate]:!1,[g.EditPublicPage]:!1,[g.Email]:!0,[g.Expand]:!1,[g.Export]:!0,[g.GithubLinkback]:!0,[g.GoogleEvent]:!0,[g.Import]:!1,[g.Initialization]:!1,[g.JoinTeam]:!1,[g.LastVisitedPage]:!1,[g.LeaveOrRemove]:!1,[g.LinkInPage]:!1,[g.Login]:!1,[g.MentionInPage]:!1,[g.Move]:!1,[g.NativeShareLink]:!0,[g.Notification]:!1,[g.Onboarding]:!1,[g.PeekClose]:!1,[g.PeekOpen]:!1,[g.PeekScroll]:!1,[g.PersonalHomeViewAll]:!1,[g.PushNotification]:!0,[g.Restore]:!1,[g.Search]:!1,[g.SearchCustomAction]:!1,[g.SearchQuery]:!1,[g.SearchRecents]:!1,[g.Sidebar]:!1,[g.SidebarBookmark]:!1,[g.SidebarHome]:!1,[g.SidebarPrivate]:!1,[g.SidebarQuickAdd]:!1,[g.SidebarRecents]:!1,[g.SidebarShared]:!1,[g.SidebarTeam]:!1,[g.SidebarTeamBrowser]:!1,[g.SidebarTrash]:!1,[g.SidebarWorkspace]:!1,[g.SidebarPublicPageTemplateIncludes]:!1,[g.Slack]:!0,[g.SwitchSpace]:!1,[g.Widget]:!0,[g.PersonalHomePage]:!1,[g.PersonalHomeTasksCreate]:!1,[g.PersonalHomeTileTasksCreate]:!1,[g.PersonalHomeCustomDbCreate]:!1,[g.PersonalHomeTileCustomDbCreate]:!1,[g.PersonalHomeTileRecents]:!1,[g.PersonalHomeTileTrending]:!1,[g.PersonalHomeTileShared]:!1,[g.PersonalHomeTileTasks]:!1,[g.PersonalHomeTileTips]:!1,[g.PersonalHomePageTasks]:!1,[g.PersonalHomeUnknown]:!1,[g.PersonalHomeTileSimilarUsers]:!1,[g.PersonalHomeTileRecentsQuickAdd]:!1,[g.PersonalHomeNotes]:!1,[g.PageError]:!1,[g.LocationProperty]:!1,[g.TypedDBConversionToast]:!1,[g.DuplicateTemplateSwitchSpace]:!1,[g.PersonalHomeTileTemplates]:!1,[g.SiteSettings]:!1,[g.SiteBanner]:!1,[g.CommentPublicPage]:!1,[g.MobileInbox]:!1,[g.TurnOnSprints]:!1,[g.PersonalHomeLink]:!1,[g.PersonalHomeEmailLink]:!1,[g.PersonalHomeErrorRedirect]:!1,[g.PersonalHomeTileMostVisited]:!1,[g.PersonalHomeTileLastEdited]:!1,[g.PersonalHomeTileFavorites]:!1,[g.PersonalHomeTileCustomDb]:!1,[g.PersonalHomePageCustomDb]:!1,[g.PageLayoutEditor]:!1,[g.PersonalHomeCalendarAttachment]:!1,[g.AiWriterDeeplink]:!1,[g.SearchTabs]:!1,[g.SidebarPrivatePane]:!1,[g.SidebarSharedPane]:!1,[g.SiteCustomHeader]:!1,[g.PageTableOfContents]:!1,[g.DeletedFromTrashPageActions]:!1,[g.ChartsLaunchModal]:!1,[g.SitesTooltipsTourDeeplink]:!1,[g.SitesLaunchModal]:!1,[g.HomeDigestEmail]:!0,[g.FormViewResponses]:!1,[g.FormPublicPage]:!1,[g.FormInternalPage]:!1,[g.AIGoogleDriveQna]:!1,[g.FormsLaunchModal]:!1,[g.WorkflowTemplateOnboarding]:!1,[g.ShareMenu]:!1,[g.NewWorkspaceWorkflowTemplateOnboarding]:!1,[g.FormEmbed]:!1,[g.DemoTour]:!1,[g.TeamHomePage]:!1,[g.FormResponseSnapshot]:!1,[g.Workflows]:!1,[g.CreateFormDeepLink]:!1,[g.AuditLog]:!1,[g.MiniQuickFind]:!1,[g.ActivityDigestEmail]:!0,[g.SavePageTranslationCopy]:!1,[g.VerifiedPagesSettings]:!1,[g.EmbedPublicPageViewOriginal]:!0,[g.BrowsePage]:!1,[g.WorkflowTemplatesDeeplink]:!0,[g.MarketingMagicBox]:!0,[g.PublicShareLink]:!1,[g.UnifiedFeed]:!1,[g.TeamFeed]:!1,[g.UnifiedFeedScroll]:!1,[g.TeamFeedScroll]:!1,[g.ChartsDrilldown]:!1,[g.UnifiedDigestEmail]:!0,[g.SharingEmptySidebarSection]:!1,[g.Backlinks]:!1,[g.TranscriptionBlockPopup]:!1,[g.WorkspaceSettingsPeopleSectionHeader]:!1,[g.PublicSiteShareViaSocialButton]:!0,[g.PublicSiteViewerShareViaSocialButton]:!0,[g.CollectionViewBlockWorkflowControl]:!1,[g.LibraryPage]:!1,[g.PublicPageSettings]:!1,[g.ManageDataSourcesMenu]:!1,[g.GenericFeed]:!1,[g.TranscriptionRecordingStoppedNotification]:!1,[g.PublicSiteMobileShareViaSocialButton]:!0,[g.WorkspaceDiscovery]:!1,[g.SearchOfflinePages]:!1,[g.ExternalPagesSidebar]:!1,[g.AiBlock]:!1,[g.MeetingsPage]:!1,[g.WorkspaceDiscoverySharedPage]:!1,[g.TranscriptionBlockNotification]:!1,[g.CommentAuthor]:!1,[g.PersonHoverCard]:!1,[g.PersonProfileLink]:!1,[g.PersonProfileRecentActivity]:!1,[g.PersonProfileTopCollaborators]:!1,[g.NewAiChatInput]:!1,[g.Calendar]:!0,[g.BrowserExtensionNewTab]:!0,[g.BrowserExtensionNewTabSettings]:!0}[e]}function v(e){return e===g.CopyLink?[d,u]:[l,String(e)]}function _(e){const t=e[l];if(t)return t;const n=e[d];return String((o=n,{[u]:g.CopyLink}[o]??""));var o}function y(e){return e.includes(`${l}=`)||e.includes(`${d}=`)}function w(e,t,n,r){const[a,s]=v(n);return i().O$(e,{...r??{},[c().BM]:o().sb(t),[a]:s})}function S(e){const t=[],n=[["initial_","setOnce"],["","set"]];for(const[o,r]of n)for(const n of["notion_",""])for(const[a,i]of Object.entries(e))t.push({name:`${o}${n}${a}`,value:i,operation:r});return t}class k{constructor(e){this.invoked=void 0,this.completed=(0,r().yX)(),this.loader=e}async load(e){const{config:t,isEnabled:n,endpoint:o,deviceId:a,disableCookies:i}=e;if(this.invoked)return this.invoked.promise;this.invoked=(0,r().yX)();const s=t&&n?await this.loader({config:t,endpoint:o,deviceId:a,disableCookies:i}):void 0;return this.invoked.resolve(s),this.completed.resolve(s),s}async getInstance(){return this.completed.promise}}const A={anonymousDeviceId:I()};function C(){A.anonymousDeviceId=I()}function I(){return`a_${(0,a().ot)(s().JW())}`}function P(e){return e?(0,a().S2)(e):A.anonymousDeviceId}},590265:(e,t,n)=>{n.d(t,{LY:()=>f,Ln:()=>i,T6:()=>c,YD:()=>s,Zc:()=>u,Zi:()=>m,gQ:()=>g,gg:()=>b,xv:()=>l});n(814603),n(147566),n(198721);var o=()=>n(534177),r=()=>n(427704);const a=["creators","categories","templates","search","collections","profiles"],i=["life","school","work","personal"];function s(e,t){const n=p(e,t);return`${n.pathname}${n.search}`}function c(e){for(const t of a)if(e===t)return!0;return!1}function l(e){const{routePageType:t,slug:n}=e;switch(t){case"categories":if(n)return i.includes(n)?"templateTopicPage":"templateCategoryPage";break;case"collections":return n?"templateCollectionPage":"templateCollectionsPage";case"creators":case"profiles":return n?"templateCreatorPage":"templateCreatorsPage";case"templates":if(n)return"templateDetailPage";break;case"search":return"templateSearchPage";default:return(0,o().HB)(t)}}const d=["templates"];function u(e){return d.includes(e)}function p(e,t){let n=`${e.domainBaseUrl}/${t.name}`;t.pageType&&(n+=`/${t.pageType}`),t.slug&&(n+=`/${t.slug}`);const o=new URL(n);return t.query&&o.searchParams.append("query",encodeURIComponent(t.query)),t.preview&&o.searchParams.append("preview",encodeURIComponent("true")),t.tags&&o.searchParams.append("tags",t.tags),t.groupedTags&&o.searchParams.append("groupedTags",t.groupedTags),t.showMarketplaceCheckoutModal&&o.searchParams.append("checkout","true"),t.showBuyerProfileModal&&o.searchParams.append("bp","true"),t.orderBy&&o.searchParams.append("orderBy",encodeURIComponent(t.orderBy)),t.paid&&o.searchParams.append("paid",encodeURIComponent(t.paid)),t.qualification&&o.searchParams.append("qualification",encodeURIComponent(t.qualification)),t.locales&&o.searchParams.append("locales",encodeURIComponent(t.locales)),t.crumbs&&o.searchParams.append("cr",encodeURIComponent(t.crumbs)),o}function m(e,t){return p(e,t).toString()}function f(e){const{docMatch:t,routeName:n,parsed:o}=e,{pageType:a,slug:i}=t,s=o.query.preview?decodeURIComponent(o.query.preview):void 0,c=o.query.bp?decodeURIComponent(o.query.bp):void 0;return[{name:n,pageType:a,slug:i,tags:o.query.tags?decodeURIComponent(o.query.tags):void 0,groupedTags:o.query.groupedTags?decodeURIComponent(o.query.groupedTags):void 0,orderBy:o.query.orderBy?decodeURIComponent(o.query.orderBy):void 0,madeBy:o.query.madeBy?decodeURIComponent(o.query.madeBy):void 0,paid:o.query.paid?decodeURIComponent(o.query.paid):void 0,crumbs:o.query.cr?decodeURIComponent(o.query.cr):void 0,preview:"true"===s,showBuyerProfileModal:"true"===c,locales:o.query.locales?decodeURIComponent(o.query.locales):void 0,showMarketplaceCheckoutModal:"true"===(o.query.checkout?decodeURIComponent(o.query.checkout):void 0),coupon:o.query.coupon?decodeURIComponent(o.query.coupon):void 0,from:o.query.from?decodeURIComponent(o.query.from):void 0,showReviewModal:"true"===(o.query.review?decodeURIComponent(o.query.review):void 0),templateGalleryItem:o.query.tg?decodeURIComponent(o.query.tg):void 0},"gallery"===n?r().JZ.gallery:r().JZ.marketplace]}function g(e){const{docMatch:t,routeName:n,parsed:o}=e,a=o.query.bp?decodeURIComponent(o.query.bp):void 0,{pageType:i}=t;return[{name:n,pageType:i,query:o.query.query,orderBy:o.query.orderBy,qualification:o.query.qualification,showBuyerProfileModal:"true"===a,showMarketplaceCheckoutModal:"true"===(o.query.checkout?decodeURIComponent(o.query.checkout):void 0),showReviewModal:"true"===(o.query.review?decodeURIComponent(o.query.review):void 0),templateGalleryItem:o.query.tg?decodeURIComponent(o.query.tg):void 0},"gallery"===n?r().JZ.gallery:r().JZ.marketplace]}function b(e){const{routeName:t,parsed:n}=e;return[{name:t,showBuyerProfileModal:"true"===(n.query.bp?decodeURIComponent(n.query.bp):void 0),showMarketplaceCheckoutModal:"true"===(n.query.checkout?decodeURIComponent(n.query.checkout):void 0),showReviewModal:"true"===(n.query.review?decodeURIComponent(n.query.review):void 0),templateGalleryItem:n.query.tg?decodeURIComponent(n.query.tg):void 0},"gallery"===t?r().JZ.gallery:r().JZ.marketplace]}},591779:(e,t,n)=>{n.d(t,{$x:()=>i,MR:()=>r,ZC:()=>a,_$:()=>s,b_:()=>d,lW:()=>l,wb:()=>u});var o=n(296540);function r(e,t){if(!e||!t)return!1;if(e.length!==t.length)return!1;if(e===t)return!0;for(let n=0;n<e.length;n++)if(e[n]!==t[n])return!1;return!0}function a(e,t){const n=(0,o.useRef)(t);return(0,o.useEffect)((()=>{n.current=e}),[e]),n.current}function i(e,t){const n=!t(a(e),e);return(0,o.useDebugValue)(n),n}function s(e,t){const n=!t(a(e,e),e);return(0,o.useDebugValue)(n),n}function c(e,t){const n=(0,o.useRef)(0);return i(e,t)&&n.current++,(0,o.useDebugValue)(n.current),n.current}function l(e,t,n){return(0,o.useDebugValue)(void 0===t?"disabled":`${t}ms`),d(e,e,t,n)}function d(e,t,n,r){const[a,i]=(0,o.useState)(t),s=c(e,r);return(0,o.useDebugValue)(void 0===n?"disabled":`${n}ms`),(0,o.useEffect)((()=>{if(void 0===n)return;const t=window.setTimeout((()=>{i(e)}),n);return()=>{window.clearTimeout(t)}}),[n,s]),void 0===n?e:a}function u(e,t,n){const[r,a]=(0,o.useState)(e),i=(0,o.useRef)(),s=(0,o.useRef)(null),l=(0,o.useRef)(0),d=c(e,n);return(0,o.useDebugValue)(void 0===t?"disabled":`${t}ms`),(0,o.useEffect)((()=>{if(i.current||void 0===t)s.current=e,l.current=!0;else{a(e);const n=()=>{l.current?(l.current=!1,a(s.current),i.current=window.setTimeout(n,t)):i.current=void 0};i.current=window.setTimeout(n,t)}}),[t,d]),(0,o.useEffect)((()=>()=>{i.current&&clearTimeout(i.current)}),[]),void 0===t?e:r}},592328:(e,t,n)=>{n.d(t,{A:()=>o});n(517642),n(658004),n(733853),n(845876),n(432475),n(515024),n(731698);const o=class{constructor(){this.listeners=new Set}addListener(e){this.listeners.add(e)}addOnceListener(e){var t=this;const n=function(){e(...arguments),t.removeListener(n)};this.addListener(n)}removeListener(e){this.listeners.delete(e)}emit(){for(const e of this.listeners)e(...arguments)}listenerCount(){return this.listeners.size}}},603250:(e,t,n)=>{n.d(t,{e:()=>r});n(898992),n(737550);const o=["4r5e","5h1t","5hit","a55","ar5e","arrse","arse","ass-fucker","assfucker","assfukka","asshole","assholes","asswhole","a_s_s","b!tch","b00bs","b17ch","b1tch","ballbag","ballsack","beastial","beastiality","bellend","bestial","bestiality","bi+ch","biatch","bitch","bitcher","bitchers","bitches","bitchin","bitching","blow job","blowjob","blowjobs","boiolas","bollock","bollok","boner","boob","boobs","booobs","boooobs","booooobs","booooooobs","buceta","bugger","bunny fucker","butthole","buttmunch","buttplug","c0ck","c0cksucker","carpet muncher","cawk","chink","cipa","cl1t","clit","clitoris","clits","cnut","cock","cock-sucker","cockface","cockhead","cockmunch","cockmuncher","cocks","cocksuck ","cocksucked ","cocksucker","cocksucking","cocksucks ","cocksuka","cocksukka","cokmuncher","coksucka","coon","cummer","cumming","cums","cumshot","cunilingus","cunillingus","cunnilingus","cunt","cuntlick ","cuntlicker ","cuntlicking ","cunts","cyalis","cyberfuc","cyberfuck ","cyberfucked ","cyberfucker","cyberfuckers","cyberfucking ","d1ck","dickhead","dildo","dildos","dirsa","dog-fucker","donkeyribber","doosh","duche","dyke","ejaculate","ejaculated","ejaculates ","ejaculating ","ejaculatings","ejaculation","ejakulate","f u c k","f u c k e r","f4nny","fag","fagging","faggitt","faggot","faggs","fagot","fagots","fags","fannyflaps","fannyfucker","fanyy","fatass","fcuk","fcuker","fcuking","feck","fecker","felching","fellate","fellatio","fingerfuck ","fingerfucked ","fingerfucker ","fingerfuckers","fingerfucking ","fingerfucks ","fistfuck","fistfucked ","fistfucker ","fistfuckers ","fistfucking ","fistfuckings ","fistfucks ","fook","fooker","fuck","fucka","fucked","fucker","fuckers","fuckhead","fuckheads","fuckin","fucking","fuckings","fuckingshitmotherfucker","fuckme ","fucks","fuckwhit","fuckwit","fudge packer","fudgepacker","fuk","fuker","fukker","fukkin","fuks","fukwhit","fukwit","fux","fux0r","f_u_c_k","gangbang","gangbanged ","gangbangs ","gaylord","gaysex","goatse","god-dam","god-damned","goddamn","goddamned","hardcoresex ","heshe","hoar","hoare","hoer","homo","horniest","horny","hotsex","jack-off ","jackoff","jerk-off ","jism","jiz ","jizm ","jizz","knobead","knobed","knobend","knobhead","knobjocky","knobjokey","kock","kummer","kumming","kunilingus","l3i+ch","l3itch","labia","lmfao","m0f0","m0fo","m45terbate","ma5terb8","ma5terbate","masochist","master-bate","masterb8","masterbat*","masterbat3","masterbate","masterbation","masterbations","masturbate","mo-fo","mof0","mofo","mothafuck","mothafucka","mothafuckas","mothafuckaz","mothafucked ","mothafucker","mothafuckers","mothafuckin","mothafucking ","mothafuckings","mothafucks","mother fucker","motherfuck","motherfucked","motherfucker","motherfuckers","motherfuckin","motherfucking","motherfuckings","motherfuckka","motherfucks","muff","mutha","muthafecker","muthafuckker","muther","mutherfucker","n1gga","n1gger","nazi","nigg3r","nigg4h","nigga","niggah","niggas","niggaz","nigger","niggers ","nob jokey","nobhead","nobjocky","nobjokey","numbnuts","nutsack","orgasim ","orgasims ","orgasm","orgasms ","p0rn","penisfucker","phonesex","phuck","phuked","phuking","phukked","phukking","phuks","phuq","pigfucker","pimpis","pissed","pisser","pissers","pisses ","pissflaps","pissin ","pissing","pissoff ","porn","porno","pornography","pornos","pron","pube","pusse","pussi","pussies","pussy","pussys ","retard","rimjaw","rimming","s hit","s.o.b.","sadist","schlong","scroat","scrote","sh!+","sh!t","sh1t","shagger","shaggin","shagging","shemale","shi+","shit","shitdick","shite","shited","shitey","shitfuck","shitfull","shithead","shiting","shitings","shits","shitted","shitter","shitters ","shitting","shittings","shitty ","skank","slut","sluts","smegma","smut","son-of-a-bitch","s_h_i_t","t1tt1e5","t1tties","testical","titfuck","tittie5","tittiefucker","titties","tittyfuck","tittywank","titwank","tosser","tw4t","twat","twathead","twatty","twunt","twunter","v14gra","v1gra","viagra","w00se","wank","wanker","wanky","whoar","whore","willies","xrated","xxx"].map((e=>e.toLowerCase()));function r(e){const t=e.toLowerCase();return o.some((e=>t.includes(e)))}},604341:(e,t,n)=>{n.r(t),n.d(t,{exposeDebugGetter:()=>i,exposeDebugInstance:()=>s,exposeDebugValue:()=>a,isConsoleEnabled:()=>u,onConsoleFirstEnabled:()=>l,unregisterConsoleFirstEnabledCallback:()=>d});const o={};async function r(){localStorage.setItem("__console","true"),o.isEnabled=!0;for(const[e,t]of c)await t();c.clear()}function a(e,t){return o.isEnabled?(o[e]=t,t):t}function i(e,t){o.isEnabled&&Object.defineProperty(o,e,{get:t})}function s(e,t){0}"undefined"!=typeof window?(o.isEnabled=function(){var e;if(navigator.webdriver)return!0;if(null!==localStorage.getItem("__console"))return"true"===localStorage.getItem("__console");0;if("CONFIG"in window&&"object"==typeof window.CONFIG&&null!==(e=window.CONFIG)&&void 0!==e&&e.isAdminMode)return!0;return!1}(),window.__console=o,o.enable=async()=>{console.log("Loading __console..."),await r(),console.log("__console enabled. You may need to refresh the page to access some __console functionality.")},o.enableAndReload=()=>{r(),window.location.reload(),console.log("Reloading...")},o.disable=()=>{localStorage.setItem("__console","false"),o.isEnabled=!1,console.log("__console disabled, please refresh the page for the setting to take effect.")}):void 0!==n.g&&(n.g.__console=o,o.isEnabled=!0);const c=new Map;function l(e,t){u()?t():c.set(e,t)}function d(e){c.delete(e)}function u(){return Boolean(o.isEnabled)}a("debugCaptureError",(async function(e){try{return{value:await e()}}catch(t){return{error:t}}}))},606932:(e,t,n)=>{n.d(t,{A:()=>i});var o=()=>n(646447);function r(){var e;const t=null===(e=n(765957).default.state.mainEditorCurrentBlockStore)||void 0===e?void 0:e.getType();if(t)return"collection_view_page"===t||"collection_view"===t?"collection":"page"}function a(e){const t=null===(n=o().H6.CollectionViewBlock.getLoadingMetrics())||void 0===n?void 0:n.resolvedAt;var n;return!!t&&(e?e<t:void 0)}const i=new class{constructor(){this._timings=void 0,this._navigatingFrom=void 0,this._switchingView=void 0,this._opfsMetrics=void 0,this._opfsMetadata=void 0,this._isDeduplicatingLcpc=void 0}popTimings(){const e=this._timings;this._timings=void 0;const t=this._isDeduplicatingLcpc;return this._isDeduplicatingLcpc=void 0,{timings:e,isDeduplicatingLcpc:t}}popOPFSData(){const e=this._opfsMetrics,t=this._opfsMetadata;return this._opfsMetrics=void 0,this._opfsMetadata=void 0,{metrics:e,metadata:t}}markNavigationStartTime(e,t){this._timings={navigationStart:performance.now()},this._navigatingFrom=void 0,this._switchingView=void 0,this._opfsMetrics=void 0,this._opfsMetadata=void 0,"page"===e.name&&"page"===t.name&&e.blockId&&e.blockId&&(this._navigatingFrom=r(),this._switchingView=e.blockId===t.blockId&&e.collectionViewId!==t.collectionViewId)}getNavigationStartTime(){var e;return null===(e=this._timings)||void 0===e?void 0:e.navigationStart}markRequestFirstChunkStart(e){this._timings&&(this._timings={...this._timings,requestFirstChunkStart:performance.now()}),this._isDeduplicatingLcpc=e.isDeduplicatingLcpc}markRequestFirstChunkEnd(e){const{chunkSource:t,chunkLocalSource:n}=e;this._timings&&(this._timings={...this._timings,requestFirstChunkEnd:performance.now(),requestFirstChunkSource:t,requestFirstChunkLocalSource:n})}markPageViewBlockRender(e){this._timings&&(this._timings={...this._timings,pageViewBlockRender:e})}updateOPFSMetrics(e){this._opfsMetrics={...this._opfsMetrics,...e}}updateOPFSMetadata(e){this._opfsMetadata={...this._opfsMetadata,...e}}getDatabaseNavigationMetrics(e){return{cvb_bundle_loaded:a(e),navigating_from:this._navigatingFrom,navigating_to:r(),switching_view:this._switchingView}}}},615234:(e,t,n)=>{n.d(t,{Gq:()=>c,TD:()=>o,WY:()=>g,Xb:()=>r,_h:()=>m,i5:()=>p,lG:()=>s,m1:()=>d,nD:()=>i,pT:()=>a,uu:()=>u,wK:()=>l,xq:()=>f});const o=1e3,r=60*o,a=60*r,i=24*a,s=7*i,c=365*i,l=Number(1),d=60*l,u=60*d,p=24*u,m=7*p,f=365*p;function g(e){return Math.round(Math.floor(e/r)*r)}},617871:(e,t,n)=>{n.d(t,{$1:()=>N,AC:()=>R,Aj:()=>j,CX:()=>B,E8:()=>S,EY:()=>M,F6:()=>p,Fo:()=>v,Fv:()=>k,Hd:()=>l,Iy:()=>y,KB:()=>U,LF:()=>C,MF:()=>V,MN:()=>b,Nu:()=>O,XF:()=>A,XO:()=>q,ZY:()=>T,a1:()=>D,bh:()=>a,d0:()=>m,e1:()=>d,eL:()=>L,eg:()=>w,io:()=>r,k0:()=>c,me:()=>u,mi:()=>F,mj:()=>x,mn:()=>_,ob:()=>i,tU:()=>E,wK:()=>h,zG:()=>s});var o=()=>n(45331);const r="c0d82879-3eea-4c21-b0d1-42a775022c4b",a="cdc46cd9-f0e9-48fd-b3aa-18481098e29e",i="b759b994-5c32-4268-bbb0-41f435abb8d9",s="7f5d87f7-be5f-45ee-83d3-b9af153f0ee0",c="e0dbc237-dcea-4ed7-8de0-bfc1ea6ac768",l="2e19d8ee-fc61-48c1-be14-07a3aff43542",d="3a6a5bc3-6b3a-467e-9fc5-de4b6024a0e1",u="15d02cbd-b82a-4ccd-928e-f2ff0806f9ba",p="ccb795df-ffbb-414b-9a98-15a5cfb3297c",m="249a0797-abfd-4ee9-b8c3-30c32489eb2b",f="042f18e5-d630-4b45-964d-583e1d62b602",g="59487270-7a29-4dff-b0a1-3fc4b0f08fa8",b="7df961b7-66d9-4fb1-a963-ebcd171d6148",h="45c081d8-e28b-43d0-87be-c373ca160336",v="e719abb7-effd-42f1-aa3f-0f449710fdc0",_="8e608b04-c0f8-4a71-8a38-b33ea0464d4d",y="2ee61629-4bb5-451e-8602-ff69872ed50e",w="9a9a6abb-ac0a-4cc8-9041-7626f934465c",S="e4c2ee0b-cd35-4e55-9e27-87690ae043f8",k="e8db4f1e-32ef-4588-8b7f-dcbf2e05e21f",A="adc9a52f-3aab-444b-83b7-e73200ee0279",C="9de4e48a-10ee-4f74-97d4-5cf3ad7f72fc",I="13b0eb06-40ac-446a-8c93-a061e8f64070",P="00000000-0000-4000-8000-000000000005",T="00000000-0000-4000-8000-000000000003",E=[l,d,c,_],R=[w];function D(e){return[P,f].includes(e)}function M(e){return D(e)||e===_}function q(e){return[l,d].includes(e)}const O={[a]:"github",[i]:"github",[u]:"jira",[s]:"slack","70570080-b12c-4484-a5ef-7c917ea6af1e":"zoom",[b]:"asana",[h]:"trello",[d]:"figma","4d0f0ca4-c5f2-42c8-b6a6-795a590a3e57":"dropbox","ef9a1f68-a912-4bc0-9523-1688a2a52645":"onedrive",[f]:"google_calendar","09c1d111-fdc8-4ab8-8351-8b4b4edf2976":"hubspot",[v]:"gitlab",[m]:"box","************************************":"adobe_xd","a5cac57e-e610-4b62-b515-9e2757c0a945":"clickup",[l]:"google_drive","48fba5a0-411d-43eb-aad4-1daff8c3c64d":"pagerduty",[c]:"zendesk",[r]:"cron",[_]:"salesforce",[y]:"salesforce_user",[g]:"quip",[w]:"gmail",[S]:"google_contacts",[k]:"jira_data_center",[p]:"jira",[A]:"notion_automation_public_api",[C]:"microsoft_contacts",[I]:"discord"};function B(e){const{integrationId:t}=e;return t in O||t===T||t===P||(0,o().Y)(t)}function x(e){return e===i||e===_}function N(e){return function(e){return[T,a].includes(e)}(e)?"github":D(e)?"google_calendar":O[e]}function L(e){return e===_?y:void 0}function U(e){return e===_}function F(e){return e===_?j:"Integration"}function V(e){return!!B({integrationId:e.id})&&("published"!==e.status&&[_,y].includes(e.id))}const j="Salesforce"},632055:e=>{e.exports={}},638681:(e,t,n)=>{n.r(t),n.d(t,{any:()=>ne,array:()=>q,bigInt:()=>T,binary:()=>_,boolean:()=>A,buffer:()=>te,caseInsensitiveLiteral:()=>S,contains:()=>X,createType:()=>i().rv,dateString:()=>v,extendType:()=>i().xs,failIf:()=>i().cS,flexibleDashesUuid:()=>g,gt:()=>J,gte:()=>j,instanceOf:()=>ce,integer:()=>V,intersection:()=>L,isNull:()=>R,isUndefined:()=>M,keyValidator:()=>Y,lazy:()=>re,literal:()=>w,literals:()=>ae,lt:()=>W,lte:()=>H,matchesRegExp:()=>y,maxLength:()=>$,minLength:()=>z,nonEmpty:()=>U,nullable:()=>se,number:()=>I,object:()=>x,percentage:()=>K,record:()=>B,shortID:()=>G,string:()=>l,tuple:()=>O,undefinable:()=>ie,union:()=>N,unknown:()=>oe,uuid:()=>u,uuidWithoutDashes:()=>m,without:()=>Q});n(944114),n(581454);var o=()=>n(244641),r=()=>n(498212),a=()=>n(534177),i=()=>n(671593);function s(e){return()=>e}const c=(0,i().rv)((function(e){return i().cS.not`defined`(void 0===e)||i().cS.not`a string`("string"!=typeof e)}),s("string"));function l(){return c}const d=(0,i().xs)(c,(function(e){return i().cS.not`a valid uuid`(!(0,r().uj)(e))}),s("uuid"));function u(){return d}const p=(0,i().xs)(c,(function(e){return i().cS.not`a valid uuid`(!(0,r().c_)(e))}),s("UUIDWithoutDashes"));function m(){return p}const f=(0,i().xs)(c,(function(e){return i().cS.not`a valid uuid`(!(0,r().c_)((0,r().Xw)(e)))}),s("FlexibleDashesUuid"));function g(){return f}function b(e){const t=function(e){if(e.match(/^\d{1,2}:\d{2}/))return;const t=o().c9.fromISO(e);if(t.isValid)return t;const n=e.replace(/ /,"T");if(n!==e){const e=o().c9.fromISO(n);if(e.isValid)return e}}(e);return Boolean(t)}const h=(0,i().xs)(c,(function(e){return i().cS.not`a valid ISO 8601 date string`(!b(e))}),s("ISO8601DateString"));function v(){return h}function _(){return c}function y(e){return(0,i().xs)(c,(function(t){return i().cS.not`matches regexp ${String(e)}`(!t.match(e))}))}function w(e){return(0,i().rv)((function(t){return i().cS.not`defined`(void 0===t)||i().cS.not`${e}`(t!==e)}),JSON.stringify(e))}function S(e){return(0,i().xs)(c,(function(t){return i().cS.not`defined`(void 0===t)||i().cS.not`${e}`(t.toUpperCase()!==e.toUpperCase())}),(()=>JSON.stringify(e)))}const k=(0,i().rv)((function(e){return i().cS.not`defined`(void 0===e)||i().cS.not`a boolean`("boolean"!=typeof e)}),"boolean");function A(){return k}const C=(0,i().rv)((function(e){return i().cS.not`defined`(void 0===e)||i().cS.not`a number`("number"!=typeof e)}),"number");function I(){return C}const P=(0,i().rv)((function(e){return i().cS.not`defined`(void 0===e)||i().cS.not`a bigint`("bigint"!=typeof e)}),"bigint");function T(){return P}const E=(0,i().rv)((function(e){return i().cS.not`defined`(void 0===e)||i().cS.not`${null}`(null!==e)}),"null");function R(){return E}const D=(0,i().rv)((function(e){return i().cS.not`${void 0}`(void 0!==e)}),"undefined");function M(){return D}function q(e){return(0,i().rv)((function(t){return i().cS.not`defined`(void 0===t)||i().cS.not`an array`(!Array.isArray(t))||i().cS.anyFails(Array.from(t.keys()),(n=>i().cS.keyIsNot(e,n,t)))}),(()=>`Array<${e}>`))}function O(e){return(0,i().rv)((function(t){return i().cS.not`defined`(void 0===t)||i().cS.not`an array`(!Array.isArray(t))||i().cS.anyFails(e,((e,n)=>i().cS.keyIsNot(e,n,t)))||i().cS.keyIsNot(w(e.length),"length",t)}),(()=>`[${e.map(String).join(", ")}]`))}function B(e,t){return(0,i().rv)((function(e){return i().cS.not`defined`(void 0===e)||i().cS.not`an object`("object"!=typeof e||null===e||Array.isArray(e))||i().cS.anyFails((0,a().uv)(e),(n=>i().cS.keyIsNot(t,n,e)))}),(()=>`Record<${e}, ${t}>`))}function x(e){let{required:t,optional:n,exact:o}=e;return(0,i().rv)((function(e){const r=i().cS.not`defined`(void 0===e)||i().cS.not`an object`("object"!=typeof e||null===e||Array.isArray(e))||i().cS.anyFails((0,a().uv)(t),(n=>i().cS.keyIsNot(t[n],n,e)))||i().cS.anyFails((0,a().uv)(n),(t=>i().cS.keyIsNot(N([n[t],D]),t,e)));if(!o||r)return r;const s=[];for(const o of(0,a().uv)(e)){o in t||o in n||s.push({claim:"not present",path:[o]})}return s.length>0?s:null}),(()=>`{ ${[...(0,a().WP)(t).map((e=>{let[t,n]=e;return`${"string"==typeof t?t:String(t)}: ${n}`})),...(0,a().WP)(n).map((e=>{let[t,n]=e;return`${"string"==typeof t?t:String(t)}?: ${n}`}))].join("; ")} }`))}function N(e){return(0,i().rv)((function(t){return i().cS.noneOf(e,t)}),(()=>e.map(String).join(" | ")))}function L(e){return(0,i().rv)((function(t){return i().cS.anyFails(e,(e=>i().cS.noneOf([e],t)))}),(()=>e.map(String).join("&")))}function U(e){return(0,i().xs)(e,(function(e){return i().cS.not`populated`(0===Object.keys(e).length)}))}const F=(0,i().xs)(C,(function(e){return i().cS.not`an integer`(!Number.isInteger(e))}));function V(){return F}function j(e,t){return(0,i().xs)(e,(function(e){return i().cS.not`≥ ${t}`(e<t)}))}function J(e,t){return(0,i().xs)(e,(function(e){return i().cS.not`> ${t}`(e<=t)}))}function H(e,t){return(0,i().xs)(e,(function(e){return i().cS.not`≤ ${t}`(e>t)}))}function W(e,t){return(0,i().xs)(e,(function(e){return i().cS.not`< ${t}`(e>=t)}))}function $(e,t){return(0,i().xs)(e,(function(e){return i().cS.keyIsNot(H(I(),t),"length",e)}))}function z(e,t){return(0,i().xs)(e,(function(e){return i().cS.keyIsNot(j(I(),t),"length",e)}))}const Z=(0,i().xs)(U(c),(function(e){return i().cS.anyFails([...[...e].keys()],(t=>i().cS.keyIsNot(ae(...n(183558).Mb),t,e)))}),s("shortID"));function G(){return Z}function K(){return H(j(I(),0),100)}function Q(e,t){return(0,i().xs)(e,(function(e){return i().cS.not`without ${t}`(e.includes(t))}))}function X(e,t){return(0,i().xs)(e,(function(e){return i().cS.not`contains ${t}`(!e.includes(t))}))}const Y=Q(Q(U(l()),"{"),"}"),ee=(0,i().rv)((function(e){return i().cS.not`a buffer`(!Buffer.isBuffer(e))}),s("Buffer"));function te(){return ee}function ne(){return(0,i().rv)((function(e){return null}),"any")}function oe(){return(0,i().rv)((function(e){return null}),"unknown")}function re(e){let t;return(0,i().rv)((function(n){return t||(t=e()),i().cS.noneOf([t],n)}),(()=>(t||(t=e()),t.toString())))}function ae(){for(var e=arguments.length,t=new Array(e),n=0;n<e;n++)t[n]=arguments[n];return N(t.map(w))}function ie(e){return N([e,M()])}function se(e){return N([e,R()])}function ce(e){const t=e.name||String(e);return(0,i().rv)((function(n){return i().cS.not`instanceof ${t}`(!(n instanceof e))}),t)}x({required:{},optional:{foo:l()}}).value;x({required:{foo:l()},optional:{}}).value;x({required:{foo:N([l(),M()])},optional:{}}).value;x({required:{foo:ne()},optional:{}}).value;x({required:{},optional:{foo:l()}}).value;x({required:{foo:l()},optional:{}}).value;x({required:{foo:l()},optional:{bar:l()}}).value},640254:(e,t,n)=>{n.r(t)},644425:(e,t,n)=>{n.r(t),n.d(t,{initializeEarlyLogging:()=>c,updateNativeErrorHandler:()=>s});var o=()=>n(726637),r=()=>n(857639),a=()=>n(502800);let i=e=>{};function s(e){i=e}function c(){r().initialize(o().A),function(){const e=e=>{const t=e.reason?e.reason.message:e.message,n=e.reason?e.reason.status:e.status;if("HttpRequestError"===e.name)r().log({level:"info",from:"main",type:e.name,error:(0,a().convertErrorToLog)(e)});else if("QueueApiError"===e.name)r().log({level:"info",from:"main",type:e.name,error:(0,a().convertErrorToLog)(e)});else if("XHR Error: 0"===t||"Token was invalid or expired."===t||"No local database found."===t||"Script error."===t||"ResizeObserver loop completed with undelivered notifications."===t);else if(400===n)r().log({level:"warning",from:"main",type:"badRequest",error:(0,a().convertErrorToLog)(e)});else if("Request Timeout after 30000ms"===t)r().log({level:"warning",from:"main",type:t,error:(0,a().convertErrorToLog)(e)});else{const t={level:"error",from:"main",type:"ClientError",error:(0,a().convertErrorToLog)(e,JSON.stringify,!1)};r().log(t)}};window.addEventListener("error",e),window.addEventListener("unhandledrejection",e)}(),document.addEventListener("securitypolicyviolation",(e=>{const{blockedURI:t,violatedDirective:n,sourceFile:o}=e,a=e.disposition;r().log({level:"report"===a?"warning":"error",from:"main",type:"securitypolicyviolation",data:{miscDataToConvertToString:{blockedURI:t,violatedDirective:n,sourceFile:o}}})}))}},645873:(e,t,n)=>{n.d(t,{O2:()=>m,Qz:()=>y,_h:()=>h,fJ:()=>g,jQ:()=>v,u2:()=>f});n(944114),n(898992),n(803949);var o=n(296540),r=()=>n(56366),a=()=>n(187174),i=()=>n(558842),s=()=>n(319625),c=()=>n(763824),l=()=>n(11048),d=()=>n(765957),u=()=>n(227440),p=n(474848);class m{constructor(e,t,o){this.name=void 0,this.loader=void 0,this.promise=void 0,this.retryMs=void 0,this.attempts=void 0,this.loadAttempt=async e=>{e&&await Promise.all([c().wR(this.retryMs),u().A.waitUntil((()=>u().A.state.online))]);const t=this.attempts++;try{return await this.loader()}catch(o){throw n(449412).Fg(o,{extra:{waitMs:this.retryMs,attempts:t},tags:{dependencyName:this.name}}),n(857639).log({level:"warning",from:"useDependency",type:"loadError",error:(0,n(502800).convertErrorToLog)(o),data:{miscDataToConvertToString:{waitMs:this.retryMs},name:this.name}}),this.retryMs=Math.min(2*this.retryMs,3e4),o}},this.options=o,this.name=e,this.loader=t,this.promise=new(c().Il)(this.loadAttempt),this.retryMs=500,this.attempts=0}getLoadingState(){return this.promise.state}getLoadingMetrics(){const{state:e}=this.promise;if("resolved"===e.status){const{startedAt:t,resolvedAt:n}=e;return{startedAt:t,resolvedAt:n}}}reset(){this.promise=new(c().Il)(this.loadAttempt),this.retryMs=500}async load(){var e;null!==(e=this.options)&&void 0!==e&&e.waitForInitialPageRender&&!d().default.isInitialRenderComplete()&&await d().default.waitUntil((()=>d().default.isInitialRenderComplete()));const t=await this.promise.runWithRetry(),o=this.getLoadingMetrics();return o&&o.startedAt&&o.resolvedAt&&n(905343).A.addTrace({type:"lazy_load",name:this.name,start:o.startedAt,end:o.resolvedAt}),t}}function f(e){const{dependency:t,renderLoading:n,renderOfflineError:o,children:s,forceRenderLoading:c}=e,d=g(t),m=!c&&"resolved"===d.status,f=(0,a().BC)({state:d,spinAfterMs:300,render(e){if(n)return n(e)},forceRenderLoading:c}),b=(0,r().K8)((()=>m?s(d.value):null),[m,s,d.value],{debugName:`DependencyConsumer(${t.name}).useComputedStore`}),h=(0,r().O$)(u().e),v="rejected"===d.status&&!h;return(0,p.jsx)(l().A,{name:`Lazy_${t.name}`,children:v&&o?o():m?(0,i().Du)(b):f})}function g(e,t){const[n,r]=(0,o.useState)({asyncState:e.getLoadingState(),dependency:e});return n.dependency!==e&&r({asyncState:e.getLoadingState(),dependency:e}),(0,o.useEffect)((()=>{null!=t&&t.disabled||"idle"!==n.asyncState.status&&"rejected"!==n.asyncState.status&&"pending"!==n.asyncState.status||async function(){try{const e=await n.dependency.load();r({asyncState:{status:"resolved",value:e},dependency:n.dependency})}catch(e){r({asyncState:{status:"rejected",error:(0,s().A)(e)},dependency:n.dependency})}}()}),[null==t?void 0:t.disabled,n.dependency,n.asyncState]),n.asyncState}function b(e){const{renderLoading:t,forceRenderLoading:n,...o}=e;return o}function h(e,t){let n=arguments.length>2&&void 0!==arguments[2]?arguments[2]:{};return _(e,t,{...n,shouldForwardRef:!1,forceRenderLoading:n.forceRenderLoading??!1})}function v(e,t){let n=arguments.length>2&&void 0!==arguments[2]?arguments[2]:{};return _(e,t,{...n,shouldForwardRef:!0,forceRenderLoading:n.forceRenderLoading??!1})}function _(e,t,n){const{shouldForwardRef:r}=n,a=function(o,a){const i=o.renderLoading||n.renderLoading,s=o.forceRenderLoading||n.forceRenderLoading,c=b(o);return(0,p.jsx)(f,{renderLoading:i?e=>i(e,c):void 0,renderOfflineError:n.renderOfflineError?()=>{var e;return null===(e=n.renderOfflineError)||void 0===e?void 0:e.call(n,o)}:void 0,dependency:e,forceRenderLoading:s,children:e=>{const n=t(e);return(0,p.jsx)(n,{...r?{ref:a}:{},...c})}})};return a.displayName=`DependencyComponent(${e.name})`,r?(0,o.memo)((0,o.forwardRef)(a)):(0,o.memo)(a)}function y(e,t){const n=function(n,o){const r=n.renderLoading,a=b(n);return(0,p.jsx)(f,{renderLoading:r?e=>r(e,a):void 0,dependency:e,children:e=>t(e,{...a,ref:o})})};return n.displayName=`withDependency(${e.name})`,(0,o.forwardRef)(n)}},646447:(e,t,n)=>{n.d(t,{H6:()=>c,dN:()=>g,XA:()=>u,kU:()=>b,P2:()=>f,AQ:()=>p,YL:()=>l,fV:()=>d});var o=n(296540),r=()=>n(645873),a=()=>n(961581);function i(e){const t=(0,o.useRef)(e?"eligible":"ineligible"),r=(0,o.useCallback)((()=>{"eligible"===t.current&&(t.current="pending",a().A.update((e=>({...e,initialCollectionPendingRenderCount:e.initialCollectionPendingRenderCount+1}))))}),[]),i=(0,o.useCallback)((()=>{"pending"===t.current&&a().A.update((e=>({...e,initialCollectionPendingRenderCount:e.initialCollectionPendingRenderCount-1}))),t.current="complete"}),[]);return(0,o.useEffect)((()=>{r();const e=setTimeout((()=>{"pending"===t.current&&(i(),n(857639).log({level:"error",from:"useTrackPendingItemRender",type:"pending_render_timeout"}))}),8e3);return()=>{i(),clearTimeout(e)}}),[r,i]),i}var s=n(474848);const c={UnlistedCollectionViewDismissButton:new(r().O2)("UnlistedCollectionViewDismissButton",(async()=>await Promise.all([n.e(75134),n.e(99223),n.e(58795),n.e(17230),n.e(23740),n.e(36639),n.e(48307),n.e(14310),n.e(28398),n.e(55850),n.e(52274)]).then(n.bind(n,925409)))),UnlistedCollectionViewMoreButton:new(r().O2)("UnlistedCollectionViewMoreButton",(async()=>await Promise.all([n.e(75134),n.e(99223),n.e(58795),n.e(9773),n.e(36556),n.e(40537),n.e(17230),n.e(23740),n.e(33019),n.e(71621),n.e(92845),n.e(84468),n.e(75078),n.e(73801),n.e(48486),n.e(5605),n.e(36639),n.e(48307),n.e(14310),n.e(28398),n.e(55850),n.e(95541),n.e(82594),n.e(44745)]).then(n.bind(n,796722)))),CollectionViewSettingsButton:new(r().O2)("CollectionViewSettingsButton",(async()=>await Promise.all([n.e(75134),n.e(99223),n.e(58795),n.e(17230),n.e(23740),n.e(36639),n.e(48307),n.e(14310),n.e(28398),n.e(55850),n.e(26483),n.e(21871)]).then(n.bind(n,355505)))),CollectionViewBlock:new(r().O2)("CollectionViewBlock",(async()=>await Promise.all([n.e(75134,"high"),n.e(99223,"high"),n.e(58795,"high"),n.e(9304,"high"),n.e(96346,"high"),n.e(90825,"high"),n.e(17230,"high"),n.e(23740,"high"),n.e(48486,"high"),n.e(34359,"high"),n.e(66320,"high"),n.e(74562,"high"),n.e(28866,"high"),n.e(52651,"high"),n.e(78405,"high"),n.e(48113,"high"),n.e(36639,"high"),n.e(48307,"high"),n.e(14310,"high"),n.e(28398,"high"),n.e(55850,"high"),n.e(34438,"high"),n.e(30291,"high"),n.e(46580,"high"),n.e(22542,"high"),n.e(28464,"high"),n.e(23993,"high"),n.e(33358,"high"),n.e(89910,"high"),n.e(77690,"high"),n.e(26483,"high"),n.e(63996,"high"),n.e(40241,"high"),n.e(40126,"high"),n.e(943,"high")]).then(n.bind(n,301109)))),RestrictedCollectionView:new(r().O2)("RestrictedCollectionView",(async()=>await Promise.all([n.e(75134),n.e(99223),n.e(58795),n.e(17230),n.e(23740),n.e(36639),n.e(48307),n.e(14310),n.e(28398),n.e(55850),n.e(22114),n.e(24515)]).then(n.bind(n,217172)))),UISpacePermissionGroupToken:new(r().O2)("UISpacePermissionGroupToken",(async()=>await Promise.all([n.e(75134),n.e(99223),n.e(58795),n.e(17230),n.e(23740),n.e(36639),n.e(48307),n.e(14310),n.e(28398),n.e(55850),n.e(11528)]).then(n.bind(n,27356)))),FeedViewOnboardingTooltip:new(r().O2)("FeedViewOnboardingTooltip",(async()=>await Promise.all([n.e(75134),n.e(99223),n.e(58795),n.e(17230),n.e(23740),n.e(36639),n.e(48307),n.e(14310),n.e(28398),n.e(55850),n.e(56892)]).then(n.bind(n,964826))))},l=(0,r()._h)(c.UnlistedCollectionViewDismissButton,(e=>e.default)),d=(0,r()._h)(c.UnlistedCollectionViewMoreButton,(e=>e.default)),u=(0,r()._h)(c.CollectionViewSettingsButton,(e=>e.CollectionViewSettingsButton)),p=(0,r()._h)(c.UISpacePermissionGroupToken,(e=>e.UISpacePermissionGroupToken)),m=(0,r().jQ)(c.CollectionViewBlock,(e=>e.CollectionViewBlockWithErrorBoundary)),f=(0,r()._h)(c.RestrictedCollectionView,(e=>e.RestrictedCollectionView)),g=o.forwardRef((function(e,t){const[r]=(0,o.useState)(n(17022).e.withListenerIgnored((()=>!a().A.state.initialRenderCompleted))),[l]=(0,o.useState)(n(606932).A.getNavigationStartTime()),[d]=(0,o.useState)(c.CollectionViewBlock.getLoadingState().status),u=(0,o.useRef)(performance.now()),p=i(r);return(0,s.jsx)(m,{...e,mountedBeforeInitialPageRender:r,navigationStartTime:l,cvbLoadingStateOnInitialRender:d,lazyRenderStartedAtRef:u,trackPendingCollectionItemRender:p,ref:t})})),b=(0,r()._h)(c.FeedViewOnboardingTooltip,(e=>e.FeedViewOnboardingTooltip))},650031:(e,t,n)=>{n.d(t,{A:()=>r});class o extends(()=>n(757695))().Store{getInitialState(){return{isActive:!1,userId:void 0}}isReady(){return Boolean(this.state.remotePresenceData)}}const r=o},651170:(e,t,n)=>{n.d(t,{CS:()=>m,F:()=>p,JV:()=>d,PW:()=>c,lh:()=>s,pu:()=>l,w8:()=>u,xY:()=>a,yY:()=>i});n(16280);var o=()=>n(714681);class r extends Error{constructor(e){const{message:t,debugInfo:n}=e;super(t),this.debugInfo=void 0,this.debugInfo=n}}function a(e){return e instanceof r}class i extends r{constructor(e){const{result:t,debugInfo:n}=e;super({message:t.message,debugInfo:n}),this.name=t.name}}class s extends r{constructor(){super(...arguments),this.name="SqliteDatabaseBecameCorruptDuringSession"}}class c extends r{constructor(){super(...arguments),this.name="SqliteDatabaseWasCorruptWhenSessionBegan"}}class l extends r{constructor(){super(...arguments),this.name="SqliteInvalidResult"}}class d extends r{constructor(){super(...arguments),this.name="SqlitePreconditionFail"}}class u extends r{constructor(){super(...arguments),this.name="SqliteOutOfSpace"}}class p extends r{constructor(){super(...arguments),this.name="SqliteSharedWorkerFailedToDelegate"}}function m(e,t){let{isBrowser:n}=t;return{errorSql:a(e)?e.debugInfo.errorSql:void 0,lastSuccessfulSqlBatch:e instanceof s?e.debugInfo.lastSuccessfulSqlBatch:void 0,sqliteCode:a(e)?e.debugInfo.sqliteCode:void 0,wasmSqliteDbVersion:n?o().c:void 0}}},669484:(e,t,n)=>{n.d(t,{performPrefetchRequests:()=>s,v:()=>c});n(517642),n(658004),n(733853),n(845876),n(432475),n(515024),n(731698),n(898992),n(354520),n(581454);var o=()=>n(726637),r=()=>n(861017),a=()=>n(534177),i=()=>n(740456);function s(e){let{environment:t,currentUserId:n,options:s={}}=e;const l="prefetchCache"in t?t.prefetchCache:new(i().iO),d=s.nextRoute??(0,r().parseRoute)({url:window.location.href,isMobile:Boolean(o().A.isMobile),baseUrl:o().A.domainBaseUrl,publicDomainName:o().A.publicDomainName,protocol:o().A.protocol,currentUrl:window.location.href});(0,i().VJ)(),(0,i().h6)();const{blockId:u,collectionViewId:p,teamId:m,userId:f}=c({route:d,userId:n}),g=u&&!p&&(0,i().nD)(u)||[],b="root"===d.name,h=f&&(0,i().b8)(f).alwaysPrefetchGetSpaces;f&&(b||h)&&(0,i().Nl)({prefetchCache:l,environment:t,activeUserId:f,data:{}});if(f&&(0,i().b8)(f).prefetchSharedAndPrivatePages){const{spaceId:e}=(0,i().h2)();e&&(0,i().Xd)({prefetchCache:l,environment:t,activeUserId:f,data:{spaceId:e}})}if("root"===d.name&&!f)return l;const{isMobileNative:v,isPhone:_}=t.device,y=v&&_,{isTeamHome:w,teamHomeCollectionViewIds:S}=(0,i().ro)(m),{isHome:k,unifiedFeedEnabled:A,aiChatEnabled:C,homeCollectionViewIds:I}=(0,i().i6)(u),P=[...new Set([...I,...S,p,...g].filter(a().O9))],T=!s.skipPrefetchPageChunk&&void 0!==u,E=P.length>0||"blank"===d.name;return Promise.all([u&&T&&(0,i().s1)({prefetchCache:l,environment:t,activeUserId:f,blockId:u}),!y&&k&&(0,i().QM)({unifiedFeedEnabled:A,aiChatEnabled:C}),!y&&w&&(0,i().fn)(),E&&(0,i().fX)(),...P.map((e=>(0,i().qk)({prefetchCache:l,environment:t,activeUserId:f,collectionViewId:e})))]),l}function c(e){const{route:t,userId:n}=e;switch(t.name){case"root":return(0,i().getPredictedRootRedirectPageForPrefetch)();case"page":return{blockId:t.blockId,collectionViewId:t.collectionViewId,teamId:void 0,userId:n};case"team":return{blockId:void 0,collectionViewId:void 0,teamId:t.teamId,userId:n};default:return{blockId:void 0,collectionViewId:void 0,teamId:void 0,userId:n}}}},669528:(e,t,n)=>{n.r(t)},671593:(e,t,n)=>{n.d(t,{Qq:()=>m,Xj:()=>p,cS:()=>l,jA:()=>f,rv:()=>a,tf:()=>u,xs:()=>i});n(16280),n(944114),n(517642),n(658004),n(733853),n(845876),n(432475),n(515024),n(731698),n(898992),n(354520),n(803949),n(581454),n(908872);var o=()=>n(302777),r=()=>n(973647);function a(e,t){return{validator:e,toString:"string"==typeof t?()=>t:t}}function i(e,t,n){const o=e.validator;return a((e=>o(e)||t(e)),n??e.toString)}class s{constructor(){this.failures=new Set,this.subtree=new Map}}function c(e){let t,n;try{t=JSON.stringify(e)}catch(a){n=a}try{t??=String(e)}catch(a){n=a}n&&void 0===t?t=`error creating string representation: ${n}`:t??="(unknown)";const o=t.slice(0,55),r=o.length<t.length?"...":"";return`\`${o.replace(/`/g,"\\`")}${r}\``}const l={not:function(){for(var e=arguments.length,t=new Array(e),n=0;n<e;n++)t[n]=arguments[n];return e=>{if(!1===e)return null;{const e=function(e){const t=[];for(var n=arguments.length,o=new Array(n>1?n-1:0),r=1;r<n;r++)o[r-1]=arguments[r];for(const a in e)if(t.push(e[a]),a in o){const e=o[a];t.push(c(e))}return t.join("")}(...t);return[{claim:e,path:[]}]}}},noneOf:function(e,t){if(0===e.length)throw new Error("No types provided.");const n=[];for(const o of e){const e=o.validator(t);if(!e)return null;n.push(...e)}return n},keyIsNot:function(e,t,n){let o=e.validator(n[t]);return o&&(o=o.map((e=>{let{path:n,...o}=e;return{...o,path:[t,...n]}}))),o},anyFails:function(e,t){for(const n of e.keys()){const o=t(e[n],n,e);if(o)return o}return null}};function d(e,t,n){const r=function(e){const t=new s;return e.forEach((e=>{let{claim:n,path:o}=e;o.reduce(((e,t)=>{let n=e.subtree.get(t);return void 0===n&&(n=new s,e.subtree.set(t,n)),n}),t).failures.add(n)})),t}(t),a=function(e,t){return function e(t){let n=arguments.length>1&&void 0!==arguments[1]?arguments[1]:[],o=arguments.length>2&&void 0!==arguments[2]?arguments[2]:[];return t.failures.size>0&&(o=o.concat({path:n,isNots:[...t.failures]})),t.subtree.forEach(((t,r)=>{o=e(t,n.concat(r),o)})),o}(e,[t])}(r,n),i=a.map((e=>e.path.length)),l=Math.max(...i),d=a.filter((e=>e.path.length===l));return[d.length>1?`${n} failed validation. Fix one:`:`${n} failed validation:`,...d.map((t=>{let{path:n,isNots:r}=t;const a=function(e){let t="";for(const n of e){const e=String(n);let o;o="string"!=typeof n?`[${e}]`:""===t?e:`.${e}`,t+=o}return t}(n),i=(l="or",(s=r).slice(0,-2).concat("").join(", ")+s.slice(-2).join(`${s.length>2?",":""} ${l} `));var s,l;const d=function(e,t){const n=t.slice(1);return c(n.length>0?(0,o().OU)(e,n):e)}(e,n);return`${a} should be ${i}, instead was ${d}.`}))].join(d.length>1?"\n":" ")}function u(e,t){let n=arguments.length>2&&void 0!==arguments[2]?arguments[2]:{};const o=e.validator(t);if(o){const e=d(t,o,n.rootVariableName||"payload");return new Error(e)}}function p(e,t){return void 0===u(e,t)}function m(e,t){return r().Q.unwrapOr(f(e,t),void 0)}function f(e,t){const n=u(e,t,arguments.length>2&&void 0!==arguments[2]?arguments[2]:{});return void 0!==n?{error:n}:{value:t}}},671887:(e,t,n)=>{n.d(t,{D:()=>o});const o=new(n(178624).R)({key:"spaceIdToShortId",namespace:n(419494).Bq,important:!0,trackingType:"necessary"})},671973:(e,t,n)=>{n.d(t,{N:()=>r});function o(e){return"page"===e||"new_page"===e||"collection"===e}const r=new class{constructor(){this.state={id:void 0,parameters:void 0,initialPageRendered:!1,transitionReady:!1,type:void 0,navigationRendered:!1}}markNativeStart(e){this.state={...this.state,id:e.id,parameters:e,transitionReady:!1,type:void 0,navigationRendered:!1}}markInitialPageRendered(){this.state={...this.state,initialPageRendered:!0}}markTransitionReady(e,t,n){if(this.state={...this.state,type:t},o(t)){const t=this.state;t.initialPageRendered||t.navigationRendered?this.trackMetricEnd(e):n?this.state={...this.state,transitionReady:!0}:this.trackMetricEnd(e,!0)}}markNavigationRender(e){const t=this.state;t.transitionReady?o(t.type)&&this.trackMetricEnd(e):this.state={...this.state,navigationRendered:!0}}trackMetricEnd(e,t){var n,o,r;const a=this.state,i=a.id;if(!i)return;const s={time:Date.now()-a.parameters.start_time_ms,is_initial_transition:a.initialPageRendered,transition_type:a.type,before_app_start:(null===(n=a.parameters)||void 0===n?void 0:n.before_app_start)??!1,start_time_ms_since_app_start:null===(o=a.parameters)||void 0===o?void 0:o.start_time_ms_since_app_start,transition_to_same_page:t??!1};null===(r=e.mobileNative)||void 0===r||r.nativeToWebRenderEnd(i,s),this.state={id:void 0,parameters:void 0,initialPageRendered:!1,transitionReady:!1,type:void 0,navigationRendered:!1}}}},675742:(e,t,n)=>{n.d(t,{electronApi:()=>o});const o=window.__electronApi},677338:(e,t,n)=>{n.d(t,{$A:()=>ie,$P:()=>ce,$m:()=>te,ED:()=>H,Fo:()=>F,G1:()=>ke,HP:()=>Q,HU:()=>ve,Ht:()=>ae,IT:()=>me,IV:()=>ge,J0:()=>ye,JU:()=>V,K:()=>ne,KB:()=>K,L_:()=>he,NI:()=>Z,OC:()=>re,P1:()=>_e,PM:()=>Se,SQ:()=>le,TR:()=>z,Ug:()=>L,dC:()=>pe,dY:()=>se,dv:()=>we,dw:()=>ue,f3:()=>de,fu:()=>oe,hl:()=>ee,hn:()=>j,ip:()=>Y,j4:()=>J,l0:()=>be,oJ:()=>W,pG:()=>G,r9:()=>fe,sY:()=>U,wA:()=>X,x0:()=>$});var o=()=>n(534177),r=()=>n(720665),a=()=>n(206267),i=()=>n(820103),s=()=>n(861017),c=()=>n(427704);const l="sessionTags",d="urlAnalytics",u="sessionTabId",p="activeUserId",m="sessionAppRefreshed",f="oauthAuthorizationPage",g="postLoginRedirectURL",b="postLoginRedirectURLTS",h="postOnboardingRedirectURL",v="postOnboardingRedirectURLTS",_="oauthInitiatedTimestamp",y="postLoginFallbackRedirectUrl",w="postLoginFallbackRedirectUrlTimestamp",S="onboardingFormResponseId",k="onboardingFormSecretKey",A="onboardingFormSpaceId",C="onboardingFormSpaceIntent",I="onboardingFormResponseIdTimestamp",P="newUserSignupSourceKey",T="magicBoxPrompt",E="magicBoxPromptTimestamp",R="partnerProgramPromoCode",D="partnerProgramPromoCampaign",M="partnerProgramOnboardingTimestamp",q="isMailUser",O="featureIntent",B="featureIntentTimestamp",x="signupReferralCode",N="signupReferralCodeTimestamp";function L(e){sessionStorage.setItem(l,JSON.stringify(e))}function U(){const e=sessionStorage.getItem(l);if(e)return(0,r().$l)(e)}function F(e){const{pathname:t,query:n,previous_path:o,...r}=e;sessionStorage.setItem(d,JSON.stringify(r))}function V(){const e=sessionStorage.getItem(d);if(e)return(0,r().$l)(e)}function j(e){sessionStorage.setItem(p,e)}function J(){return sessionStorage.getItem(p)||void 0}function H(e){sessionStorage.setItem(m,JSON.stringify(e))}function W(){const e=sessionStorage.getItem(m);return!!e&&(0,r().$l)(e)}function $(){let e=sessionStorage.getItem(u);return e||(e=a().JW(),sessionStorage.setItem(u,e)),e}function z(){sessionStorage.setItem(_,`${Date.now()}`)}function Z(e){void 0===e?sessionStorage.removeItem(f):sessionStorage.setItem(f,JSON.stringify(e))}function G(){const e=sessionStorage.getItem(f);if(e)return(0,r().$l)(e)}function K(e){const t=sessionStorage.getItem(g);if(!t)return;const{redirectUrl:n,shouldRedirectForExistingUsersOnly:o}=(0,r().$l)(t),a=sessionStorage.getItem(b),i=sessionStorage.getItem(_),{config:l,fileHostProtocol:d,fileHostName:u}=e;return l&&i&&n&&s().m6(n,l.domainBaseUrl)?parseInt(i)>Date.now()-12e4?{type:"oauth",redirectUrl:n,shouldRedirectForExistingUsersOnly:o}:void sessionStorage.removeItem(_):n&&(n.startsWith(`${c().JZ.globalOauthAuthorization}`)||n.startsWith(`${c().JZ.globalOauthPostLogin}`))?{type:"global_oauth",redirectUrl:n,shouldRedirectForExistingUsersOnly:o}:n&&a?parseInt(a)>Date.now()-9e5?n.startsWith(`${d}://${u}/`)?{type:"file",redirectUrl:n,shouldRedirectForExistingUsersOnly:o}:{type:"other",redirectUrl:n,shouldRedirectForExistingUsersOnly:o}:void Q():void 0}function Q(){sessionStorage.removeItem(g),sessionStorage.removeItem(b)}function X(e){let{url:t,shouldRedirectForExistingUsersOnly:n}=e;sessionStorage.setItem(g,JSON.stringify({redirectUrl:t,shouldRedirectForExistingUsersOnly:n})),sessionStorage.setItem(b,`${Date.now()}`)}function Y(){sessionStorage.removeItem(S),sessionStorage.removeItem(I),sessionStorage.removeItem(k),sessionStorage.removeItem(A),sessionStorage.removeItem(C)}function ee(e,t,n,o){sessionStorage.setItem(S,e),sessionStorage.setItem(k,t),sessionStorage.setItem(A,n),o&&sessionStorage.setItem(C,o),sessionStorage.setItem(I,`${Date.now()}`)}function te(){const e=sessionStorage.getItem(S),t=sessionStorage.getItem(k),n=sessionStorage.getItem(A),o=sessionStorage.getItem(C),a=sessionStorage.getItem(I);return a&&e&&parseInt(a)<Date.now()-10*r().Xb?null:e&&t&&n?{formResponseId:e,formSecretKey:t,formSpaceId:n,formSpaceIntent:o??void 0}:null}function ne(e,t){sessionStorage.setItem(R,e),sessionStorage.setItem(D,t),sessionStorage.setItem(M,`${Date.now()}`)}function oe(){sessionStorage.removeItem(R),sessionStorage.removeItem(D),sessionStorage.removeItem(M)}function re(){const e=sessionStorage.getItem(M);if(e&&parseInt(e)<Date.now()-15*r().Xb)return void oe();const t=sessionStorage.getItem(R),n=sessionStorage.getItem(D);return"string"==typeof t&&"string"==typeof n?{partnerProgramPromoCode:t,partnerProgramPromoCampaign:n}:void 0}function ae(e){sessionStorage.setItem(P,e)}function ie(){sessionStorage.removeItem(P)}function se(){const e=sessionStorage.getItem(P);if("string"==typeof(t=e)&&i().Rw.includes(t))return e;var t}function ce(e){sessionStorage.setItem(y,e),sessionStorage.setItem(w,`${Date.now()}`)}function le(){sessionStorage.removeItem(y),sessionStorage.removeItem(w)}function de(){const e=sessionStorage.getItem(y),t=sessionStorage.getItem(w);return t&&e&&parseInt(t)<Date.now()-10*r().Xb?null:e}function ue(){const e=sessionStorage.getItem(h),t=sessionStorage.getItem(v);return t&&e&&parseInt(t)<Date.now()-15*r().Xb?null:e}function pe(){sessionStorage.removeItem(h),sessionStorage.removeItem(v)}function me(e){sessionStorage.setItem(h,e),sessionStorage.setItem(v,`${Date.now()}`)}function fe(){const e=sessionStorage.getItem(T),t=sessionStorage.getItem(E);return t&&e&&parseInt(t)<Date.now()-15*r().Xb?null:e}function ge(){sessionStorage.removeItem(T),sessionStorage.removeItem(E)}function be(e){sessionStorage.setItem(T,e),sessionStorage.setItem(E,`${Date.now()}`)}function he(){sessionStorage.setItem(q,"true")}function ve(){const e=sessionStorage.getItem(q);return Boolean(e)}function _e(e){sessionStorage.setItem(O,e),sessionStorage.setItem(B,`${Date.now()}`)}function ye(){const e=sessionStorage.getItem(O),t=sessionStorage.getItem(B);return t&&parseInt(t)<Date.now()-15*r().Xb?(sessionStorage.removeItem(O),void sessionStorage.removeItem(B)):(n=e,(0,o().Xk)(i().IL,n)?e:void 0);var n}function we(e){sessionStorage.setItem(x,e),sessionStorage.setItem(N,`${Date.now()}`)}function Se(){return sessionStorage.getItem(x)??void 0}function ke(){sessionStorage.removeItem(x),sessionStorage.removeItem(N)}},680091:(e,t,n)=>{n.d(t,{X:()=>o});const o=new class{constructor(){this.processingTime=void 0}setProcessingTime(e){this.processingTime=e}getProcessingTime(){return this.processingTime}}},692186:(e,t,n)=>{n.r(t)},697938:(e,t,n)=>{n.d(t,{E:()=>a});n(16280),n(944114),n(517642),n(658004),n(733853),n(845876),n(432475),n(515024),n(731698),n(581454);var o=()=>n(496603),r=()=>n(534177);function a(e,t){const n=new Set,a=['  graph [rankdir = "LR"];'],i=new Map,s=new Map;for(const o of e.nodes){const e=[];let c=o.label;if("store"===o.type){if(t&&o.recordStoreDetails){const{id:e}=o.recordStoreDetails;let t=i.get(e);if(t||(t={id:e,table:o.recordStoreDetails.table,nodes:[]},i.set(e,t)),o.recordStoreDetails.table!==t.table)throw new Error("Huh, didn't expect that to happen");if(o.blockStoreDetails){const e=o.blockStoreDetails.type;if(t.blockType){if(t.blockType!==e)throw new Error("Hmm this data is weird")}else t.blockType=e;n.add(o.id)}s.set(o.id,`"${e}":"${o.id}"`),t.nodes.push(o);continue}if(o.recordStoreDetails){const e=o.recordStoreDetails;o.blockStoreDetails?(c=`${c}: ${e.table} ${e.id} (${o.blockStoreDetails.type} block)`,n.add(o.id)):e.path.length>0&&(c=`${c} path: ${e.path.join(".")}`)}e.push("shape=box")}else"computedstore"===o.type?e.push("shape=diamond"):"component"===o.type?e.push("shape=ellipse"):"unknown"===o.type?e.push("shape=tripleoctagon"):(0,r().HB)(o.type);e.push(`label="${c}"`),a.push(`  "${o.id}" [${e.join(",")}];`)}for(const[r,c]of i.entries()){const e=["shape=record"];for(const o of c.nodes){let e=o.label;o.recordStoreDetails&&o.recordStoreDetails.path.length&&(e=`${e}: ${o.recordStoreDetails.path.join(".")}`),o.label=e}const t=o().My(c.nodes,["label"]).map((e=>`<${e.id}> ${e.label}`)).join("|");let n=`${c.table} ${c.id}`;c.blockType&&(n+=` (${c.blockType} block)`);const i=`<__title> ${n}|${t}`;e.push(`label="${i}"`),a.push(`  "${r}" [${e.join(",")}];`)}if(!t)for(const o of e.nodes)o.parentUIStoreId&&a.push(`  "${o.parentUIStoreId}" -> "${o.id}" [style=dotted];`);for(const{from:o,to:r}of e.edges){const e=s.get(o)??`"${o}"`,t=[];n.has(o)&&t.push("color=red"),a.push(`  ${e} -> "${r}" [${t.join(",")}];`)}return`digraph G {\n${a.join("\n")}\n}`}},705059:(e,t,n)=>{function o(e){return{isHomeKey:`isHome:${e}`,homeCollectionViewIdsKey:`homeCollectionViewIds:${e}`,unifiedFeedEnabledKey:`unifiedFeedEnabled:${e}`,aiChatEnabledKey:`aiChatEnabled:${e}`}}n.d(t,{h:()=>o})},706762:(e,t,n)=>{function o(){return function(){if(!(window.navigator&&window.navigator.storage&&window.FileSystemFileHandle&&window.FileSystemFileHandle.prototype.createWritable))return!1;return!0}()&&function(){if(!window.SharedWorker)return!1;return!0}()}function r(){return function(){if(!window.Worker)return!1;return!0}()}n.d(t,{n:()=>r,s:()=>o})},711059:(e,t,n)=>{n.r(t),n.d(t,{AllVersionHeaders:()=>A,AndroidVersionHeader:()=>S,ClientVersionHeader:()=>v,IOSVersionHeader:()=>w,MacVersionHeader:()=>_,PublicApiVersionHeader:()=>k,UpdateType:()=>r,WindowsVersionHeader:()=>y,formatVersion:()=>c,getUpdateType:()=>l,isEqualVersion:()=>d,isGreaterThanOrEqualToVersion:()=>p,isGreaterThanVersion:()=>u,isLessThanOrEqualToVersion:()=>f,isLessThanVersion:()=>m,maxVersion:()=>g,parseMobileAppVersion:()=>s,parseVersion:()=>i,parseVersionStringFromTag:()=>a,versionStringGreaterThanOrEqualToVersion:()=>h});n(16280),n(898992),n(908872);var o=()=>n(857639);let r=function(e){return e[e.Major=0]="Major",e[e.Minor=1]="Minor",e[e.Patch=2]="Patch",e[e.Silent=3]="Silent",e}({});function a(e){const t=e.split("-v");return t.length>1?t[1].split(",")[0]:e}function i(e){const t=e.match(/(\d+)\.(\d+)\.(\d+)\.(\d+)/);if(t&&e===t[0])return[parseInt(t[1]),parseInt(t[2]),parseInt(t[3]),parseInt(t[4])];const n=e.match(/(\d+)\.(\d+)\.(\d+)/);return n&&e===n[0]?[parseInt(n[1]),parseInt(n[2]),parseInt(n[3])]:void 0}function s(e,t){if(!e)return;let n;if(t){let t;const o=e.split(".");t="beta"===o[o.length-1]?o.slice(0,o.length-1).join("."):o.join("."),n=i(t)}else n=i(e);return n}function c(e){if(e)return e.join(".")}function l(e){const{currentVersion:t,nextVersion:n,app:a}=e;if(!t||!n)return r.Major;if("client"===a&&4===t.length&&function(e){return 3===e.length}(n))throw new Error(`Failed attempting to update client from ${t} to ${n}`);return n[0]>t[0]?r.Major:n[1]>t[1]?r.Minor:n[2]>t[2]?r.Patch:"client"===a?r.Silent:(o().log({level:"error",from:"versionHelpers",type:"getUpdateType",error:{message:"Failed to get update type"},data:{miscDataToConvertToString:{currentVersion:t,nextVersion:n,currentVersionType:typeof t,nextVersionType:typeof n}}}),r.Patch)}function d(e,t){return!(!e||!t)&&0===b(e,t)}function u(e,t){return-1===b(e,t)}function p(e,t){const n=b(e,t);return-1===n||0===n}function m(e,t){return 1===b(e,t)}function f(e,t){const n=b(e,t);return 1===n||0===n}function g(e){return e.reduce(((e,t)=>-1===b(t,e)?t:e),e[0])}function b(e,t){for(let n=0,o=0;e.length,o<t.length;n++,o++){if(e[n]>t[o])return-1;if(e[n]<t[o])return 1}return e.length>t.length?-1:t.length>e.length?1:0}function h(e,t){const n=i(e);if(void 0===n)return!1;const o=b(n,t);return 0===o||-1===o}const v="notion-client-version",_="notion-mac-version",y="notion-windows-version",w="notion-ios-version",S="notion-android-version",k="notion-version",A=[v,_,y,w,S,k]},713996:(e,t,n)=>{n.d(t,{AM:()=>p,D8:()=>s,DE:()=>u,Tr:()=>c,Vo:()=>r,Xe:()=>i,_P:()=>o,al:()=>d,hx:()=>g,kr:()=>a,o3:()=>m,oh:()=>l,yp:()=>f});const o={name:"onboarding",pattern:"/onboarding"},r={name:"general",pattern:"/general{/:subtab}"},a={name:"people",pattern:"/people{/:subtab}"},i={name:"security",pattern:"/security{/:subtab}"},s={name:"data_and_compliance",pattern:"/data-and-compliance{/:subtab}"},c={name:"analytics",pattern:"/analytics{/:subtab}"},l=[o,r,a,i,s,c];let d=function(e){return e.Workspaces="workspaces",e.EmailDomains="email-domains",e.Scim="scim",e}({}),u=function(e){return e.Members="members",e.Guests="guests",e.Groups="groups",e.ManagedUsers="managed-users",e}({});const p=["general","workspaces","groups","audit_log"],m=["general","members","teamspaces","pages"],f=["general","members","guests","groups","teamspaces"],g=["general","members","browse_content","exports"]},714681:(e,t,n)=>{n.d(t,{S:()=>r,c:()=>o});const o="v5";function r(e){return`notion-tab-${e}`}},715668:(e,t,n)=>{n.d(t,{deliverAndPersistReactivityVersion:()=>o});async function o(e,t){}},715854:(e,t,n)=>{n.d(t,{L:()=>l,getOPFSPageCacheInstance:()=>d});n(16280),n(944114),n(816573),n(878100),n(177936),n(748140),n(821903),n(491134),n(128845),n(237467),n(444732),n(979577),n(581454),n(814603),n(147566),n(198721);var o=()=>n(681335);class r{constructor(e){this.remote=void 0;const t=function(e){const t="dedicated-worker"===e.type?new Worker(new URL(n.p+n.u(39047),n.b),{type:void 0}):new SharedWorker(new URL(n.p+n.u(39047),n.b),{type:void 0});return t}(e);t instanceof SharedWorker?(t.port.start(),this.remote=o().LV(t.port),t.port.postMessage({name:"debug",enabledValue:this.getDebugValue()})):(this.remote=o().LV(t),t.postMessage({name:"debug",enabledValue:this.getDebugValue()}))}async readBuffer(e,t){return await this.remote.readBuffer(e,t)}async readJSON(e,t){return await this.remote.readJSON(e,t)}async write(e,t){return await this.remote.write(e,t)}async checkIfExists(e){return await this.remote.checkIfExists(e)}async delete(e){return await this.remote.delete(e)}async deleteAll(){return await this.remote.deleteAll()}async migrateAll(){return await this.remote.migrateAll()}async abort(e,t){return await this.remote.abort(e,t)}getDebugValue(){return localStorage.getItem("debug")}}function a(e){performance.mark(`OPFS:PageCache:${e}`)}function i(e,t,n){return performance.measure(`OPFS:PageCache:${e}`,`OPFS:PageCache:${t}`,`OPFS:PageCache:${n}`)}class s{constructor(e){this.client=void 0,this.client=e}async readBuffer(e,t,n){const o=this.getKey(e,t);if(a("readBuffer.start"),null!=n&&n.aborted)throw new Error(n.reason);let r;n&&(r=this.registerAbortListener(n));try{var s;const{buffer:e,metrics:t}=await this.client.readBuffer(o,null===(s=r)||void 0===s?void 0:s.abortId);a("readBuffer.got-buffer");const n=i("readBuffer","readBuffer.start","readBuffer.got-buffer"),c=new Uint8Array(e),{metadata:l,offset:d}=this.getMetadata(c);return{metadata:l,buffer:e,iterator:this.getChunkIterator(c.subarray(d)),metrics:{...t,total:n.duration}}}finally{var c;null===(c=r)||void 0===c||c.unregister()}}async readJSON(e,t,n){a("readJSON.start");const{iterator:o,metrics:r,metadata:s}=await this.readBuffer(e,t,n);a("readJSON.got-text");const c=[];for(const a of o)c.push(a);a("readJSON.parsed-json");const l=i("readJSON.got-text","readJSON.start","readJSON.got-text"),d=i("readJSON.parse-json","readJSON.got-text","readJSON.parsed-json"),u=i("readJSON.total","readJSON.start","readJSON.parsed-json");return{metadata:s,chunks:c,metrics:{...r,getText:l.duration,parseJSON:d.duration,total:u.duration}}}async write(e,t,n){const o=this.getKey(e,t);return await this.client.write(o,n.map((e=>JSON.stringify(e.toJson()))))}async checkIfExists(e,t){const n=this.getKey(e,t);return await this.client.checkIfExists(n)}async delete(e,t){const n=this.getKey(e,t);return await this.client.delete(n)}async deleteAll(){return await this.client.deleteAll()}getKey(e,t){return`${e}__${t}`}registerAbortListener(e){const t=(0,n(498212).Ay)(),o=()=>this.client.abort(t,e.reason);return e.addEventListener("abort",o,{once:!0}),{abortId:t,unregister:function(){e.removeEventListener("abort",o)}}}getMetadata(e){const t=e.indexOf(10),n=-1===t?e.length:t,o=(new TextDecoder).decode(e.subarray(0,n));return{metadata:JSON.parse(o),offset:n+1}}*getChunkIterator(e){const t=new TextDecoder;let n=0;for(;n<e.length;){const o=e.indexOf(10,n),r=-1===o?e.length:o+1,a=t.decode(e.subarray(n,r));if(a.length>0){const e=JSON.parse(a);yield e}n=r}}}let c;function l(e){if(c)throw new Error("OPFSPageCache already initialized, can only do this once");return c=new s(new r(e)),c}function d(){if(c)return c}},720665:(e,t,n)=>{n.d(t,{$l:()=>C,$z:()=>k,AH:()=>h,Bu:()=>i().B,D_:()=>E,Et:()=>w,Fs:()=>T,Gq:()=>a().Gq,HP:()=>d,Hg:()=>g,LG:()=>p,MU:()=>m,Om:()=>P,TD:()=>a().TD,Up:()=>f,WY:()=>a().WY,Xb:()=>a().Xb,Z$:()=>A,Z4:()=>v,Zg:()=>I,g_:()=>l,h_:()=>b,hr:()=>_,i5:()=>a().i5,lG:()=>a().lG,m1:()=>a().m1,nD:()=>a().nD,pN:()=>c,pT:()=>a().pT,qE:()=>S,qt:()=>s,sb:()=>r().s,uu:()=>a().uu,w$:()=>y,wK:()=>a().wK,xq:()=>a().xq,zu:()=>u});n(16280),n(944114),n(269479),n(517642),n(658004),n(733853),n(845876),n(432475),n(515024),n(731698),n(898992),n(354520),n(430670),n(581454),n(908872);var o=()=>n(496603),r=()=>n(806080),a=()=>n(615234),i=()=>n(140283);function s(e){return o().Mp(e,o().n4)}function c(e){return null==e}function l(e){const t=e;for(const n in t)void 0===t[n]&&(t[n]=null);return t}function d(e){return Object.keys(e)}function u(e){return d(e).map((t=>e[t]))}function p(e,t){return o().LG(e,t)}const m=Object.fromEntries;function f(e,t){return o().Up(e,t)}function g(e){let t=!1;return function(){if(t)return e(...arguments);t=!0}}function b(e,t){return e.filter((e=>!t(e)))}function h(e){return Object.fromEntries(Object.entries(e).map((e=>{let[t,n]=e;return[n,t]})))}function v(e,t,n,r){const a=o().pY(n,r);if(Object.keys(a).length!==n.length)throw new Error("zipBy indexes must be unique for each value");const i=o().pY(e,t);if(Object.keys(i).length!==e.length)throw new Error("zipBy indexes must be unique for each value");const s=e.map((e=>[e,a[t(e)]])),c=n.filter((e=>!i[r(e)])).map((e=>[void 0,e]));return s.concat(c)}function _(e){return e.split("\n").map((e=>e.trim())).join("\n")}function y(e,t){let n=e;if(n.sticky||!n.global){const t=new Set(n.flags.split(""));t.delete("y"),t.add("g"),n=new RegExp(e.source,Array.from(t).join(""))}const o=n.lastIndex,r=[];let a=null;for(;null!==(a=n.exec(t));)r.push(a);return n.lastIndex=o,r}function w(e){return o().MI(e)}function S(e,t,n){return Math.max(t,Math.min(e,n))}function k(e,t,n){const o=new Map;for(let r=0;r<e.length;r++){const a=e[r],i=t(a,r);let s=o.get(i);s||(s=[],o.set(i,s)),s.push(n?n(a,r):a)}return o}function A(e,t){const n=o().sb(e);if(n.length>1)throw new Error(t);return n[0]}function C(e){try{return JSON.parse(e)}catch(t){return}}function I(e,t,n){return e[t]=n,e}function P(e){return Object.keys(e).reduce(((t,n)=>({...t,[o().LW(n)]:"object"==typeof e[n]?P(e[n]):e[n]})),{})}function T(){for(var e=arguments.length,t=new Array(e),n=0;n<e;n++)t[n]=arguments[n];return function(e){let n=e;for(const o of t)n=o(n);return n}}function E(e){return e}},732430:(e,t,n)=>{n.d(t,{AN:()=>p,D2:()=>d,Lb:()=>i,QF:()=>r,XQ:()=>a,mx:()=>c,s9:()=>l,xB:()=>u});var o=()=>n(645873);const r=new(o().O2)("posts",(()=>Promise.all([n.e(75134),n.e(99223),n.e(58795),n.e(9304),n.e(9773),n.e(36556),n.e(40537),n.e(16471),n.e(90825),n.e(17230),n.e(23740),n.e(33019),n.e(71621),n.e(92845),n.e(84468),n.e(75078),n.e(42660),n.e(34359),n.e(44934),n.e(21194),n.e(66320),n.e(63479),n.e(78405),n.e(1707),n.e(48113),n.e(36639),n.e(48307),n.e(14310),n.e(28398),n.e(55850),n.e(34438),n.e(64271),n.e(55269),n.e(85898),n.e(1694),n.e(63840),n.e(84591),n.e(3263),n.e(48986),n.e(43396)]).then(n.bind(n,963120)))),a=(0,o()._h)(r,(e=>e.PostChannelCollectionView)),i=(0,o()._h)(r,(e=>e.MobileUnifiedFeed)),s={PostsLaunchModal:new(o().O2)("PostsLaunchModal",(async()=>await Promise.all([n.e(75134),n.e(99223),n.e(58795),n.e(17230),n.e(23740),n.e(36639),n.e(48307),n.e(14310),n.e(28398),n.e(55850),n.e(23904)]).then(n.bind(n,357397))))},c=(0,o()._h)(s.PostsLaunchModal,(e=>e.PostsLaunchModal)),l=(0,o()._h)(r,(e=>e.PostPresence)),d=new(o().O2)("topicRecommendationsPost",(()=>Promise.all([n.e(75134),n.e(99223),n.e(58795),n.e(17230),n.e(23740),n.e(34877),n.e(11676),n.e(8184),n.e(36639),n.e(48307),n.e(14310),n.e(28398),n.e(55850),n.e(91866)]).then(n.bind(n,506180)))),u=((0,o()._h)(d,(e=>e.TopicRecommendationsPost)),new(o().O2)("feedPage",(()=>Promise.all([n.e(75134),n.e(99223),n.e(58795),n.e(17230),n.e(23740),n.e(36639),n.e(48307),n.e(14310),n.e(28398),n.e(55850),n.e(64626)]).then(n.bind(n,92284))))),p=((0,o()._h)(u,(e=>e.FeedPage)),new(o().O2)("activityDigestPostContainer",(()=>Promise.all([n.e(75134),n.e(99223),n.e(58795),n.e(9773),n.e(36556),n.e(40537),n.e(16471),n.e(17230),n.e(23740),n.e(62475),n.e(3151),n.e(98629),n.e(33019),n.e(71621),n.e(92845),n.e(84468),n.e(75078),n.e(42660),n.e(34359),n.e(44934),n.e(21194),n.e(66320),n.e(63479),n.e(36639),n.e(48307),n.e(14310),n.e(28398),n.e(55850),n.e(94562),n.e(11135),n.e(65351),n.e(63840),n.e(3263),n.e(48071)]).then(n.bind(n,815348)))));(0,o()._h)(p,(e=>e.ActivityDigestPostContainer))},734459:(e,t,n)=>{n.d(t,{Zf:()=>i,ho:()=>s,re:()=>c});var o=()=>n(775084),r=()=>n(939768),a=()=>n(427704);const i="new";function s(e){const t=`${a().JZ.settingsConsoleOrganization}{/:organizationId}`;return e?`${t}${e.pattern}`:t}function c(e){const{queryParams:t,tabRoute:n,organizationId:a}=e,i=e.properties??{};if(n){const e=s(n),c=(0,o().wE)(e)({...i,organizationId:a});return t?(0,r().Gm)({url:c,query:t}):c}const c=s();return(0,o().wE)(c)({organizationId:a})}},735270:(e,t,n)=>{n.d(t,{i:()=>i});n(898992),n(354520),n(803949);var o=()=>n(430476),r=()=>n(68336),a=()=>n(469425);class i{constructor(e){this.debug=!1,this.log=void 0,this.preconditionSchemaCheck=void 0,this.migrationsCompleted=void 0,this.args=e,this.preconditionSchemaCheck=this.getPreconditionSchemaCheckSql(e.migrations.endSchema.pragmas.user_version),this.migrationsCompleted=void 0,this.log=this.args.log??n(857639).log,this.debug=this.args.debug??!1,(0,n(604341).exposeDebugValue)("sqlite",this)}getPreconditionSchemaCheckSql(e){return(0,n(720665).hr)(`\n\t\t\tSELECT\n\t\t\t\tCASE user_version\n\t\t\t\tWHEN ${e} THEN 1\n\t\t\t\tELSE 0 END AS precondition_result\n\t\t\t\tFROM pragma_user_version() LIMIT 1\n\t\t`)}async ensureMigrated(){if(!this.migrationsCompleted){const e=performance.now();this.migrationsCompleted=(0,a().p6)({connection:this.args.connection,target:this.args.migrations,log:this.log}).then((()=>{n(567417).default.setState({startTime:e,endTime:performance.now()})}))}await this.migrationsCompleted}async optimize(){const e=[r().F4`PRAGMA analysis_limit=40000`.asRead(),r().F4`PRAGMA optimize`.asRead()];await(0,o().G2)({connection:this.args.connection,statements:e,queryName:"optimize"})}reinitialize(e){this.args=e,this.preconditionSchemaCheck=this.getPreconditionSchemaCheckSql(e.migrations.endSchema.pragmas.user_version),this.migrationsCompleted=void 0}async execSqliteBatch(e){let t,o;if(this.debug){o=performance.now(),console.groupCollapsed(`SqliteConnectionManager.execSqliteBatch ${e.queryName??"unknown"}`);const t=e.body.filter((e=>"BEGIN"!==e.sql&&"COMMIT"!==e.sql));t.forEach(((e,n)=>{t.length>1&&console.log(`--- #${n+1} ---`),console.log(e.sql),console.log(e.args)})),console.log("batch",e)}switch(this.args.type){case"v1":await this.ensureMigrated(),t=await this.args.connection.execSqliteBatch(e);break;case"v2":await this.ensureMigrated(),t=await this.args.connection.execSqliteBatchV2({batch:e,precondition:{sql:this.preconditionSchemaCheck,getData:!0}});break;default:(0,n(534177).HB)(this.args)}return this.debug&&(console.log("result",t),console.log("duration",performance.now()-(o??0)),console.log("to disable: localStorage.sqlitedebug = false"),console.groupEnd()),t}async completelyRebuildSqliteDb(){await this.args.connection.completelyRebuildSqliteDb(),this.migrationsCompleted=void 0,await this.ensureMigrated()}debugSchema(){return(0,a().CY)(this.args.connection)}async debugQuery(e,t){const n="debug query round-trip from JS";console.time(n);const r=await(0,o().qU)({connection:this.args.connection,sql:e,args:t});return console.timeEnd(n),r}async debugTransaction(e){const t="debug transaction round-trip from JS";console.time(t);const n=await(0,o().G2)({connection:this.args.connection,statements:e});return console.timeEnd(t),n}async debugDeleteAllDataAndResetDatabase(){console.log("Resetting database");const e=(0,a().XI)(r().F4,this.args.migrations.endSchema);await(0,o().G2)({connection:this.args.connection,statements:e})}}},740456:(e,t,n)=>{n.d(t,{Nl:()=>q,QM:()=>V,VJ:()=>L,Xd:()=>O,b8:()=>z,fX:()=>F,fn:()=>j,getPredictedRootRedirectPageForPrefetch:()=>K,h2:()=>J,h6:()=>U,hK:()=>R,i6:()=>H,iO:()=>D,nD:()=>x,qk:()=>B,ro:()=>W,s1:()=>M,v8:()=>$});n(944114),n(898992),n(823215);var o=n.n(n(625473)),r=()=>n(726637),a=()=>n(17022),i=()=>n(105751),s=()=>n(857639),c=()=>n(206267),l=()=>n(851941),d=()=>n(763884),u=()=>n(436604),p=()=>n(386164),m=()=>n(496603),f=()=>n(763824),g=()=>n(498212),b=()=>n(680091),h=()=>n(865085),v=()=>n(459225),_=()=>n(705059),y=()=>n(321098),w=()=>n(671887),S=()=>n(906551),k=()=>n(754704),A=()=>n(807054),C=()=>n(71509),I=()=>n(100875),P=()=>n(347320),T=()=>n(554493);function E(e){return{url:e.url,method:e.method,format:e.format,headers:(0,m().cJ)(e.headers,[u().B3]),data:e.data?(0,m().cJ)(e.data,["omitExistingRecordVersions","page.spaceId","returnPropertyResultCache"]):void 0}}function R(e){const t=E(e);return(0,g().gB)(o()(t))}class D{constructor(){this.cache=new Map,this.cacheAnalytics=new Map}getPrefetchAnalytic(e){return this.cacheAnalytics.get(e)}async prefetchMultiCellHttpRequest(e,t,n){const o=(0,k().createApiHttpJsonRequestOptions)({...n}),r=await this.prefetchHttpRequest(e,o);if("failed"===r.type||!r.data.fanoutData)return;const a=async o=>{const r=await f().lX(o,10,(async o=>{const r=(0,k().createApiHttpJsonRequestOptions)({environment:n.environment,eventName:t,data:o.request,activeUserId:n.activeUserId,tracking:n.tracking,abortSignal:n.abortSignal,encoding:n.encoding,eventListeners:n.eventListeners,headers:o.headers}),a=R(r),i=`${e}-${a}`;return this.prefetchHttpRequest(i,r)})),i=[];for(const e of r)e&&"success"===e.type&&e.data.fanoutData&&e.data.fanoutData.length&&i.push(...e.data.fanoutData);i.length&&await a(i)};await a(r.data.fanoutData)}prefetchHttpRequest(e,t){const o=new AbortController;t.abortSignal=o.signal;const r=performance.now(),a=(0,n(974233).default)(t),i={request:t,responsePromise:a,abortController:o,responseStatus:"loading",requestStartTime:r,responseEndTime:void 0};return this.cache.set(e,i),this.cacheAnalytics.set(e,{type:"attempted",started_at:r}),a.then((t=>{this.cache.get(e)&&this.cache.set(e,{...i,responseStatus:t.type,responseEndTime:performance.now()})})),a}logPageUnloadBeforeRequestCompletion(e,t){function n(){s().log({level:"warning",from:"prefetchHelpers",type:"pageUnloadedBeforePrefetchCompleted",data:{message:`Prefetch request for "${e}" was still in-flight when page was unloaded.`,prefetchKey:e},keepalive:!0})}window.addEventListener("unload",n),t.finally((()=>{window.removeEventListener("unload",n)}))}logIfKeyIsNeverConsulted(e){const t=Date.now();setTimeout((()=>{if(this.cache.has(e)){const n=Date.now()-t;s().log({level:"warning",from:"prefetchHelpers",type:"prefetchKeyNeverConsulted",data:{message:`Prefetch request for "${e}" was not consulted after ${n} ms.`,prefetchKey:e},keepalive:!0})}}),Number(n(720665).Xb))}getPrefetchedHttpRequest(e){const{key:t,request:n,abortSignal:o,isMobileNative:r}=e,a=this.cache.get(t);if(a){this.cache.delete(t);const e=function(e){const t=E(e.request),n=E(e.cachedRequest);if(t.url!==n.url)return"url";if(t.method!==n.method)return"method";if(t.format!==n.format)return"format";if(!(0,m().n4)(t.headers,n.headers))return"headers";if(!(0,m().n4)(t.data,n.data))return"data"}({request:n,cachedRequest:a.request});if(!e){const e={type:"hit-match",started_at:a.requestStartTime,finished_at:a.responseEndTime};return this.cacheAnalytics.set(t,e),o&&function(e,t){if(e.aborted)return void t.abort();e.addEventListener("abort",(()=>{t.abort()}))}(o,a.abortController),a.responsePromise}{var i,c;s().log({level:"info",from:"prefetchHelpers",type:"prefetchHitMismatch",data:{message:`Got cache hit but it's mismatched for prefetch key "${t}". Mismatch type: ${e}`,prefetchKey:t,mismatchType:e,incomingRequest:n[e],cachedRequest:a.request[e]}});const o=r?{type:"hit-mismatch",mismatch_type:e,cached_page_id:null===(i=a.request[e])||void 0===i||null===(i=i.page)||void 0===i?void 0:i.id,incoming_page_id:null===(c=n[e])||void 0===c||null===(c=c.page)||void 0===c?void 0:c.id,started_at:a.requestStartTime,finished_at:a.responseEndTime}:{type:"hit-mismatch",mismatch_type:e,started_at:a.requestStartTime,finished_at:a.responseEndTime};this.cacheAnalytics.set(t,o)}}else if(!this.cacheAnalytics.has(t)){const e={type:"miss"};this.cacheAnalytics.set(t,e)}}getPrefetchStatus(e){var t;return null===(t=this.cache.get(e))||void 0===t?void 0:t.responseStatus}}async function M(e){const{prefetchCache:t,environment:n,activeUserId:r,blockId:s,omitExistingRecordVersions:d}=e;if(await(0,i().getHtmlStreamQueueEntry)("serverSidePrefetchDataPending"))return;if(r&&h().OPFSBootupRegistry.isEnabled&&h().OPFSBootupRegistry.isPageInCache)return;(0,A().updateLoadCachedPageChunkCalledAt)();const m=(0,l().dR)(e.blockId),_="loadCachedPageChunkV2",S=y().g.state?c().JW():void 0;(0,C().w)(s,S);const I={environment:n,eventName:_,data:{page:{id:s,spaceId:void 0},cursor:{stack:[]},verticalColumns:n.device.isMobile,...d?{omitExistingRecordVersions:d}:{},...S?{dedupeSessionId:S}:{}},activeUserId:r,tracking:void 0,headers:m?{[u().eG]:`${m}`}:void 0,eventListeners:(0,v().getPerformanceEventListeners)({eventName:_,isPrefetchRequest:!0})};let P=3;const T=t.prefetchHttpRequest(`loadCachedPageChunk(${I.data.page.id})`,(0,k().createApiHttpJsonRequestOptions)(I)),E=await T,R=[];"success"===E.type&&(R.push(...E.data.cursors),function(e){const t=performance.now(),{pageChunk:n}=e,o=n.recordMap;if(!Object.keys(o.collection_view??{}).length)return;F();const r=performance.now();b().X.setProcessingTime(r-t)}({environment:n,pageId:s,pageChunk:E.data}));let D=E;for(;P>0&&R.length>0;){const e=[...R];R.length=0,await f().lX(e,5,(async e=>{if(e.stack.length>0){const n=(0,g().gB)(o()(e)),r=(0,p().WS)({cellId:e.cellId}),i={...I,data:{...I.data,cursor:e},headers:r};D=await t.prefetchHttpRequest(`loadCachedPageChunk(${I.data.page.id})-${n}`,(0,k().createApiHttpJsonRequestOptions)(i)),a().e.withListenerIgnored((()=>{"success"===D.type&&D.data.spaceId&&D.data.spaceShortId&&w().D.setState({...w().D.state,[D.data.spaceId]:D.data.spaceShortId})})),"success"===D.type&&R.push(...D.data.cursors)}})),P--}return D}function q(e){const{prefetchCache:t,environment:n,activeUserId:o,data:r}=e;d().K.get({userId:e.activeUserId,key:(0,T().H)("prefetchGetSpaces")})?t.prefetchMultiCellHttpRequest("getSpaces","getSpacesFanout",{environment:n,eventName:"getSpacesInitial",data:r,activeUserId:o,tracking:void 0,eventListeners:(0,v().getPerformanceEventListeners)({eventName:"getSpaces",isPrefetchRequest:!0})}):t.prefetchHttpRequest("getSpaces",(0,k().createApiHttpJsonRequestOptions)({environment:n,eventName:"getSpaces",data:r,activeUserId:o,tracking:void 0,eventListeners:(0,v().getPerformanceEventListeners)({eventName:"getSpaces",isPrefetchRequest:!0})}))}function O(e){const{prefetchCache:t,environment:n,activeUserId:o,data:r}=e;t.prefetchHttpRequest("getUserSharedPagesInSpace",(0,k().createApiHttpJsonRequestOptions)({environment:n,eventName:"getUserSharedPagesInSpace",data:r,activeUserId:o,tracking:void 0,eventListeners:(0,v().getPerformanceEventListeners)({eventName:"getUserSharedPagesInSpace",isPrefetchRequest:!0})}))}function B(e){const{prefetchCache:t,environment:n,activeUserId:o,collectionViewId:r}=e,a=(0,P().A)(r),i=Q(a);i&&t.prefetchHttpRequest(a,(0,k().createApiHttpJsonRequestOptions)({environment:n,eventName:"queryCollection",data:i,activeUserId:o,tracking:{src:"initial_load"},abortSignal:void 0,eventListeners:(0,v().getPerformanceEventListeners)({eventName:"queryCollection",isPrefetchRequest:!0}),headers:(0,p().WS)({spaceId:i.source.spaceId})}))}function x(e){const t=Object(Q(P().O));if(!t)return[];const n=t[e];return n&&Array.isArray(n.v)?n.v??[]:[]}const N={SidebarComponent:n(362749).lA.SidebarComponent,CollectionViewBlock:n(646447).H6.CollectionViewBlock,personalHome:n(117296).KM.personalHome,posts:n(732430).QF,teamHome:n(3243).tq.TeamHome,aiChatView:n(277942).s7,BlockPropertyRouter:n(239391).J};function L(){r().A.isMobile||N.SidebarComponent.load()}function U(){N.BlockPropertyRouter.load()}function F(){N.CollectionViewBlock.load()}function V(e){N.personalHome.load(),e.unifiedFeedEnabled&&N.posts.load(),e.aiChatEnabled&&N.aiChatView.load()}function j(){N.teamHome.load(),N.posts.load()}function J(){return{route:Q("lastVisitedRoute"),userId:Q("lastVisitedRouteUserId"),spaceId:Q("lastVisitedRouteSpaceId")}}function H(e){if(!e)return{isHome:!1,homeCollectionViewIds:[],unifiedFeedEnabled:!1,aiChatEnabled:!1};const{isHomeKey:t,homeCollectionViewIdsKey:n,unifiedFeedEnabledKey:o,aiChatEnabledKey:r}=(0,_().h)(e);return{isHome:Boolean(Q(t)),homeCollectionViewIds:Q(n)??[],unifiedFeedEnabled:Boolean(Q(o)),aiChatEnabled:Boolean(Q(r))}}function W(e){if(!e)return{isTeamHome:!1,teamHomeCollectionViewIds:[]};const{teamHomeCollectionViewIdsKey:t}=(0,S().k)(e);return{isTeamHome:!0,teamHomeCollectionViewIds:Q(t)??[]}}function $(e){let{userId:t,gates:n}=e;localStorage.removeItem(Z(t));const o=G(t);Object.values(n).every((e=>!e))?localStorage.removeItem(o):function(e,t){localStorage.setItem(`LRU:KeyValueStore2:${e}`,JSON.stringify({timestamp:Date.now(),value:t,important:!0}))}(o,n)}function z(e){if(!e)return{alwaysPrefetchGetSpaces:!1,prefetchSharedAndPrivatePages:!1,enableSidebarStateCache:!1,enableStrictRenderCompletion:!1};const t=Q(G(e)),n=Q(Z(e));return{alwaysPrefetchGetSpaces:Boolean(null==t?void 0:t.alwaysPrefetchGetSpaces)||Boolean(n),prefetchSharedAndPrivatePages:Boolean(null==t?void 0:t.prefetchSharedAndPrivatePages),enableSidebarStateCache:Boolean(null==t?void 0:t.enableSidebarStateCache),enableStrictRenderCompletion:Boolean(null==t?void 0:t.enableStrictRenderCompletion)}}function Z(e){return`prefetchGetSpaces:${e}`}function G(e){return`sidebarLatencyGates:${e}`}function K(){const{route:e,userId:t,spaceId:n}=J(),o=(0,I().S9)({userId:t,spaceId:n}),r=o?Q(o):"last_visited_page";switch(r){case"personal_home":return{blockId:(0,g().gB)(`${t}-${n}-main`),collectionViewId:void 0,teamId:void 0,userId:t};case"chat":return{blockId:void 0,collectionViewId:void 0,teamId:void 0,userId:t};case"first_page":const o=(0,I().qm)({userId:t,spaceId:n});return{blockId:o?Q(o):void 0,collectionViewId:void 0,teamId:void 0,userId:t};case"last_visited_page":case void 0:return"team"===(null==e?void 0:e.name)?{blockId:void 0,collectionViewId:void 0,teamId:e.teamId,userId:t}:"page"===(null==e?void 0:e.name)?{blockId:e.blockId,collectionViewId:e.collectionViewId,teamId:void 0,userId:t}:{blockId:void 0,collectionViewId:void 0,teamId:void 0,userId:t};default:return{blockId:void 0,collectionViewId:void 0,teamId:void 0,userId:t}}}function Q(e){const t=localStorage.getItem(`LRU:KeyValueStore2:${e}`);if(!t)return;const n=JSON.parse(t);if(!n)return;return n.value}},745875:(e,t,n)=>{n.d(t,{Io:()=>r,JZ:()=>d,Yo:()=>c,fp:()=>l,ox:()=>i,rQ:()=>a});var o=n.n(n(883503));function r(e,t){return Math.sqrt(Math.pow(e[0]-t[0],2)+Math.pow(e[1]-t[1],2))}function a(e){return!(e<=0)&&(e>=100||100*Math.random()<e)}function i(e){return!(e<=0)&&(e>=1||(arguments.length>1&&void 0!==arguments[1]?arguments[1]:s)()<e)}function s(){return Math.random()}function c(e,t){if(t<=0)return!1;if(t>=100)return!0;const n=function(e,t){return parseInt(o()(e).slice(0,8),16)/4294967295*t}(e,100);return n<t}function l(e,t){return c(e,100*t)}function d(e){if(0===e.length)return NaN;const t=Math.floor(e.length/2),n=[...e].sort(((e,t)=>e-t));return e.length%2!=0?n[t]:(n[t-1]+n[t])/2}},754704:(e,t,n)=>{n.r(t),n.d(t,{buildQueryParametersForTracking:()=>c,createApiHttpJsonRequestOptions:()=>u,createApiHttpRequestOptions:()=>p,getTrackingHeaders:()=>l});n(814603),n(147566),n(198721);var o=()=>n(726637);const r="x-notion-active-user-header",a="notion-audit-log-platform";var i=()=>n(711059),s=()=>n(520348);function c(e){let t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:Date.now();const n=new URLSearchParams;null!=e&&e.queuedTimestamp&&n.set("throttledMs",(t-e.queuedTimestamp).toString()),null!=e&&e.src&&n.set("src",e.src),null!=e&&e.trigger&&n.set("trigger",e.trigger);const o=n.toString();return o?`?${o}`:""}function l(e){const t={};return null!=e&&e.userFlow&&(t["X-Notion-User-Flow"]=null==e?void 0:e.userFlow),t}function d(e){const{device:t}=e,n={};return t.version&&(n[i().ClientVersionHeader]=t.version),t.desktopAppVersion&&(t.isMac?n[i().MacVersionHeader]=t.desktopAppVersion:t.isWindows&&(n[i().WindowsVersionHeader]=t.desktopAppVersion)),t.mobileAppVersion&&(t.isIOS?n[i().IOSVersionHeader]=t.mobileAppVersion:t.isAndroid&&(n[i().AndroidVersionHeader]=t.mobileAppVersion)),t.auditLogPlatform&&(n[a]=t.auditLogPlatform),n}function u(e){return p({...e,format:"json"})}function p(e){const{environment:t,eventName:n,data:a,activeUserId:i,tracking:u,abortSignal:p,format:m,encoding:f,eventListeners:g,headers:b}=e,h={environment:t,url:`${o().A.api.http}/${n}${c(u)}`,method:"POST",data:a,format:m,headers:{...d(t),...l(u),[r]:i||"",..."defaultRecordCache"in t&&"currentUser"in t?s().L8(t):{},...s().P4(),...b},abortSignal:p,encoding:f,eventListeners:g};return null!=b&&b["x-notion-space-short-id"]&&delete b["x-notion-space-id"],h}},763824:(e,t,n)=>{n.d(t,{Il:()=>v,Mi:()=>w,O4:()=>u,TA:()=>k,Vq:()=>g,aN:()=>_,bT:()=>a,dS:()=>y,e2:()=>h,lG:()=>p,lX:()=>c,mO:()=>d,nQ:()=>f,qt:()=>S,vA:()=>s,wR:()=>l,xz:()=>b,yL:()=>i,yX:()=>m});n(16280),n(944114),n(898992),n(354520),n(581454),n(759848);var o=()=>n(532659),r=()=>n(882362);function a(){return new Promise((e=>setImmediate(e)))}function i(e){return Boolean(e&&("object"==typeof e||"function"==typeof e)&&"then"in e&&"function"==typeof e.then)}function s(e,t,n){return new Promise(((o,r)=>{let a=0;const i=[],s=()=>{const c=e.slice(a,a+t);a+=t,c.length>0?n(c).then((e=>{i.push(e),setImmediate(s)})).catch(r):o(i)};s()}))}async function c(e,t,n){if(t<=0)throw new Error(`Invalid concurrency limit: ${t}`);let r;if(Array.isArray(e)){if(e.length<=t){const t=(e,t)=>n(e,t,t);return Promise.all(e.map(t))}r=new Array(e.length)}else r=[];const a=o().jY.withIndex(e)[Symbol.iterator]();let i=!1;const s=async e=>{try{for(;!i;){const t=a.next();if(t.done)return;const[o,i]=t.value,s=await n(i,o,e);r[o]=s}}catch(t){throw i=!0,t}},c=[];for(let o=0;o<t;o++)c.push(s(o));return await Promise.all(c),r}function l(e){let t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:r().u;return new Promise((n=>{t.setTimeout((()=>{n()}),e)}))}function d(e,t){return new Promise((n=>{setTimeout((()=>{n(t)}),e)}))}async function u(e){const t=m(),n=Promise.all(e.map((async(e,n)=>{await e,t.resolve(n)})));return{winner:await t.promise,rest:n}}function p(e){return Promise.race(e).then((e=>({status:"fulfilled",value:e})),(e=>({status:"rejected",reason:e})))}function m(){let e,t;const n=new Promise(((n,o)=>{e=n,t=o}));return{resolve:e,reject:t,promise:n}}async function f(e,t){let n;const o=new Promise((t=>{n=setTimeout((()=>{n=void 0,t({result:void 0,timeout:!0})}),e)})),r=i(t)?t:Promise.race(t);return await Promise.race([o,r.then((e=>({result:e,timeout:!1}))).finally((()=>{n&&clearTimeout(n)}))])}function g(e,t){return f(t,e)}async function b(e,t,n){const o=await g(n(),t);return e<=1||!o.timeout?o:b(e-1,t,n)}class h{constructor(){let e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:r().u;this.deferredPromise=m(),this.isCompleted=!1,this.timeSource=e}async wait(e,t){e>0&&await l(e,this.timeSource);const n=t-e;n>0&&await Promise.race([this.deferredPromise.promise,l(n,this.timeSource)]),this.isCompleted||(this.isCompleted=!0,this.deferredPromise.resolve(void 0))}trigger(){this.isCompleted||this.deferredPromise.resolve(void 0),this.isCompleted=!0}}class v{constructor(e){this._state=void 0,this.runTask=e,this._state={status:"idle"}}get status(){return this._state.status}get state(){return this._state}get elapsedMs(){return"pending"===this._state.status?performance.now()-this._state.startedAt:"resolved"===this._state.status?this._state.resolvedAt-this._state.startedAt:"rejected"===this._state.status?this._state.rejectedAt-this._state.startedAt:void 0}get settledAt(){return"resolved"===this._state.status?this._state.resolvedAt:"rejected"===this._state.status?this._state.rejectedAt:void 0}get result(){return"resolved"===this._state.status?{value:this._state.value}:"rejected"===this._state.status?{error:this._state.error}:void 0}async runImpl(e){const t=performance.now();try{const n=this.runTask(e);this._state={status:"pending",startedAt:t,promise:n};const o=await n;return this._state={status:"resolved",value:o,startedAt:t,resolvedAt:performance.now()},o}catch(o){throw this._state={status:"rejected",error:(0,n(319625).A)(o),startedAt:t,rejectedAt:performance.now()},o}}async runWithRetry(){const e=this._state;return"rejected"===e.status?await this.runImpl(e.error):await this.run()}async run(){const e=this._state;switch(e.status){case"idle":return await this.runImpl();case"pending":return await e.promise;case"resolved":return e.value;case"rejected":throw e.error;default:(0,n(534177).HB)(e)}}async getPendingOrResolved(){const e=this._state;switch(e.status){case"pending":return await e.promise;case"resolved":return e.value;default:return}}}async function _(e){const t=await Promise.allSettled(e),n=[];for(const o of t){if("rejected"===o.status)throw o.reason;n.push(o.value)}return n}async function y(e,t,n){return c(e,t,n).then((t=>e.filter(((e,n)=>t[n]))))}function w(e){return async function(t){return await e(t),t}}async function S(e){return new Promise(((t,n)=>{let o="pending";e((e=>{o="resolved",t(e)}),(e=>{o="rejected",n(e)})).catch((e=>{"pending"===o?n(e):console.warn(`PromiseUtils.promise: Async promise executor threw after promise was already ${o}`,e)}))}))}function k(){return Promise.resolve()}},763884:(e,t,n)=>{n.d(t,{K:()=>r});var o=()=>n(419494);const r=new class{constructor(e,t){this.localStorageStore=void 0,this.localStorageStore=new(o().Ay)({namespace:e,important:t,trackingType:"necessary"})}get(e){let{userId:t,key:n}=e;return this.localStorageStore.get(this.makeUserKey(t,n))}set(e){let{userId:t,key:n,value:o}=e;return this.localStorageStore.set(this.makeUserKey(t,n),o)}remove(e){let{userId:t,key:n}=e;return this.localStorageStore.remove(this.makeUserKey(t,n))}clearStorageForUser(e){this.localStorageStore.scan((t=>{t.startsWith(e)&&this.remove({userId:e,key:t})}))}scan(e){this.localStorageStore.scan(e)}makeUserKey(e,t){return`${e||"guest"}:${t}`}}(o().cd,!1)},765957:(e,t,n)=>{n.r(t),n.d(t,{AppStoreCurrentSpaceStore:()=>d,AppStoreCurrentSpaceViewStore:()=>u,AppStoreCurrentUserSettingsStore:()=>m,AppStoreMainEditorCurrentBlockStore:()=>p,SIDEBAR_RENDER_DEADLINE_MS:()=>i,default:()=>l,isTransitioningSpaces:()=>a});n(517642),n(658004),n(733853),n(845876),n(432475),n(515024),n(731698),n(898992),n(354520);var o=()=>n(496506),r=()=>n(534177);function a(e){switch(e){case"notTransitioning":return!1;case"joiningOrCreatingSpace":case"switchingToOrLoadingSpace":return!0;default:(0,r().HB)(e)}}const i=1e3;class s extends(()=>n(757695))().Store{getInitialState(){return{initialized:!1,spaceTransitionStatus:"notTransitioning",renderPhase:"booting",pageVisitStore:new(n(547726).A),presenceStore:new(n(650031).A),inAppCalloutStore:new(n(841946).A),pendingCriticalRenders:new Set(["page"])}}waitUntilRendered(){return this.waitUntil((()=>this.isInitialRenderComplete()))}setPageRendered(){this.removeCriticalRender("page"),this.state.pendingCriticalRenders.has("sidebar")&&setTimeout((()=>{this.state.pendingCriticalRenders.has("sidebar")&&this.removeCriticalRender("sidebar")}),i)}setSidebarRendered(){this.removeCriticalRender("sidebar")}addPendingCriticalRender(e){this.setState({...this.state,pendingCriticalRenders:new Set([...this.state.pendingCriticalRenders,e])})}removeCriticalRender(e){const t=new Set(this.state.pendingCriticalRenders.values().filter((t=>t!==e))),n=0===t.size?"rendered":this.state.renderPhase;this.setState({...this.state,pendingCriticalRenders:t,renderPhase:n})}isPageRendered(){return!this.state.pendingCriticalRenders.has("page")}isInitialRenderComplete(){return"rendered"===this.state.renderPhase}}const c=new s,l=c,d=new(o().ComputedStore)((()=>c.getState().currentSpaceStore),{debugName:"AppStore.CurrentSpaceStore"}),u=new(o().ComputedStore)((()=>c.getState().currentSpaceViewStore),{debugName:"AppStore.CurrentSpaceViewStore"}),p=new(o().ComputedStore)((()=>c.getState().mainEditorCurrentBlockStore),{debugName:"AppStore.MainEditorCurrentBlockStore"}),m=new(o().ComputedStore)((()=>c.getState().currentUserSettingsStore),{debugName:"AppStore.CurrentUserSettingsStore"})},787212:(e,t,n)=>{n.r(t)},796237:(e,t,n)=>{n.d(t,{D:()=>i,l:()=>a});var o=n.n(n(285914)),r=n.n(n(190031));function a(e){const t=r().encode(e);return o().encode(t)}function i(e){const t=o().decode(e);return r().decode(t)}},798880:(e,t,n)=>{n.d(t,{Ay:()=>i,BF:()=>c,EP:()=>f,MF:()=>p,O5:()=>d,ai:()=>m,hR:()=>g,mc:()=>l,oo:()=>a,zz:()=>u});var o=()=>n(498212),r=()=>n(292588);const a="notion_user",i={table:a,columnTypes:{id:r().A.UUID,version:r().A.Number,last_version:r().A.Number,email:r().A.String,given_name:r().A.String,family_name:r().A.String,profile_photo:r().A.String,onboarding_completed:r().A.Boolean,mobile_onboarding_completed:r().A.Boolean,clipper_onboarding_completed:r().A.Boolean,reverify:r().A.Boolean,name:r().A.String,is_banned:r().A.Boolean,suspended_time:r().A.Number},model:(0,n(152853).P)({RecordStore:!0,interfaces:{ActorModelInterface:"@notionhq/shared/models/ActorModelInterface"},properties:{admin_user:{getMethod:!1,getKeyStoreMethod:!1},global_oauth_client:{getMethod:!1,getKeyStoreMethod:!1},given_name:{getMethod:!1},family_name:{getMethod:!1},name:{getMethod:!1}}})},s="https://s3.us-west-2.amazonaws.com/public.notion-static.com/5478707d-f8ea-46d8-902a-d6d932ac20da.png",c={id:o().rN,version:0,email:"<EMAIL>",admin:!0,name:"Notion App",profile_photo:s},l={id:o().zj,version:0,email:"<EMAIL>",admin:!0,name:"Notion Support",profile_photo:s};o().bC;function d(e){return e===c.id}const u={id:o().TC,version:0,email:"<EMAIL>",admin:!0,name:"Notion AI",profile_photo:"https://s3-us-west-2.amazonaws.com/public.notion-static.com/7b0ea76e-4936-406d-8896-ae8bce45615d/image_1-2.png"};function p(e){return e===u.id}const m={id:o().$h,version:0,email:"<EMAIL>",admin:!0,name:"Notion App",profile_photo:s},f={id:o().$4,version:0,email:"<EMAIL>",admin:!1,name:"Notion",profile_photo:s},g={id:o().OB,version:0,email:"<EMAIL>",admin:!0,name:"Anonymous",profile_photo:s}},806080:(e,t,n)=>{n.d(t,{i:()=>o,s:()=>r});n(517642),n(658004),n(733853),n(845876),n(432475),n(515024),n(731698);function o(e,t){if(0===e.length||t<=0)return[];const n=new Array(Math.ceil(e.length/t));for(let o=0,r=0;o<e.length;o+=t,r++)n[r]=e.slice(o,o+t);return n}function r(e){return Array.from(new Set(e))}},807054:(e,t,n)=>{n.r(t),n.d(t,{getDesktopLoadOrigin:()=>b,getFilteredSubMetrics:()=>l,getSubMetricsAndMetricDataForTracking:()=>c,getTimeOriginToAppStartSubMetrics:()=>p,isPrewarmedTabInitialRender:()=>m,setDesktopLoadContext:()=>g,updateLoadCachedPageChunkCalledAt:()=>d,waitForInitialPageRenderToComplete:()=>u});var o=()=>n(410690),r=()=>n(361242),a=()=>n(534177),i=()=>n(939768),s=()=>n(961581);function c(e){const t={},{subMetricsStore:n,metricDataStore:o,OPFSMetricDataStore:r}=s().A.state;for(const[i,s]of(0,a().WP)(n.state)){if(void 0===s)continue;const{startTime:n,endTime:o}=s;if(e.isPrewarmedTab){if(o<=e.prewarmedTabNavigationStart){t[i]=0;continue}if(n<e.prewarmedTabNavigationStart&&o>e.prewarmedTabNavigationStart){t[i]=o-e.prewarmedTabNavigationStart;continue}}t[i]=o-n}return{sub_metrics:t,opfs_metrics:r.state,...o.state}}function l(){const{page_origin_to_app_start:e,app_start_to_main_start:t,main_start_to_prefetch_initiated:n,prefetch_initiated_to_statsig_bootup_blocking_start:o,statsig_bootup_blocking:r,render_start_to_render_end:a,render_end_to_await_page_chunk_start:i,await_page_chunk_start_to_await_page_chunk_end:c,await_page_chunk_end_to_browser_repaint_start:l}=s().A.state.subMetricsStore.state;return{page_origin_to_app_start:e,app_start_to_main_start:t,main_start_to_prefetch_initiated:n,prefetch_initiated_to_statsig_bootup_blocking_start:o,statsig_bootup_blocking:r,render_start_to_render_end:a,render_end_to_await_page_chunk_start:i,await_page_chunk_start_to_await_page_chunk_end:c,await_page_chunk_end_to_browser_repaint_start:l}}function d(){const{initialLoadCachedPageChunkCalledAt:e}=s().A.state;void 0===e&&s().A.update((e=>({...e,initialLoadCachedPageChunkCalledAt:performance.now()})))}async function u(){await s().A.waitUntil((()=>s().A.state.initialRenderCompleted))}function p(){const e=o().getAppStartTime();if(!e)return;let t;try{const n=performance.getEntriesByType("navigation")[0];t={time_origin_to_fetch_start:n.fetchStart,fetch_start_to_domain_lookup_start:f({from:n.fetchStart,to:n.domainLookupStart}),domain_lookup_start_to_domain_lookup_end:f({from:n.domainLookupStart,to:n.domainLookupEnd}),connect_start_to_secure_connection_start:f({from:n.connectStart,to:n.secureConnectionStart}),secure_connection_start_to_connect_end:f({from:n.secureConnectionStart,to:n.connectEnd}),connect_end_to_request_start:f({from:n.connectEnd,to:n.requestStart}),request_start_to_response_start:f({from:n.requestStart,to:n.responseStart}),response_start_to_response_end:f({from:n.responseStart,to:n.responseEnd}),response_end_to_app_start:f({from:n.responseEnd,to:e})}}catch(n){}return t}function m(e){return e.RouterStore.state.wasPrewarmedTab}function f(e){let{from:t,to:n}=e;return 0===t||0===n?0:n-t}function g(){const e=i().qn(window.location.href,r().Sn),t=i().qn(window.location.href,r().g4);if(!e)return;const n=i().qm(window.location.href,r().Sn),o=i().qm(n,r().g4);window.history.replaceState(window.history.state,"",o),s().A.setDesktopLoadContext(e,t)}function b(e){return e?"new_prewarmed_tab":s().A.state.desktopLoadOrigin}},820103:(e,t,n)=>{n.d(t,{IL:()=>i,JT:()=>o,Rw:()=>a,uL:()=>r});const o="default",r=["samsung_preload_onboarding_flow","partner_program_onboarding_flow","meeting_notes_flow","notion_for_work_flow","form_response_onboarding","education_flow","mail_onboarding","calendar_onboarding","performance_marketing_term_onboarding","mobile_promote_app_download","mobile_tutorial_onboarding",o,"undefined"],a=["mail","calendar"],i=["meeting_notes","notion_for_work","notion_sites_signup"]},821062:(e,t,n)=>{n.d(t,{A:()=>s});n(517642),n(658004),n(733853),n(845876),n(432475),n(515024),n(731698);const o=Object.prototype.toString,r=Object.prototype.hasOwnProperty,{getPrototypeOf:a}=Object,i=Object.prototype;function s(e,t){if(e===t)return!0;if(Array.isArray(e)){if(!Array.isArray(t))return!1;if(e.length!==t.length)return!1;if(0===e.length)return!0;for(let n=0;n<e.length;n++)if(!d(e[n],t[n]))return!1;return!0}if("object"!=typeof e||null===e||"object"!=typeof t||null===t)return!1;if(c(e)){if(!c(t))return!1;const n=Object.keys(e),o=Object.keys(t);if(n.length!==o.length)return!1;for(const a in e)if(r.call(e,a)){if(!r.call(t,a))return!1;const n=a.slice(-5);if("props"===n||"style"===n||"Props"===n||"Style"===n){if(!s(e[a],t[a]))return!1}else if(!d(e[a],t[a]))return!1}return!0}if(e instanceof Set){if(!(t instanceof Set))return!1;if(e.size!==t.size)return!1;if(0===e.size)return!0;const n=e.values();let o;for(;o=n.next(),!o.done;)if(!t.has(o.value))return!1;return!0}if(e instanceof Map){if(!(t instanceof Map))return!1;if(e.size!==t.size)return!1;if(0===e.size)return!0;const n=e.entries();let o;for(;o=n.next(),!o.done;)if(!t.has(o.value[0])||!d(t.get(o.value[0]),o.value[1]))return!1;return!0}return!1}function c(e){if("[object Object]"!==o.call(e))return!1;const t=a(e);return null===t||t===i}function l(e){for(const t in e)if(r.call(e,t))return!1;return!0}function d(e,t){return e===t||(Array.isArray(e)?!(0!==e.length||!Array.isArray(t)||0!==t.length):"object"==typeof e&&null!==e&&!!(c(e)&&l(e)&&"object"==typeof t&&null!==t&&c(t)&&l(t)))}},824862:(e,t,n)=>{n.d(t,{Ay:()=>s,FF:()=>a,GP:()=>i,eR:()=>c});var o=()=>n(498212),r=()=>n(292588);const a={notion_user:!0,space:!0},i="bot",s={table:i,columnTypes:{id:r().A.UUID,version:r().A.Number,last_version:r().A.Number,name:r().A.String,parent_table:r().A.String,parent_id:r().A.UUID,created_at:r().A.Number,created_by_id:r().A.UUID,created_by_table:r().A.String,last_edited_at:r().A.Number,last_edited_by_id:r().A.UUID,last_edited_by_table:r().A.String,alive:r().A.Boolean,type:r().A.String,integration_id:r().A.UUID,icon:r().A.String,space_id:r().A.UUID,capabilities:r().A.JSON},model:(0,n(152853).P)({RecordStore:!0,interfaces:{ActorModelInterface:"@notionhq/shared/models/ActorModelInterface"}})},c={id:o().rL,version:0,name:"Automation",type:"automation",created_at:0,created_by_id:o().rN,created_by_table:"notion_user",space_id:o().rN,parent_id:o().rN,parent_table:"notion_user",alive:!0,icon:"https://s3.us-west-2.amazonaws.com/public.notion-static.com/5478707d-f8ea-46d8-902a-d6d932ac20da.png",integration_id:n(617871).XF,capabilities:{read_content:!0}}},835243:(e,t,n)=>{n.d(t,{$e:()=>p,DZ:()=>a,aZ:()=>r,n4:()=>u});n(944114),n(581454);var o=()=>n(526147);function r(e){const{spaceId:t}=e;return{table:"record_key",id:(0,o().F3)(e),spaceId:t}}const a=(0,n(959013).YK)({prefixInvalidError:{id:"collectionUniqueIdHelpers.prefixInvalidError",defaultMessage:"The prefix must start with a letter, followed by one or more (up to 9) alphanumeric characters or hyphens."}}),i=10,s=`[A-Z][A-Z0-9-]{1,${i-1}}`,c=new RegExp(`^${s}$`),l=`${s}-[0-9]+`,d=new RegExp(`^${l}$`);new RegExp(`(?<uniqueId>${l})`,"g");function u(e){return c.test(e)}function p(e){return d.test(e)}},836251:(e,t,n)=>{n.d(t,{UL:()=>y,MN:()=>_,wU:()=>S,j5:()=>k,xS:()=>f,hK:()=>A,VY:()=>P,J0:()=>T,Gn:()=>I,J9:()=>C,Al:()=>w,G5:()=>E,ok:()=>g,w6:()=>b,$o:()=>v,nR:()=>h});n(944114),n(814603),n(147566),n(198721);var o=()=>n(726637),r=()=>n(662303);const a=JSON.parse('{"guides.guides":"https://www.notion.com/help","guides.comments":"https://www.notion.com/help/comments-mentions-and-reminders","guides.backlinks":"https://www.notion.com/help/create-links-and-backlinks","guides.notionForTeams":"https://www.notion.com/teams","guides.keyboardShortcuts":"https://www.notion.com/help/keyboard-shortcuts","guides.reinstallMac":"https://www.notion.com/help/notion-for-desktop#mac-desktop-app","guides.reinstallWindows":"https://www.notion.com/help/notion-for-desktop#windows-desktop-app","guides.whyDesktop":"https://www.notion.com/help/notion-for-desktop#why-use-the-desktop-app","guides.upgradeAndroid":"https://www.notion.com/help/notion-for-mobile","guides.upgradeiOS":"https://www.notion.com/help/notion-for-mobile","guides.dataAndIntegrations":"https://www.notion.com/help/category/import-export-and-integrate","guides.import":"https://www.notion.com/help/import-data-into-notion","guides.importEvernote":"https://www.notion.com/help/import-data-into-notion#evernote","guides.importGoogleDocs":"https://www.notion.com/help/import-data-into-notion#google-docs","guides.importQuip":"https://www.notion.com/help/import-data-into-notion#quip","guides.importDropboxPaper":"https://www.notion.com/help/import-data-into-notion#dropbox-paper","guides.importWorkflowy":"https://www.notion.com/help/import-data-into-notion#workflowy","guides.importConfluence":"https://www.notion.com/help/import-data-into-notion#confluence","guides.importAsana":"https://www.notion.com/help/asana","guides.sharing":"https://www.notion.com/help/sharing-and-permissions","guides.invitePerson":"https://www.notion.com/help/add-members-admins-guests-and-groups","guides.formulas":"https://www.notion.com/help/formulas","guides.formulasWhatsChanged":"https://www.notion.com/help/new-formulas-whats-changed","guides.allUpdates":"https://www.notion.com/help/updates-and-notifications","guides.notifications":"https://www.notion.com/help/updates-and-notifications","guides.quickFind":"https://www.notion.com/help/searching-with-quick-find","guides.relations":"https://www.notion.com/help/relations-and-rollups","guides.githubRelation":"https://www.notion.com/help/github","guides.reminders":"https://www.notion.com/help/reminders","guides.trash":"https://www.notion.com/help/duplicate-delete-and-restore-content","guides.pageHistory":"https://www.notion.com/help/duplicate-delete-and-restore-content#version-history","guides.pageHistoryRestore":"https://www.notion.com/help/duplicate-delete-and-restore-content#restore-past-versions-of-a-page","guides.database":"https://www.notion.com/help/intro-to-databases","guides.databaseView":"https://www.notion.com/help/views-filters-and-sorts#view","guides.databaseFilter":"https://www.notion.com/help/views-filters-and-sorts#filter","guides.databaseSort":"https://www.notion.com/help/views-filters-and-sorts#sort","guides.databaseGroup":"https://www.notion.com/help/boards#reorder-columns-&-cards","guides.databaseProperties":"https://www.notion.com/help/database-properties","guides.databaseCalendar":"https://www.notion.com/help/calendars","guides.sidebarHome":"https://www.notion.com/help/home-and-my-tasks","guides.taskDatabases":"https://www.notion.com/help/guides/give-your-to-dos-a-home-with-task-databases","guides.syncedDatabases":"https://www.notion.com/help/guides/synced-databases-bridge-different-tools","guides.syncedContent":"https://www.notion.com/help/link-previews-and-synced-databases","guides.tableOfContents":"https://www.notion.com/help/columns-headings-and-dividers#table-of-contents","guides.billingAndPayment":"https://www.notion.com/help/billing-and-payment-info","guides.billingAndPaymentRevised":"https://www.notion.com/help/category/plans-billing-and-payment","guides.equations":"https://www.notion.com/help/math-equations","guides.profileSettings":"https://www.notion.com/help/account-settings#profile-settings","guides.notificationSettings":"https://www.notion.com/help/notification-settings","guides.referralsAndCredit":"https://www.notion.com/help/billing-and-payment-info","guides.exportWorkspace":"https://www.notion.com/help/workspace-settings","guides.exportMembers":"https://www.notion.com/help/workspace-settings","guides.deleteWorkspace":"https://www.notion.com/help/create-delete-and-switch-workspaces#delete-workspace","guides.members":"https://www.notion.com/help/add-members-admins-guests-and-groups","guides.groups":"https://www.notion.com/help/add-members-admins-guests-and-groups","guides.changeBillingInterval":"https://www.notion.com/help/billing-and-payment-info","guides.databaseTemplates":"https://www.notion.com/help/database-templates","guides.samlSettings":"https://www.notion.com/help/saml-sso-configuration","guides.securitySettings":"https://www.notion.com/help/workspace-settings","guides.scim":"https://www.notion.com/help/provision-users-and-groups-with-scim","guides.imagesFaqs":"https://www.notion.com/help/images-files-and-media","guides.videosFaqs":"https://www.notion.com/help/images-files-and-media","guides.audioFaqs":"https://www.notion.com/help/images-files-and-media","guides.embedsFaqs":"https://www.notion.com/help/embed-and-connect-other-apps","guides.remoteTips":"https://www.notion.com/help/guides/using-notion-for-remote-work","guides.transclusions":"https://www.notion.com/help/synced-blocks","guides.unfurling":"https://www.notion.com/help/embed-and-connect-other-apps#link-previews","guides.joinOrCreateWorkspace":"https://www.notion.com/help/create-delete-and-switch-workspaces#join-an-existing-workspace","guides.autogeneratedDomains":"https://www.notion.com/help/public-pages-and-web-publishing","guides.billingGuide":"https://www.notion.com/help/billing-and-payment-info","guides.connectedApps":"https://www.notion.com/help/embed-and-connect-other-apps","guides.publicAPI":"https://www.notion.com/help/add-and-manage-connections-with-the-api","guides.enterpriseIntegrationSettings":"https://www.notion.com/help/add-and-manage-connections-with-the-api#enterprise-connection-settings","guides.tasksAndProjectsGettingStarted":"https://www.notion.com/help/guides/getting-started-with-projects-and-tasks","templateGallery":"https://www.notion.com/templates","guides.howToSubmitATemplate":"https://www.notion.com/help/guides/the-ultimate-guide-to-notion-templates","community":"https://www.notion.so/notion/04f306fbf59a413fae15f42e2a1ab029","terms":"https://www.notion.so/notion/Terms-and-Privacy-28ffdd083dc3473e9c2da6ec011b58ac","terms.discounts.ai.summer.2023":"https://www.notion.so/notion/Notion-AI-Add-On-Summer-2023-Discount-Terms-and-Conditions-41a44ee5ddef45dd9594fdb599e9f4cf","terms.currency.fxRates":"https://notion.notion.site/Local-Currency-FAQs-bb75546f474a440dbb3447382b9db19b","contentPolicy":"https://www.notion.so/notion/1b9a773d5583486cb5c1d39a8d777a55","guides.enterpriseAdmins":"https://notion.com/help/enterprise-admins","guides.auditLog":"https://www.notion.com/help/audit-log","guides.auditLog.pageEvents":"https://www.notion.com/help/audit-log#audit-log-events","guides.teamspacesLearnMore":"https://www.notion.com/help/guides/teamspaces-give-teams-home-for-important-work","guides.teamspacesWorkspaceOwner":"https://www.notion.com/help/guides/grant-access-teamspaces","guides.teamspacesSettingsAndPermissions":"https://www.notion.com/help/intro-to-teamspaces#modify-teamspace-settings","guides.customizePageView":"https://www.notion.com/help/customize-and-style-your-content#customize-backlinks-&-comments","guides.domainManagement":"https://www.notion.com/help/domain-management","guides.samlJIT":"https://www.notion.com/help/saml-sso-configuration#just-in-time-(jit)-provisioning","guides.workspaceAnalytics":"https://notion.com/help/workspace-analytics","guides.pageAnalytics":"https://notion.com/help/page-analytics","guides.students":"https://www.notion.com/product/notion-for-education","guides.transferContent":"https://www.notion.com/help/transfer-content-deprovisioned-user","guides.duplicateToSpace":"https://www.notion.com/help/transfer-content-to-another-account","guides.mfa":"https://notion.com/help/two-step-verification","adminContentSearch":"https://www.notion.com/help/admin-content-search","ai.fairUsePolicies":"https://www.notion.com/help/ai-pricing-and-usage","ai.terms":"https://notion.notion.site/fa9034c8b5a04818a6baf3eac2adddbb","guides.wikis":"https://notion.com/help/wikis-and-verified-pages","ai.dataSharingTerms":"https://www.notion.so/notion/Notion-Data-Sharing-Terms-3a6400543d2a4aba8b4d1053c6770810","guides.workspaceConsolidation":"https://notion.notion.site/Notion-Workspace-Consolidation-Guide-0eca1f05f2614ff6818c86c3b3fb0357","guides.dataResidencyWorkspaceMigration":"https://notion.notion.site/Notion-Workspace-Migration-for-Data-Residency-1b9efdeead0580cf8390e3d4e0643893","blog.pmLaunch":"https://www.notion.com/blog/new-ai-powered-notion-projects","blog.enterpriseLaunch":"https://www.notion.com/blog/the-next-chapter-in-notion-for-enterprise","blog.notionCalendarLaunch":"https://www.notion.com/blog/introducing-notion-calendar","guides.sprintPlanning":"https://www.notion.com/help/guides/product-engineering-notion-sprint-planning","guides.aiAutofill":"https://www.notion.com/help/autofill","guides.managedUsers":"https://www.notion.com/help/managed-users-dashboard","guides.guestRequests":"https://www.notion.com/help/add-members-admins-guests-and-groups#guests","embed.guides":"https://www.notion.com/embed/help","guides.altText":"https://www.notion.com/help/images-files-and-media#alt-text","guides.externalImportAndSync":"https://www.notion.com/help/jira#synced-databases","guides.twoWaySyncJira":"https://notion.notion.site/Alpha-Jira-2-way-Sync-23eefdeead058051b90df8f45cd29dc9","database.loadTimesAndPerformance":"https://www.notion.com/help/optimize-database-load-times-and-performance","guides.customDataRetentionRules":"https://notion.com/help/custom-data-retention-settings","guides.homeCalendar":"https://www.notion.com/help/home-and-my-tasks#upcoming-events","charts.launchModalPage":"https://notion.notion.site/27827b36230e497ca178d8a763f86d18","guides.charts":"https://www.notion.com/help/charts","guides.chartsVisualizeData":"https://notion.com/help/guides/charts-visualize-data-track-progress-in-notion","layouts.launchModalPage":"https://www.notion.so/notion/Layout-Builder-Beta-Overview-Doc-49149951bff0410fa194b73c952b5982?pvs=4","guides.hipaaCompliancy":"https://www.notion.com/help/hipaa","guides.hipaaBAA":"https://notion.notion.site/Business-Associate-Agreement-909d9f4ccca041b1a23d0fe6e56fa111","guides.forms":"https://www.notion.com/help/forms","product.forms":"https://www.notion.com/product/forms","guides.projects":"https://www.notion.com/product/projects","guides.docs":"https://www.notion.com/product/docs","guides.marketplaceSelling":"https://www.notion.com/help/selling-on-marketplace","resurrectionLearnMore":"https://notion.notion.site/Limited-Time-Plus-Plan-Discount-10cefdeead05800fb455f8c4512aff44","guides.addingTemplateFromMarketplace":"https://www.notion.com/help/finding-templates-on-marketplace#add-a-template","guides.aiConnectors":"https://www.notion.com/help/notion-ai-connectors","guides.aiConnectorGithub":"https://www.notion.com/help/notion-ai-connector-for-github","guides.aiConnectorSlack":"https://www.notion.com/help/notion-ai-connectors-for-slack","guides.aiConnectorJira":"https://www.notion.com/help/notion-ai-connector-for-jira","guides.webhookAction":"https://www.notion.com/help/webhook-actions","guides.passkeys":"https://www.notion.com/help/passkeys","guides.aiConnectorLinear":"https://www.notion.com/help/notion-ai-connector-for-linear","guides.notionAiForWork":"https://notion.com/blog/notion-ai-for-work","guides.meetingNotes":"https://www.notion.com/help/ai-meeting-notes","guides.meetingNotes.saveLocalRecordings":"https://www.notion.com/help/ai-meeting-notes#how-notion-processes-and-stores-captured-audio-and-transcriptions","guides.meetingNotes.consent":"https://www.notion.com/help/ai-meeting-notes#consent-message","guides.aiConnectorMicrosoftTeams":"https://www.notion.com/help/notion-ai-connector-for-microsoft-teams","guides.aiConnectorSharepoint":"https://www.notion.com/help/notion-ai-connector-for-microsoft-sharepoint-and-onedrive","guides.mcpHelp":"https://www.notion.com/help/notion-mcp","guides.mcpConnect":"https://developers.notion.com/docs/get-started-with-mcp#connect-through-your-ai-tool","pricing":"https://www.notion.com/pricing"}');var i=()=>n(861017),s=()=>n(939768),c=()=>n(852100),l=()=>n(155959),d=()=>n(765957),u=()=>n(948914),p=()=>n(982254),m=()=>n(492553);function f(e){return r().J&&r().J[e]?r().J[e]:a[e]}function g(){try{return window.location.host===new URL(o().A.domainBaseUrl).host}catch{}return!1}function b(e){const{RouterStore:t}=e;return h(t.state.route)}function h(e){return Boolean(("page"===e.name||"space"===e.name)&&e.requestedOnPublicDomain)}function v(e){return Boolean(("page"===e.name||"space"===e.name)&&e.requestedOnExternalDomain)}function _(e){const{TabbedRouterStore:t,RouterStore:n}=e;return w(e)?t.canGoForward():e.device.isElectron?m().H.canGoForward():n.canGoForward()}function y(e){const{TabbedRouterStore:t,RouterStore:n}=e;return w(e)?t.canGoBack():e.device.isElectron?m().H.canGoBack():n.canGoBack()}function w(e){const{device:t}=e;return Boolean(o().A.isMobile)&&t.isPhone&&(0,l().T)("supportsWebManagedTabNavigation")}function S(e){const t=(0,i().parseRoute)({url:e,baseUrl:o().A.domainBaseUrl,publicDomainName:o().A.publicDomainName,isMobile:!1,protocol:o().A.protocol,currentUrl:void 0});return"page"===t.name?t.blockId:"team"===t.name?t.peekViewBlockId:void 0}function k(e){return`${o().A.domainBaseUrl}/${e||r().J.login}`}function A(e){const{route:t}=e.RouterStore.state;if(t&&"marketplace"===t.name&&"templates"===t.pageType&&t.slug)return(0,c().sO)(t.slug,t)}function C(e){const{route:t}=e.RouterStore.state;return"team"===t.name}function I(e){const{teamStore:t,environment:n}=e;if(!t)return!1;const{route:o}=n.RouterStore.state;return"team"===o.name&&(null==t?void 0:t.id)===o.teamId}function P(e){const{routeId:t,spaceId:n,formId:o}=e;return(0,s().Gm)({url:f(t),query:{from:"forms",spaceId:n,formId:o}})}function T(e){try{if((0,s().cW)(e))return!0;const t=new URL(e),n=["https://notion.so","https://notion.com","https://notion.site"];"localhost"===window.location.hostname&&n.push("http://localhost:3000");return n.some((e=>(0,s().mJ)(t.href,e)))}catch(t){return!1}}function E(e){var t;let{environment:n,nextRouteOrUrl:r}=e;const a=n.RouterStore.state.route,s=p().A.state.currentTab.isPinned;if(!(null===(t=u().A.state.preferences)||void 0===t?void 0:t.isPinnedTabNewTabEnabled))return!1;if(!s)return!1;let c;if(c="string"==typeof r?(0,i().parseRoute)({url:r,baseUrl:o().A.domainBaseUrl,publicDomainName:o().A.publicDomainName,isMobile:n.device.isMobile,protocol:o().A.protocol,currentUrl:window.location.href}):r,(0,i().WZ)(a)&&("page"===c.name||"chat"===c.name||"team"===c.name)){var l;const e="page"===a.name&&"page"===c.name&&a.blockId===c.blockId,t=Boolean(c.peekViewBlockId),n=Boolean(a.peekViewBlockId&&("chat"===c.name||"team"===c.name||(null===(l=d().default.state.mainEditorCurrentBlockStore)||void 0===l?void 0:l.id)===c.blockId)&&!t);return!e&&!t&&!n}return!1}},838364:(e,t,n)=>{n.d(t,{Hc:()=>a,lS:()=>r,nG:()=>o});const o="reactNativeWebViewSchemeHandlerBody",r=44,a=52},839816:(e,t,n)=>{n.d(t,{II:()=>c,Pb:()=>a,Rv:()=>r,X6:()=>i,dc:()=>o,fr:()=>s});const o=["necessary","preference","performance","targeting"],r=["notion_user_id","notion_users","notion_public_domain_user_id","notion_browser_id","notion_ghost_admin_user_id","notion_ghost_preferred_role","notion_cookie_consent","notion_check_cookie_consent","notion_locale","notion_experiment_device_id","csrf","notion_calendar_csrf","NEXT_LOCALE","file_token","growSumoPartnerKey","notion_s2s_tracking_params","device_id","sync_session","l_sync_session","s_sync_session","d_sync_session","p_sync_session","notion_cookie_sync_completed","notion_test_cookie_sync_completed","notion_sync_user_id","notion_personalization","notion_client_deploy_preview"],a=["l_sync_session","d_sync_session","s_sync_session","p_sync_session"],i={token_v2:"necessary",file_token:"necessary",sync_session:"necessary",l_sync_session:"necessary",d_sync_session:"necessary",s_sync_session:"necessary",p_sync_session:"necessary",notion_user_id:"necessary",notion_users:"necessary",notion_public_domain_user_id:"performance",notion_browser_id:"performance",notion_ghost_admin_user_id:"necessary",notion_ghost_preferred_role:"necessary",notion_cookie_consent:"necessary",notion_check_cookie_consent:"necessary",notion_locale:"necessary",notion_experiment_device_id:"necessary",NEXT_LOCALE:"necessary",eap_csrf:"necessary",sip_csrf:"necessary",csrf:"necessary",notion_calendar_csrf:"necessary",io:"necessary",_fbp:"targeting",growSumoPartnerKey:"performance",notion_s2s_tracking_params:"necessary",notion_cookie_sync_completed:"necessary",notion_test_cookie_sync_completed:"necessary",notion_sync_user_id:"necessary",device_id:"necessary",notion_personalization:"preference",momentic_test_main_cell_outage:"testOnly",momentic_test_canary_cell:"testOnly",notion_client_deploy_preview:"preference"},s={amplitude:"performance",intercom:"preference",zendesk:"preference",statsig:"necessary",clearbit:"targeting",loggly:"necessary",mutiny:"performance",google_tag_manager:"targeting",marketo:"targeting",customer_io:"targeting",partner_stack:"targeting",metadata_io:"targeting",reddit:"targeting",just_words:"targeting"},c={amplitude:["amp_","amplitude_"],intercom:["intercom"],zendesk:["zendesk"],statsig:["STATSIG_"],clearbit:["cb_"],loggly:["loggly"],mutiny:["mutiny","_mutiny"],google_tag_manager:["_gcl_"],marketo:["_mkto_"],customer_io:["_cio"],just_words:[],partner_stack:["fs_uid","_gid","_dw","entry_time","_dwrf","_ga","__ssid","__zlcmid"],metadata_io:["Metadata_"],reddit:["_rdt_"]}},841126:(e,t,n)=>{n.r(t)},841946:(e,t,n)=>{n.d(t,{A:()=>c,z:()=>o});n(16280),n(517642),n(658004),n(733853),n(845876),n(432475),n(515024),n(731698),n(581454);const o={dummy_modal:0,dummy_modal_2:0,wiki_promo:0,ai_assistant_announcement:0,ai_github_ai_connector_announcement:0,ai_jira_universal_qna_announcement:0,ai_microsoft_teams_universal_qna_announcement:0,notion_calendar_launch_promo:0,organization_onboarding_modal:0,posts_onboarding_modal:0,notion_mail_launch_modal:0,notion_mail_launch_modal_2:0,passkey_nudge_modal:0,ai_for_work_announcement_modal:0,meeting_notes_prelaunch_modal:0,referral_announcement:0,expansion_offer_announcement:0,jira_sync_info_popup:0,improve_jira_sync_popup:0,offline_mode_electron_launch_modal:0,offline_mode_web_launch_modal:0,sidebar_tour:1,better_template_initialization:2,asana_post_import_tour:2,ai_slack_qna_embed_tooltip:2,adoption_nux_second_session_database_learning_tooltip:2,onboarding_workflow_template_callout:3,import_data_prompt:3,customer_io:3,add_on_discount_popup:3,ai_onboarding_tooltip:3,ai_sales_assisted_notification_tooltip:3,ai_slack_qna_notification_tooltip:3,ai_google_drive_qna_notification_tooltip:3,ai_assistant_origin_element_tooltip:3,ai_enhanced_qna_notification_tooltip:3,student_ai_offer_popup:3,clear_templates_opt_out:3,open_in_calendar_tooltip:3,desktop_preference_popup:3,cookie_preference_popup:3,dictation_new_page_origin_element_tooltip:3,startup_sidebar_footer:3,sidebar_upgrade_nudge:3,ai_limit_nudge:3,block_limit_nudge:3,student_org_prompt:3,language_switch_prompt:3,desktop_download_sidebar:3,forms_share_form_tooltip:3,forms_add_conditional_logic_tooltip:3,demo_tour:3,post_reverse_trial_sidebar:3,onboarding_reverse_trial_sidebar:3,resurrection_offer_sidebar:3,plus_ai_to_business_upgrade_offer_sidebar:3,customize_slug_tooltip:3,collection_add_item_button_tooltip:3,ai_page_translation:3,workflow_template_installation:3,jira_ai_sidebar_reminder:3,collaboration_callout:3,public_share_link_tooltip:3,self_serve_business_trial_nudge_d0:3,self_serve_business_trial_nudge_d1:3,self_serve_business_trial_nudge_d2:3,self_serve_business_trial_nudge_d3:3,self_serve_business_trial_nudge_d5:3,self_serve_business_trial_nudge_d6:3,self_serve_business_trial_nudge_d8:3,self_serve_business_trial_nudge_d10:3,self_serve_business_trial_nudge_d11:3,self_serve_business_trial_nudge_d14:3,self_serve_business_trial_nudge_d17:3,self_serve_business_trial_nudge_d20:3,self_serve_business_trial_ending_soon:3,search_unified_find_result_tooltip:3,research_mode_chat_history_tooltip:3,anyone_with_the_link_sidebar_callout:3,research_mode_discoverability_tooltip:3,notion_mail_launch_2_callout:3,feed_view_onboarding_tooltip:3,feed_view_onboarding_callout:3,try_notion_for_work_sidebar_callout_revamped:3,get_notified_onboarding_tooltip:3,notion_calendar_existing_user_activation_callout:3,share_referral_link_notification_tooltip:3,offline_mode_education_tooltip:3,offline_mode_sidebar_callout:3},r=["cookie_preference_popup","desktop_preference_popup","student_ai_offer_popup","demo_tour","ai_page_translation","notion_mail_launch_2_callout"],a=["ai_assistant_announcement","notion_calendar_launch_promo","dummy_modal","organization_onboarding_modal","posts_onboarding_modal","notion_mail_launch_modal","notion_mail_launch_modal_2","referral_announcement","expansion_offer_announcement","try_notion_for_work_sidebar_callout_revamped"];class i extends(()=>n(757695))().Store{getInitialState(){return{activeCallouts:new Set,didShowModal:{value:!1,modalShown:null}}}updateCalloutStatus(e){let{calloutId:t,visible:n,validateCanShow:o=!1}=e;const{activeCallouts:r}=this.state;if(n){if(o&&!this.getCalloutVisibility(t).canShow)return!1;const e=new Set(r).add(t);for(const n of r){const o=s(n);s(t)<o&&e.delete(n)}this.setState({activeCallouts:e,didShowModal:{value:this.state.didShowModal.value||a.includes(t),modalShown:a.includes(t)?t:this.state.didShowModal.modalShown}})}else{const e=new Set(r);e.delete(t);this.setState({...this.state,activeCallouts:e})}return!0}getCalloutVisibility(e){const{activeCallouts:t}=this.state;if(t.has(e))return{canShow:!0};if(r.includes(e)&&this.state.didShowModal.value)return{canShow:!1,conflictingKey:e};if(a.includes(e)&&this.state.didShowModal.value&&this.state.didShowModal.modalShown!==e)return{canShow:!1,conflictingKey:e};for(const n of t){if(s(n)<=s(e))return{canShow:!1,conflictingKey:n}}return{canShow:!0}}}function s(e){if(e in o){return o[e]}return 100}const c=i},851941:(e,t,n)=>{n.d(t,{X2:()=>r,Yf:()=>a,dR:()=>i});var o=()=>n(498212);function r(e){const t=e.length===o().sO?e.slice(3,12):e.slice(3,8)+e.slice(9,13);return parseInt(t,16)}const a={production:58102339916,staging:4561079407,development:22037813777,local:12471060754};function i(e){if(void 0===e)return;const t=(0,o().Iq)(e);switch(t){case"uuid-v4":case"unknown":default:return;case"notion-v1":case"notion-v1d":case"notion-v0":}const n=r(e);return function(e){return e===a.production||e===a.staging||e===a.development||e===a.local}(n)?"main":n}},852100:(e,t,n)=>{n.d(t,{$L:()=>u,bq:()=>d,li:()=>l,sO:()=>c});n(814603),n(147566),n(198721);var o=()=>n(726637),r=()=>n(427704),a=()=>n(720665),i=()=>n(11113);const s=["profiles","categories","search","collections"];function c(e,t,n){const a=new URL(`${o().A.domainBaseUrl}${r().JZ.marketplace}/templates/${encodeURIComponent(e)}`);if(t&&t.pageType&&s.includes(t.pageType)){const e=(0,i().oY)(t.pageType,"search"===t.pageType?t.query:t.slug);e&&a.searchParams.append("cr",encodeURIComponent(e))}return t&&t.crumbs&&(a.searchParams.has("cr")||a.searchParams.append("cr",encodeURIComponent(t.crumbs))),n&&n.username!==t.slug&&(a.searchParams.delete("cr"),a.searchParams.append("cr",`pro:${encodeURIComponent(n.username)}`)),a.toString()}function l(e,t){const n=new URL(c(e,t));return n.searchParams.append("preview",encodeURIComponent("true")),n.toString()}function d(e){return Math.floor((Date.now()-e.created_at)/a().lG)<=2}function u(e){switch(e){case"public_page":case"notion_template_gallery":return e;default:return}}},854150:(e,t,n)=>{n.d(t,{Ay:()=>u,fL:()=>i,nJ:()=>l,y:()=>a,yK:()=>d});var o=()=>n(638681),r=()=>n(292588);const a=["none","team_level_guest","member","owner"],i={space:!0},s={disable_public_access:o().boolean(),disable_team_page_edits:o().boolean(),disable_guests:o().boolean(),disable_export:o().boolean()},c={disable_invite_link:o().boolean()},l=(o().union([o().object({required:{visibility:o().literals("team_members","space_members"),space_member_join_access:o().literal("invite_only"),invite_access:o().literals("team_members","team_owners")},optional:{...s,...c}}),o().object({required:{visibility:o().literal("space_members"),space_member_join_access:o().literal("self_join"),invite_access:o().literal("team_members")},optional:{...s,...c}})]),["open","closed","private"]),d="team",u={table:d,columnTypes:{id:r().A.UUID,version:r().A.Number,last_version:r().A.Number,space_id:r().A.UUID,name:r().A.String,description:r().A.String,icon:r().A.String,created_time:r().A.Number,created_by_table:r().A.String,created_by_id:r().A.UUID,last_edited_time:r().A.Number,last_edited_by_table:r().A.String,last_edited_by_id:r().A.UUID,archived_by:r().A.UUID,archived_at:r().A.Number,team_pages:r().A.UUIDArray,pinned_pages:r().A.UUIDArray,parent_id:r().A.UUID,parent_table:r().A.String,settings:r().A.JSON,is_default:r().A.Boolean,membership:r().A.JSON,permissions:r().A.JSON},model:(0,n(152853).P)({RecordStore:!0,properties:{pinned_pages:{getMethod:!1},settings:{getKeyStoreMethod:!0},icon:{getMethod:"getRawIcon"},membership:{getMethod:"getRawMembership"}}})}},855337:(e,t,n)=>{e.exports=n.p+"de374e383d7dff35.ts"},857639:(e,t,n)=>{n.r(t),n.d(t,{BatchedLogger:()=>S,clientLogglyEnvironmentData:()=>p,initialize:()=>u,log:()=>h,logWithSampleRate:()=>v,pushWithMaxLength:()=>w,rateLimitedLog:()=>y,setConsoleLogLevel:()=>m});n(944114);var o=()=>n(502800),r=()=>n(745875);n(18107),n(517642),n(658004),n(733853),n(845876),n(432475),n(515024),n(731698),n(967357),n(898992),n(803949);function a(e){return e}function i(){const e=new Array;return function(t,n){if("object"!=typeof n||null===n)return n;for(;e.length>0&&e.at(-1)!==this;)e.pop();return e.includes(n)?"[Circular]":(e.push(n),n)}}class s{constructor(e){let{token:t,transform:n}=e;this.token=void 0,this.transform=void 0,this.handles=void 0,this.token=t,this.transform=n??a,this.handles=new Set}static initialize(e){let{splunk:t}=e;const n=Array.isArray(globalThis._DualLogger)?globalThis._DualLogger:new Array;globalThis._DualLogger=new s(t),n.forEach((e=>{var t;return null===(t=globalThis._DualLogger)||void 0===t?void 0:t.push(e)}))}async pushAsync(e){const t=await this.transform(e);return(o=s.SPLUNK_URL,r={method:"POST",headers:{"Content-Type":"text/plain; charset=utf-8",Authorization:`Splunk ${this.token}`},mode:"cors",keepalive:e.keepalive,body:JSON.stringify(t,i()),priority:"low"},n.g.fetch(o,r)).then((0,n(763824).Mi)((t=>{!t.ok&&globalThis&&globalThis.console&&console.error(`Failed to log to splunk with error code (${t.status})`,e)}))).catch((e=>{globalThis&&globalThis.console&&console.error("Failed to connect to splunk server",e)}));var o,r}push(e){const t=this.pushAsync(e);this.handles.add(t),t.finally((()=>this.handles.delete(t)))}async flush(){const e=Array.from(this.handles);e.forEach((e=>this.handles.delete(e))),await Promise.all(e)}}s.SPLUNK_URL="https://http-inputs-notion.splunkcloud.com/services/collector/raw";const c=new Array;globalThis._DualLogger??=c;let l="debug",d="local";function u(e){let t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{};l=e.isLocalhost?"warning":"debug",d=e.env,s.initialize({splunk:{...t.splunk,token:e.splunk.token}})}const p={};function m(){l=arguments.length>0&&void 0!==arguments[0]?arguments[0]:"debug"}(0,n(604341).exposeDebugValue)("setLogLevel",m);const f=["error","warning","info","debug"];function g(e,t){return f.indexOf(e)<=f.indexOf(t)}function b(e){const{level:t,from:n,type:o,...r}=e,a=`${(new Date).toISOString()} [${t}] ${n}: ${o}`;console.info(a,r)}function h(e,t){v({logMessage:e,samplePercentage:_(e),forceSuppressConsoleLog:null==t?void 0:t.forceSuppressConsoleLog})}function v(e){var t;let{logMessage:n,samplePercentage:a,forceSuppressConsoleLog:i}=e;if(g(n.level,l)&&!i&&b(n),!(0,r().rQ)(a)||!function(e){if("ios"===p.os&&"error"===e.level&&e.error&&"{}"===(0,o().WS)(e.error))return!1;return!0}(n))return;if(n.data&&n.data.miscDataToConvertToString){const{miscDataToConvertToString:e,...t}=n.data;n.data=Object.assign(t,e)}const s={environment:d,...n,instantClientData:{href:"undefined"!=typeof window?window.location.href:void 0,clientTimestamp:Date.now()},clientEnvironmentData:p};void 0!==n.data&&(s.data=(0,o().iX)(n.data)),null===(t=globalThis._DualLogger)||void 0===t||t.push(s)}function _(e){return e.error&&"VersionError"===e.error.name?1:e.error&&"ClientError"===e.error.name?10:100}const y=(0,n(496603).nF)(h,2e3);function w(e,t,n){e.push(t),e.length>n&&e.splice(0,e.length-n)}class S{constructor(e){this.from=void 0,this.type=void 0,this.team=void 0,this.level=void 0,this.logToConsole=void 0,this.maxLength=void 0,this.messages=[],this.flush=()=>{if(0===this.messages.length)return;h({level:this.level,team:this.team,from:this.from,type:this.type,data:{miscDataToConvertToString:{messages:this.messages}}}),this.messages=[]};const{from:t,type:n,level:o,maxLength:r,logToConsole:a,team:i}=e;this.maxLength=r,this.team=i,this.from=t,this.type=n,this.level=o,this.logToConsole=a??!0}log(e){this.logToConsole&&g(e.level,this.level)&&b(e),w(this.messages,{...e,ts:Date.now()},this.maxLength)}}},861017:(e,t,n)=>{n.d(t,{I2:()=>$,oU:()=>Y,Bi:()=>ce,ak:()=>te,tt:()=>le,Rq:()=>fe,TD:()=>ge,S_:()=>he,_:()=>be,g$:()=>ue,LC:()=>de,Hs:()=>pe,nX:()=>ye,p0:()=>_e,bQ:()=>me,YO:()=>ve,b1:()=>ne,OB:()=>Z,L6:()=>G,bn:()=>ie,hZ:()=>ke,Jq:()=>re,v1:()=>K,bh:()=>Ce,Jx:()=>X,WZ:()=>Q,Dk:()=>Ae,bD:()=>Pe,L7:()=>ee,parseRoute:()=>oe,BH:()=>we,m6:()=>Se});n(581454),n(964979),n(814603),n(147566),n(198721);var o=()=>n(775084),r=()=>n(496603),a=()=>n(498212),i=()=>n(502492),s=()=>n(534177),c=()=>n(939768),l=()=>n(720665),d=()=>n(885443),u=()=>n(584262);const p="allTemplates",m={type:"private"},f=!0;function g(e){if(e&&(0,a().iv)(e))return(0,a().G2)(e)}var b=()=>n(796237),h=()=>n(835243),v=()=>n(547568),_=()=>n(236005),y=()=>n(590265),w=()=>n(671593),S=()=>n(638681);const k=S().object({required:{name:S().literal("nativeTab"),tab:S().literal("search")},optional:{spaceId:S().undefinable(S().string()),searchQuery:S().undefinable(S().string()),searchRequest:S().undefinable(S().string())}}),A=S().object({required:{name:S().literal("nativeTab"),tab:S().literal("updates")},optional:{spaceId:S().undefinable(S().string())}}),C=S().object({required:{name:S().literal("nativeTab"),tab:S().literal("assistant")},optional:{spaceId:S().undefinable(S().uuid())}}),I=S().object({required:{name:S().literal("nativeTab"),tab:S().literal("trash")},optional:{spaceId:S().undefinable(S().uuid())}}),P=S().object({required:{name:S().literal("nativeTab"),tab:S().literal("settings")},optional:{spaceId:S().undefinable(S().uuid()),addPasskey:S().undefinable(S().string())}}),T=S().object({required:{name:S().literal("nativeTab"),tab:S().literal("members")},optional:{spaceId:S().undefinable(S().uuid())}}),E=S().object({required:{name:S().literal("nativeTab"),tab:S().literal("help")},optional:{spaceId:S().undefinable(S().uuid())}}),R=S().object({required:{name:S().literal("nativeTab"),tab:S().literal("templates")},optional:{spaceId:S().undefinable(S().uuid())}}),D=S().object({required:{name:S().literal("nativeTab"),tab:S().literal("offline")},optional:{spaceId:S().undefinable(S().uuid()),from:S().undefinable(S().union([S().literal("offline_indicator"),S().literal("settings")]))}}),M=S().object({required:{name:S().literal("nativeTab"),tab:S().literal("empty")},optional:{spaceId:S().undefinable(S().uuid())}}),q=S().union([k,A,C,I,P,T,E,R,D,M]);function O(e,t){const n={name:"nativeTab",tab:e,...t},o=(0,w().Qq)(q,n);return o||{name:"unknown"}}var B=()=>n(427704),x=()=>n(824862),N=()=>n(332393);const L="is_from_push_notification";var U=()=>n(734459),F=()=>n(713996),V=()=>n(61844),j=()=>n(530199);const J={intercom:!0,import:!0,billing:!0,plans:!0,members:!0,audit_log:!0,connected_apps:!0,identity_provisioning:!0,analytics:!0,admin_content_search:!0,contact_us:!0,profile:!0,notifications_and_settings:!0,notifications:!0,user_settings:!0,language_and_region:!0,settings:!0,quickfind:!0,security:!0,qna:!0,ai_writer:!0,create_form:!0,tryAI:!0,upgrade_requests:!0,ai:!0},H=["front_personal"],W=["sidebar_private_section","sidebar_workspace_section","sidebar_team_section","sidebar_outliner_item","mobile_bottom_bar","mobile_widget","new_page_and_dictate_shortcut","new_page_and_dictate_action"],$={popupRedirect:"externalIntegrationPopupRedirect",authCallback:"externalIntegrationAuthCallback",datadogAuthCallback:"datadogAuthCallback"},z=["workflowViewType","workflowViewId","workflowPrompt","chatThreadId","agentChatThreadId"];function Z(e){if("page"!==e.name&&"chat"!==e.name)return;const t={};for(const n of z)e[n]&&(t[n]=e[n]);return Object.keys(t).length>0?t:void 0}function G(e){const t={};for(const n of z){const o=e[n];o&&"string"==typeof o&&(t[n]=o)}return t}function K(e){const t="page"===e.name||"space"===e.name||"agent"===e.name||"chat"===e.name||"marketplace"===e.name||"gallery"===e.name||"team"===e.name||"posts"===e.name||"agents"===e.name||"meet"===e.name||"personProfileRedirect"===e.name?e:void 0;return Boolean(t)}function Q(e){if(!K(e))return!1;switch(e.name){case"chat":case"meet":case"page":case"team":case"posts":return!0;default:return!1}}function X(e){if("page"===e.name)return!0;return!(!Q(e)||!e.peekViewBlockId)}const Y=[n(179034).ev,n(96689).NX,n(798880).oo,n(854150).yK],ee={front:!0,invoiceRedirect:!0,unsubscribe:!0,upcomingInvoice:!0,invoiceById:!0,templatesRedirect:!0,communityRedirect:!0,guideRedirect:!0},te={appleAuthCallback:!0,googleAuthCallback:!0,loginWithEmailCallback:!0,loginSuccessCallback:!0,microsoftAuthCallback:!0,samlAuthCallback:!0,passkeyAuthCallback:!0,passkeyRegistrationCallback:!0,trelloAuthCallback:!0,asanaAuthCallback:!0,slackAuthCallback:!0,passwordResetCallback:!0,externalAuthCallback:!0,externalAuthNativeCallback:!0},ne={page:!0,space:!0};function oe(e){return re(e)[0]}function re(e){var t,n,o,r,l;if(e.url&&(e.url.startsWith("/api/v3/")||e.url.startsWith("/signed/")||e.url.startsWith("/image/")||"/getStatus"===e.url))return[{name:"unknown"},"/?"];let d=e.url;try{d=new URL(e.url,e.currentUrl||e.baseUrl).toString()}catch{}const w=c().qg(d);let S=Boolean(e.publicDomainName&&(null===(t=w.host)||void 0===t?void 0:t.endsWith(e.publicDomainName)));const k="dev.notion.so"===w.host||"stg.notion.so"===w.host,A="localhost"===w.hostname&&"3101"===w.port||"http://localhost:3101"===e.baseUrl&&k,C="localhost"===w.hostname&&"3003"===w.port,I="worker.dev.notion.so"===w.hostname,P=null===(n=w.hostname)||void 0===n?void 0:n.includes("pr-preview"),T="https:"===w.protocol&&((null===(o=w.hostname)||void 0===o?void 0:o.endsWith(".ngrok-free.app"))||(null===(r=w.hostname)||void 0===r?void 0:r.endsWith(".ngrok.app"))),E=null===(l=globalThis.Meticulous)||void 0===l?void 0:l.isRunningAsTest,R="file-local.notion.so"===w.hostname||"local.notion.so"===w.hostname||C||A||I||P||T||E;let D;if(w.protocol&&w.host&&w.hostname){const Ze=Boolean(e.baseUrl===`${w.protocol}//${w.host}`||S||R);let Ge=!1;if(e.protocol){i().$J({httpUrl:e.baseUrl,protocol:e.protocol})===`${w.protocol}//${w.hostname}`&&(Ge=!0)}const Ke=Boolean(e.currentUrl&&e.currentUrl.startsWith(`${w.protocol}//${w.host}`));if(Ze||Ge)D=!1;else{if(!Ke)return[{name:"external"},"/external"];D=!0,S=!0}}const M=w.query.origin;if(S){if(!w.pathname)return[{name:"unknown"},"/?"];const Qe=function(e){const{hostname:t,publicDomainName:n,requestedOnPublicDomain:o,requestedOnExternalDomain:r}=e;if(r)return t||void 0;if(o)return c().t4({publicDomainName:n},t||"")}({hostname:w.hostname,publicDomainName:e.publicDomainName,requestedOnPublicDomain:!0,requestedOnExternalDomain:D}),Xe=w.pathname.startsWith(`${B().GJ.embedPublicPages}/`),Ye=Xe?w.pathname.substring(B().GJ.embedPublicPages.length+1):(w.pathname||"/").substring(1),[et,tt]=Ye.split("/"),nt=tt||et,ot=ie(nt);return ot?[{name:"page",blockId:ot,spaceId:void 0,spaceDomain:Qe,peekViewBlockId:void 0,peekMode:void 0,requestedOnPublicDomain:!0,requestedOnExternalDomain:D,embedded:Xe,origin:M,...se(w,e.isMobile)},"/:spaceDomain?/:blockId"]:[{name:"page",blockId:void 0,slug:nt??"",spaceId:void 0,spaceDomain:Qe,peekViewBlockId:void 0,peekMode:void 0,requestedOnPublicDomain:!0,requestedOnExternalDomain:D,embedded:Xe,...se(w,e.isMobile),origin:M},"/:spaceDomain?/:slug"]}if(w.pathname&&w.pathname.startsWith("/www.notion.so")&&(w.pathname=w.pathname.slice(14)),w.pathname||(w.pathname="/"),"/"!==w.pathname&&w.pathname.endsWith("/")&&(w.pathname=w.pathname.slice(0,-1)),"/"===w.pathname){return[{name:"root",target:J[w.query.target]?w.query.target:void 0,origin:M,templateGalleryItem:w.query.tg},"/"]}const q=function(e){if(!e.pathname)return;const t=(0,U().ho)(),n=ae({pattern:t,pathname:e.pathname});if(n){const{organizationId:o,...r}=n;return o&&(a().iv(o)||o===U().Zf)?[{name:"settingsConsoleOrganization",tabRoute:void 0,organizationId:o,properties:r,params:e.query},t]:[{name:"settingsConsoleOrganization",tabRoute:void 0,organizationId:void 0,properties:r,params:e.query},t]}for(const i of F().oh){const t=(0,U().ho)(i),n=ae({pattern:t,pathname:e.pathname});if(n){const{organizationId:o,...r}=n;return o&&(a().iv(o)||o===U().Zf)?[{name:"settingsConsoleOrganization",tabRoute:i,organizationId:o,properties:r,params:e.query},t]:[{name:"settingsConsoleOrganization",tabRoute:void 0,organizationId:void 0,properties:r,params:e.query},t]}}const o=`${B().JZ.settingsConsoleDefault}{/*extra}`,r=ae({pattern:o,pathname:e.pathname});if(r)return[{name:"settingsConsoleDefault"},o];return}(w);if(q)return q;if(w.pathname===B().JZ.modal){return[{name:"modal",templateGalleryItem:w.query.tg,projectManagementLaunch:w.query.pjm},B().JZ.modal]}if(w.pathname===B().JZ.appleAuthCallback){const{code:rt,error:at,encryptedUserObject:it,di:st}=w.query;let ct,lt,dt;try{const ut=JSON.parse(b().D(w.query.state));ct=ut.callbackType,lt=ut.encryptedToken,dt=ut.encryptedNonce}catch{}return[{name:"appleAuthCallback",code:rt,encryptedToken:lt,encryptedNonce:dt,callbackType:ct,encryptedUserObject:it,error:at,webBrowserDeviceId:st},B().JZ.appleAuthCallback]}if(w.pathname===B().JZ.googleAuthCallback){const{code:pt,error:mt,di:ft}=w.query;let gt,bt,ht;try{const vt=JSON.parse(b().D(w.query.state));gt=vt.callbackType,bt=vt.encryptedToken,ht=vt.drive}catch(ze){}return ht?[{name:"googleDriveAuthCallback",code:pt,error:mt},B().JZ.googleAuthCallback]:[{name:"googleAuthCallback",code:pt,callbackType:gt,encryptedToken:bt,error:mt,webBrowserDeviceId:ft},B().JZ.googleAuthCallback]}if(w.pathname===B().JZ.microsoftAuthCallback){const{code:_t,error:yt,di:wt,state:St}=w.query;let kt,At,Ct;try{const It=JSON.parse(b().D(St));kt=It.callbackType,At=It.encryptedToken,Ct=It.encryptedNonce}catch{}return[{name:"microsoftAuthCallback",code:_t,encryptedToken:At,encryptedNonce:Ct,callbackType:kt,state:St,error:yt,webBrowserDeviceId:wt},B().JZ.microsoftAuthCallback]}if(w.pathname===B().JZ.passkeyAuthVerify)return[{name:"passkeyAuthVerify",callbackType:w.query.callbackType},B().JZ.passkeyAuthVerify];if(w.pathname===B().JZ.passkeyAuthCallback)return[{name:"passkeyAuthCallback",attestation:w.query.attestation,attemptId:w.query.attemptId},B().JZ.passkeyAuthCallback];if(w.pathname===B().JZ.passkeyRegistrationVerification)return[{name:"passkeyRegistrationVerification",callbackType:w.query.callbackType,csrfToken:w.query.csrfToken,attemptId:w.query.attemptId,options:JSON.parse(w.query.options)},B().JZ.passkeyRegistrationVerification];if(w.pathname===B().JZ.passkeyRegistrationCallback)return[{name:"passkeyRegistrationCallback",csrfToken:w.query.csrfToken,attemptId:w.query.attemptId,response:w.query.response},B().JZ.passkeyRegistrationCallback];if(w.pathname===B().JZ.externalAuthNativeCallback)return[{name:"externalAuthNativeCallback",notionState:w.query.notion_state},B().JZ.externalAuthNativeCallback];if(w.pathname===B().JZ.slackAuthCallback){const{type:Pt,state:Tt}=w.query;return"new"===Pt?[{name:"slackAuthCallback",type:"new",state:Tt,error:w.query.error},w.pathname]:[{name:"slackAuthCallback",type:"legacy",code:w.query.code,state:Tt},B().JZ.slackAuthCallback]}if(w.pathname===B().JZ.logout)return[{name:"logout"},B().JZ.logout];if(w.pathname===B().JZ.addAnotherAccount)return[{name:"addAnotherAccount",redirectURL:w.query.redirectURL},B().JZ.addAnotherAccount];if(w.pathname.startsWith("/native/"))return[{name:"nativeRedirect",redirect:c().ZO(e.url).slice(7)},"/native/?"];if(w.pathname.startsWith("/nativemail/"))return[{name:"nativeMailRedirect",redirect:c().ZO(e.url).slice(11)},"/nativemail/?"];if(w.pathname.startsWith("/nativecalendar/"))return[{name:"nativeCalendarRedirect",redirect:c().ZO(e.url).slice(15)},"/nativecalendar/?"];if(w.pathname===B().JZ.login)return[{name:"login",email:w.query.email,redirectURL:w.query.redirectURL},B().JZ.login];if(w.pathname===B().JZ.loginCalendar)return[{name:"login/calendar",email:w.query.email,redirectURL:w.query.redirectURL,addAnotherAccount:Boolean(w.query.addAnotherAccount)},B().JZ.loginCalendar];if(w.pathname===B().JZ.loginMail)return[{name:"login/mail",email:w.query.email,redirectURL:w.query.redirectURL,addAnotherAccount:Boolean(w.query.addAnotherAccount)},B().JZ.loginMail];if(w.pathname===B().JZ.ekmError)return[{name:"ekmError"},B().JZ.ekmError];if(w.pathname===B().JZ.notFound)return[{name:"notFound"},B().JZ.notFound];const L="/signup/:trialUpsell",z=ae({pattern:L,pathname:w.pathname});if(z){const{trialUpsell:Et}=z;if(Et&&(0,V().Wx)(Et))return[{name:"signupWithTrial",trialUpsell:Et},L]}const Z="/signup/referral/:referralCode",G=ae({pattern:Z,pathname:w.pathname});if(null!=G&&G.referralCode)return[{name:"signupWithReferral",referralCode:G.referralCode},Z];if(w.pathname===B().JZ.signup){const Rt=w.query.referrer;return[{name:"signup",email:w.query.email,prompt:w.query.prompt,referrer:(0,s().Xk)(H,Rt)?Rt:void 0},B().JZ.signup]}if(w.pathname===B().JZ.signupCalendar){const Dt=w.query.referrer;return[{name:"signup/calendar",email:w.query.email,referrer:(0,s().Xk)(H,Dt)?Dt:void 0,redirectURL:w.query.redirectURL,addAnotherAccount:Boolean(w.query.addAnotherAccount)},B().JZ.signupCalendar]}if(w.pathname===B().JZ.signupMail){const Mt=w.query.referrer;return[{name:"signup/mail",email:w.query.email,referrer:(0,s().Xk)(H,Mt)?Mt:void 0,redirectURL:w.query.redirectURL,addAnotherAccount:Boolean(w.query.addAnotherAccount)},B().JZ.signupMail]}if(w.pathname===B().JZ.loginWithEmail){const qt=w.query,{state:Ot,password:Bt,redirectURL:xt,di:Nt,isMicrosoft:Lt}=qt;return[{name:"loginWithEmailCallback",state:Ot,password:Bt,isSignup:"true"===w.query.isSignup,redirectURL:xt,webBrowserDeviceId:Nt,isMicrosoft:Lt},B().JZ.loginWithEmail]}if(w.pathname===B().JZ.loginSuccess){const Ut=w.query,{di:Ft}=Ut;return[{name:"loginSuccessCallback",webBrowserDeviceId:Ft},B().JZ.loginSuccess]}if(w.pathname===B().JZ.loginPasswordReset){const Vt=w.query,{state:jt,password:Jt}=Vt;return[{name:"passwordResetCallback",state:jt,password:Jt},B().JZ.loginPasswordReset]}if(w.pathname===B().JZ.passwordChangeRedirect)return[{name:"passwordChangeRedirect"},B().JZ.passwordChangeRedirect];if(w.pathname===B().JZ.samlAuthCallback){const{userId:Ht,error:Wt,isNewSignup:$t,token:zt,previousUserId:Zt,csrfState:Gt}=w.query;return[{name:"samlAuthCallback",userId:Ht,previousUserId:Zt,error:Wt,isNewSignup:void 0!==$t&&"true"===$t.toLowerCase(),token:zt,csrfState:Gt},B().JZ.samlAuthCallback]}if(w.pathname.startsWith("/profiles/")){const Kt=w.pathname.split("/");if(Kt.length<4)return[{name:"notFound"},w.pathname];let Qt,Xt;try{Qt=g(Kt[2]),Xt=g(Kt[3])}catch(ze){return[{name:"notFound"},w.pathname]}return Xt&&Qt?[{name:"personProfileRedirect",userId:Xt,spaceId:Qt},w.pathname]:[{name:"notFound"},w.pathname]}const K=(0,_().Mq)(w.pathname,!1);if(K){const[Yt,en]=K;return[{name:"front",type:Yt,alreadyRedirected:Boolean(w.query[_().Kr])},`/front${en}`]}if(w.pathname===B().JZ.templates)return[{name:"templatesRedirect"},B().JZ.templates];if(w.pathname===B().JZ.help)return[{name:"guideRedirect"},B().JZ.help];if(w.pathname===B().JZ.community)return[{name:"communityRedirect"},B().JZ.community];if(w.pathname===B().JZ.deprecatedGuideRedirect)return[{name:"guideRedirect"},B().JZ.deprecatedGuideRedirect];if(w.pathname===B().JZ.unsubscribe)return[{name:"unsubscribe",payload:w.query.payload},B().JZ.unsubscribe];if(w.pathname===B().JZ.make)return[{name:"make",prompt:w.query.prompt},B().JZ.make];if(w.pathname===B().JZ.onboarding)return[{name:"onboarding"},B().JZ.onboarding];if(w.pathname===B().JZ.googleOneTapRedirect)return[{name:"googleOneTapRedirect",code:w.query.code,frontPathName:w.query.frontPathName,trialName:(0,V().Wx)(w.query.trialName)?w.query.trialName:void 0},B().JZ.googleOneTapRedirect];if(w.pathname===B().JZ.invoice)return[{name:"invoiceRedirect"},B().JZ.invoice];if(w.pathname===B().JZ.terms)return[{name:"termsRedirect"},B().JZ.terms];if(w.pathname===B().JZ.contentPolicy)return[{name:"contentPolicyRedirect"},B().JZ.contentPolicy];if(w.pathname===B().JZ.workflowTemplates){const tn=g(w.query.s),nn=g(w.query.u),on="allTemplates"!==(Q=w.query.t)&&"aiBuilder"!==Q?p:Q,rn=function(e){const t=null!=e&&e.startsWith("team:")?g(e.slice(5)):void 0;return void 0!==t?{type:"team",teamId:t}:"private"===e?{type:"private"}:m}(w.query.loc),an=function(e){return"false"!==e&&("true"===e||f)}(w.query.ac),sn=function(e){if(e)return decodeURIComponent(e)}(w.query.prompt),cn=function(e){if("marketing_magic_box"===e||"workflow_templates_deeplink"===e)return e}(w.query.origin);return[{name:"workflowTemplates",spaceId:tn,userId:nn,modalType:on,autoCreateDatabase:an,location:rn,prompt:sn,origin:cn},B().JZ.workflowTemplates]}var Q;if(w.pathname===B().JZ.desktopEmailConfirm)return[{name:"desktopEmailConfirm"},B().JZ.desktopEmailConfirm];if(w.pathname===B().JZ.home){return[{name:"home",peekViewBlockId:w.query[j().ZI],peekMode:w.query[j().fT],targetConfig:Te(w.query[j().q8]),origin:M},B().JZ.home]}if(w.pathname===B().JZ.posts){return[{name:"posts",peekViewBlockId:a().parseUUIDWithoutDashes(w.query[j().ZI]),peekMode:w.query[j().fT]},B().JZ.posts]}const X=`${B().JZ.nativeTab}/:tab`,ee=ae({pattern:X,pathname:w.pathname});if(ee){const{tab:ln}=ee;return[O(ln,w.query),X]}const te="/team/:teamId",ne=ae({pattern:te,pathname:w.pathname});if(ne){const{teamId:dn}=ne,un=a().parseUUIDWithoutDashes(w.query[j().ZI]),pn=w.query[j().fT];if(dn){return e.isMobile&&un?[{name:"page",spaceDomain:"notion",spaceId:void 0,blockId:un,peekViewBlockId:void 0,peekMode:void 0,requestedOnPublicDomain:S,...se(w,e.isMobile)},te]:[{name:"team",teamId:dn,spaceId:w.query.spaceId,configureOpenInDesktopApp:"true"===w.query[j().uu],peekViewBlockId:un,peekMode:pn},te]}}const oe="{/:spaceDomain}/invite/:inviteCode",re=ae({pattern:oe,pathname:w.pathname});if(re){const{inviteCode:mn,spaceDomain:fn}=re;if(mn)return[{name:"teamInvite",inviteCode:mn,spaceDomain:fn},oe]}const ce="/team/:teamId/join",le=ae({pattern:ce,pathname:w.pathname});if(le){const{teamId:gn}=le;if(gn)return[{name:"teamsInvite",teamId:gn},ce]}const de="/ai-gtm/:blockId",ue=ae({pattern:de,pathname:w.pathname});if(ue){const bn=a().parseUUIDWithoutDashes(ue.blockId);if(bn)return[{name:"aiGtmFavoriteImport",blockId:bn},de]}const pe="/invoice/:id",me=ae({pattern:pe,pathname:w.pathname});if(me){const{id:hn}=me;if(hn)return"upcoming"===hn?[{name:"upcomingInvoice",spaceId:w.query.spaceId},pe]:[{name:"invoiceById",invoiceId:hn},pe]}const fe="/design{/:page}",ge=ae({pattern:fe,pathname:w.pathname});if(ge){const{page:vn}=ge;return[{name:"uiDoc",page:vn},fe]}if(w.pathname===B().JZ.creatorProfile)return[{name:"creatorProfile"},B().JZ.creatorProfile];if(w.pathname.startsWith(B().JZ.creatorProfileTemplates)){return[{name:"creatorProfileTemplates",initialValues:{url:w.query.url?decodeURIComponent(w.query.url):void 0,name:w.query.name?decodeURIComponent(w.query.name):void 0}},B().JZ.creatorProfileTemplates]}if(w.pathname===B().JZ.creatorProfileAnalytics)return[{name:"creatorProfileAnalytics"},B().JZ.creatorProfileAnalytics];if(w.pathname.startsWith(B().JZ.creatorProfileCoupons))return[{name:"creatorProfileCoupons"},B().JZ.creatorProfileCoupons];const be=`${B().JZ.creatorProfileIntegrations}/:type/:id{/:subpageType}`,he=ae({pattern:be,pathname:w.pathname});if(he){const{type:_n,id:yn,subpageType:wn}=he,Sn=a().parseUUIDWithoutDashes(yn)||yn;return"public"===_n&&Sn?[{name:"creatorProfileIntegrations",pointer:{id:Sn,table:N().Li},publish:"publish"===wn,lab:"lab"===wn},be]:"internal"===_n&&Sn?[{name:"creatorProfileIntegrations",pointer:{id:Sn,table:x().GP}},be]:[{name:"creatorProfileIntegrations",pointer:void 0},be]}const ve=`${B().JZ.creatorProfileIntegrations}/:id`;if(ae({pattern:ve,pathname:w.pathname}))return[{name:"creatorProfileIntegrations",pointer:void 0},ve];if(w.pathname===B().JZ.creatorProfileIntegrations)return[{name:"creatorProfileIntegrations"},B().JZ.creatorProfileIntegrations];if(w.pathname===B().JZ.creatorProfileConsultantApplication)return[{name:"creatorProfileConsultantApplication"},B().JZ.creatorProfileConsultantApplication];if(w.pathname===B().JZ.localizedTemplates)return[{name:"localizedTemplates"},B().JZ.localizedTemplates];const _e=ae({pattern:"/marketplace/:pageType",pathname:w.pathname}),ye=ae({pattern:"/marketplace/:pageType/:slug",pathname:w.pathname});if(ye)return(0,y().LY)({routeName:"marketplace",parsed:w,docMatch:ye});if(_e)return(0,y().gQ)({routeName:"marketplace",parsed:w,docMatch:_e});if(w.pathname===B().JZ.marketplace)return(0,y().gg)({routeName:"marketplace",parsed:w});const we=ae({pattern:"/gallery/:pageType",pathname:w.pathname}),Se=ae({pattern:"/gallery/:pageType/:slug",pathname:w.pathname});if(Se)return(0,y().LY)({routeName:"gallery",parsed:w,docMatch:Se});if(we)return(0,y().gQ)({routeName:"gallery",parsed:w,docMatch:we});if(w.pathname===B().JZ.gallery)return(0,y().gg)({routeName:"gallery",parsed:w});if(w.pathname===B().JZ.templateSubmission)return[{name:"inAppTemplateSubmission"},B().JZ.templateSubmission];if(w.pathname===B().JZ.templateCreatorSubmission)return[{name:"inAppTemplateCreatorSubmission"},B().JZ.templateCreatorSubmission];if(w.pathname===B().JZ.studentGroupSignup)return[{name:"studentGroupSignup"},B().JZ.studentGroupSignup];if(w.pathname===B().JZ.startupsApplication)return[{name:"startupsApplication"},B().JZ.startupsApplication];if(w.pathname===B().JZ.smbsApplication)return[{name:"smbsApplication"},B().JZ.smbsApplication];if(w.pathname===B().JZ.lennyApplication)return[{name:"lennyApplication"},B().JZ.lennyApplication];if(w.pathname===B().JZ.creatorProgramApplication)return[{name:"creatorProgramApplication"},B().JZ.creatorProgramApplication];const ke="{/:spaceDomain}/:maybeUniqueId",Ae=ae({pattern:ke,pathname:w.pathname});if(Ae){const{spaceDomain:kn,maybeUniqueId:An}=Ae;if(An){const Cn=An.toUpperCase();if((0,h().$e)(Cn))return[{name:"uniqueId",uniqueId:Cn,spaceDomain:kn},ke]}}const Ce="/__export/:maybeBlockId{/:blockExportType}",Ie=ae({pattern:Ce,pathname:w.pathname});if(Ie){const{maybeBlockId:In,blockExportType:Pn}=Ie;if(In){const Tn=ie(In);if(Tn&&("markdown"===Pn||"pdf"===Pn||"html"===Pn||void 0===Pn))return[{name:"exportPreview",blockId:Tn,blockExportType:Pn||"html"},Ce]}}if(w.pathname===B().JZ.admin)return[{name:"admin"},B().JZ.admin];if(w.pathname.startsWith(B().JZ.adminListData))return[{name:"adminListData",category:w.pathname.slice(B().JZ.adminListData.length+1)},"/admin/data/:category"];const Pe=`${B().JZ.adminObject}/:id`,Ee=ae({pattern:Pe,pathname:w.pathname});if(Ee)return[{name:"adminObject",id:Ee.id||""},Pe];if(w.pathname===B().JZ.adminPermissions)return[{name:"adminPermissions"},B().JZ.adminPermissions];if(w.pathname===B().JZ.privacyCenterRedirect)return[{name:"privacyCenterRedirect"},B().JZ.privacyCenterRedirect];if(w.pathname===B().JZ.navigateToBlock&&("b"===w.query.bt||"c"===w.query.bt))return[{name:"navigateToBlock",id:w.query.id,bt:w.query.bt},B().JZ.navigateToBlock];const Re=`${B().JZ.admin}/:table/:id`,De=ae({pattern:Re,pathname:w.pathname});if(De){const{table:En}=De;let{id:Rn}=De;if(Rn){function Dn(){return"user"===En?"notion_user":"page"===En?"block":En}const Mn=Dn();if("block"===Mn&&(Rn=ie(Rn)||"unknown"),Y.includes(Mn))return[{name:"adminSingleRecord",table:Mn,id:Rn},Re]}}if(w.pathname===B().JZ.newPage){const qn=w.query.spaceId,On=a().parseUUIDWithoutDashes(qn),Bn=W.find((e=>e===w.query.from))?w.query.from:void 0;return[{name:"new",spaceId:On,type:w.query.type,id:w.query.id,from:Bn},B().JZ.newPage]}if(w.pathname===B().JZ.oauthAuthorization)return[{name:"oauthAuthorization",responseType:w.query.response_type,redirectUri:w.query.redirect_uri,integrationId:w.query.client_id,state:w.query.state,owner:w.query.owner,userId:w.query.user_id,spaceId:w.query.space_id},B().JZ.oauthAuthorization];if(w.pathname===B().JZ.notionCalendarAuthorization)return[{name:"notionCalendarAuthorization",csrf:w.query.csrf,calendarCsrf:w.query.calendar_csrf,redirectUri:w.query.redirect_uri,spaceId:w.query.space_id,state:w.query.state},B().JZ.notionCalendarAuthorization];if(w.pathname===B().JZ.globalOauthPostLogin)return[{name:"globalOauthPostLogin",state:w.query.state,clientId:w.query.client_id,redirectUri:w.query.redirect_uri,scope:w.query.scope,responseType:w.query.response_type},B().JZ.globalOauthPostLogin];if(w.pathname===B().JZ.externalAuthCallback){let xn;const Nn=w.query.error_description;return Nn&&(xn=decodeURIComponent(Nn)),[{name:"externalAuthCallback",notionState:w.query.notion_state,error:w.query.error,errorDescription:xn},B().JZ.externalAuthCallback]}if(ae({pattern:B().JZ.externalIntegrationPopupRedirect,pathname:w.pathname}))return[{name:$.popupRedirect,userId:w.query.userId,spaceId:w.query.spaceId,integrationId:w.query.integrationId,externalObjectInstanceBlockId:w.query.externalObjectInstanceBlockId,notionAuthorizationCode:w.query.notionAuthorizationCode,callbackType:w.query.callbackType,redirectToAuth:"true"===w.query.redirectToAuth},B().JZ.externalIntegrationPopupRedirect];if(w.pathname===B().JZ.initiateExternalAuthentication)return[{name:"initiateExternalAuthentication",notion_user_id:w.query.notion_user_id,notion_workspace_id:w.query.notion_workspace_id,notion_last_visited_url:w.query.notion_last_visited_url,notion_authorization_code:w.query.notion_authorization_code,external_object_instance_block_id:w.query.external_object_instance_block_id,callback_type:w.query.callback_type,integration_id:w.query.integration_id,...w.query.integration_id&&{integration_id:w.query.integration_id}},B().JZ.initiateExternalAuthentication];if(w.pathname===B().JZ.initiateExternalAuthenticationFromDesktop)return[{name:"initiateExternalAuthenticationFromDesktop",redirectUri:w.query.redirectUri},B().JZ.initiateExternalAuthenticationFromDesktop];if(w.pathname.toLowerCase()===B().JZ.externalIntegrationAuthCallback.toLowerCase()){let Ln;const Un=w.query.error_description;return Un&&(Ln=decodeURIComponent(Un)),[{name:$.authCallback,state:w.query.state,code:w.query.code,error:w.query.error,errorDescription:Ln,errorUri:w.query.error_uri},B().JZ.externalIntegrationAuthCallback]}if(w.pathname===B().JZ.datadogAuthCallback){let Fn;const Vn=w.query.error_description;return Vn&&(Fn=decodeURIComponent(Vn)),[{name:$.datadogAuthCallback,organizationId:w.query.dd_oid,organizationName:w.query.dd_org_name,site:w.query.site,domain:w.query.domain,state:w.query.state,code:w.query.code,error:w.query.error,errorDescription:Fn,errorUri:w.query.error_uri},B().JZ.datadogAuthCallback]}if(w.pathname===B().JZ.githubStudentPackAuthCallback){return[{name:"githubStudentPackAuthCallback",code:w.query.code,state:w.query.state},B().JZ.githubStudentPackAuthCallback]}if(w.pathname===B().JZ.githubStudentPackHome)return[{name:"githubStudentPackHome"},B().JZ.githubStudentPackHome];if(w.pathname===B().JZ.formResponse)return[{name:"formResponse",formResponseId:w.query.id,secretKey:w.query.secretKey,formSpaceId:w.query.spaceId,formSpaceIntent:w.query.i},B().JZ.formResponse];if(w.pathname===B().JZ.partnerProgram)return[{name:"partnerProgram",promoCode:w.query.promoCode,promoCampaign:w.query.promoCampaign},B().JZ.partnerProgram];const Me=`${B().JZ.myIntegrations}/:integrationType/:id/lab`,qe=ae({pattern:Me,pathname:w.pathname});if(qe){const{integrationType:jn,id:Jn}=qe,Hn=a().parseUUIDWithoutDashes(Jn);return"public"===jn&&Hn?[{name:"creatorProfileIntegrations",pointer:{id:Hn,table:N().Li},lab:!0,publish:!1},Me]:[{name:"creatorProfileIntegrations",pointer:void 0},Me]}const Oe=`${B().JZ.myIntegrations}/:integrationType/:id`,Be=ae({pattern:Oe,pathname:w.pathname});if(Be){const{integrationType:Wn,id:$n}=Be,zn=a().parseUUIDWithoutDashes($n);return"public"===Wn&&zn?[{name:"myIntegrations",pointer:{id:zn,table:N().Li}},Oe]:"internal"===Wn&&zn?[{name:"myIntegrations",pointer:{id:zn,table:x().GP}},Oe]:[{name:"myIntegrations",pointer:void 0},Oe]}const xe=`${B().JZ.myIntegrations}/:id`;if(ae({pattern:xe,pathname:w.pathname}))return[{name:"myIntegrations",pointer:void 0},xe];if(w.pathname===B().JZ.myIntegrations)return[{name:"myIntegrations"},B().JZ.myIntegrations];if(w.pathname===B().JZ.quickSearch)return[{name:"quickSearch"},B().JZ.quickSearch];if(w.pathname===B().JZ.meetingNotification)return[{name:"meetingNotification"},B().JZ.meetingNotification];if(w.pathname===B().JZ.blank)return[{name:"blank"},B().JZ.blank];const Ne=ae({pattern:`${B().JZ.chat}/:id`,pathname:w.pathname});if(w.pathname===B().JZ.chat||Ne){const Zn=w.query[j().ZI]?ie(w.query[j().ZI]):void 0,Gn=w.query[j().fT],Kn=(0,u().G5)(w.query)||void 0,Qn=w.hash?w.hash.substring(1):"",Xn=a().parseUUIDWithoutDashes(Qn),Yn=w.query[j().P5],eo=Yn?a().parseUUIDWithoutDashes(Yn):void 0,to=Te(w.query[j().q8]);return[{name:"chat",scrollToBlockId:Xn,peekViewBlockId:Zn,pageVisitSource:Kn,peekMode:Gn,configureOpenInDesktopApp:"true"===w.query[j().uu],assistantQueryPrefill:w.query[j().ah],spaceId:w.query.spaceId,deepFind:w.query.deepFind,threadId:eo,defaultUserMessage:w.query[j().ah]||w.query[j().dG],targetConfig:to},B().JZ.chat]}if(w.pathname===B().JZ.agents)return[{name:"agents"},B().JZ.agents];const Le="/agent/:workflowId",Ue=ae({pattern:Le,pathname:w.pathname});if(Ue){const{workflowId:no}=Ue,oo=a().parseUUIDWithoutDashes(no),ro=w.query.wfv,ao=w.query.wfk?a().np(w.query.wfk):void 0,io=w.query.wfp,so=w.query[j().P5]??w.query.ct,co=so&&a().c_(so)?a().np(so):so,lo=w.query[j().bd],uo=lo&&a().c_(lo)?a().np(lo):lo;if(oo)return[{name:"agent",workflowId:oo,workflowViewType:ro,workflowViewId:ao,workflowPrompt:io,chatThreadId:co,agentChatThreadId:uo},Le]}if(w.pathname===B().JZ.meet){const po=w.query[j().ZI]?ie(w.query[j().ZI]):void 0,mo=w.query[j().fT],fo=(0,u().G5)(w.query)||void 0,go=w.hash?w.hash.substring(1):"";return[{name:"meet",scrollToBlockId:a().parseUUIDWithoutDashes(go),peekViewBlockId:po,pageVisitSource:fo,peekMode:mo,configureOpenInDesktopApp:"true"===w.query[j().uu],spaceId:w.query.spaceId},B().JZ.meet]}const Fe=w.query.q,Ve=w.query.searchRequest,je=w.query.state;if(w.pathname.startsWith("/space/")){const bo=w.pathname.slice(7),ho=a().parseUUIDWithoutDashes(bo);return ho?[{name:"space",spaceId:ho,spaceDomain:void 0,requestedOnPublicDomain:S,searchQuery:Fe,searchRequest:Ve,state:je},"/space/:spaceId"]:[{name:"unknown"},"/space/:spaceId"]}const Je=(w.pathname||"/").substring(1),[He,We]=Je.split("/"),$e="/:spaceDomain?/:blockId";if(We){const vo=ie(We);if(!vo)return[{name:"unknown"},"/?"];let _o;return _o=v().$.has(He)?void 0:He,[{name:"page",blockId:vo,spaceId:void 0,spaceDomain:_o,peekViewBlockId:void 0,peekMode:void 0,requestedOnPublicDomain:!1,...se(w,e.isMobile),origin:M},$e]}{const yo=ie(He);let wo;return yo?[{name:"page",blockId:yo,spaceId:void 0,spaceDomain:wo,peekViewBlockId:void 0,peekMode:void 0,requestedOnPublicDomain:!1,...se(w,e.isMobile),origin:M},$e]:He.length>0&&v().$.has(He)?[{name:"unknown"},"/?"]:[{name:"space",spaceId:void 0,spaceDomain:wo||He,requestedOnPublicDomain:!1,searchQuery:Fe,searchRequest:Ve,state:void 0},"/:spaceDomain"]}}function ae(e){const t=(0,o().YW)(e.pattern)(e.pathname);if(t)return(0,l().MU)((0,s().WP)(t.params).map((e=>{let[t,n]=e;return[t,"string"==typeof n?n:void 0]})))}function ie(e){const t=e.substring(e.length-a().sO);return a().parseUUIDWithoutDashes(t)||(a().uj(e)?a().Xw(e):void 0)}function se(e,t){const n=e.hash?e.hash.substring(1):"",o=a().parseUUIDWithoutDashes(n),r=a().parseUUIDWithoutDashes(e.query[j().h4]),i=a().parseUUIDWithoutDashes(e.query[j().ZI]),s=e.query[j().fT],c="true"===e.query.duplicate,l=e.query.workspaceId,d=Number.parseInt(e.query.updateSidebarTab),p=Number.isFinite(d)?d:void 0,m=a().parseUUIDWithoutDashes(e.query[j().IG]),f=a().parseUUIDWithoutDashes(e.query[j().V3]),g=Boolean(e.query.showMoveTo),b=Boolean(e.query.saveParent),h=e.query.q,v=e.query.searchRequest,_=e.query[u().CW],y=e.query.tid,w=e.query.from,S="true"===e.query[j().uu],k=e.query.tg,A="true"===e.query.pjm,C=e.query[u().k3],I={isPush:"true"===e.query[L]};const P=e.query.state,T="true"===e.query.demoWorkspaceMode,E=e.query.demoTemplateSlug,R=e.query.wfv,D=e.query.wfk?a().np(e.query.wfk):void 0,M=e.query.wfp,q=e.query.bts?JSON.parse(e.query.bts):void 0,O=e.query[j().P5]??e.query.ct,B=O&&a().c_(O)?a().np(O):O,x=Te(e.query[j().q8]);return t&&i?{blockId:i,scrollToBlockId:o,collectionViewId:r,discussionId:m,notificationId:f,showMoveTo:g,saveParent:b,shouldDuplicate:c,workspaceId:l,searchQuery:h,searchRequest:v,tid:y,from:w,templateGalleryItem:k,projectManagementLaunch:A,configureOpenInDesktopApp:S,pageVisitSource:C,mobileData:I,queryId:_,state:P,demoWorkspaceMode:T,demoTemplateSlug:E,workflowViewType:R,workflowViewId:D,workflowPrompt:M,chatThreadId:B,backToSpace:q}:{scrollToBlockId:o,updateSidebarTab:p,collectionViewId:r,peekViewBlockId:i,peekMode:s,discussionId:m,notificationId:f,showMoveTo:g,saveParent:b,shouldDuplicate:c,workspaceId:l,searchQuery:h,searchRequest:v,tid:y,from:w,templateGalleryItem:k,projectManagementLaunch:A,configureOpenInDesktopApp:S,pageVisitSource:C,mobileData:I,queryId:_,state:P,demoWorkspaceMode:T,demoTemplateSlug:E,workflowViewType:R,workflowViewId:D,workflowPrompt:M,chatThreadId:B,targetConfig:x,backToSpace:q}}const ce=["popup","redirect","nativeredirect","native","nativemailredirect","nativecalendarredirect"];function le(e,t){const n={callbackType:t.authType.callbackType};t.authType.redirectToAuth&&(n.redirectToAuth="true");const o=(0,d().iK)();return o&&(n.tid=o),c().Gm({url:`${e}${B().JZ.applePopupRedirect}`,query:n})}function de(e,t){const n={callbackType:t.authType.callbackType};t.authType.redirectToAuth&&(n.redirectToAuth="true"),t.source&&(n.source=t.source);const o=(0,d().iK)();return o&&(n.tid=o),c().Gm({url:`${e}${B().JZ.microsoftPopupRedirect}`,query:n})}function ue(e,t,n){const o={callbackType:t.authType.callbackType};return t.email&&(o.email=t.email),t.contacts&&(o.contacts="true"),t.authType.redirectToAuth&&(o.redirectToAuth="true"),t.source&&(o.source=t.source),n&&(o.requestId=n),c().Gm({url:`${e}${B().JZ.googlePopupRedirect}`,query:o})}function pe(e,t){const n={callbackType:t.authType.callbackType,blockId:t.blockId,userId:t.userId};return t.authType.redirectToAuth&&(n.redirectToAuth="true"),c().Gm({url:`${e}${B().JZ.slackPopupRedirect}`,query:n})}function me(e,t){const n={callbackType:t.authType.callbackType,userId:t.userId,isElectronDevice:t.isElectronDevice?"true":"false"};return t.authType.redirectToAuth&&(n.redirectToAuth="true"),c().Gm({url:`${e}${B().JZ.trelloPopupRedirect}`,query:n})}function fe(e,t){const n={callbackType:t.authType.callbackType,userId:t.userId};return t.authType.redirectToAuth&&(n.redirectToAuth="true"),c().Gm({url:`${e}${B().JZ.asanaPopupRedirect}`,query:n})}function ge(e,t){const n={callbackType:t.authType.callbackType,userId:t.userId,isElectronDevice:t.isElectronDevice?"true":"false"};return t.authType.redirectToAuth&&(n.redirectToAuth="true"),c().Gm({url:`${e}${B().JZ.evernotePopupRedirect}`,query:n})}function be(e,t){const n={callbackType:t.authType.callbackType};return t.authType.redirectToAuth&&(n.redirectToAuth="true"),c().Gm({url:`${e}${B().JZ.googleDrivePopupRedirect}`,query:n})}function he(e,t){return c().Gm({url:`${e}${B().JZ.googleDrivePickerPopup}`,query:t})}function ve(e,t){const n=window.btoa(JSON.stringify(t));return c().Gm({url:`${e}/upgraded-account`,query:{state:n}})}function _e(e){const{route:t}=e;if("space"===t.name)return t.spaceId?ye(t.spaceId):`/${t.spaceDomain}`;(0,s().HB)(t.name)}function ye(e){return`/space/${a().Xw(e)}`}function we(e){return"nativeTab"!==e.name&&"quickSearch"!==e.name&&"meetingNotification"!==e.name&&"chat"!==e.name&&"marketplace"!==e.name&&"gallery"!==e.name&&"team"!==e.name&&"posts"!==e.name&&"blank"!==e.name&&"meet"!==e.name&&"agent"!==e.name&&"agents"!==e.name&&"personProfileRedirect"!==e.name}function Se(e,t){if(c().bk(e)){const n=c().qg(e),o=c().qg(t);return n.host===o.host&&n.pathname===B().JZ.initiateExternalAuthenticationFromDesktop}return!1}function ke(e){return"page"===e.name?r().oE([e.blockId,e.peekViewBlockId]):Ae(e)?r().oE([e.peekViewBlockId]):[]}function Ae(e){return"chat"===e.name||"team"===e.name||"posts"===e.name||"meet"===e.name}function Ce(e){return Boolean("page"===e.name&&e.embedded)}const Ie=["search","researcher","setup-generator","markdown-chat"];function Pe(e){return!!(0,s().Xk)(Ie,e)}function Te(e){if(!e)return;const t=decodeURIComponent(e);return function(e){try{const t=JSON.parse(decodeURI(e)),n=t.type;return n&&Pe(n)?t:void 0}catch(t){return}}(t)??(Pe(n=t)?{type:n}:void 0);var n}},862114:(e,t,n)=>{n.r(t)},865085:(e,t,n)=>{n.r(t),n.d(t,{OPFSBootupRegistry:()=>r});n(814603),n(147566),n(198721);var o=()=>n(726637);const r=new class{constructor(){if(this.isEnabled=!1,this.options={type:"shared-worker"},this.pageCache=void 0,this.isPageInCache=!1,this.pageRecordMapBufferPromise=void 0,this.IPRMetricData=void 0,this.opfsBootupRegistryMetadata={},this.opfsBootupRegistryMetadata.creationTime=performance.now(),!(0,n(706762).s)())return;if(new URL(n(855337),n.b).origin!==window.location.origin)return;const e=this.getConfigFromLocalStorage();null!=e&&e.isEnabled&&(this.isEnabled=!0,this.options=(null==e?void 0:e.options)??this.options,this.pageCache=(0,n(715854).L)(this.options),this.pageRecordMapBufferPromise=this.readInitialPagePromise())}readInitialPagePromise(){const e=this.getUserIdFromCookie(),t=this.getPageBlockIdFromRoute();let n;var o,r;if(e&&t)return n=null===(o=this.pageCache)||void 0===o?void 0:o.readBuffer(e,t).then((e=>(e.buffer.byteLength>0&&(this.isPageInCache=!0),this.updateInitialMetricData({get_handle_duration:e.metrics.getHandle,get_file_duration:e.metrics.getFile,load_content_duration:e.metrics.getBuffer,read_duration:e.metrics.total}),e))),null===(r=n)||void 0===r||r.catch((e=>{})),n}updateInitialMetricData(e){this.IPRMetricData={...this.IPRMetricData,...e}}getConfigFromLocalStorage(){const e=localStorage.getItem("OPFS:PageCache:BootupRegistry:config");if(e){return JSON.parse(e)}}updateLocalStorageFromStatsig(e){let{isEnabled:t,options:n}=e;localStorage.setItem("OPFS:PageCache:BootupRegistry:config",JSON.stringify({isEnabled:t,options:n}))}getUserIdFromCookie(){const e=new URLSearchParams(document.cookie.replaceAll("; ","&")).get("notion_user_id");if(e)return e}getPageBlockIdFromRoute(){const e=(0,n(861017).parseRoute)({url:window.location.href,isMobile:o().A.isMobile??!1,baseUrl:o().A.domainBaseUrl,publicDomainName:o().A.publicDomainName,protocol:o().A.protocol,currentUrl:window.location.href});if("page"===e.name&&e.blockId)return e.blockId}}},870723:(e,t,n)=>{n.d(t,{$t:()=>r,RP:()=>i,kW:()=>a,vJ:()=>o});n(517642),n(658004),n(733853),n(845876),n(432475),n(515024),n(731698);function o(e){return!!(r(e)||function(e){return c.has(e)}(e)||a(e))}function r(e){return s.has(e)}function a(e){return l.includes(e)}function i(e){return r(e.table)}const s=new Set(["agent","automation_action","automation","block","collection_view","collection","comment","custom_emoji","discussion","external_authentication_token","external_object","follow","form_question","invite","page_exit","page_visit","reaction","record_counter","record_key","record_mention","scheduled_digest_message","server_integrations__jira_webhook","skill","snapshot","space_bot","space_permission_group","space_permission_group_member","space_user","space_view","space","subscription_banner","team","webhook_subscription","workflow_template_instance","space_user_recovery","assistant_chat_step","assistant_chat_session","layout","ai_embedding_config","site","trusted_domain","assistant_session_starter","free_chart","channel","related_content","form_response_snapshot","workflow","workflow_automation_run","workflow_transcript","workflow_artifact","workflow_external_scoped_connection","workflow_module","inference_transcript","thread","thread_message","schedule_for_event","text_slice_block_mapping","upgrade_request","workspace_encryption_key","legal_hold_page","legal_hold_page_actor","user_seen_record","bot_extended_metadata","file_upload","space_entitlement_usage","space_user_entitlement_usage","referral"]),c=new Set(["activity"]),l=["notification"]},882362:(e,t,n)=>{n.d(t,{u:()=>o});const o=new class{setTimeout(e,t){for(var n=arguments.length,o=new Array(n>2?n-2:0),r=2;r<n;r++)o[r-2]=arguments[r];setTimeout(e,t,...o)}}},885443:(e,t,n)=>{n.d(t,{B6:()=>c,O8:()=>l,iK:()=>s,sg:()=>r});n(517642),n(658004),n(733853),n(845876),n(432475),n(515024),n(731698);var o=()=>n(939768);const r="tid",a=new Set(["visit","page_visit","landing_page_visit","login_success","onboarding_show","desktop_app_signup_browser_visit"]);let i;function s(){return i}function c(e){if(a.has(e))return s()}function l(e){if(!function(e){return!i&&!!(0,o().qn)(e,r)}(e))return;const t=(0,o().qn)(e,r);i=t;const n=(0,o().qm)(e,r);window.history.replaceState(window.history.state,"",n)}},902006:(e,t,n)=>{n.d(t,{$B:()=>I,B6:()=>p,Ds:()=>T,Dy:()=>u,Ei:()=>E,Fl:()=>y,GK:()=>f,Gb:()=>s,KI:()=>k,Mf:()=>A,My:()=>M,PI:()=>h,Py:()=>q,RW:()=>N,S:()=>w,SC:()=>a,Si:()=>d,Tq:()=>D,V9:()=>O,Ve:()=>L,_u:()=>v,aE:()=>m,aK:()=>b,e5:()=>_,fK:()=>C,fl:()=>r,h5:()=>B,lF:()=>S,n1:()=>P,q1:()=>l,w3:()=>x});n(898992),n(672577);var o=()=>n(534177);const r=["slack","google-drive","github","jira","microsoft-teams","sharepoint","gmail","linear","outlook","notion-mail","google-calendar","notion-calendar"];function a(e){return!!(0,o().Xk)(r,e)}const i=["gmail","outlook","notion-mail","google-calendar","notion-calendar"];function s(e){return!!(0,o().Xk)(i,e)}const c=["notion-mail"];function l(e){return!!(0,o().Xk)(c,e)}const d=["gmail","google-drive","google-calendar"];function u(e){return!!(0,o().Xk)(d,e)}const p=["microsoft-teams","outlook","sharepoint"];function m(e){return!!(0,o().Xk)(p,e)}const f={"google-drive":"google_drive_qna_ingestion",jira:"jira_qna_ingestion_v2",github:"github_qna",gmail:"gmail_ai_connector","microsoft-teams":"microsoft_teams_qna",sharepoint:"sharepoint_qna",linear:"linear_ai_connector",outlook:"outlook_ai_connector","notion-mail":"notion_mail_connector","google-calendar":"google_calendar_ai_connector","notion-calendar":"notion_calendar_ai_connector"},g={"google-drive":"google_drive","microsoft-teams":"microsoft_teams"};function b(e){return(0,o().O)(g,e)?g[e]:e}const h=[...r,"web","githubCode"],v="7c57d10c-00cc-48bc-a552-fb3911dd7745",_="1031cb6c-4935-4cc1-9c08-c9a4ecaae323",y="65683ef9-c923-4a14-8c5b-bfcf591cf5a1",w="c1f230b1-6635-4ab0-a3f5-f7eb105a87c3",S="c91548d4-25d0-44a2-ab2e-19f6f0b11d96",k="1724f2a1-f543-43a7-82af-8a0f85bda112",A="57bdd966-95fa-4586-ab89-c76454713dd0",C="07351274-83bb-4118-9f5b-9d75f0453999",I="6694bee1-9451-4484-a972-f56741db481e",P="5f7ef930-12b9-452b-8eb9-a385e84b7aa6",T="86ac6e3c-8ca9-4aab-9a25-fea9a0a1af57",E="471f22ca-eb03-4629-99e5-2c4b82c78bb0",R=[{regex:/https:\/\/(?<workspace>[\w-]+(?:\.[\w-]+)?)\.slack\.com\/archives\/(?<channel>[^\/]+)\/p(?<timestamp>\d+)(\?thread_ts=(?<thread_ts>\d+\.\d+)&cid=(?<cid>[^\/]+))?/,type:"slack"},{regex:/https:\/\/docs\.google\.com\/(?<fileType>document|spreadsheets|presentation)\/d\/(?<fileId>[a-zA-Z0-9_-]+)(?:\/\S*)?/,type:"google-drive"},{regex:/https:\/\/github\.com\/(?<orgName>[^\/]+)\/(?<repoName>[^\/]+)\/(?:blob|tree|blame)\/(?<branchName>[^\/]+)\/(?<codePath>[^#]+)(#(?<lineNumber>L\d+(?:C\d+)?(?:-L\d+)?))?/,type:"githubCode"},{regex:/https:\/\/github\.com\/(?<orgName>[^\/]+)\/(?<repoName>[^\/]+)\/pull\/(?<prNumber>\d+)(\/(files|commits|checks)?)?/,type:"github"},{regex:/https:\/\/app\.graphite\.dev\/github\/pr\/(?<orgName>[^\/]+)\/(?<repoName>[^\/]+)\/(?<prNumber>\d+)(?:\/\S*)?/,type:"github"},{regex:/https:\/\/(?:[^\.]+)\.sourcegraphcloud\.com\/github\.com\/(?<orgName>[^\/]+)\/(?<repoName>[^\/@]+)(?:@(?<branchName>[a-f0-9]+))?\/-\/blob\/(?<codePath>[^?]+)\?(?<lineNumbers>L\d+|l\d+)?/,type:"githubCode"},{regex:/https:\/\/(?<site>[^/]+\.atlassian\.net).*?(?:browse\/|[?&]selectedIssue=)(?<issueKey>[A-Z0-9]+-\d+).*?/,type:"jira"},{regex:/https:\/\/linear\.app\/(?<orgKey>[^/]+)\/issue\/(?<issueKey>[A-Z0-9]+-\d+).*?/,type:"linear"},{regex:/https:\/\/teams\.microsoft\.com\/l\/message\/(?<channelId>[^\/?&]+)\/(?<timestamp>\d+)\?[^#]*?(tenantId=(?<tenantId>[0-9a-fA-F-]{36}))?(?:\S*)?/,type:"microsoft-teams"},{regex:/https:\/\/(?<site>[^.]+)\.sharepoint\.com\/(:(?<fileType>[pwx]):\/r\/)?(?:sites|personal)\/(?<driveName>[^\/]+)\/.*[?&]sourcedoc=(?:%7B|\{)(?<sourceDocId>[0-9A-Fa-f-]+)(?:%7D|\})/,type:"sharepoint"},{regex:/https:\/\/(?<site>[^.]+)\.sharepoint\.com\/:(?<fileType>[pwxb]):\/(?:s|g)\/(personal\/)?(?:(?<driveName>[^\/]+)\/)?(?:(?<docHash>[0-9A-Za-z\-_]{46}))?/,type:"sharepoint"}];function D(e){const t=R.find((t=>t.regex.test(e)));return(null==t?void 0:t.type)??"web"}const M=[...["slack_unfurl_ai_action",...r.map((e=>`${b(e)}_connect_button`))],"ai_full_page_welcome_connector_action_card","ai_corner_chat_welcome_connector_action_card","ai_corner_chat_connectors_button","link_preview_unfurl_menu_ai_connector_action","ai_workspace_settings_connector_cards","ai_chat_followup_upsell_suggestion","ai_chat_search_results_tab","ai_chat_scoped_search_menu","assistant_overflow_menu_add_connectors","ai_connectors_sunset_banner","ai_connector_ai_upsell_intro_modal","ai_connector_ai_plan_upsell_intro_modal_opened","multi_search_scope_menu","ai_chat_connectors_banner"],q="#";function O(e){return e}function B(e){return e}function x(e){return e}function N(e){return e}function L(e){return e}},904819:(e,t,n)=>{n.d(t,{initializeStatsig:()=>r});n(16280);var o=()=>n(726637);async function r(e){let{environment:t,currentUserId:r,fetchConfigFilePromise:a}=e;const{statsigClientLoader:i,StatsigInitializer:s,getOrCreateStableID:c}=await Promise.resolve().then(n.bind(n,165162)),{getDeviceAttributesForStatsigUser:l}=await Promise.resolve().then(n.bind(n,386466)),d=await Promise.resolve().then(n.bind(n,529543)),{getPerformanceEventListeners:u}=await Promise.resolve().then(n.bind(n,459225)),{log:p}=await Promise.resolve().then(n.bind(n,857639)),{convertErrorToLog:m}=await Promise.resolve().then(n.bind(n,502800)),{locale:f}=await Promise.resolve().then(n.bind(n,662303)),g=c();let b,h,v,_;try{const e=await Promise.all([d.getBrowserId(t),d.getExperimentDeviceId(t)]);b=e[0],h=e[1]}catch(y){return p({level:"error",from:"statsig",type:"fetchCookies",error:m(y),data:{userId:r}}),{initializedOnServer:!1}}s.environment=t;try{"on"===localStorage.getItem("statsig:fetchLocalEvalConfigSpec")&&(v=i.fetchConfigFile(a))}catch(y){p({level:"error",from:"statsig",type:"fetchConfigFile",error:m(y),data:{userId:r}})}try{_=i.loadStageOne({currentUserId:r,device:t.device,deviceId:h,browserId:b,overrideStableID:g,locale:f,configFilePromise:v})}catch(y){p({level:"error",from:"statsig",type:"loadStageOne",error:m(y),data:{userId:r}})}return s.initializePromise=async function(){const{parseRoute:e}=await Promise.resolve().then(n.bind(n,861017)),a=e({url:window.location.href,isMobile:t.device.isMobile,baseUrl:o().A.domainBaseUrl,publicDomainName:o().A.publicDomainName,protocol:o().A.protocol,currentUrl:window.location.href});let s,c=r;if("page"===a.name||"root"===a.name){const{getPredictedRootRedirectPageForPrefetch:e}=await Promise.resolve().then(n.bind(n,740456)),t="root"===a.name?e():{blockId:a.blockId,userId:r};s=t.blockId,c=t.userId}const d=await Promise.resolve().then(n.bind(n,754704)),p={browserId:b,deviceId:h,device:l(t.device),stableID:g,...s?{page:{id:s,spaceId:void 0}}:{},locale:f,shouldStringifyInitialValues:!0,clientSdkApiKey:o().A.statsig.apiKey},m=d.createApiHttpJsonRequestOptions({environment:{device:t.device},eventName:"getStatsigResults",data:p,activeUserId:c,tracking:void 0,eventListeners:u({eventName:"getStatsigResults",isPrefetchRequest:!0})}),{default:_}=await Promise.resolve().then(n.bind(n,974233)),y=_(m),w=await y;"success"===w.type&&await i.loadStageTwo(w.data,v)}().then((()=>{s.isComplete=!0}),(e=>{e instanceof Error?s.error=e:s.error=new Error("Unknown error when initializing Statsig")})),_&&await _,{initializedOnServer:!0,finishInitializePromise:s.initializePromise}}},906551:(e,t,n)=>{function o(e){return{teamHomeCollectionViewIdsKey:`teamHomeCollectionViewIds:${e}`}}n.d(t,{k:()=>o})},928217:(e,t,n)=>{n.d(t,{Pv:()=>u,Rs:()=>c,Yg:()=>m,jM:()=>r,ji:()=>f,nD:()=>l,oR:()=>p});n(898992),n(737550);var o=()=>n(638681);const r="ai.grant032023",a="ai.qnaBetaRefresh",i="ai.limitedQnaRefresh",s="ai.assistantLaunchGrant",c="ai.studentGitHubGrant",l=[r,a,i,s,c],d=o().object({required:{singlePlayerAmount:o().number(),multiplayerAmount:o().number(),unit:n(7615).Yq},optional:{}}),u=o().object({required:{baseGrant:d,userGrant:d,grant032023:o().intersection([d,o().object({required:{waitMs:o().number()},optional:{}})]),studentGrant:d,studentGitHubGrant:d,assistantLaunchGrant:o().intersection([d,o().object({required:{limit:o().number()},optional:{}})]),maxAllowance:o().object({required:{free:o().number(),paid:o().number()},optional:{}})},optional:{}});function p(e,t){return m({space:t,grantId:a})?e.qna:e.writer}function m(e){var t;let{space:n,grantId:o}=e;return Boolean(null==n||null===(t=n.getSettings())||void 0===t||null===(t=t.grant_awards)||void 0===t?void 0:t.some((e=>e.id===o)))}function f(e,t,n){if(n>1){const e=t.multiplayerAmount*n;return t.limit&&e>t.limit?t.limit:e}return t.singlePlayerAmount}},939768:(e,t,n)=>{n.d(t,{$_:()=>p,AY:()=>U,FB:()=>N,GP:()=>d,Gm:()=>u,Jf:()=>k,Jh:()=>b,O$:()=>_,P0:()=>x,Z$:()=>s,ZL:()=>C,ZO:()=>m,al:()=>B,bk:()=>P,cW:()=>f,d9:()=>q,hd:()=>h,he:()=>O,iW:()=>M,mJ:()=>F,pf:()=>g,q7:()=>A,qg:()=>l,qh:()=>L,qm:()=>v,qn:()=>y,t4:()=>I,t7:()=>V});n(16280),n(517642),n(658004),n(733853),n(845876),n(432475),n(515024),n(731698),n(898992),n(803949),n(581454),n(814603),n(147566),n(198721);var o=()=>n(188835),r=()=>n(534177),a=()=>n(720665);const i="/v1/oauth/authorize";function s(e){try{e=decodeURI(e)}catch(t){if(!(t instanceof URIError))throw t}return e.substring(e.lastIndexOf("/")+1)}let c=null;function l(e){let t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{};try{return o().parse(e,!0,t.slashesDenoteHost)}catch(n){try{return{...o().parse(e),query:{}}}catch(n){return c||(c=o().parse("",!0)),c}}}function d(e){return o().format(e)}function u(e){const t=l(e.url);return t.search=null,t.query=e.query||{},t.hash=e.hash||null,d(t)}function p(e){const t=l(e);return d({protocol:t.protocol,auth:t.auth,host:t.host})}function m(e){const t=l(e);return t.protocol=null,t.host=null,t.hostname=null,t.slashes=!1,d(t)}function f(e){const t=l(e);return Boolean(!t.host&&!t.hostname)}function g(e){const t=l(e.relativeUrl),n=l(e.baseUrl);return t.protocol=n.protocol,t.host=n.host,t.hostname=n.hostname,d(t)}function b(e){const t=l(e.url);return t.path=null,t.pathname=e.pathname,d(t)}function h(e,t){return b({url:e,pathname:t})}function v(e,t){const n=l(e);return n.search=null,delete n.query[t],d(n)}function _(e,t){const n=l(e);return n.search=null,n.query={...n.query,...t},d(n)}function y(e,t){return l(e).query[t]}const w={"thumpmagical.top":!0,"geoloc8.com":!0,"kutabminaj.top":!0,"cutisbuhano.xyz":!0,"bhapurimillat.xyz":!0,"kingoffightermens.top":!0,"boxgeneral.xyz":!0,"ahnd.ga":!0,"steptossmessage.top":!0,"earthdiscover.xyz":!0,"sopecasniteroi.com.br":!0,"clangchapshop.xyz":!0},S=["http:","https:","mailto:","itms-apps:","tel:","cron:","cronlocal:","x-apple.systempreferences:","zoommtg:","notionmaillocal:","notionmaildev:","notionmailstg:","notionmail:","cursor:"];function k(e){const{str:t,allowNoProtocol:n}=e;if(t&&"string"==typeof t)try{const e=l(t);if(e.host&&w[e.host])return;if(e.protocol&&e.host)return A(t);if(!e.protocol){try{const{host:e}=new URL(`stub:${t}`);if(w[e])return}catch{}try{const{host:e}=new URL(`stub://${t}`);if(w[e])return}catch{}}if(e.protocol&&S.includes(e.protocol)||n&&!e.protocol)return t}catch(o){return}}function A(e){if(e)try{const t=new URL(e);if(w[t.host])return;if(S.includes(t.protocol))return t.href}catch{}}function C(e){return(e||"").replace(/(?:https?|ftp):\/\/[\n\S]+/g,"")}function I(e,t){let{publicDomainName:n}=e;if(!n||!t)return;const o=Array.from(new Set([n,n.split(":")[0]]).values());for(const r of o)if(t.endsWith(`.${r}`))return t.substring(0,t.length-r.length-1)}function P(e){try{return new URL(e)}catch{return}}const T="none",E={utm_source:T,utm_medium:T,utm_campaign:T,utm_term:T,utm_content:T},R=["utm_source","utm_medium","utm_campaign","utm_term","utm_content","fbclid","gclid","device","targetid","criterionid","previous_path","ps_partner_key","ps_xid","trial_source"];function D(e){const t={};return R.forEach((n=>{t[n]=e.get(n)??void 0})),t}function M(e,t){const n=P(e);if(!n)return;const{searchParams:o}=n,i={...D(o),pathname:n.pathname,query:n.search},s={...E,...t},c={...i};return Object.keys(c).forEach((e=>{const t=s[e];(0,r().O9)(c[e])||(0,a().pN)(t)||(c[e]=t)})),c}function q(e){e=e.trim().toLowerCase();const t="àáäâèéëêìíïîòóöôùúüûñç·/_,:;";for(let n=0,o=28;n<o;n++)e=e.replace(new RegExp(t.charAt(n),"g"),"aaaaeeeeiiiioooouuuunc------".charAt(n));return e=e.replace(/[<>:"/\\|?*\x00-\x1F]| +$/g,"").replace(/\s+/g,"-").replace(/-+/g,"-")}function O(e){var t;const n=l(e);if(null!==(t=n.pathname)&&void 0!==t&&t.startsWith("/native"))throw new Error("Already on native redirect URL");return n.pathname=`/native${n.pathname}`,d(n)}function B(e){const t=P(e);if(t)return t.searchParams}function x(e){const{baseUrl:t,clientId:n,redirectUri:o,state:r}=e,a={client_id:n,response_type:"code",owner:"user",redirect_uri:o};return r&&(a.state=r),u({url:g({baseUrl:t,relativeUrl:i}),query:a})}function N(e){return new URL(e).host.replace("www.","")}function L(e){try{return N(e)}catch(t){return}}function U(){let e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:"";try{const t=new URL(e);return null!==t&&("http:"===t.protocol||"https:"===t.protocol)}catch(t){return!1}}Symbol("UrlString");function F(e,t){if(!e||!t)return!1;const[n,o]=[e,t].map(N),r=n.split("."),a=o.split("."),[i,s]=r.length<a.length?[r,a]:[a,r];return s.slice(-i.length).join(".")===i.join(".")}function V(e,t){if(!e||!t)return!1;const[n,o]=[e,t].map((e=>l(e).pathname||""));if(n===o)return!0;try{const e=decodeURIComponent(decodeURIComponent(n));return e===decodeURIComponent(decodeURIComponent(o))}catch(r){return!1}}},945522:(e,t,n)=>{n.r(t),n.d(t,{MobileNativeService:()=>g,createMobileNativeService:()=>b});n(16280),n(517642),n(658004),n(733853),n(845876),n(432475),n(515024),n(731698);var o=n.n(n(952224)),r=()=>n(726637),a=()=>n(857639),i=()=>n(502800),s=()=>n(763824),c=(n(944114),()=>n(155959));function l(e,t){return void 0===t?null:t}class d{constructor(e){var t=this;this.api=void 0,this.environment=void 0,this.receiveHandlers=void 0,this.sendChannel=void 0,this.sendChannelJson=void 0,this.sendChannelJsonWithReply=void 0,this.sendViaMessagePort=void 0,this.responseMap={},this.rejectMap={},this.preEnvironmentBridgeMetrics=[],this.notionPerformance=void 0,this.experimentStore=void 0,this.handleReceiveStringFromChannel=async e=>{if("MessagePort"===e)return;let t;try{t=JSON.parse(e)}catch(n){return void a().log({level:"error",from:"eventBasedApi",type:"JsonParseError",error:(0,i().convertErrorToLog)(n),data:{message:e}})}return this.handleReceiveChannel(t)},this.handleReceiveChannel=async e=>{if("request"===e.type){if(this.receiveHandlers[e.name]){const t=await this.receiveHandlers[e.name](...e.args),n={id:e.id,type:"response",name:e.name,result:t,error:void 0};if((0,c().T)("supportsJsonBridgeV3")){if((0,c().T)("supportsJsonBridgeWithReplyV3"))return n;this.sendChannelJson(n)}else this.sendViaMessagePort(JSON.stringify(n,l))}}else if("response"===e.type){if(this.responseMap[e.id]){const t=this.responseMap[e.id],n=this.rejectMap[e.id];delete this.responseMap[e.id],delete this.rejectMap[e.id],e.error?n(new Error(e.error)):t(e.result)}}else a().log({level:"error",from:"eventBasedApi",type:"JsonParseError",error:new Error("native->web data has no recognizable type"),data:{message:e}});return null},this.sendChannel=e.sendChannel,this.sendChannelJson=e.sendChannelJson,this.sendChannelJsonWithReply=e.sendChannelJsonWithReply,this.sendViaMessagePort=e.sendViaMessagePort,this.receiveHandlers=e.receiveHandlers;const n={};for(const o of e.sendCapabilities)n[o]=function(){const e=(Math.random()*Math.pow(10,16)).toString(),n=performance.now();for(var r=arguments.length,a=new Array(r),i=0;i<r;i++)a[i]=arguments[i];const s={id:e,type:"request",name:o,args:a};let d;if((0,c().T)("supportsJsonBridgeV3"))if((0,c().T)("supportsJsonBridgeWithReplyV3"))d=t.sendChannelJsonWithReply(s).then((e=>e.error?t.sendChannelJsonWithReply(JSON.stringify(s,l)).then((e=>e.result)):e.result));else{const n=new Promise(((n,o)=>{t.responseMap[e]=n,t.rejectMap[e]=o}));t.sendChannelJson(s),d=n}else{const n=JSON.stringify(s,l),o=new Promise(((n,o)=>{t.responseMap[e]=n,t.rejectMap[e]=o}));(0,c().T)("supportsWebMessagePort")&&t.sendViaMessagePort(n)||t.sendChannel(n),d=o}return d.then((e=>{const r=o.toString(),a=performance.now(),i={metric:{metricName:"mobilenative.bridge_request.web",startTime:n,endTime:a},data:{method_name:r}};return t.environment?t.sendBridgeMetric(i):t.preEnvironmentBridgeMetrics.push(i),e}))};this.api=n}enableMetricCollection(e,t,n){this.environment=e,this.notionPerformance=t,this.experimentStore=n;for(const o of this.preEnvironmentBridgeMetrics)this.sendBridgeMetric(o);this.preEnvironmentBridgeMetrics.splice(0)}sendBridgeMetric(e){const t=this.environment,n=this.experimentStore,o=this.notionPerformance;t&&n&&o&&n.checkGate({gateName:"mobile_bridge_performance_logging"})&&o.DO_NOT_USE_measureLegacy(e.metric,{environment:t,data:e.data})}}const u="__reactNativeCapabilities";class p{constructor(){this.emitter=new(n(137007).EventEmitter)}addListener(e,t,n,o){const r=`${e}:${n}:${t}`;return this.emitter.on(r,o),()=>this.emitter.off(r,o)}dispatch(e,t,n,o){const r=`${e}:${n}:${t}`;this.emitter.emit(r,o)}}let m=null;window.onmessage=e=>{if("MessagePort"===e.data&&e.ports&&e.ports.length){const o=e.ports[0];var t,n;if(m&&m.onmessage)o.onmessage=null===(t=m)||void 0===t?void 0:t.onmessage,null===(n=m)||void 0===n||n.close(),m.onmessage=null;m=o}};const f=window.DEVICE_READY_P;class g{constructor(e){this.sqliteConnection=void 0,this.listeners=new Set,this.receiveHandlers=void 0,this.eventBasedApi=void 0,this.initialNotification=void 0,this.device=void 0,this.startupMetric=void 0,this.horizontalSizeClass=void 0,this.componentEvents=new p,(0,n(604341).exposeDebugValue)("mobileNative",this),this.device=e.device,this.horizontalSizeClass=e.horizontalSizeClass,this.receiveHandlers={nativeToWebRenderStart:e=>{},connectivityTypeChanged:e=>{},toggleAvailableOffline:e=>{},openLink:e=>{this.initialNotification={url:e,clearHistory:!1}},openLinkV2:e=>{this.initialNotification=e},pushNotificationTokenRefresh:()=>{},backButtonPress:()=>{},getLocalizedUrl:e=>[{error:"Browser API not ready yet"}],keyboardWillShow:e=>{},keyboardDidShow:e=>{},keyboardWillHide:()=>{},keyboardDidHide:()=>{},keyboardConfigChanged:e=>{},safeAreaConfigChanged:e=>{},keyboardShortcut:()=>{},nativeBottomBarDidChange:e=>{},pause:()=>{},resume:async()=>{},appUpdateError:()=>{},appUpdateChecking:()=>{},appUpdateAvailable:()=>{},appUpdateNotAvailable:()=>{},appUpdateProgress:()=>{},appUpdateReady:()=>{},appUpdateFinished:()=>{},statusBarTap:()=>{},themeChanged:e=>{},track:()=>{},refreshSubscriptionData:async e=>{},refreshSubscriptionDataV2:async()=>{},processMobileActionBarAction:e=>{},logout:e=>{},logoutV2:e=>{},logoutAll:()=>{},openDestinationV2:e=>{},openDestination:e=>{},updateTransactionState:e=>{},setCurrentUserId:e=>{},setCurrentSpace:e=>{},updateFileUploadProgress:e=>{},updateHorizontalSizeClass:e=>{},updateTabbedRouterState:e=>[{}],getLocalSearchResults:e=>[{error:"Browser API not ready yet"}],customAddCommentMenuItemTapped:()=>{},filterValidMoveToBlocks:e=>[{error:"Browser API not ready yet"}],convertEnrichedMarkdownToBlocks:e=>[{error:"Browser API not ready yet"}],completeMoveToTransaction:e=>[{error:"Browser API not ready yet"}],undoRevision:()=>{},searchTeams:e=>[{error:"Browser API not ready yet"}],getImageBlockUrls:e=>[{error:"Browser API not ready yet"}],dismissKeyboard:()=>{},focusOnBlock:e=>{}};const t=window.ReactNativeWebView?{send:e=>window.ReactNativeWebView.postMessage(e),sendJson:e=>window.ReactNativeWebView.postJsonMessage(e),sendJsonWithReply:e=>window.ReactNativeWebView.postJsonMessageWithReply(e),listen:e=>window.addEventListener("message",e)}:{send:e=>window.postMessage(e,"*"),sendJson:e=>{},sendJsonWithReply:e=>{},listen:e=>document.addEventListener("message",e)};window.ReactNativeWebView&&(window.ReactNativeWebView.browserApiRequest=async e=>this.eventBasedApi.handleReceiveChannel(e)),m&&(0,c().T)("supportsWebMessagePort")&&(m.onmessage=e=>{this.eventBasedApi.handleReceiveStringFromChannel(e.data)});t.listen((e=>{if(e.data&&!function(e){return e.source===window&&"string"==typeof e.data&&0===e.data.indexOf("setImmediate$")}(e)&&!function(e){var t,n;return"string"==typeof e.data&&e.data.startsWith("webpack")||"string"==typeof(null===(t=e.data)||void 0===t?void 0:t.type)&&(null===(n=e.data)||void 0===n?void 0:n.type.startsWith("webpack"))}(e))for(const t of Array.from(this.listeners))t(e.data)})),this.eventBasedApi=new d({sendChannel:t.send,sendChannelJson:t.sendJson,sendChannelJsonWithReply:t.sendJsonWithReply,sendViaMessagePort:e=>!!m&&(m.postMessage(e),!0),receiveHandlers:this.receiveHandlers,sendCapabilities:e.sendCapabilities}),(0,n(644425).updateNativeErrorHandler)((e=>{const t=this.eventBasedApi.api.handleWebError;t&&t(e)}))}async initialize(e){let{sendCapabilities:t}=e;const o=t.indexOf("execSqliteBatch")>-1;if(!this.api.execSqliteBatch||!o)return;const r={execSqliteBatch:async e=>{if(!this.api.execSqliteBatch)throw new Error("execSqlBach API removed after SqliteConnection was created");const t=await this.api.execSqliteBatch(e).catch((e=>{throw a().log({level:"error",from:"mobileNative.ts",type:"execSqliteBatch",error:(0,i().convertErrorToLog)(e),data:{}}),e}));if(t.error){const e=new Error(t.error.message);throw e.name=t.error.name,e}return t.value},completelyRebuildSqliteDb:()=>Promise.resolve()};this.sqliteConnection=new(n(735270).i)({connection:r,migrations:await(0,n(203587).G)(r),type:"v1"})}markInitializationComplete(e){this.startupMetric={metricName:"mobilenative.service_initialization",startTime:e,endTime:performance.now()}}get api(){return this.eventBasedApi.api}updateReceiveApiHandlers(e){Object.assign(this.receiveHandlers,e)}share(e){this.api.share&&this.api.share(e)}copyText(e,t){this.api.copyToClipboard&&this.api.copyToClipboard({contents:e,message:t})}async moveTo(e){if(this.api.moveTo)return await this.api.moveTo(e)}async openEmojiPicker(){if(this.api.openEmojiPicker)return await this.api.openEmojiPicker()}async openFilePicker(e){if(this.api.openFilePicker)return await this.api.openFilePicker(e)}async uploadFile(e){if(this.api.uploadFile)return await this.api.uploadFile(e)}setTheme(e,t){this.api.setAppTheme&&this.api.setAppTheme(t?"system":e)}openLink(e){"in-app"===n(410555).A.state?this.openInAppBrowser(e):this.openExternalBrowser(e)}openInAppBrowser(e){this.api.openInAppBrowser&&this.api.openInAppBrowser(o()(e))}openExternalBrowser(e){this.api.openExternalBrowser?this.api.openExternalBrowser(e):this.openInAppBrowser(e)}openAppLanguageSettings(){this.api.openAppLanguageSettings&&this.api.openAppLanguageSettings()}openInternalSettings(){this.api.openInternalSettings&&this.api.openInternalSettings()}openAuthSessionBrowser(e){this.api.openAuthSessionBrowser?this.api.openAuthSessionBrowser(e):this.openExternalBrowser(e)}closeInAppBrowser(){this.api.closeInAppBrowser&&this.api.closeInAppBrowser()}debugLog(e){var t,n;null===(t=(n=this.api).debugLog)||void 0===t||t.call(n,e)}openUpgradeModal(e){this.api.openUpgradeModalV2?this.api.openUpgradeModalV2(e):this.api.openUpgradeModal&&this.api.openUpgradeModal(e.spaceId,e.from)}exitApp(){this.api.exitApp&&this.api.exitApp()}showSplashscreen(){this.api.showSplashScreen&&this.api.showSplashScreen()}hideSplashscreen(){this.api.hideSplashScreen&&this.api.hideSplashScreen()}handlePerformanceMetricsUpdate(e){this.api.handlePerformanceMetricsUpdate&&this.api.handlePerformanceMetricsUpdate(e)}buzz(){this.api.buzz&&this.api.buzz()}enableBridgeMetricsCollection(e,t,n){this.startupMetric&&(t.DO_NOT_USE_measureLegacy(this.startupMetric,{environment:e,data:{}}),this.startupMetric=void 0),this.eventBasedApi.enableMetricCollection(e,t,n)}showLightBox(e){this.api.showLightBox&&this.api.showLightBox(e)}showLightBoxV2(e){const{url:t,previewUrl:n,type:r,from:a,state:i}=e;if(!t&&!n&&this.device.isIOS&&!(0,c().T)("supportsSecureLightboxImageUrlFetchingState"))return;const s=n?o()(n):void 0,l=t?o()(t):void 0;this.showLightBox({items:[{type:r,previewUrl:s,originalUrl:l,downloadName:e.downloadName,state:i}],startingIndex:0,from:a})}setBadgeNumber(e,t){this.api.setBadgeNumber&&this.api.setBadgeNumber(e,t)}openUpdateSettings(){this.api.openUpdateSettings&&this.api.openUpdateSettings()}toggleBottomBar(e){this.api.toggleBottomBar&&this.api.toggleBottomBar(e)}toggleNativeHome(){this.api.toggleNativeHome&&this.api.toggleNativeHome()}async setCookie(e){this.api.setCookie&&await this.api.setCookie(e)}sidebarVisibility(e,t){if(this.api.sidebarVisibility){const n={isVisible:e,width:t};this.api.sidebarVisibility(n)}}hasNativeAppleLogin(){return Boolean(this.api.requestNativeAppleAuth)}hasNativeGoogleLogin(){return this.device.isAndroid&&Boolean(this.api.requestGoogleJwt)}supportsNativeHomeOnPhone(){return(0,c().T)("supportsNativeHome")&&this.device.isPhone}async requestNativeAppleAuth(){if(this.api.requestNativeAppleAuth)return this.api.requestNativeAppleAuth()}async requestGoogleJwt(){if(this.device.isAndroid&&this.api.requestGoogleJwt)return this.api.requestGoogleJwt({webClientId:r().A.googleOAuth.clientId})}async logoutOfGoogle(){if(this.device.isAndroid&&this.api.logoutOfGoogle)return this.api.logoutOfGoogle({webClientId:r().A.googleOAuth.clientId})}async resetAssetCache(){this.api.resetAppCache&&await this.api.resetAppCache()}async setLogglyData(e){this.api.setLogglyData&&await this.api.setLogglyData(e)}async unregisterPushNotifications(){this.api.unregisterPushNotifications&&await this.api.unregisterPushNotifications()}async cancelUserBackgroundTasks(){this.api.cancelUserBackgroundTasks&&await this.api.cancelUserBackgroundTasks()}renderMobileActionBar(e){this.api.renderMobileActionBar&&this.api.renderMobileActionBar(e)}sendRawMessageStoreMessage(e){this.api.sendRawMessageStoreMessage&&this.api.sendRawMessageStoreMessage(e)}sendRawAudioProcessorMessage(e){this.api.sendRawAudioProcessorMessage&&this.api.sendRawAudioProcessorMessage(e)}get setWebViewAllowsNavigationGestures(){return this.api.setWebViewAllowsNavigationGestures}subscribeToOpenLink(e){if(this.updateReceiveApiHandlers({openLink:t=>e(t,!1,!1),openLinkV2:t=>e(t.url,t.clearHistory,t.showWeb??!0)}),this.initialNotification){const{url:t,clearHistory:n,showWeb:o}=this.initialNotification;this.initialNotification=void 0,setTimeout((()=>e(t,n,o??!0)))}}subscribeToUpdateTransactionState(e){this.updateReceiveApiHandlers({updateTransactionState:e})}showNativeHomeTab(){let e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:()=>{};(0,c().T)("supportsNativeHome")&&(this.device.isAndroid?this.exitApp():this.toggleNativeHome(),n(496603).cb(e,350))}markTransitionReady(e){const t=()=>{var t,o;null===(t=(o=this.api).transitionReady)||void 0===t||t.call(o,{type:e.type,id:e.id}),n(671973).N.markTransitionReady(e.environment,e.type,e.isNavigationEvent)};e.sendImmediately?t():window.requestAnimationFrame((()=>{setTimeout((()=>{t()}),0)}))}recordPageVisit(e,t){this.api.recordPageVisit&&this.api.recordPageVisit({userId:e,...t})}nativeToWebRenderEnd(e,t){this.api.nativeToWebRenderEnd&&this.api.nativeToWebRenderEnd({id:e,metrics:t})}updateCustomAddCommentMenuItemEnabled(e){this.api.updateCustomAddCommentMenuItemEnabled&&this.api.updateCustomAddCommentMenuItemEnabled(e)}updateAiAssistantEnabledState(e){this.api.updateAiAssistantEnabledState&&this.api.updateAiAssistantEnabledState(e)}updateAiAssistantVisibilityState(e){this.api.updateAiAssistantVisibilityState&&this.api.updateAiAssistantVisibilityState(e)}requestKeepScreenOn(e){var t,n;null===(t=this.api)||void 0===t||null===(n=t.requestKeepScreenOn)||void 0===n||n.call(t,e)}forceKeyboardVisibility(e){var t,n;null===(t=this.api)||void 0===t||null===(n=t.forceKeyboardVisibility)||void 0===n||n.call(t,e)}async getSqliteDiskUsage(){var e,t;const n=await(null===(e=this.api)||void 0===e||null===(t=e.getSqliteDiskUsage)||void 0===t?void 0:t.call(e));return n?Number((n/1024/1024).toFixed(2)):0}submitUserFeedback(e,t){this.api.submitUserFeedback?this.api.submitUserFeedback(e):t()}}async function b(e,t){await f,await s().wR(0);const n=window[u]||[],o=new g({device:e.device,sendCapabilities:n,horizontalSizeClass:t});return await o.initialize({sendCapabilities:n}),o}},948914:(e,t,n)=>{n.d(t,{A:()=>a,b:()=>i});class o extends(()=>n(757695))().Store{getInitialState(){return{}}}const r=new o,a=r,i=new(n(496506).ComputedStore)((()=>{var e;return Boolean((null===(e=r.state.preferences)||void 0===e?void 0:e.isAlwaysOnTabBarEnabled)||r.state.isShowingTabBar)}),{debugName:"isShowingTabBarStore"})},959013:(e,t,n)=>{n.d(t,{XU:()=>i.XU,dT:()=>i.dT,sA:()=>s.A,Gr:()=>i.Gr,Dk:()=>c.A,EY:()=>l.E,MT:()=>d.MT,YK:()=>u,Vq:()=>r,LS:()=>a,tz:()=>p});var o=n(327025);n(898992),n(354520);function r(e){return null!=e}n(581454);const a=function(e,t){return`{${e}, plural, ${Object.entries(t).map((e=>{let[t,n]=e;return`${t} {${n}}`})).join(" ")}}`};var i=n(804106),s=n(408666),c=n(335596),l=n(360665),d=n(531234);Symbol.for("LocalizedString"),Symbol("defined message descriptor");function u(e){return e}const p=o.A},961581:(e,t,n)=>{n.d(t,{A:()=>a});var o=()=>n(757695);class r extends o().Store{getInitialState(){return{subMetricsStore:o().Store.createValue({},{name:"subMetricsStore"}),OPFSMetricDataStore:o().Store.createValue({},{name:"OPFSMetricDataStore"}),metricDataStore:o().Store.createValue({num_api_calls_initiated:0,num_api_calls_completed:0,wasm_sqlite_initialized:"skipped-unsupported-device"},{name:"metricDataStore"}),initialRenderCompleted:!1,initialRenderAfterLoginCompleted:!1,initialLoadCachedPageChunkCalledAt:void 0,initialCollectionPendingRenderCount:0,prewarmedTabAppStartTimeOverride:void 0,opfsMetadata:void 0}}incrementNumApiCallsInitiated(){this.state.initialRenderCompleted||this.state.metricDataStore.update((e=>({...e,num_api_calls_initiated:e.num_api_calls_initiated+1})))}incrementNumApiCallsCompleted(){this.state.initialRenderCompleted||this.state.metricDataStore.update((e=>({...e,num_api_calls_completed:e.num_api_calls_completed+1})))}setDesktopLoadContext(e,t){const n=t?parseInt(t):void 0;this.update((t=>({...t,desktopLoadOrigin:e,desktopTabCount:n})))}}const a=new r},973647:(e,t,n)=>{n.d(t,{Q:()=>o,j:()=>r});n(944114);const o={success:e=>({value:e}),fail:e=>({error:e}),isSuccess:e=>!("error"in e),isFail:e=>"error"in e,unwrap(e){if(o.isFail(e))throw e.error;return e.value},unwrapOr:(e,t)=>o.isFail(e)?t:e.value,reduce(e,t){let n={value:e[0]};for(let r=1;r<e.length;r++){const a=e[r];if(o.isFail(n))return n;n=t(n.value,a)}return n},map(e,t){const n=[];for(const r of e){const e=t(r);if(o.isFail(e))return e;n.push(e.value)}return{value:n}},partition(e){const t=[],n=[];for(const r of e)o.isSuccess(r)?t.push(r.value):n.push(r.error);return{successes:t,errors:n}},toOutcomeString:e=>o.isSuccess(e)?"success":"error"};function r(e){try{const t=e();return function(e){switch(typeof e){case"undefined":case"string":case"bigint":case"symbol":case"boolean":return!1;case"function":case"object":return Boolean(e&&"then"in e&&"function"==typeof e.then)}return!1}(t)?Promise.resolve(t.then((e=>({value:e})),(e=>({error:e})))):{value:t}}catch(t){return{error:t}}}},974233:(e,t,n)=>{n.d(t,{C:()=>c,V:()=>l,default:()=>f});n(16280),n(944114),n(964979);var o=()=>n(206267),r=()=>n(449412),a=()=>n(534177),i=()=>n(155959);const s=!1;class c extends Error{constructor(e,t){super(e),this.name=void 0,this.message=void 0,this.data=void 0,this.message=e,this.name="HttpRequestError",this.data=t}}function l(e){var t;return e.offline?"Offline":null!==(t=e.body)&&void 0!==t&&t.debugMessage?e.body.debugMessage:`HTTP ${e.status}`}function d(e){return{"X-Notion-User-Id":e.headers.get("X-Notion-User-Id")||void 0,"X-Notion-Request-Id":e.headers.get("X-Notion-Request-Id")||void 0,"X-Notion-Client-Log-Call-Stack":e.headers.get("X-Notion-Client-Log-Call-Stack")||void 0,"content-type":e.headers.get("content-type")||void 0,"cf-ray":e.headers.get("cf-ray")||void 0}}async function*u(e){if(!e.body)return;const t=e.body.getReader(),n=new TextDecoder;let o="";try{for(;;){const e=await t.read();if(e.done){const e=o.trim();e.length>0&&(yield JSON.parse(e));break}{o+=n.decode(e.value,{stream:!0});const t=o.split("\n");o=t[t.length-1];for(let e=0;e<t.length-1;e++){const n=t[e].trim();n.length>0&&(yield JSON.parse(n))}}}}catch(r){await t.cancel(r)}}async function p(e){try{return await async function(e){const t={type:"application/json"},n=new Blob([e],t).stream().pipeThrough(new CompressionStream("gzip")).getReader(),o=[];for(;;){const{done:e,value:t}=await n.read();if(e)break;o.push(t)}const r=new Blob(o,t);return s&&console.log(`Gzipped payload: ${e.length} bytes uncompressed, ${r.size} bytes compressed`),r}(e)}catch(t){(0,r().O8)(t,{from:"httpRequest",type:"gzipFailed"})}}function m(e){try{for(var t=arguments.length,n=new Array(t>1?t-1:0),o=1;o<t;o++)n[o-1]=arguments[o];null==e||e(...n)}catch{}}const f=async function(e){var t;const r={...e.headers};let s;"GET"===e.method||"json"!==e.format&&"jsonStream"!==e.format||(r["Content-Type"]="application/json"),"jsonStream"===e.format&&(r.Accept="application/x-ndjson");let l=0;if(e.data){const t=JSON.stringify(e.data);if(s=t,l=t.length,"gzip"===e.encoding&&"json"===e.format&&(f=e.environment.device,window.CompressionStream&&(!f.isMobileNative||(0,i().T)("supportsBodyInHttpRequests")))){const e=await p(s);if(e){const t=await e.arrayBuffer();s=t,l=t.byteLength,r["Content-Encoding"]="gzip"}}}var f;const g={method:e.method,credentials:"same-origin",headers:r,body:s,signal:e.abortSignal};let b,h=e.url;e.environment.device.isMobileNative&&!(0,i().T)("supportsBodyInHttpRequests")&&void 0!==s&&(h=n(939768).O$(h,{[n(838364).nG]:s}),delete g.body),m(null===(t=e.eventListeners)||void 0===t?void 0:t.onRequestStart,l);try{b=await fetch(h,g)}catch(A){var v;if(m(null===(v=e.eventListeners)||void 0===v?void 0:v.onRequestFailed,void 0),A instanceof ReferenceError)throw A;if(A instanceof DOMException&&"AbortError"===A.name)return function(e){const{url:t,requestBody:n,headers:r}=e;return{type:"failed",offline:!1,status:0,error:new c("Request aborted.",{url:t,requestBody:n,responseBody:void 0,offline:!1,status:0}),body:{errorId:o().JW(),name:"AbortedError",clientData:{type:"aborted"}},headers:r}}({url:h,requestBody:e.data,headers:r});if(A instanceof TypeError)return function(e){const{url:t,requestBody:n,cause:r}=e;return{type:"failed",offline:!0,status:0,error:new c("Offline.",{url:t,requestBody:n,responseBody:void 0,offline:!0,status:0,cause:r}),body:{errorId:o().JW(),name:"HttpRequestError",clientData:{type:"offline"}}}}({url:h,requestBody:e.data,cause:A});throw A}var _,y;if(200!==b.status)return m(null===(_=e.eventListeners)||void 0===_?void 0:_.onRequestFailed,b),await async function(e,t){let n;try{n=await t.json()}catch(A){}const r=`Received HTTP ${t.status}`;if(0===t.status)return{type:"failed",offline:!0,status:t.status,error:new c(r,{url:e.url,requestBody:e.data,responseBody:n,offline:!0,status:t.status}),body:{errorId:o().JW(),name:"HttpRequestError",clientData:{type:"offline"}}};{const o=d(t);return{type:"failed",offline:!1,status:t.status,headers:o,error:new c(r,{url:e.url,requestBody:e.data,responseBody:n,offline:!1,status:t.status}),body:n}}}(e,b);m(null===(y=e.eventListeners)||void 0===y?void 0:y.onRequestFetched,b);try{var w,S;m(null===(w=e.eventListeners)||void 0===w?void 0:w.onParseResponseStart);const t=await async function(e,t){const n=d(t);let o=e.format;return"jsonStream"!==e.format||function(e){const t=e.headers.get("Content-Type");return!!t&&"application/x-ndjson"===t}(t)||(o="json"),"json"===o?{type:"success",status:t.status,data:await t.json(),headers:n}:"text"===o?{type:"success",status:t.status,data:await t.text(),headers:n}:"jsonStream"===o?{type:"success",status:t.status,data:u(t),headers:n}:void(0,a().HB)(o)}(e,b);return m(null===(S=e.eventListeners)||void 0===S?void 0:S.onParseResponseDone,b),t}catch(A){var k;m(null===(k=e.eventListeners)||void 0===k?void 0:k.onParseResponseFailed,b);const t=(0,n(319625).A)(A);return{type:"failed",offline:!1,status:b.status,headers:d(b),error:new c(`Unable to parse HTTP response: ${t.message}`,{url:e.url,requestBody:e.data,responseBody:b.body,offline:!1,status:b.status,cause:t}),body:e.data}}}},982254:(e,t,n)=>{n.d(t,{A:()=>a});class o extends(()=>n(757695))().Store{getInitialState(){return{tabs:[],currentTab:{id:void 0,isPinned:!1},unauthorizedIframeUrls:[]}}}const r=new o;(0,n(604341).exposeDebugValue)("ElectronAppStateStore",r);const a=r},994310:(e,t,n)=>{n.d(t,{A:()=>r});var o=()=>n(419494);const r=new(o().Ay)({namespace:o().Bq,important:!0,trackingType:"necessary"})},999822:(e,t,n)=>{async function o(){const{createDevice:e}=await Promise.resolve().then(n.bind(n,141625)),{Store:t}=await Promise.resolve().then(n.bind(n,757695)),{default:o}=await Promise.resolve().then(n.bind(n,52523)),r=t.createValue("unknown",{name:"horizontalSizeClassStore"}),a=e(window,{horizontalSizeClassStore:r});if(a.isElectron){const[e,t]=await Promise.all([Promise.resolve().then(n.bind(n,711059)),Promise.resolve().then(n.bind(n,219187))]);a.desktopAppVersion=e.formatVersion(await t.getAndCacheDesktopVersionAsync())}const{ComputedStore:i}=await Promise.resolve().then(n.bind(n,496506)),s=new i((()=>({...a})),{debugName:"SharedDeviceStore"});if(a.isMobileNative){const e=performance.now(),[{createMobileNativeService:t},i]=await Promise.all([Promise.resolve().then(n.bind(n,945522)),Promise.resolve().then(n.bind(n,475133))]),c=await t({device:a},r),l=i.getMobileNativeDeviceInfo();a.isIPhoneX=i.isIPhoneX(),a.isAndroid&&(a.mobileNativeAndroidId=l.mobileNativeAndroidId),a.mobileAppVersion=l.mobileNativeAppVersion,a.isMobileBeta=l.is_mobile_beta??!1,l.ramSizeInGB&&(a.ramSizeInGB=parseFloat(l.ramSizeInGB)),c.markInitializationComplete(e);const d={device:a,deviceStore:s,mobileNative:c};return{...d,cookieService:new o(d)}}const c={device:a,deviceStore:s,mobileNative:void 0};return{...c,cookieService:new o(c)}}n.d(t,{createMinimalEnvironment:()=>o})}},i={};function s(e){var t=i[e];if(void 0!==t)return t.exports;var n=i[e]={id:e,loaded:!1,exports:{}};return a[e].call(n.exports,n,n.exports,s),n.loaded=!0,n.exports}s.m=a,s.amdO={},e=[],s.O=(t,n,o,r)=>{if(!n){var a=1/0;for(d=0;d<e.length;d++){for(var[n,o,r]=e[d],i=!0,c=0;c<n.length;c++)(!1&r||a>=r)&&Object.keys(s.O).every((e=>s.O[e](n[c])))?n.splice(c--,1):(i=!1,r<a&&(a=r));if(i){e.splice(d--,1);var l=o();void 0!==l&&(t=l)}}return t}r=r||0;for(var d=e.length;d>0&&e[d-1][2]>r;d--)e[d]=e[d-1];e[d]=[n,o,r]},s.n=e=>{var t=e&&e.__esModule?()=>e.default:()=>e;return s.d(t,{a:t}),t},n=Object.getPrototypeOf?e=>Object.getPrototypeOf(e):e=>e.__proto__,s.t=function(e,o){if(1&o&&(e=this(e)),8&o)return e;if("object"==typeof e&&e){if(4&o&&e.__esModule)return e;if(16&o&&"function"==typeof e.then)return e}var r=Object.create(null);s.r(r);var a={};t=t||[null,n({}),n([]),n(n)];for(var i=2&o&&e;"object"==typeof i&&!~t.indexOf(i);i=n(i))Object.getOwnPropertyNames(i).forEach((t=>a[t]=()=>e[t]));return a.default=()=>e,s.d(r,a),r},s.d=(e,t)=>{for(var n in t)s.o(t,n)&&!s.o(e,n)&&Object.defineProperty(e,n,{enumerable:!0,get:t[n]})},s.f={},s.e=(e,t)=>Promise.all(Object.keys(s.f).reduce(((n,o)=>(s.f[o](e,n,t),n)),[])),s.u=e=>(({22:"CollectionChartView",48:"OrganizationOnboardingModal",209:"TeamJoinLeaveButton",287:"tourTutorial",943:"CollectionViewBlock",1042:"WikiPromoPopup",1423:"PagePropertiesRowNameMenu",1660:"icon-creditCard",1797:"consoleHelpers",1978:"third-party-scripts",2040:"emoji-norwegian",2144:"LoginWithTemplate",2147:"icon-deepnote",2268:"emoji-korean",2411:"localDuplicate",2501:"MarketplaceReviewNudge",2569:"AutomationModal",2597:"TopbarLayoutInfoButton",2686:"ImproveJiraSyncPopup",2703:"chatSidebar",2865:"RestrictedTransclusionBlockContent",2922:"icon-present",3130:"groupedNotification",3272:"emoji-french",3476:"breadcrumb",3551:"offlineSettings",3773:"TranslateBanner",3791:"icon-claude",4249:"icon-arrowExpandDiagonalBottomLeftToTopRight",4586:"linkMenu",4750:"TeamspacesPageEmptyState",4876:"moveToTeam",4902:"transcriptionErrorActions",5053:"ai",5140:"icon-page",5287:"CollectionLinkExistingModal",5815:"icon-apple",5834:"transformActions",5857:"icon-paperPlane",5938:"FilePropertyModule",5999:"MarketplaceReviewModal",6122:"icon-miro",6178:"DisconnectContactModal",6893:"notifications",6993:"isTemplate",7091:"opfs-console",7097:"desktop",7391:"icon-lucidspark",7457:"icon-infoCircle",7520:"selfServeBusinessTrialsActions",7717:"ExperimentSettings",7989:"PinnedInfoPanelToggleButton",8243:"AgentModelPicker",8340:"translation",8881:"icon-plus",8953:"SidebarTrash",9304:"PageViewBlock",9423:"emoji-danish",9555:"icon-trello",9647:"oauthAuthorization",9832:"icon-zoom",10143:"icon-microsoftTeams",10399:"workflowDeeplinkActions",10408:"customEmoji",10815:"DowngradeModal",10831:"collectionFormsShared",10871:"ConfirmOverwriteModal",11126:"contextualizedOnboardingFlowHelpers",11235:"icon-zendesk",11528:"UISpacePermissionGroupToken",11922:"TimelineItemDateRange",12039:"mobileBottomBar",12062:"CollectionItemNameConfigMenuItemNotInProd",12071:"addCollectionViewSourceToNewCollection",12827:"WorkflowLinkBlock",13017:"ConnectOAuthIntegrationModal",13073:"SidebarFeedButton",13319:"activityUpdate",13326:"sentry",14068:"errorBoundaryDebugger",14079:"icon-make",14201:"SnapshotAdminDebugButton",14227:"SharingEnabledPropertyNameTooltip",14310:"RecordMap",14344:"icon-xMark",14358:"UniversalQnaModals",14491:"selfServeBusinessTrialExplorePlanModal",15135:"icon-workflowy",15399:"WorkflowSettingsLinkedCollections",15498:"lowPriKeyboardShortcuts",15529:"WorkflowTemplatesOnboardingModal",15538:"icon-arrowInCircleUp",15711:"upgrade-actions",15780:"ConfigureIntegrationModal",15891:"icon-slack",15961:"assistantAnimatedFace",16006:"VerifiedPagesSettings",16130:"conditionalLogicTooltip",16345:"PublishSite",16453:"icon-plug",16999:"icon-confluence",17278:"emoji-thai",17546:"icon-googleCalendar",17586:"loadWorkflowTemplateInstances",18071:"initiateExternalAuthentication",18331:"mobileCommentsModalRenderer",18480:"offline-mode-launch-modal",18710:"icon-arrowUpRightSquare",18766:"RelationMenuRow",18905:"PermissionDebugCommands",19085:"transcriptionNewPageActions",19314:"icon-pagerDuty",19388:"icon-codegen",19512:"KeyboardShortcutsModal",19596:"organizationSettingsConsole",19884:"admin",20370:"trackDataSources",20488:"AddAnotherAccountLoginModal",20512:"icon-hex",20589:"PageHeaderInfoPanelToggleButton",20684:"icon-pointer",21056:"BackToWorkspaceCallout",21088:"AuthSyncListener",21377:"UpdateSidebarTabInfo",21446:"JiraSyncSourceTooltip",21697:"icon-grid",21871:"CollectionViewSettingsButton",21914:"icon-hubSpot",21990:"CollectionSettingsSaveControl",22348:"icon-ellipsis",22542:"formulas",22844:"workflow-console",22903:"SidebarSwitcherExternalPagesButton",22931:"CreatorProfileModal",23133:"CollectionFormEditorView",23285:"ErrorInfoUI",23754:"designPreviews",23873:"icon-excalidraw",23904:"PostsLaunchModal",24515:"RestrictedCollectionView",24598:"WorkflowTemplatesTooltip",24708:"WorkflowSettingsConfirmAddTemplate",24754:"ConnectionsIntroModals",25171:"InviteDropdownMenuItemLabel",25556:"use-page-subscription-manager",25583:"drawing",25928:"icon-squareGrid2X2",26207:"pageVerificationMenuRenderer",26403:"collectionDebugSettings",26845:"publicTopbarShareButton",27229:"SitesDomainsSection",27315:"emoji-portuguese",27476:"SecondSessionDatabaseLearningController",27689:"duplication-console",27761:"forkPageModal",27942:"ScrollableBlockLimitBanner",28307:"SidebarInboxButton",28763:"JiraDCSyncModals",28958:"inPageFind",29021:"recurrence",29136:"icon-arrowStraightRight",29348:"icon-people",29366:"katex",29409:"duplicateTo",29623:"SidebarMeetingsButton",29945:"RelationPropertyMenu",30001:"SpaceProfileSettings",30015:"PublicShareLinkLoginModal",30022:"icon-arrowChevronSingleUp",30214:"LoggedOutAppBanner",30260:"icon-sparkles",30313:"icon-arrowChevronSingleRight",30512:"icon-lucidchart",30603:"TranscriptionBlockDeferredUI",30676:"tableSelectionButtonAIActions",30737:"icon-dropbox",30848:"icon-notionCalendar",31061:"sidebar",31388:"selfServeBusinessTrialEndingModal",31517:"AppTemplates",31743:"selfServeBusinessTrialStartModal",32179:"icon-codepen",32203:"icon-loading",32413:"ModalOverlayCollectionView",32568:"DesktopEmailConfirmPage",32626:"workflowTemplatesActions",32644:"imageHyperlink",32831:"icon-cursor",33168:"icon-googleDrive",33429:"wasm-sqlite-shared-worker",33590:"PublicPageSidebarContent",34208:"pageTemplateModalActions",34251:"icon-dashlane",34420:"contextualizedOnboardingActions",34616:"icon-gear",35115:"sidebarMobile",35364:"icon-inVision",35390:"emoji-german",35537:"formulaPermissions",35756:"CollectionViewSelect",35818:"radioModalRenderer",35905:"CollapsiblePanel",36432:"UncollectibleExperienceOverlay",36448:"icon-amplitude",36733:"primusV8",37043:"framer-motion",37045:"createAndDuplicatePageInSpace",37062:"PostImportIntroPopup",37125:"comments",37342:"AIChatStore",37353:"markdown-linkify-it",37398:"GoogleImportContactsButton",37565:"icon-medicine",37596:"canvas-confetti",38507:"SidebarAgentsButton",38949:"icon-polymer",39047:"opfs-page-cache-worker",39301:"PublishedSiteSettings",39689:"icon-pitch",39778:"CollectionSettingsCustomizeMenuPrototype",39953:"icon-jira",40198:"pageCovers",40200:"CreatorProfile",40418:"icon-code",40432:"TrialInfoModal",40454:"ActionBarUI",40471:"icon-abstract",40589:"tableSelectionOverlayPropertyActions",40642:"NewDomainModal",40902:"SidebarLibraryButton",41091:"slashMenuIcons",41135:"personalHomeTipsHelpers",41412:"pageVerificationBadge",41835:"icon-arrowSquarePathUpDown",41837:"icon-mixpanel",41882:"WorkflowTriggerConfiguration",42050:"createHasSchemaChangedForQueryStore",42481:"HelpButtonContent",42704:"TranscriptionBlockTabMenu",43024:"MeetingsPage",43151:"peekRenderer",43396:"posts",43862:"icon-whimsical",43935:"TableHeaderPropertyCreationMenu",44032:"icon-checkmarkCircle",44158:"automationTypecheck",44225:"icon-asana",44425:"offlineSync",44539:"icon-figma",44632:"PersonProfileContainer",44745:"UnlistedCollectionViewMoreButton",44802:"personalHome",44852:"icon-microsoftOutlook",45016:"Onboarding",45248:"PulsingWrapper",45624:"PerformanceToolbar",45758:"integrations",45813:"publicTopbarLikeButton",45915:"LanguageSwitchPromptPopup",46204:"SiteSettingsLayoutEditor",46283:"AgentChatView",46726:"TranscriptionBlockSettingsMenu",46743:"PublicSiteBanner",46933:"exportJsxRenderer",46990:"BlockPropertyRouter",47057:"StartupsApplication",47108:"AIChatView",47161:"PostUpgradeAnnouncementModal",47307:"SharingEnabledIcon",47381:"Toaster",47536:"CollectionTasksEmptyStateModal",47779:"TableHeaderCellMenu",48071:"activityDigestPostContainer",48486:"loadLocalSubtree",49044:"icon-alarm",49222:"mobileActionBar",49229:"icon-replit",49291:"icon-chat",49373:"icon-googleDocs",49875:"CollectionNewViewPopupComponent",49908:"TooltipOnSlackEmbed",50437:"WorkflowInPlaceEditor",50462:"CalendarAgendaView",50568:"HIPAASuccessModal",50708:"icon-browserAdd",50710:"icon-personCropCircleBadgeExclamationPoint",51092:"ExpansionOfferAnnouncementModal",51106:"emoji-dutch",51207:"AgentPage",51226:"GoogleImportsInviteWithModal",51363:"TeamHomeMoreMenu",51466:"RemoveAddOnModal",51609:"outlinerViewAll",51646:"SelectableHoverMenuOverlay",51680:"CollectionMapView",51799:"WorkspaceDiscoveryAdminSetting",51859:"FormShareMenu",51872:"emoji-english",52009:"SlideBlock",52084:"icon-clickUp",52274:"UnlistedCollectionViewDismissButton",52573:"ResearchModeNewPageEntrypointModal",52583:"icon-notionMail",52614:"floatingTableOfContents",52840:"AIForWorkModal",52903:"ConfirmPrivilegedActionModal",53095:"coediting",53147:"FeatureIntroPopup",53179:"topbar",53631:"emoji-swedish",53638:"mobileCalendarDayMenu",53925:"LeaveWorkspaceForSharedPageModal",53928:"MarketplaceThirdPartyLinkModal",53974:"icon-twitter",53977:"OutlinerToggleOpenSetupModalButton",54087:"restrictedContentDialog",54134:"icon-personCircle",54215:"MaybeMarketplaceReviewNudge",54220:"simpleFormulas",54398:"MailLaunchModal",54518:"dictation",54564:"githubStudentPack",54928:"topbarActionButtons",54980:"icon-commentInfo",55072:"WorkspaceDiscovery",55632:"icon-priceTag",55724:"icon-youTube",55776:"formulaEditor",55901:"SlackImportContactsButton",56188:"JiraSyncTeamSpaceModal",56301:"selfServeBusinessTrialLossAversionModal",56353:"oauthPostLogin",56388:"MeetingNotesPrelaunchModal",56409:"activityNotification",56859:"DomainsSection",56892:"FeedViewOnboardingTooltip",56989:"UpgradeRequestFormModal",57314:"restrictedAccess",57478:"ContactSalesModal",57613:"personPropertyInviteActions",58204:"TipsInAppModal",58251:"CreatorProgram",58319:"icon-linkedin",58427:"meetingNotification",58587:"chrono-node",58596:"FullPageAIChatLoader",58657:"initiateExternalAuthenticationFromDesktop",58703:"AgentThreadHistoryMenu",58777:"notionCalendarAuthorization",58790:"PerfmarkTrialIneligibleToast",58795:"RecordModel",59042:"icon-badgeCheck",59082:"icon-arrowTurnDownLeft",59111:"formPropertyRenderer",59287:"icon-office365",59337:"emoji-finnish",59388:"serverBackedLocalDuplicate",59430:"icon-coda",59698:"icon-microsoftSharePoint",59970:"payments",59994:"icon-squareOnSquarePlus",60213:"SimpleTableSelectionUI",60245:"icon-bitwarden",60280:"ClientPreviewBanner",60453:"PasskeyNudgeModal",60858:"icon-notion",60896:"invoice",60983:"icon-heart",61242:"icon-addSquareRounded",61362:"switchFromTabbedToSimpleLayout",61440:"offline-pages-listener",61487:"icon-typeform",61488:"crdt-debugging-overlay",61492:"icon-quip",61560:"MailLaunchModal2",62033:"topbarSidebarButton",62058:"MobileNativePerformanceListener",62342:"contextualizedOnboardingHelpers",62396:"icon-globe",62516:"RecentsCachingListener",62620:"CollectionNewViewsPopupComponent",62989:"SimpleMonacoWrapper",63075:"UnifiedChatInput",63137:"createPageInTeamSync",63538:"BuilderAddModuleButtonEducationTooltip",63539:"mobileRedesigned",63717:"extendedUserProfileActions",63990:"switchFromSimpleToTabbedLayout",64087:"icon-exclamationMarkCircle",64199:"message-store-debug-panel",64457:"icon-clock",64557:"icon-sketch",64626:"feedPage",64632:"icon-eye",64981:"MicrosoftImportsInviteWithModal",65015:"emojiData",65129:"react-pdf",65213:"subscriptionActions",65688:"FormBlock",66042:"mobileNativeFullPageComponents",66073:"MarketplaceEmailCaptureModal",66169:"LazyAiLandingPageExperimentSwitcher",66249:"SidebarBottomActions",66549:"SecondSessionDatabaseLearningTooltip",66592:"CollectionSettingsCustomizeMenu",66805:"verifyPasskeyRedirectPage",66960:"PageError",66972:"icon-arrowDiagonalUpRight",67045:"AddPasskeyRedirectPage",67199:"icon-microsoftWord",67601:"icon-basecamp",67678:"icon-arrowStraightDown",67920:"TransferWorkspaceModal",68070:"minisearch",68117:"SlackImportsInviteWithModal",68126:"LoginWithAIPrompt",68224:"publicSharingTopbar",68280:"offline-mode-sidebar-callout",68540:"TranscriptionBlockSecondaryUI",68548:"react-day-picker",68642:"icon-loom",68744:"JiraSyncInfoPopup",69095:"formEditorActionButtons",69181:"collectionSchemaErrorActions",69184:"clipboardActions",69224:"CustomDBPanelEmptyState",69307:"PrivatePageEmptyState",69734:"codeBlockValue",69945:"emoji-chinese",70074:"icon-zapier",70216:"@simplewebauthn/browser",70522:"updateSyncedCollection",70568:"SidebarSwitcherYourWorkspaceButton",70593:"LargeSurfaceRenderError",70605:"views-main-builder-modules",70916:"icon-gitLab",70938:"icon-newspaper",70959:"icon-arrowLeftRight",70998:"about-developers-modal",71204:"workflowTemplatesHelpers",71677:"TrialModalsDeps",71739:"offlineDownloadIndicator",71768:"icon-onePassword",71830:"icon-templates",72423:"icon-microsoft",72925:"icon-arrowStraightUp",72982:"SidebarComponent",73006:"external-object-instance-block-menu",73090:"CollectionSettingsConnectedRelationUpsell",73502:"StudentAiOfferPopup",73801:"serverDuplicate",73865:"icon-airtable",74085:"mobile",74688:"callouts",75152:"icon-arrowUTurnUpLeft",75436:"MidtermCheckoutModal",75528:"wasm-sqlite-worker",75681:"postRender",76378:"icon-bubbleEllipses",76793:"formulaAnalytics",76983:"moveTo",77282:"RequestMembersModal",77355:"icon-instagram",77375:"icon-openAi",77651:"GeneralAccessPermissionSection",77757:"emoji-japanese",77761:"ConfirmHIPAAModal",77773:"miniagent",77836:"AgentsPage",77957:"icon-oneDrive",77970:"trialActions",78171:"SitesSettingsTab",78217:"mainApp",78389:"icon-gmail",78472:"icon-google",78724:"icon-maps",78984:"assistantWriter",79239:"LocalizedTemplates",79254:"icon-adobeXd",79665:"automations",79883:"CollectionItemCover",80071:"buyerProfile",80401:"icon-link",80931:"icon-tiktok",80983:"MapTile",81074:"imageEdit",81330:"PresentationMode",81561:"opfs-meeting-notes-cache-worker",81638:"icon-emojiFace",81764:"icon-salesforce",81929:"onboardingActions",82028:"PingSalesModal",82094:"SavePageToOPFS",82106:"icon-questionMarkCircle",82120:"PropertyModulePersonProperty",82430:"errorBoundaryDebuggingStore",82926:"AIModePicker",82970:"contactVisitButton",83433:"icon-megaphone",83728:"topLevelMobileComponents",83755:"AdminReactJson",83965:"GuestUpsellModal",84084:"userSignalsHelpers",84217:"icon-trash",84358:"WorkflowModuleConfiguration",84455:"createRelationViewsModule",84605:"personalSharingComponents",84672:"ReferralAnnouncementModal",84909:"icon-hourglass",84969:"StudentGroupSignup",86227:"collectionFormsEditor",86279:"LennyApplication",86774:"BlockErrorFallback",86967:"confirmDeleteLastViewOfOwnedCollection",87102:"icon-framer",87137:"tabSpacesPopup",87153:"icon-smartSheet",87543:"spreadsheetPrototype",87700:"WorkflowPageBlock",87771:"TrialEndModal",88208:"ConfigureViewsMainModuleAddViewButton",88472:"icon-box",88488:"AliasBlock",88873:"TabletSidebarButton",88895:"AllProjectsTooltip",89041:"teamHome",89253:"icon-evernote",89256:"icon-aiFace",89440:"monaco-editor",89509:"icon-personCropCircleBadgePlus",89920:"transport-support",90518:"InlineRenderError",90871:"duplicateActions",91221:"referralTrialIneligibleModal",91325:"PublicPagesSection",91795:"CustomizeMenuConnectionsSettings",91866:"topicRecommendationsPost",92214:"prism",92222:"RelationPropertyCustomizeLayoutMenu",92579:"WorkflowSettingsViews",92668:"icon-pencilLine",92821:"workflowNavigationHelpers",92903:"slashMenu",92919:"partnerProgramOnboardingActions",93139:"workflowNavigationActions",93629:"MicrosoftImportContactsButton",94146:"tabHistoryPopup",94153:"BannersRenderer",94211:"GithubAIConnectorAnnouncementModal",94366:"notionCalendarLogos",94412:"LockedSidebarSection",94495:"agentAnalyticsActions",94516:"MinimizableCollectionViewBlockHeaderActions",94611:"WorkflowTemplatesBuilderNotInProd",94802:"icon-docBadgePlus",94814:"chatSidebarModeButton",94869:"GlobalInAppMessageListener",94891:"pageVerification",95264:"emoji-spanish",95281:"SecondarySidebar",95496:"tableSelectionOverlayActions",95699:"FullPageAI",95737:"icon-arrowChevronSingleDown",96029:"icon-arrowInCircleDown",96166:"icon-book",96304:"mermaid",96316:"tinymce-word-paste-filter",96431:"workflowTemplateOnboardingPills",96772:"icon-checkmark",96773:"icon-microsoftOneNote",96966:"login",97101:"automationActionRegistry",97132:"WorkflowSetupInstructions",97651:"icon-exclamationMarkTriangle",98012:"twitter",98288:"ExternalImportAndSyncIndicator",98396:"PageLayoutModuleErrorFallback",98682:"SearchMiniSearch",98713:"icon-monday",98814:"monaco-editor-react",98906:"DeletePaidWorkspaceModal",98942:"contextualizedOnboardingStageHelpers",99013:"workflowTemplateTourTooltip",99108:"collectionSettings",99171:"selectableBlockActions",99223:"RecordStore",99314:"legacyTransclusionActions",99355:"subscriptionDebugCommands",99426:"JiraAIConnectorAnnouncementModal",99718:"teamspaceMenus",99998:"icon-protonPass"}[e]||e)+"-"+{22:"e1829f7eed29e4dd",48:"13f8db547b7d49f6",126:"828004b8daafdd49",183:"cbd6b5637f32367e",209:"eb2d1c0cbec2c28f",287:"04f44bc3214eca91",481:"b924a145411b4080",943:"033817a0378af218",1042:"f58724b1b0e6dbc9",1046:"57871252816c75dc",1117:"b47e605f35a0b9c0",1122:"7281fc3227a9e4a3",1172:"11927094c56cdf58",1423:"a2f4ae43f0559512",1438:"1d50c438178d1f3a",1440:"02afea45cfe15200",1442:"5f0614ed161d99ae",1560:"d3db3b07afeb8b34",1615:"d5d2f845c29451dc",1660:"646e5ce32eb1e551",1694:"14f12190c94a58df",1707:"d6512d05c2540d24",1797:"22ba573aee74b357",1827:"7a890a071150a2fc",1978:"80ab9e7c52af7013",2040:"2d34b37851c55994",2144:"378ea93b4aeb2bcd",2147:"fb6de0d980660c16",2264:"01d86034a2027518",2268:"73e5bf159667bb10",2278:"ae1fb49dac2d7b3b",2287:"a62fd22d39dd834e",2411:"867a0aa86689229e",2485:"effc5d2bfcfd3cef",2501:"54f19ad919936eb6",2569:"8aaa921343410172",2597:"a93fb3cadb52e9be",2611:"b9fba9a19ba2f4a2",2681:"d715e735e24549f0",2686:"7db851b95b0a2bd8",2703:"35db87178e2fd09e",2865:"5e9e7e4a59e973de",2922:"4ad18f239030f984",3071:"f6c380119c40a01d",3122:"4d774c0c1eb33d57",3130:"500c51fec5997e94",3151:"2c8788d90385e1cb",3185:"bdf5abfc30e179ff",3263:"359f40109a24a676",3272:"f930bd919d69aa0b",3476:"9d51df5a7b790841",3551:"4534d3b06510c68c",3581:"d938afac2fcb9a1b",3622:"606924f3b1a6554f",3723:"629d17c495af97e0",3773:"5d1e9b8cf277380a",3791:"cc42a778576e0c5b",3862:"933f97f99d0de46a",3953:"649f1f7a1045cf3c",4115:"b1513fe3f850fd83",4120:"82de7ce789b56499",4223:"bdf5abfc30e179ff",4249:"51e17a58110c05fd",4330:"add01dc7aa5f24fb",4422:"9f9b3b9692c3a57a",4430:"4cbf355b63bed838",4586:"02a9386d46f3bf39",4590:"17e0144142bc5ca1",4614:"bdf5abfc30e179ff",4643:"ce198765a9d0ed95",4694:"77e10d25a9cd43c4",4750:"841ad5887c9cdff8",4876:"47e54117afcd8a18",4902:"d97e189cd23e2e14",4964:"030f43897a5ba43f",5053:"234a879b0efc86ba",5130:"5a715a8b075d897f",5140:"2d3f986edaf3c0c1",5268:"facf8af460088df8",5287:"627036a3cf8bcdb5",5605:"23ed337b4a23ca84",5815:"747e55c5f41d66b1",5834:"867a0aa86689229e",5857:"d9ddcddf41b6290c",5938:"65f2ede401ee4825",5999:"5252a85820a79fbd",6040:"3872c7c4cd60d156",6122:"d1bfa7135fd2fc38",6178:"ce2d91a3a37ecd75",6258:"5207d6a8d46c9f73",6452:"14967781fda795a7",6637:"334c5b6c486ab56c",6893:"1ffbe2952b42ba6d",6907:"2c049a46cb32967d",6960:"dcbb4bcb130070bf",6993:"0b0cd43fe2931004",7091:"18af0b9b92a3505d",7097:"f4432a6f43b32747",7103:"d68440ce64320081",7391:"663212404aff9b87",7396:"fb09ee4624704d26",7439:"8c905fd1aa159188",7457:"7151173d59f755df",7520:"a064f3586d9bab5b",7717:"7aca31b6458c7434",7759:"b0250318c9617569",7972:"b6cbbd49929c5630",7989:"06382bf17deed506",8142:"ad34c39dc56761bb",8184:"7d34ea4017ece276",8243:"8b171cbd4d90a101",8247:"4417a5abd9a6c85a",8340:"86441ac3d7efd661",8418:"e2ef503abeb5a589",8461:"6ec7c6ce6e608fda",8589:"c7e1550dac92a597",8592:"90e8ddc57505478b",8675:"32cc098be4e99d49",8841:"6766bd1d3e4467ec",8881:"d2d4742d05e0bf86",8953:"23f2c46e5573f4a9",9304:"b04b38a627723011",9423:"1da3914cca569dbf",9426:"60857f4532eac4fa",9555:"57786065661bead8",9562:"276580293b5f6c13",9586:"8a518c991e09f223",9647:"6519ed1ecc772f3e",9773:"b6a6a23fcfa4efef",9832:"e7ff5e755ebc6845",10143:"bc7c4f5f7c0ca4a3",10251:"f0e0df594a99db46",10375:"4d44334a6f75d0ba",10399:"96d94d3537a013af",10408:"e641e79326d33253",10494:"e7a48a045c042bd3",10809:"debc6d4cba785ceb",10815:"35362ef5e73d645f",10831:"ab575051fb4a6277",10871:"0330ec9e60335d54",10890:"497d56f646e156f1",10957:"b924a145411b4080",10968:"8cb102118b9c5a3f",11072:"63d0f6bae65d4bb6",11126:"067b3eae1812e38c",11135:"6e283f404707967f",11235:"71ee1694e76d7665",11341:"a0b1f0c41be9fca2",11528:"1ceae1dffde45b99",11621:"e7925b0b265a9e47",11674:"6a1aff671896d132",11676:"d1b4df0fd3b9099b",11888:"eab84b4ce5b5219b",11922:"74d3fdfb8bfc6d40",11939:"980b87bb4fd46ad6",12039:"8d715fc7fb9b4b27",12062:"c2387522ca137c6f",12071:"a248a850cf09c2c6",12119:"e870b55e7f81e0f8",12244:"7522a8bfbb212ce6",12437:"7565bce14ceeee8e",12564:"d4890063901ad50a",12720:"7df358ae8413318f",12827:"edaf41e6937946c1",12978:"8c29fec9515945b0",13017:"f64921285545de53",13073:"e0f4e74c27cd54be",13208:"445960192b0644da",13319:"130e2b92fe31ba1f",13326:"defef6c82f54860b",13450:"58dc9c8ec3576cbe",13485:"fce5d55caf4df590",13707:"66153d7b75fcc921",13726:"cf736f0131fb023e",13788:"6fdcf3ddea33db41",14068:"4b2b6b0d3ad8992c",14079:"1739edc8105c6f3a",14201:"6650ada7a086fd60",14220:"a5435ef9d56246e2",14227:"2d4cdb5247981ac0",14246:"2b990a0a76e048af",14310:"51df2cd615b3721b",14329:"69802123dcb42208",14344:"6361d649666c9420",14358:"45339fd970562998",14491:"3cf0d40266834572",14586:"4da056cad0821e06",14699:"bdf5abfc30e179ff",14897:"8d73670396c2afbc",15090:"bdf5abfc30e179ff",15135:"5577e59d5f817e0f",15139:"26bc237dff4d77a8",15399:"db125371d31db90e",15498:"8eb8fffde75b6334",15529:"db2ae466f03cf920",15538:"1f979be6ea1a397a",15662:"7103defeb52e11aa",15711:"ba31df622af94984",15780:"57530550db899f84",15830:"69199099df8fe13e",15891:"0726af2fce3d193a",15961:"bf62d59fa9a34f17",16006:"ab6cae0d2c3f77aa",16079:"08dfc83a79c99f01",16128:"bdf5abfc30e179ff",16130:"66e5f4720e24a75c",16134:"8323a7afc6a6750c",16195:"6298fb8081917b00",16345:"8fe075b067ee2b69",16453:"94cff9012fe35290",16471:"2ca78723661bf4a6",16708:"74d848a21671bf3e",16742:"414d3374b672bf02",16767:"b661651142cfbde0",16939:"dc4d4fbe1a283313",16999:"51345101b0f1e4b2",17217:"9bba5f6ea6ad9308",17230:"03a704aa5e4cc0fc",17254:"e813cb5a44012102",17278:"8bf866cd19024db0",17546:"a9ee11ae6078cdd0",17586:"0ec2bae2e24c7e5b",17603:"769ab6818bf3c834",18051:"4df174b210a35017",18071:"f78e7c1acf132a02",18183:"69964ddf4cf10d2e",18331:"ad8ca1238a7630cd",18480:"c52a7fa4095fba1c",18563:"212d77741348ab34",18679:"7e5c2f949cae7864",18710:"88b9c576d7a11871",18766:"2018d3976cc91be6",18905:"6d90c142b0ae820d",18959:"c8568ea3b7714d10",19085:"52713046c1bb3acb",19131:"5d4b0a8d4258bcf3",19314:"efac8244ce78d739",19388:"e88c776231586c15",19512:"bac05ed1ed96bf3b",19596:"a67d31c81ee6f8ef",19617:"747c82e86581f144",19801:"cba01b2a10d19237",19860:"8aeedfa36baa277c",19884:"4916fe1a38ec7a1d",19980:"13a8c5ea3680573b",20108:"c8d12f2a80bfdd34",20148:"a7e1e9d111c3c126",20283:"7b32ed935cbd50a8",20370:"9b1d915196ed1d08",20488:"5b6b1a791ed522e8",20512:"d6c7003528e61e72",20589:"c97826d53d403cfa",20637:"5598139d92bfaca5",20684:"fabc84908946d99b",20873:"6d12ff8fb3ea6060",21007:"f27c8a7c5c0b4096",21042:"09f1ecd88405a258",21056:"36ba649d7dac9907",21088:"d2ae2760c1cae2a8",21118:"d878e09dcae4e881",21194:"4a41e0cc2b26fff6",21377:"6bea8ef463702f51",21379:"b41aba3e6bc310df",21420:"1403ef34f10cccc7",21446:"6379df8197d9eda8",21491:"1561a9d6bbc389a5",21610:"d92c4a55a44e12be",21697:"f5e2418ea1b5ee03",21871:"47d9ce2a1e601e3a",21914:"0310821072e53ac6",21932:"409b79e425ac8f98",21990:"e06de3c6019d20ec",22094:"0120fe7319ff2231",22114:"6630302d383faab6",22130:"b8fc5031e6b4c707",22348:"ecd5c59003fe1816",22542:"354899cd1b61ef59",22586:"731fa142730d2772",22844:"57a438a0df69c2f4",22862:"b924a145411b4080",22893:"7413beecb88cf39e",22900:"4faf6c932d9bd461",22903:"fbb09b05c3535411",22931:"48f8e57465426532",23133:"139ee0d7af853d57",23202:"70f5da0351c2032c",23285:"824c160893809344",23740:"7a011f5781d8dfb4",23754:"4bcc7663533720ab",23873:"699d8ce2463401fe",23904:"2db6052b08d2c06a",23930:"3e39c12675112777",23993:"6e7bbab4df58f152",24022:"6e565fe9b5eca107",24119:"c8e5be136f05110f",24515:"0cfdac8942cb8a21",24587:"a66fdac7367e080a",24598:"8e8cf53751164f07",24630:"c314553797409b13",24698:"afe90cb2dd21d57a",24708:"0ebad6a0686b6cb8",24722:"22d3f7a6f17c03e4",24754:"230230b76b198887",25171:"7b0944f94fae7dbb",25196:"87eb340028008ef6",25364:"c11ebca72b71bf33",25406:"4f48d090c7795a5c",25409:"4776a7f1405291ee",25556:"75ac269950252e5c",25580:"1f06d1358f527232",25583:"f3d2fc97f3700659",25588:"5ca9c3b06c941a3b",25618:"dd95391ba22c0be4",25647:"ac82d16915ce8eca",25700:"52cf3ec9bc253724",25786:"424cc02322dfea45",25891:"d7d22fdc4ddb1837",25928:"a575b799c3efcf48",25966:"b23cc20ce12e284d",26018:"7b111dba61113cd3",26180:"cfc4cc8f67d59409",26207:"8e12118b606ce120",26346:"4b2125948ea25561",26357:"d0b52b7b80ebd0b2",26403:"0639cf10274efd7e",26414:"7de29998b6b2c7c0",26483:"059fa521bcc24faa",26545:"0990ce473c33d361",26590:"81a90aed52b7820a",26604:"bdf5abfc30e179ff",26845:"203b26fd99c67b09",26942:"5c9ddbbe0b7b98d3",26990:"cd0dc6df5b540d32",26995:"bdf5abfc30e179ff",27056:"16aff484a476cfa3",27164:"ffae5b1a88feb031",27229:"8b74e2433c2bb692",27315:"a9f954b80c129da1",27476:"3dfa975e232b5b02",27616:"fe72cad3c6bf53ef",27634:"fa9abec011be461f",27689:"16313f219fed40c7",27761:"addb7b021a6b4b40",27796:"dff08994ad855cc9",27882:"25cf3173b3dda7f1",27942:"86a432c82b355917",27991:"7e830464d399069c",28137:"70505080613414a3",28184:"2e2a9051166f51f1",28230:"f3ce45ae8ac1a53d",28271:"144d5b4e96b9afc3",28307:"b14d815d425dcb77",28323:"53e578eed96ff8e6",28366:"97c9a06a10f6e167",28398:"c68098212adba37c",28464:"e0c58af431a49163",28480:"07b230920b8570f7",28566:"1bd0ff3ebdb178a9",28763:"eb80841a0f426101",28866:"c6101e2524aad374",28908:"a229ccb1b4f19269",28958:"5c883ff03ab1fe6b",29021:"1537f53f989302a5",29136:"ccbae2b5c8c2efc5",29151:"bb0bce8f852da946",29252:"aa69cb479b062392",29288:"a00764aa71f30766",29348:"5eaeb704151d2407",29366:"377e28d9287e8779",29390:"3c953c883a909e27",29409:"b3cc5ea1897ac45f",29422:"b4f98f6163cc7996",29485:"d5703e70c94f71a5",29510:"3f8335f31330ee1d",29623:"8296e2a90924ff80",29750:"4be693dad7b33fd9",29815:"d5f3e48dfbcec13d",29875:"2dc3e26543192e96",29932:"b6711d6df45519bc",29945:"dc99137d077b52fa",29995:"e5fb9fe34759e76e",30001:"f5528c7c540b3c42",30015:"4141a0226cd0e3da",30022:"bba071b84e2d3dc9",30030:"3d44541cba1972ee",30079:"05d52ed9547da906",30116:"e3cd7b20412cafb3",30189:"4c04c0feaee9017c",30214:"9593733c7ec2ab56",30260:"1841c2242312b054",30291:"21b42be23512d021",30313:"fe3a655c65d8802d",30431:"eaacb7db1eb13137",30512:"1a805d12226ad534",30603:"cb553cca153a2553",30676:"1ac463a6efe68fa3",30720:"79003469425461da",30737:"387431631859b1b5",30753:"d57ea658af1aa340",30848:"a374962e0d2f9d94",30858:"26f863bc0be7f06b",30970:"c37048bf1a1739e1",31003:"8a78ca1339919e95",31061:"dddfbdf12fd15a5c",31131:"237f2e2bef5c1ce4",31276:"b49a54233ef213a5",31331:"2f816f4a30a0445f",31343:"872c075d8f229f06",31388:"14ea6dbd6ed91c03",31414:"8c87ed39d44fc1d9",31517:"3d6f8dbbd710b409",31743:"9705c9fa533a68c9",31857:"bb8285506800cd9b",31965:"f6de0297e6d3481b",32148:"6b8d408482aff1c5",32162:"95fcb2a0bcc7be2f",32179:"fcf1b208672c139a",32203:"12b0c7c3e5a9b029",32212:"dd2a60ccefa47d36",32221:"62f14c5353806cbe",32254:"75be5ccfdd4632aa",32257:"3b884a9854b32d64",32296:"1c511ff7eed2a40b",32413:"793abcfaff92bbd0",32568:"b9f126819dce543e",32594:"885bfa9e25fbe366",32626:"32856de97b295347",32644:"8a8cccb6d6ebb220",32686:"c7219aff4e87a2e6",32715:"de5a60cfa2062ad1",32769:"9d9d37482419b963",32831:"f61926412466e9e5",33004:"626dfd52d54841a0",33019:"7441d748559ef656",33081:"7b811b9124afff40",33112:"e3f18d88f023f9a4",33168:"b9e859246f674d24",33277:"bcc8fb5b04486837",33338:"b924a145411b4080",33358:"7c3a92572af36790",33429:"e1c5304f2723af9d",33438:"4ee7e326f23832d3",33535:"3af456baedcb1ba4",33590:"418cc927697ff739",33882:"87e6411f6a144a16",33931:"dde902d8c900f030",34208:"7351ca91c5db973e",34251:"4d8888c75fcb82f1",34273:"9e7572951bdec148",34359:"aea9ec81f15c8153",34420:"20e25605a155b26c",34438:"be6c2cde0ba94cce",34442:"e57c1d9ccd9a54cb",34526:"4810a8e4d8932d4c",34616:"cf7de4dfacd1f1bd",34709:"5eebca73c0512408",34877:"dc2fc594fd194413",34958:"cfd74213cfffc150",35094:"53686104240dd73d",35115:"03e41a28a2edfc10",35189:"7415095005e6dc8f",35364:"bb7752539be1c4f8",35390:"49684c2568f6b572",35503:"03e9607c316a0d80",35537:"0caab74eebe45a92",35578:"96f8eacc68111676",35610:"adf1515262d9c8b9",35643:"373090820b388393",35733:"166b6cde66a97dd6",35756:"08a4c993552d3f72",35818:"059cc5fab85eb426",35837:"de52cc7e7c0322b5",35839:"70b788765cd9451e",35905:"4126834989a1047a",35986:"a29fc0a7ba076362",36192:"5de88357b680e05d",36193:"f5a8bb44497308e2",36417:"58e2b4ce9b6f470d",36425:"c52dff7d19308e24",36432:"5bb30a236ede4704",36448:"0bff9cc26036e55c",36479:"bbe01c00cc9c622f",36556:"8a800e0fbeb11cc5",36639:"84c12b0331d2c039",36733:"29cd429bc0631283",37043:"7100f8c4c578637b",37045:"ae644662cd1a85e8",37062:"4a7be8a6dc3138cf",37080:"bdf5abfc30e179ff",37125:"2a776092aef846f3",37160:"aa8bbf1e72720840",37217:"eef94fbca4ccd46d",37303:"42ac1919d34907e0",37342:"77c2f130fb500404",37353:"e53def7f0e5e64ef",37398:"88884426b8a57020",37471:"bdf5abfc30e179ff",37565:"f527056a8a9895cf",37596:"04ac66e6bdcf7702",37636:"83311b0ab995392f",37748:"c846ed77d18b1296",37763:"bc97f1900449e83b",37972:"bd2ba8359786520d",38334:"bf3304579d6b974e",38405:"4961cd7214de5a98",38507:"6e171c7613dfa3c5",38536:"01006d6616a98464",38949:"3f8fae881b6dd38a",39047:"0b6f5194b0742294",39130:"0902e1c90bcdc37b",39285:"5577ed183abdee87",39301:"0ffb77ee8e30c8c4",39438:"f62cd30e5685fad3",39689:"f38f444534ad58b9",39720:"e57f99ee90f422ba",39747:"7b40a723ad98e10d",39778:"b94825188c74d61c",39899:"b5235e1d1f07755e",39953:"a5472c18ec0e743c",39987:"213c0b48c970b425",40126:"8a72b1bd531e18a8",40198:"ed401e0016eadb87",40200:"2da94edefd82d88d",40241:"68149770017a01ea",40418:"3c56eaef4c313192",40432:"3e9ce12c5b6b99a5",40454:"dca1839acb5c2fd2",40471:"eea64397a7eae7cb",40532:"60013cd30b9835c1",40537:"35a3fa18eb681690",40589:"68b674b35f6787b2",40642:"f81090aef44f108a",40902:"b9afaf1947e22b5d",41091:"385016311c83b8df",41135:"47d8fb4f9bdfab82",41265:"31fbdcbb2f7bb3e7",41412:"56fce8cdc9258359",41670:"c88aa789fdaa6ba9",41740:"d3b8ae0e08ec94aa",41805:"972c0bea4fa4b3e2",41835:"3833c2793cf364a5",41837:"9d92dbdfc2a3d343",41882:"b40e968bdaa13ddb",42050:"a4979fff388d1c59",42315:"1e7da88d631e5a8c",42399:"0265cfdd3557f29d",42440:"c6aec7b17292ba82",42481:"c1d4a3e50dd98649",42525:"994f1a389e4f7135",42660:"8c45df0c3d06fe90",42668:"057ea70144c8d8c2",42683:"b30aeddb0a7b19b1",42690:"9d6110916ec9c78e",42704:"d6820165718f3fe4",42838:"43b7a690c7a3e8a3",42896:"569f717953af7cea",42951:"75abbb9c21b7f7dd",42962:"c1747c128e813044",42975:"9a0e00c182dd5490",43024:"41594e4986fe4304",43151:"0285945cf9632763",43152:"436198bb3fd02d55",43182:"b071f6db81e5a475",43249:"f8b156ee7569a710",43274:"d116862691025aa9",43396:"4cafc839a074bc17",43459:"83436b4de366c542",43497:"e6c450bf9b588d53",43741:"6222a736882c2b20",43774:"aaa98de5eb6cf7a3",43862:"542874fbc9543ad5",43895:"644dab6e446c5544",43935:"3e8bfcbe28fb1b2c",43953:"dd1a64260b852e2d",44032:"98f296e5b851a3b3",44084:"c309ca8fdd48def6",44158:"9e6475499a7ba9bc",44225:"3525388fb11940d4",44425:"024685f1810d5ae3",44539:"cbd590851812d79a",44632:"d23cd7ccdb5bb81c",44680:"b712da883550e3ed",44711:"66cba93563271f17",44745:"f2cafca87e8d371d",44763:"7445714a3303382e",44802:"ddc318cd5fe11db7",44839:"78643d5a8532a8e3",44852:"9f3ae5849f6b1e22",44934:"af0a81dd98494852",44962:"21118b3bb3fc0211",45016:"b655ebffeed5135c",45196:"48394f9ca7eec2a1",45243:"b924a145411b4080",45248:"cd393d87243bd2bf",45351:"d78d5588a65a506b",45624:"e931a4f59df9905f",45683:"a6c8489fbf87fd20",45758:"f3dcc7f5ef4917ea",45813:"3a56ab406b4bd874",45915:"b92ce51add31bd88",46014:"4349b4abda326b79",46030:"bb87099d10b48869",46204:"057ae1ba9200784d",46283:"2679b7bdaa654421",46292:"4184b82798dd911b",46357:"63cb1e18654b7053",46414:"abcdeea7456eb01a",46510:"fcfa9c30ab95016e",46580:"b3036a7a89b37c95",46726:"dd521bb11572077a",46743:"69cde4432716ee87",46827:"818be25add834a51",46913:"a4c0fa98cade5736",46933:"17cf16196240cc53",46990:"937273c3a36e9e13",47057:"6c0ed70070a08900",47108:"5649bc1204666580",47160:"2ee9e2208266f343",47161:"c371eb3fb20d1cf3",47307:"c0337b27f3c228d7",47311:"cd945d2c79af25c0",47381:"31a07e183a58da14",47536:"931bafbee8d47878",47674:"a1ee6299216c7d95",47779:"075a69f5800c07a4",47797:"cd0b8a707fe3616b",48071:"761ec2cbf3598d9a",48113:"02ef7ab61c5377cf",48307:"697492febaea0efe",48486:"003bad49a0dd0601",48591:"9fcda53e3e1e6b71",48686:"ae7507a73b2f7b94",48717:"616e6576b4153683",48760:"ea5ba158ba554046",48823:"e84535f0096f512b",48861:"93b7cf90d3b9da6a",48985:"bdf5abfc30e179ff",48986:"7e64a90b82f6b345",49044:"8285c9331efed60e",49102:"87d62d86afba9402",49121:"206af9c369224b42",49172:"86567f4f5e7f09e7",49189:"3f8321b43e25e7e8",49222:"194816b1a341a08e",49229:"1404c552fe455f4c",49291:"0e05e8d5ac2d63c3",49373:"3162cbe478346ebe",49376:"bdf5abfc30e179ff",49761:"445c767d0b7d8bed",49875:"107ba3e01b648b11",49889:"42fda699803f7de7",49908:"b949cef7cce7077a",49951:"fa2be158429b79bc",50086:"f83feda30c931fe4",50150:"92159cce3ddc52cd",50335:"833ed771425ae054",50437:"e71f0d92dfc81890",50462:"080e47a15e9cc506",50568:"a5e116d618fdc76a",50569:"d8a2200c02465cab",50672:"d2050f5832ecff8f",50708:"b127d52e0e4d92f8",50710:"78f8e709ed3c6db6",51092:"2fdfb0543703ca19",51106:"8a9107fe06e65f88",51207:"10d3a2f4ff020ce8",51226:"0fa139f906bea261",51252:"a69281b6a6fa859b",51363:"08a167b48c3b1459",51466:"0f799f538333c329",51609:"e97fed6d806d03af",51646:"e3ff197215451290",51680:"b8e2eee5329000e2",51706:"5b63f2c0f63a454b",51799:"aa8683f3dfe9ea27",51843:"0e5f14751c09519b",51855:"915406104c56fb28",51859:"f811fd7189fe4c02",51872:"30a7bd650f2c0fe0",51935:"3acd8982b2300c45",52009:"ac6d18f04ed31839",52056:"558d1e49bf9478d6",52084:"7ae5a1147764a3b1",52094:"f7488cacc9e712f9",52274:"904370f33c06e2d2",52491:"15c66947000707ca",52573:"28a5c0000da41e5e",52583:"7b86d937c686db3c",52614:"ce1e66ce12fd4f9b",52651:"4e46de04b1c5c04e",52745:"56a2e4b09eba7754",52840:"dce88f7cef1d2fc1",52903:"5037795b8937cf8a",53062:"ce82b2bf44c3c49e",53066:"565b9567cde093d7",53071:"d14a0543781e88d6",53095:"5de5ba8a78dcb912",53147:"b1372c4120b98563",53179:"1ef8726cf40d2100",53223:"7210fa26628d9545",53237:"d8641f16bfa1831c",53321:"3f9ec927c1551d27",53463:"001181fac41a882b",53491:"a565dd75c9211249",53631:"8440aee0af55dcdb",53638:"05e760faaaf08543",53925:"87d273bcc50db11a",53928:"f71b153e8f91feec",53974:"3e4093fefac7fc8f",53977:"75090ee0c7901d86",54087:"5e3632f865a718db",54134:"83b420cf619500a3",54189:"20391c26367e300c",54215:"bf46cc8f368f3940",54220:"f2c9546e9b244d77",54398:"a36ae65ff562487a",54518:"19f1da4ba8face2f",54564:"f977de56b139a7c5",54643:"90d833bd6f81b86e",54928:"e2414cbaf7efaaad",54980:"7ed0114a63adb7e0",54998:"399a1b9e9483a075",55072:"3829e297fa777cb0",55150:"934efcd5f3eca501",55269:"3b3c03821e050b8e",55373:"56e2765615a50b8b",55570:"a3b570e8925d61d1",55632:"fdde8a62098f5fcb",55719:"b924a145411b4080",55724:"eaf757ff40f838ba",55776:"4b203ab25dbbf09e",55850:"6e2ac0c3db45361b",55901:"aec150089e4c591e",55996:"86986e0abf6b9a80",56051:"f75ae48d9fdeb70a",56188:"9d09fec75db733ff",56301:"d86438f96e2ff6e4",56302:"7cce2aa38275b5b3",56352:"d7e0297c1691aef6",56353:"c9ff8a5825cac976",56388:"722f278bbcf6d674",56407:"08c363e3442ea7f7",56409:"5b7ac63911e8e8b7",56580:"b6b21bd7813ac32a",56591:"14de4c1d1f4eeec2",56780:"84fac78e5dc8e52a",56823:"9b7073d6c4f4711b",56859:"8667531e038e4406",56892:"4844667c02f00852",56940:"dc2855db2c0ab382",56989:"2e7447a02d9476dd",57314:"80d7f9af3dfdd881",57478:"34e309ee38e8c5ff",57507:"ff8e16d53a35d83d",57613:"5c05dfe06e8b0f46",57667:"7690ff4ffe5f783a",57723:"afb2ed83f7edaa9b",57732:"e18bcb43060e51bb",57858:"d2e35916edecef40",57886:"7ddf5176a258d903",58034:"e8b2c3c3a11f3b88",58072:"3109401bd817b51b",58204:"e58faf7f9fe15592",58251:"a6fe871797f8045c",58319:"f5129f14387148ed",58391:"deb859e6cf69b50e",58427:"73f06e96f0e51c20",58431:"47485ac648c1261e",58468:"ebd404c0bc7b979d",58519:"f8ab9ea16ee4fc44",58587:"fdb95f23c9e19ccd",58596:"a9450815bbb3e1ea",58601:"a0116457a00e1884",58657:"9ad741fdbf215e3b",58703:"79ddd99e97bf3361",58777:"847717510a9c5c0c",58786:"2e001a6a5c84d77d",58790:"d025c64be6572c6c",58795:"cc9141390c712ded",59031:"d0ad2b5d6a1c1d22",59042:"71852d1eb01b5d03",59082:"bda5a909cbeb502d",59098:"baa10510302cd167",59111:"4fb67cd59f585224",59230:"62cbec5015bc0edd",59287:"ed26e5ce5fd3ba15",59337:"ccc75e6e3593f8e5",59355:"78e973abcbdc23b4",59388:"b9469308d07c3451",59419:"2bbf89e22554aef1",59430:"1f354e6c4d43fd01",59461:"bdf5abfc30e179ff",59698:"3d4c5a6ab95e0276",59852:"bdf5abfc30e179ff",59970:"2d88d5dd4822cdcd",59994:"fe1226a9db300085",60213:"30e1925e7eae2563",60219:"4b4da4c2a9a8f96e",60245:"8b5b969669925cb3",60257:"1f7c49df6543ccc8",60262:"5c8394b544266095",60280:"b90796ce01fff2f9",60453:"1e1bb778ade1cb21",60475:"cbc74c748cd841b5",60677:"95bdb0f52d6bb266",60747:"59121c7bb357f816",60796:"94d36938da0d3c9f",60858:"ac2d9aa447ed3b57",60896:"375805afe2420013",60983:"aa0b498e8e22dd9d",61177:"e134734b3139f4e8",61242:"ab073176acb4c5bc",61282:"5265e4c8a4cbf1ac",61299:"106c4dfe6afe87a4",61325:"ecd86e672bd927d6",61362:"32f00411d1acb4e9",61440:"0cb2e73c8444c002",61466:"7bd8cf5fab6a9566",61487:"2ea8685921f07791",61488:"e742e556c235ded9",61492:"4ae7fecaf3467461",61560:"6deef8f0ff606ca9",61574:"b1c75c8232229040",61604:"de99113299e26b09",61660:"9bc547a2a15fe6a0",61754:"a27e325479803c82",61843:"b1d30d6d39166312",61967:"d80256258226c436",61969:"3a4f2064fbcabcd3",62033:"7d7741270206c28b",62058:"57ff833ca8ef0623",62153:"59bd960672554a65",62230:"ec36dfea2edcfe73",62334:"9c4f72786f280bb9",62342:"ec57aeb4fa729e2f",62382:"678d0ee0e663a97c",62396:"90c9f7e7054f0a9c",62398:"253c73ae53eb349b",62475:"1219386ef57c4bfd",62516:"cc7528937b839279",62620:"e12b860e7846fb2b",62623:"cb31fd0281db07ff",62850:"3a63dd5b900ac130",62989:"a5d0788013e1521b",63075:"36a79b3e50f27a87",63137:"a1cdf8a672a97ad7",63248:"84ec19d54cbed7ee",63479:"5144c6ec2624ae5b",63516:"a1d54a1cdf6a31af",63538:"b98ec898287db867",63539:"0f8e26e54c103a1b",63717:"8f11b4239b9de4b5",63840:"b4cbef1174af3a05",63882:"4ba30fc31445bac2",63911:"8ebfc6e1da73f198",63990:"35ed86a9ae67c737",63996:"9fe4b76767028589",64038:"d6357b80753f2b59",64070:"f9e93b1220a7b15b",64087:"d6103bc5b7e80987",64114:"2a90eb9d15151290",64199:"1e6e7ec68a010d9e",64253:"b6f42fb375199f9e",64271:"aba2e61a9d7fbc4c",64457:"8723ffd4e9ddc4d8",64477:"a55b5ba0ef32a541",64557:"058c2c28ae1a7ec3",64583:"b73d9424af6dcaae",64626:"1ec188295913d0e6",64632:"0c6f0c36a38fe94d",64888:"93c6da6ec27c174d",64981:"5dd826490fa29a59",65015:"a2afd811e9159a94",65129:"0f79fb46ba5df15c",65213:"393e752b72e6aea3",65351:"16733e29cbfa004e",65441:"878458a402751ebe",65550:"cacad1d40510df06",65688:"0a1bcfa2f9cbeb6b",65728:"0a5944101f2ff1f3",65881:"ce5f69df8a9785bb",66042:"d04ffb5cc358cba5",66073:"8440899e3f3883e6",66110:"20d0a3314fa3843e",66169:"67927355c771b22a",66230:"780836fb803bf34c",66249:"9a39bd4c6cd77b99",66274:"36301603e805f2b8",66320:"e0cd70b39520f228",66375:"851f9c82e7191d16",66446:"ffa6db8a070b115c",66549:"06ce65949fb901cb",66559:"faec19b30da96950",66573:"46e7708e7b85d5c4",66592:"1152b8406ad8f6c5",66626:"be558e0716dbc255",66805:"a4347f4771409638",66944:"28827c06c280ed82",66960:"60060e427add730d",66972:"6a86711e215b05f1",67045:"f74a3a904ff3736e",67048:"8717cad33e84ef2b",67199:"2d241288420d206a",67246:"df710523c0dae4a2",67601:"57c50d143008ccaa",67624:"b924a145411b4080",67678:"f449ac03130b0471",67853:"e92b365be407f8e1",67876:"43bf81e8220dbc7b",67920:"4fbc3ce2848a4682",68070:"329d16c6c200b0c4",68117:"ae5683aa5bdf63f6",68126:"53f0460fe6fbfb09",68224:"7357f4ebd7582257",68280:"eb2224c42a892687",68332:"af37845955384cd4",68410:"684f247f251feb6b",68540:"3a2817788b3a11fa",68542:"d3c8f0525e7a1b0d",68548:"bc032cd5c3ac0806",68563:"299d6c595941301b",68642:"b27ed62e937dfaaa",68744:"1fb9a7a4312d136f",68748:"b83309835c359c66",68849:"724a0e1972ad72b0",68977:"8816ed3cd6aa3cc0",69038:"841e7c6a3ede7a23",69062:"c8014d7cc2cbe2ff",69095:"32332464b240a630",69181:"3f171951f1a2ae56",69184:"eec7036a32045a18",69224:"ceeae64102f52c76",69307:"947d48ae7830eb5f",69355:"20286931cc082ece",69734:"dc1486ea76309d75",69945:"8bcfbd033b7306b2",70039:"7260efc178a63f0e",70074:"96f09ddfda77e55f",70198:"e8654ff145cd6084",70216:"879dfe7723cb50a0",70398:"5794c000f9e686a0",70522:"3c86bb4dcb06e286",70558:"6ce0254170e9f1cf",70568:"fb5466e48c3643bb",70593:"db0a3a0cebb3eadb",70605:"4b522dad2ede62ec",70678:"d6df3b64cb2ebf2d",70728:"deb9586d85d9f571",70757:"1c9661c95b5fe6f6",70854:"a90e16defe81123d",70891:"d7411d39493158f7",70916:"28f36512dedce752",70938:"a99360b433cc5d49",70959:"626bce6db34428e9",70998:"30ca0bd1afe25aeb",71204:"19f4e48ccc0ba13c",71229:"75f8f1d063524e37",71300:"d139279c457b974a",71366:"bdf5abfc30e179ff",71583:"b8dc4220a02be6fb",71621:"4a286735206035d8",71677:"be51d1b07325e49f",71739:"a066044fd5d95267",71768:"2ad91a7fe72b981f",71805:"e1a248cf52e4d3a7",71830:"26c0f46e01e1e913",72077:"f485a33c900c9979",72126:"a13ec87c013a09e3",72423:"3588fddf3023f8e9",72612:"b7392b656337e158",72791:"5832876f9321a9e8",72925:"eae4a75376764511",72934:"8b5368af93a34d91",72982:"3b1df7b7ee666008",73006:"bbab4dbea43675f1",73090:"f7ff8a6ae0e0644b",73109:"0a94924a8be67ce6",73502:"f7113277d64fe725",73585:"1be77e23887742a4",73604:"8036edc5ed84ad63",73718:"de24f32dbe60857e",73801:"17d7926969f9bf03",73865:"d7ab8243b4e5f5a1",73891:"1f2a7a4686ae5b6f",74015:"b2f1ebc142061ca4",74085:"7d77879dce0355f4",74165:"46b77bc42546e9a3",74206:"1a21afc52aaef57c",74421:"85aac10393f73386",74562:"a5c3c94e689b89bf",74688:"52bb72d5c6bf7da3",74728:"7dad028ce8cb3c51",74911:"365fe7e7d4abf98b",75078:"c603d3bde8762888",75104:"5f21c7caf151f7ea",75152:"0d34dd47c9df8b1c",75436:"9ef8df56f86139ea",75528:"ce4c403c14e1dc24",75681:"39a68247f5cbc0c0",75960:"d72dd7bc35cbab4b",76260:"be7949e474f7f245",76378:"921618480cb37e30",76537:"8ce4f3738defb181",76630:"54b2db34800c533c",76690:"02b32ce96c91b6a6",76793:"02b1d9ce71aa7762",76983:"e46d14f54ab341c3",77200:"ae2881fb8f3519ce",77258:"59d2b5619994a914",77282:"37f1f89cf3133b07",77355:"252562d3940d515d",77375:"72641ab675ccb8d9",77466:"82a002cd71c7deb0",77643:"392230a246e721fa",77651:"c863ba24f29db4e7",77676:"3564311a607f050d",77690:"74c23c71b4001e44",77704:"b75392631c64bd7a",77742:"60e784614316cebc",77757:"3be41642a7e232b3",77761:"2a9557ed4c5e8f37",77773:"bc9bcb31a2b0af78",77836:"fa4f0d20f883bbbe",77957:"a31e187a1ac991be",77970:"ecc9d54a973a7d71",78113:"f0cd2c52bd18e90b",78171:"bcd67481067375ff",78205:"e8baace0a24bb2c0",78217:"6497428059f83783",78369:"0363bc25dfe5cb3a",78389:"32c800f1761d815b",78405:"ca28138a9a18ffa0",78472:"a659464af7bf1989",78494:"fd3477a6e08e595b",78574:"1be0b2f8d960e749",78596:"9dddf5f54904eaaa",78724:"97bff6fcc398a02a",78731:"cfdfd8cd3bf4f8e3",78741:"ec015bab3baeb48e",78902:"47eb0470dd55f63f",78984:"5f8391d34d6a7ab1",79008:"073e80c32f32a028",79239:"2cfcf165edf8a4e5",79254:"c51f3914c28d4c6a",79311:"f77ef253017691fa",79344:"b7f0a25b37aeea5c",79665:"1e2b6e5cc58e683a",79883:"24de6a7193a1f899",79977:"52a6e4e6c6aa7ae1",80071:"e378f2f8a736c751",80401:"6e490f01e4e13920",80720:"becaa14928897bd6",80789:"a434ba548f6100bf",80931:"8c5f2444eb1924e8",80946:"605191e200b1e2e9",80956:"9e5b87305a6000f5",80983:"2e87148b83832050",81074:"10a1591411b13a50",81239:"0328fda4e9f4970e",81328:"d89f743850c29a63",81330:"06c8cb102bdb3172",81438:"6714d6fbc3c879f3",81561:"ffdddf81dfc5dc20",81638:"68556cf752e33560",81750:"b2f1a7d0708c0a31",81764:"e784933c2e1a0d07",81768:"2579e51ef99a5e78",81842:"bdf5abfc30e179ff",81917:"f4170e1bd0b7e52a",81929:"b4df03a29f669a02",82028:"dfae36fc2bc70a30",82094:"4750b3c4bce1d7d4",82106:"32e6abc3b7c05b8e",82120:"39acb8dad756737a",82213:"b0374c37522699cd",82233:"bdf5abfc30e179ff",82237:"c350f77b796de390",82387:"48cb7284d495044d",82430:"2ec30096624e2980",82575:"3a3c117c04b17e9b",82594:"749f13855814796e",82619:"c1b1edffa21e71bd",82692:"02602f2a3a7911f3",82720:"54d5bf524e7c547e",82779:"05170c6e44b0d81a",82926:"cba5ef9278d84f5c",82970:"2f92ed9875d9a02f",82985:"10f65d4773b29a42",83042:"9934bb06500aba69",83096:"7dcc37a171a29206",83246:"c46b34a458cf7063",83295:"f79c1019ca190a86",83433:"1926116e7fbcd629",83633:"dfe372d4507cbb1c",83728:"e173651ad29c8997",83750:"50bcb2a68cb317b5",83755:"577371109c702485",83877:"5207d6a8d46c9f73",83965:"b429006748b728b4",84084:"108f56ba453f2e11",84217:"1f38b9b865c11348",84358:"9af990acebd5c92a",84360:"63ba5d67c3d24d6f",84455:"18275572a74b314a",84468:"c8263a6e315a529f",84535:"65f3fcd319792c7e",84571:"1c93a86e2a2a5aa8",84591:"02d3f075036d4476",84605:"e01b7f9ae224ac7a",84672:"1d09cf3aeb17cef2",84909:"e983da5fe32bbc40",84969:"93150080f9d813b5",84977:"aa4bcb9592ddc82a",84994:"69d85b6ad65166df",85078:"fc98c441aba7736b",85128:"45597b7975a68709",85416:"4fa39202d83fa409",85688:"6f82d44791ccae87",85898:"9995bb57b4e10772",85997:"380a00cd411ef80e",86032:"59fe4960e185ff7c",86149:"ae4e67e1f0e6d2b8",86227:"869c4566bcd55c94",86279:"7a23617ef72021f7",86280:"25fd8447a5ea6ccc",86609:"b2b1e87021ca1c32",86763:"74a9b1ef77aa1012",86774:"0411a9ce7e812c31",86830:"9034685c4e749332",86967:"0ba1777f8f543cfe",87102:"743506dad7f9b0ad",87137:"dbd931a17cc21821",87153:"19e09a4f0418cb78",87253:"b81d474af8f401b8",87333:"66e41372a591179d",87344:"fffa79fe4f530ac7",87421:"3e85a97c361a2e39",87543:"1faed38feb056b57",87661:"919f42ed634a8363",87700:"6fefbd56bcdb541b",87771:"3290c87a71e0cf3a",88208:"476f42ff073d2617",88350:"ce987b7d19392a3b",88472:"a6db974304b8dd8e",88488:"f27bfa11f305341e",88566:"70099b383f3838fc",88580:"c3ce39af488f7880",88865:"23c3743f49c9e8de",88873:"527cfa1e02c093db",88895:"cb75aa4858232ca4",88931:"fd56f3e9b8520148",88990:"d32dde24d183f02a",89034:"ae7d4e1e037ad42d",89041:"60a9c1a214d4ac52",89150:"565de5f3c42298d0",89253:"c544fdf7d0688030",89256:"2c1a1f6690c0b746",89306:"1095f975c9fdf3e9",89362:"5dc34c1532c82bf8",89379:"bb5e5444b5a68c75",89440:"e3f7507fc4d1492c",89491:"2caa0753ffa77691",89509:"9835df0260da4264",89708:"d15dfd3c754f56a0",89735:"e145e440d26b8f7a",89838:"eb18551a56ad67b3",89910:"d25127546e938963",89920:"5b675557bb6d51a6",90165:"234ccd678598ddf0",90501:"b4c53ba7ed595f96",90518:"7f58e2a7cf52812c",90825:"1a9fbf5d193e0ecc",90871:"d719ad886e6544ec",90910:"a593fabfea8e7906",90978:"b14aac00c50f5f1f",91067:"b6fa196656abd248",91121:"90857b55cde870ce",91221:"86245ab664fdaa76",91325:"f48563213dbb6977",91710:"db24d97782b1ce0b",91757:"dc08916d164fa1c3",91795:"8b495765f19b64b7",91866:"6fb79fabd44c4037",92214:"d0915abbd7075921",92222:"13f166c23aa4270a",92482:"77f6bf58bb80e401",92579:"efee3172ba067a0f",92586:"2da35ad478964ff9",92658:"0da28f37df354aef",92668:"bac4e89b845b1b58",92705:"08fa6827582a21d6",92709:"bdf5abfc30e179ff",92821:"f7525749ef1bea9a",92845:"ea9521b0846874d3",92903:"e1a73a1ecd4f9f82",92919:"ea89db09e5391c4c",92924:"436d52c64b521806",93139:"1fb377aef69f9e2b",93213:"675868b338bcc75d",93373:"ea76f664a756f2dc",93408:"a297f90349ac5414",93449:"d099c2c9ccf9159d",93518:"9888b2f16c993fe4",93559:"10572cd1f498947a",93629:"a7ccfbd7c899ae93",93747:"bdf5abfc30e179ff",93862:"23f52cb012deef47",94055:"61f696d167cf6738",94097:"6f2347a1be02329c",94146:"a0cb3648d9967716",94153:"fd07753bdb90deef",94211:"8cf2d83b52bb09ae",94242:"43c1cb63dd6d51b8",94334:"1464f88ab21db39d",94366:"1f1c33693ff99445",94379:"944c704614cacf93",94412:"1e7c54c6d2af2593",94476:"8e0fbc30c1714007",94495:"4cf483394346e2e2",94516:"454be40d3c40694a",94562:"37ab57b6304cbd7a",94611:"707efc64cfd14732",94802:"7bfac5f6ebde806d",94814:"b11b09d4f0eb9c90",94826:"a4029159ba76ca6d",94869:"0163ca0b3c320c97",94891:"786eb12e6f767d27",94995:"2f92a3a98ea8f584",95264:"e88b3e57d47cc05a",95281:"a41428e73d31096b",95357:"8b136c27e7ccf537",95490:"3c05cccf41fb22a4",95496:"afe031d35da1af81",95541:"7b802809f7aafb1d",95555:"350b249a0791facb",95600:"6f2deaf3c125f1e6",95605:"067f68a892761ea6",95699:"ef2e2d7f4cd73f16",95737:"005cbd8843a3d981",95747:"1fc8802786bd6b96",95794:"9b8a402b423fc4af",96029:"dae81ef5117bf98e",96166:"604b2a493434e009",96304:"61586fac13fc28e7",96316:"fbf22ee5d79a8df1",96346:"b1c777d727bfd72d",96431:"bb0a88610c31d47d",96599:"e5e69937a1580dc3",96642:"5aa0400c1137834c",96772:"1f646feb8a782681",96773:"78c1f93ce0050dae",96966:"4916b8ea43c84aeb",97101:"fc094e239992f56a",97132:"8e83652e98faaa14",97477:"423b34a50209b69d",97513:"28717dafdf57d25d",97517:"9995a1bfc895cf56",97651:"a77e2b3cb8618841",97836:"0767c72cff2019ba",98012:"b6d990d737c25522",98161:"84678ad534f251b4",98288:"01614b71467d0262",98390:"b2ddbef7be1c471f",98396:"1f21cea77f6b9556",98629:"2afe9d1f77708ad5",98682:"812125eb0a93948e",98713:"4f0904e398e1d2b2",98814:"28f56115b2270417",98873:"25835a4f97d0f96f",98906:"65351cea9eec3cc0",98942:"d651cb1e2c6ab231",99013:"b645100086b93a72",99043:"bae6eafd1ef9f140",99065:"1f04991cdbe72f2d",99108:"65aa397b3626164e",99117:"cbb4198ab2d77e35",99145:"68457ef58f8c43dd",99149:"140635987268cce8",99171:"49e2a7fe7abf2b04",99223:"f4be030fdcb77451",99314:"6cf17b4f65e2d57d",99334:"9e418a473e15438d",99355:"4e8c6ca1b969bf08",99426:"2560af00ee1bf8e7",99533:"16291e5bb3e8b800",99718:"020d5d6851be27c7",99977:"7fb198715e2c702f",99998:"cd4ac750336e899e"}[e]+".js"),s.miniCssF=e=>(({209:"TeamJoinLeaveButton",943:"CollectionViewBlock",1797:"consoleHelpers",8243:"AgentModelPicker",8953:"SidebarTrash",11528:"UISpacePermissionGroupToken",13073:"SidebarFeedButton",19884:"admin",21088:"AuthSyncListener",21871:"CollectionViewSettingsButton",23904:"PostsLaunchModal",24515:"RestrictedCollectionView",28307:"SidebarInboxButton",29623:"SidebarMeetingsButton",31061:"sidebar",33590:"PublicPageSidebarContent",35115:"sidebarMobile",38507:"SidebarAgentsButton",40200:"CreatorProfile",40902:"SidebarLibraryButton",43396:"posts",44745:"UnlistedCollectionViewMoreButton",44802:"personalHome",46283:"AgentChatView",46990:"BlockPropertyRouter",47108:"AIChatView",48071:"activityDigestPostContainer",51363:"TeamHomeMoreMenu",52274:"UnlistedCollectionViewDismissButton",53977:"OutlinerToggleOpenSetupModalButton",56892:"FeedViewOnboardingTooltip",58204:"TipsInAppModal",58703:"AgentThreadHistoryMenu",62516:"RecentsCachingListener",63075:"UnifiedChatInput",63137:"createPageInTeamSync",64626:"feedPage",65129:"react-pdf",68548:"react-day-picker",69224:"CustomDBPanelEmptyState",72982:"SidebarComponent",78217:"mainApp",78928:"abcjs",79239:"LocalizedTemplates",81074:"imageEdit",82926:"AIModePicker",88873:"TabletSidebarButton",89041:"teamHome",89440:"monaco-editor",91866:"topicRecommendationsPost",94412:"LockedSidebarSection",94611:"WorkflowTemplatesBuilderNotInProd",95699:"FullPageAI"}[e]||e)+"-"+{183:"41ece995bf847057",209:"5f55d314f6df5aa4",943:"5f55d314f6df5aa4",1797:"5f55d314f6df5aa4",8243:"5f55d314f6df5aa4",8953:"5f55d314f6df5aa4",11528:"5f55d314f6df5aa4",13073:"5f55d314f6df5aa4",19884:"4c1c9bec6b0f3458",21088:"5f55d314f6df5aa4",21871:"5f55d314f6df5aa4",23904:"5f55d314f6df5aa4",24515:"5f55d314f6df5aa4",28307:"5f55d314f6df5aa4",29623:"5f55d314f6df5aa4",31061:"5f55d314f6df5aa4",33590:"5f55d314f6df5aa4",35115:"5f55d314f6df5aa4",38507:"5f55d314f6df5aa4",40200:"c6e84ce72f917106",40902:"5f55d314f6df5aa4",42399:"bb851e1f054c26d2",43396:"5f55d314f6df5aa4",44745:"5f55d314f6df5aa4",44802:"5f55d314f6df5aa4",46283:"5f55d314f6df5aa4",46990:"5f55d314f6df5aa4",47108:"5f55d314f6df5aa4",48071:"5f55d314f6df5aa4",51363:"5f55d314f6df5aa4",52274:"5f55d314f6df5aa4",53977:"5f55d314f6df5aa4",56892:"5f55d314f6df5aa4",58204:"5f55d314f6df5aa4",58703:"5f55d314f6df5aa4",62516:"5f55d314f6df5aa4",63075:"5f55d314f6df5aa4",63137:"5f55d314f6df5aa4",64626:"5f55d314f6df5aa4",65129:"873fe2f746a92652",68548:"e65956a06954c697",69224:"5f55d314f6df5aa4",72982:"5f55d314f6df5aa4",78217:"5f55d314f6df5aa4",78928:"e521816b91c2b358",79239:"ff3aeefdbeab2881",81074:"99425a3fac7fea0a",82926:"5f55d314f6df5aa4",87421:"e716dd6115ec2313",88873:"5f55d314f6df5aa4",89041:"5f55d314f6df5aa4",89440:"445e289bcbcea942",91866:"5f55d314f6df5aa4",94412:"5f55d314f6df5aa4",94611:"d2cea7f0edb90424",95699:"5f55d314f6df5aa4"}[e]+".css"),s.g=function(){if("object"==typeof globalThis)return globalThis;try{return this||new Function("return this")()}catch(e){if("object"==typeof window)return window}}(),s.hmd=e=>((e=Object.create(e)).children||(e.children=[]),Object.defineProperty(e,"exports",{enumerable:!0,set:()=>{throw new Error("ES Modules may not assign module.exports or exports.*, Use ESM export syntax, instead: "+e.id)}}),e),s.o=(e,t)=>Object.prototype.hasOwnProperty.call(e,t),o={},r="notion-next:",s.l=(e,t,n,a,i)=>{if(o[e])o[e].push(t);else{var c,l;if(void 0!==n)for(var d=document.getElementsByTagName("script"),u=0;u<d.length;u++){var p=d[u];if(p.getAttribute("src")==e||p.getAttribute("data-webpack")==r+n){c=p;break}}c||(l=!0,(c=document.createElement("script")).charset="utf-8",c.timeout=120,s.nc&&c.setAttribute("nonce",s.nc),c.setAttribute("data-webpack",r+n),i&&c.setAttribute("fetchpriority",i),c.src=e),o[e]=[t];var m=(t,n)=>{c.onerror=c.onload=null,clearTimeout(f);var r=o[e];if(delete o[e],c.parentNode&&c.parentNode.removeChild(c),r&&r.forEach((e=>e(n))),t)return t(n)},f=setTimeout(m.bind(null,void 0,{type:"timeout",target:c}),12e4);c.onerror=m.bind(null,c.onerror),c.onload=m.bind(null,c.onload),l&&document.head.appendChild(c)}},s.r=e=>{"undefined"!=typeof Symbol&&Symbol.toStringTag&&Object.defineProperty(e,Symbol.toStringTag,{value:"Module"}),Object.defineProperty(e,"__esModule",{value:!0})},s.nmd=e=>(e.paths=[],e.children||(e.children=[]),e),s.p="/_assets/",(()=>{if("undefined"!=typeof document){var e=e=>new Promise(((t,n)=>{var o=s.miniCssF(e),r=s.p+o;if(((e,t)=>{for(var n=document.getElementsByTagName("link"),o=0;o<n.length;o++){var r=(i=n[o]).getAttribute("data-href")||i.getAttribute("href");if("stylesheet"===i.rel&&(r===e||r===t))return i}var a=document.getElementsByTagName("style");for(o=0;o<a.length;o++){var i;if((r=(i=a[o]).getAttribute("data-href"))===e||r===t)return i}})(o,r))return t();((e,t,n,o,r)=>{var a=document.createElement("link");a.rel="stylesheet",a.type="text/css",a.onerror=a.onload=n=>{if(a.onerror=a.onload=null,"load"===n.type)o();else{var i=n&&("load"===n.type?"missing":n.type),s=n&&n.target&&n.target.href||t,c=new Error("Loading CSS chunk "+e+" failed.\n("+s+")");c.code="CSS_CHUNK_LOAD_FAILED",c.type=i,c.request=s,a.parentNode&&a.parentNode.removeChild(a),r(c)}},a.href=t,n?n.parentNode.insertBefore(a,n.nextSibling):document.head.appendChild(a)})(e,r,null,t,n)})),t={38792:0};s.f.miniCss=(n,o)=>{t[n]?o.push(t[n]):0!==t[n]&&{183:1,209:1,943:1,1797:1,8243:1,8953:1,11528:1,13073:1,19884:1,21088:1,21871:1,23904:1,24515:1,28307:1,29623:1,31061:1,33590:1,35115:1,38507:1,40200:1,40902:1,42399:1,43396:1,44745:1,44802:1,46283:1,46990:1,47108:1,48071:1,51363:1,52274:1,53977:1,56892:1,58204:1,58703:1,62516:1,63075:1,63137:1,64626:1,65129:1,68548:1,69224:1,72982:1,78217:1,78928:1,79239:1,81074:1,82926:1,87421:1,88873:1,89041:1,89440:1,91866:1,94412:1,94611:1,95699:1}[n]&&o.push(t[n]=e(n).then((()=>{t[n]=0}),(e=>{throw delete t[n],e})))}}})(),(()=>{s.b=document.baseURI||self.location.href;var e={38792:0};s.f.j=(t,n,o)=>{var r=s.o(e,t)?e[t]:void 0;if(0!==r)if(r)n.push(r[2]);else if(78928!=t){var a=new Promise(((n,o)=>r=e[t]=[n,o]));n.push(r[2]=a);var i=s.p+s.u(t),c=new Error;s.l(i,(n=>{if(s.o(e,t)&&(0!==(r=e[t])&&(e[t]=void 0),r)){var o=n&&("load"===n.type?"missing":n.type),a=n&&n.target&&n.target.src;c.message="Loading chunk "+t+" failed.\n("+o+": "+a+")",c.name="ChunkLoadError",c.type=o,c.request=a,r[1](c)}}),"chunk-"+t,t,o)}else e[t]=0},s.O.j=t=>0===e[t];var t=(t,n)=>{var o,r,[a,i,c]=n,l=0;if(a.some((t=>0!==e[t]))){for(o in i)s.o(i,o)&&(s.m[o]=i[o]);if(c)var d=c(s)}for(t&&t(n);l<a.length;l++)r=a[l],s.o(e,r)&&e[r]&&e[r][0](),e[r]=0;return s.O(d)},n=globalThis.webpackChunknotion_next=globalThis.webpackChunknotion_next||[];n.forEach(t.bind(null,0)),n.push=t.bind(null,n.push.bind(n))})(),s.nc=void 0;var c=s.O(void 0,[75134,6187,17039,67006,68336],(()=>s(474618)));c=s.O(c)})();