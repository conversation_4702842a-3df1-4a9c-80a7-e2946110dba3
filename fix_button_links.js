// 修复按钮跳转功能的JavaScript代码
(function() {
    console.log('开始修复按钮链接...');
    
    // 等待页面加载完成
    function initButtonFix() {
        // 查找所有包含"免费开放的试用平台"的按钮
        const buttons = document.querySelectorAll('[role="button"]');
        let fixedCount = 0;
        
        buttons.forEach(button => {
            const text = button.textContent;
            
            // 检查按钮文本并添加对应的跳转链接
            if (text.includes('免费开放的试用平台') || 
                text.includes('免费定制的测试体验') || 
                text.includes('我们的免费AI体验工具')) {
                
                button.style.cursor = 'pointer';
                button.addEventListener('click', function(e) {
                    e.preventDefault();
                    e.stopPropagation();
                    window.open('https://ceshi.cyimg.cn/', '_blank');
                    console.log('跳转到测试平台:', text);
                });
                fixedCount++;
            }
            
            // 检查提示词工程目录按钮
            if (text.includes('所有提示词工程目录') || text.includes('不会过时的AI使用方法')) {
                button.style.cursor = 'pointer';
                button.addEventListener('click', function(e) {
                    e.preventDefault();
                    e.stopPropagation();
                    // 这里可以链接到具体的Notion页面
                    window.open('https://long-stamp-738.notion.site/150-244e39b4a51b81cb9d8af245ad0e939e?pvs=25', '_blank');
                    console.log('跳转到提示词目录:', text);
                });
                fixedCount++;
            }
            
            // 免费试听的重点课程
            if (text.includes('免费试听的重点课程')) {
                button.style.cursor = 'pointer';
                button.addEventListener('click', function(e) {
                    e.preventDefault();
                    e.stopPropagation();
                    window.open('https://long-stamp-738.notion.site/244e39b4a51b8185837fd8a7cfc8ea54?pvs=25', '_blank');
                    console.log('跳转到重点课程:', text);
                });
                fixedCount++;
            }
        });
        
        console.log(`已修复 ${fixedCount} 个按钮的跳转功能`);
    }
    
    // 页面加载完成后执行
    if (document.readyState === 'loading') {
        document.addEventListener('DOMContentLoaded', initButtonFix);
    } else {
        // 如果页面已经加载完成，延迟一下确保所有元素都渲染完毕
        setTimeout(initButtonFix, 1000);
    }
})();